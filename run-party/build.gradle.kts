import com.meta.compat.task.PushVersionConfig
import java.io.FileInputStream
import java.util.Properties

plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.meta.compat.manager.version")
}

val varyCode = Version.varyCode(Flavor.PARTY)
val varyName = Version.varyName(Flavor.PARTY)
android {
    namespace = "com.meta.box.party"
    compileSdk = Version.COMPILE_SDK

    // 添加 buildFeatures 配置
    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        applicationId = "com.meta.box.party"
        minSdk = Version.MIN_SDK
        targetSdk = Version.TARGET_SDK
        versionCode = varyCode
        versionName = varyName

        EnvConfigs.getManifests(Flavor.PARTY).forEach {
            manifestPlaceholders[it.key] = it.value
        }
    }

    signingConfigs {
        create("release") {
            val keystorePropertiesFile = project.file("./keystore.properties")
            if (keystorePropertiesFile.exists()) {
                val keystoreProperties = Properties()
                keystoreProperties.load(FileInputStream(keystorePropertiesFile))
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
            }
            enableV1Signing = true // 启用 JAR 签名
            enableV2Signing = true // 启用 APK 签名方案 v2
        }
    }
    buildTypes {
        getByName("debug") {
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("release")
        }
        getByName("release") {
            isDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                file(project(":dev").file("proguard-rules.pro")),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    flavorDimensions += "app"

    val partyDefImplementation by configurations.creating
    val partyDouyinImplementation by configurations.creating

    productFlavors {
        create("partyDef") {
            dimension = "app"
            // Party-Def（原理是因为missingDimensionStrategy定义了当前Flavor不存在的dimension时，使用哪个Flavor。因为壳子工程没有party或者gpark等flavor，所以会使用默认的）
            missingDimensionStrategy("product", "party")
            missingDimensionStrategy("channel", "def")
            dependencies {
                partyDefImplementation(fileTree("dir" to "../dev/src/party/libs", "include" to listOf("*.jar", "*.aar")))
            }
        }
        create("partyDouyin") {
            dimension = "app"
            // Party-Douyin
            missingDimensionStrategy("product", "party")
            missingDimensionStrategy("channel", "douyin")
            dependencies {
                partyDouyinImplementation(fileTree("dir" to "../dev/src/party/libs", "include" to listOf("*.jar", "*.aar")))
                partyDouyinImplementation(fileTree("dir" to "../dev/src/douyin/libs", "include" to listOf("*.jar", "*.aar")))
            }
        }
    }
}

dependencies {
    implementation(Libs.TIMBER)
    implementation(Libs.PANDORA)
    implementation(Libs.LIFECYCLE_RUNTIME)
    implementation(Libs.LIFECYCLE_LIVEDATA)
    implementation(Libs.EXOPLAYER_UI)
    implementation(Libs.EXOPLAYER_CORE)
    implementation(project(":dev"))
}

//apply(from = "push_version_mananger.kts")
fun firstUpperCase(flavor: String): String {
    if (flavor.isEmpty()) {
        return flavor
    }
    val chars = flavor.toCharArray()
    chars.set(0, chars.get(0) - 32)
    return String(chars)
}

try {
    tasks.named("PUSH-VERSION-MANAGER-CONFIG", PushVersionConfig::class) {
        flavor = project.properties["flavor"]?.toString() ?: ""
        dev = flavor
        projectName = "product"
        devTaskName = "assemble${firstUpperCase(flavor)}Release"
        devDebugTaskName = "assemble${firstUpperCase(flavor)}Debug"
        var app_version_name = System.getenv("app_version_name")
        var app_version_code = System.getenv("app_version_code")?.toIntOrNull()

        println("Version.app_version_name=$app_version_name")
        println("Version.app_version_code=$app_version_code")
        val varyName2 = app_version_name ?: Version.varyName(Flavor.PARTY)
        val varyCode2 = app_version_code ?: Version.varyCode(Flavor.PARTY)
        appVersionName = varyName2
        appVersionCode = varyCode2

        println("devTaskName=${devTaskName}")
        println("devDebugTaskName=${devDebugTaskName}")
        println("Version.NAME=$varyName2")
        println("Version.CODE=$varyCode2")
    }
} catch (e: Throwable) {
    e.printStackTrace()
}

jiagu {
    qihu {
        path = System.getenv("JIAGU_360_PATH") ?: ""
        username = "13810842545"
        password = "heshang4"
        if (!path.isEmpty()) {
            enable = true
            println("360加固工具路径:${path}")
        }
    }

    legu {
        path = System.getenv("JIAGU_LEGU_PATH") ?: ""
        appId = "AKIDPbV8qmDuaMSHpEhSpvRbV8UZ9ze2s31q"
        appKey = "g5HH1VfX9XeXwCFG0nrrEdrwCF4ez4uS"
        if (!path.isEmpty()) {
            enable = true
            println("乐固加固工具路径:${path}")
        }
    }
}

project.tasks.forEach { theTask: Task ->
    if (theTask.name.startsWith("generate") && theTask.name.endsWith("Assets")) {
        theTask.doFirst {
            // 1.生成assets/metaappinfo文件
            println("生成assets/metaappinfo文件 appVersionCode=${varyCode}, realVersionCode=${varyCode}")
            val writer = File(project.projectDir, "/src/main/assets/metaappinfo").writer(Charsets.UTF_8)
            writer.write("META_VERSION_CODE=${varyCode}")
            writer.append("\r\n")
            writer.append("META_VERSION_NAME=${varyName}")
            println("META_VERSION_CODE=$varyCode, META_VERSION_NAME=${varyName}")
        }
        theTask.doLast {
            println("最后assets里的文件")
        }
    }
}

project.tasks.forEach { theTask: Task ->
    // generateDevChinaDebugAssets
    if (theTask.name.startsWith("generate") && theTask.name.endsWith("Assets")) {
        theTask.doFirst {
            // 1.生成assets/metaappinfo文件
            println("生成assets/metaappinfo文件 appVersionCode=$varyCode, realVersionCode=${varyName}")
            val writer = File(project.projectDir, "/src/main/assets/metaappinfo").writer(Charsets.UTF_8)
            writer.write("META_VERSION_CODE=$varyCode")
            writer.append("\r\n")
            writer.append("META_VERSION_NAME=${varyName}")

            println("META_VERSION_CODE=$varyCode, META_VERSION_NAME=${varyName}")
        }
        theTask.doLast {
            println("最后assets里的文件")
        }
    }
}