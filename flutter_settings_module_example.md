# Flutter设置模块实现示例

## 1. Android端集成代码

### 1.1 Flutter Activity 包装器
```kotlin
// FlutterSettingsActivity.kt
package com.socialplay.gpark.flutter.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngineCache
import com.socialplay.gpark.flutter.FlutterBridgeManager

class FlutterSettingsActivity : FlutterActivity() {
    
    companion object {
        private const val ENGINE_ID = "gpark_flutter_engine"
        
        fun createIntent(context: Context, initialRoute: String = "/settings"): Intent {
            return FlutterActivity
                .withCachedEngine(ENGINE_ID)
                .build(context)
                .apply {
                    putExtra("route", initialRoute)
                }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // 确保Flutter引擎已初始化
        FlutterBridgeManager.getInstance().initialize(this)
        super.onCreate(savedInstanceState)
    }
    
    override fun getInitialRoute(): String {
        return intent.getStringExtra("route") ?: "/settings"
    }
}
```

### 1.2 原生设置页面集成
```kotlin
// 修改原有的 SettingFragment.kt
package com.socialplay.gpark.ui.account.setting

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.flutter.ui.FlutterSettingsActivity
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.function.pandora.PandoraToggle

class SettingFragment : BaseFragment<FragmentSettingBinding>() {
    
    override fun createBinding(inflater: LayoutInflater, container: ViewGroup?) =
        FragmentSettingBinding.inflate(inflater, container, false)
    
    override fun initView() {
        // 检查是否启用Flutter设置页面
        if (PandoraToggle.isFlutterSettingsEnabled()) {
            navigateToFlutterSettings()
        } else {
            // 保持原有的原生实现
            setupNativeSettings()
        }
    }
    
    private fun navigateToFlutterSettings() {
        val intent = FlutterSettingsActivity.createIntent(requireContext(), "/settings")
        startActivity(intent)
        requireActivity().finish()
    }
    
    private fun setupNativeSettings() {
        // 原有的设置页面逻辑
        // ...
    }
}
```

## 2. Flutter端实现

### 2.1 设置数据模型
```dart
// lib/modules/settings/data/models/settings_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_model.freezed.dart';
part 'settings_model.g.dart';

@freezed
class SettingsModel with _$SettingsModel {
  const factory SettingsModel({
    @Default(true) bool notificationsEnabled,
    @Default(true) bool soundEnabled,
    @Default(true) bool vibrationEnabled,
    @Default('auto') String language,
    @Default('auto') String theme,
    @Default(true) bool autoUpdate,
    @Default(false) bool debugMode,
    @Default(true) bool friendRequestNotification,
    @Default(true) bool gameInviteNotification,
    @Default(true) bool systemNotification,
  }) = _SettingsModel;
  
  factory SettingsModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsModelFromJson(json);
}

@freezed
class SettingItem with _$SettingItem {
  const factory SettingItem({
    required String key,
    required String title,
    String? subtitle,
    required SettingType type,
    dynamic value,
    List<SettingOption>? options,
    VoidCallback? onTap,
  }) = _SettingItem;
}

@freezed
class SettingOption with _$SettingOption {
  const factory SettingOption({
    required String label,
    required dynamic value,
  }) = _SettingOption;
}

enum SettingType {
  toggle,
  selection,
  navigation,
  action,
}
```

### 2.2 设置仓库
```dart
// lib/modules/settings/data/repositories/settings_repository.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/settings_model.dart';
import '../datasources/settings_datasource.dart';

part 'settings_repository.g.dart';

@riverpod
SettingsRepository settingsRepository(SettingsRepositoryRef ref) {
  return SettingsRepository(ref.watch(settingsDataSourceProvider));
}

class SettingsRepository {
  final SettingsDataSource _dataSource;
  
  SettingsRepository(this._dataSource);
  
  Future<SettingsModel> getSettings() async {
    return await _dataSource.getSettings();
  }
  
  Future<bool> updateSettings(SettingsModel settings) async {
    return await _dataSource.updateSettings(settings);
  }
  
  Future<bool> updateSetting(String key, dynamic value) async {
    return await _dataSource.updateSetting(key, value);
  }
}
```

### 2.3 设置数据源
```dart
// lib/modules/settings/data/datasources/settings_datasource.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';
import '../../../core/bridge/bridge_service.dart';

part 'settings_datasource.g.dart';

@riverpod
SettingsDataSource settingsDataSource(SettingsDataSourceRef ref) {
  return SettingsDataSource();
}

class SettingsDataSource {
  static const String _settingsKey = 'app_settings';
  
  Future<SettingsModel> getSettings() async {
    try {
      // 首先尝试从原生端获取设置
      final nativeSettings = await BridgeService.getSettings();
      if (nativeSettings != null) {
        return SettingsModel.fromJson(nativeSettings);
      }
      
      // 如果原生端没有，从本地存储获取
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        return SettingsModel.fromJson(json.decode(settingsJson));
      }
      
      // 返回默认设置
      return const SettingsModel();
    } catch (e) {
      print('Error getting settings: $e');
      return const SettingsModel();
    }
  }
  
  Future<bool> updateSettings(SettingsModel settings) async {
    try {
      // 同步到原生端
      await BridgeService.updateSettings(settings.toJson());
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, json.encode(settings.toJson()));
      
      return true;
    } catch (e) {
      print('Error updating settings: $e');
      return false;
    }
  }
  
  Future<bool> updateSetting(String key, dynamic value) async {
    try {
      final currentSettings = await getSettings();
      final updatedSettings = _updateSettingValue(currentSettings, key, value);
      return await updateSettings(updatedSettings);
    } catch (e) {
      print('Error updating setting: $e');
      return false;
    }
  }
  
  SettingsModel _updateSettingValue(SettingsModel settings, String key, dynamic value) {
    switch (key) {
      case 'notificationsEnabled':
        return settings.copyWith(notificationsEnabled: value as bool);
      case 'soundEnabled':
        return settings.copyWith(soundEnabled: value as bool);
      case 'vibrationEnabled':
        return settings.copyWith(vibrationEnabled: value as bool);
      case 'language':
        return settings.copyWith(language: value as String);
      case 'theme':
        return settings.copyWith(theme: value as String);
      case 'autoUpdate':
        return settings.copyWith(autoUpdate: value as bool);
      case 'debugMode':
        return settings.copyWith(debugMode: value as bool);
      case 'friendRequestNotification':
        return settings.copyWith(friendRequestNotification: value as bool);
      case 'gameInviteNotification':
        return settings.copyWith(gameInviteNotification: value as bool);
      case 'systemNotification':
        return settings.copyWith(systemNotification: value as bool);
      default:
        return settings;
    }
  }
}
```

### 2.4 设置状态管理
```dart
// lib/modules/settings/presentation/providers/settings_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/models/settings_model.dart';
import '../../data/repositories/settings_repository.dart';

part 'settings_provider.g.dart';

@riverpod
class SettingsNotifier extends _$SettingsNotifier {
  @override
  Future<SettingsModel> build() async {
    final repository = ref.watch(settingsRepositoryProvider);
    return await repository.getSettings();
  }
  
  Future<void> updateSetting(String key, dynamic value) async {
    final repository = ref.watch(settingsRepositoryProvider);
    
    // 乐观更新
    final currentState = await future;
    state = AsyncValue.data(_updateSettingValue(currentState, key, value));
    
    // 实际更新
    final success = await repository.updateSetting(key, value);
    if (!success) {
      // 如果失败，回滚状态
      state = AsyncValue.data(currentState);
    }
  }
  
  SettingsModel _updateSettingValue(SettingsModel settings, String key, dynamic value) {
    switch (key) {
      case 'notificationsEnabled':
        return settings.copyWith(notificationsEnabled: value as bool);
      case 'soundEnabled':
        return settings.copyWith(soundEnabled: value as bool);
      case 'vibrationEnabled':
        return settings.copyWith(vibrationEnabled: value as bool);
      case 'language':
        return settings.copyWith(language: value as String);
      case 'theme':
        return settings.copyWith(theme: value as String);
      case 'autoUpdate':
        return settings.copyWith(autoUpdate: value as bool);
      case 'debugMode':
        return settings.copyWith(debugMode: value as bool);
      case 'friendRequestNotification':
        return settings.copyWith(friendRequestNotification: value as bool);
      case 'gameInviteNotification':
        return settings.copyWith(gameInviteNotification: value as bool);
      case 'systemNotification':
        return settings.copyWith(systemNotification: value as bool);
      default:
        return settings;
    }
  }
}

@riverpod
List<SettingItem> settingItems(SettingItemsRef ref) {
  final settingsAsync = ref.watch(settingsNotifierProvider);
  
  return settingsAsync.when(
    data: (settings) => [
      // 通知设置
      const SettingItem(
        key: 'notification_header',
        title: '通知设置',
        type: SettingType.navigation,
      ),
      SettingItem(
        key: 'notificationsEnabled',
        title: '推送通知',
        subtitle: '接收应用推送通知',
        type: SettingType.toggle,
        value: settings.notificationsEnabled,
      ),
      SettingItem(
        key: 'friendRequestNotification',
        title: '好友请求',
        subtitle: '接收好友请求通知',
        type: SettingType.toggle,
        value: settings.friendRequestNotification,
      ),
      SettingItem(
        key: 'gameInviteNotification',
        title: '游戏邀请',
        subtitle: '接收游戏邀请通知',
        type: SettingType.toggle,
        value: settings.gameInviteNotification,
      ),
      
      // 声音和震动
      const SettingItem(
        key: 'sound_header',
        title: '声音和震动',
        type: SettingType.navigation,
      ),
      SettingItem(
        key: 'soundEnabled',
        title: '声音',
        subtitle: '启用应用声音',
        type: SettingType.toggle,
        value: settings.soundEnabled,
      ),
      SettingItem(
        key: 'vibrationEnabled',
        title: '震动',
        subtitle: '启用震动反馈',
        type: SettingType.toggle,
        value: settings.vibrationEnabled,
      ),
      
      // 显示设置
      const SettingItem(
        key: 'display_header',
        title: '显示设置',
        type: SettingType.navigation,
      ),
      SettingItem(
        key: 'language',
        title: '语言',
        subtitle: _getLanguageLabel(settings.language),
        type: SettingType.selection,
        value: settings.language,
        options: const [
          SettingOption(label: '自动', value: 'auto'),
          SettingOption(label: '中文', value: 'zh'),
          SettingOption(label: 'English', value: 'en'),
        ],
      ),
      SettingItem(
        key: 'theme',
        title: '主题',
        subtitle: _getThemeLabel(settings.theme),
        type: SettingType.selection,
        value: settings.theme,
        options: const [
          SettingOption(label: '跟随系统', value: 'auto'),
          SettingOption(label: '浅色', value: 'light'),
          SettingOption(label: '深色', value: 'dark'),
        ],
      ),
      
      // 其他设置
      const SettingItem(
        key: 'other_header',
        title: '其他',
        type: SettingType.navigation,
      ),
      SettingItem(
        key: 'autoUpdate',
        title: '自动更新',
        subtitle: '自动下载应用更新',
        type: SettingType.toggle,
        value: settings.autoUpdate,
      ),
      const SettingItem(
        key: 'about',
        title: '关于',
        type: SettingType.navigation,
      ),
      const SettingItem(
        key: 'logout',
        title: '退出登录',
        type: SettingType.action,
      ),
    ],
    loading: () => [],
    error: (_, __) => [],
  );
}

String _getLanguageLabel(String language) {
  switch (language) {
    case 'zh':
      return '中文';
    case 'en':
      return 'English';
    case 'auto':
    default:
      return '自动';
  }
}

String _getThemeLabel(String theme) {
  switch (theme) {
    case 'light':
      return '浅色';
    case 'dark':
      return '深色';
    case 'auto':
    default:
      return '跟随系统';
  }
}
```

这个设置模块实现提供了：

1. **完整的数据流** - 从原生端同步设置，支持双向数据绑定
2. **类型安全** - 使用Freezed生成不可变数据类
3. **状态管理** - 使用Riverpod进行响应式状态管理
4. **本地缓存** - 支持离线使用和快速加载
5. **错误处理** - 完善的异常处理和回滚机制

您希望我继续实现设置页面的UI部分，还是开始实现其他模块（如个人资料或朋友系统）？
