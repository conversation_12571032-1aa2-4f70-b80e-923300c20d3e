import com.android.tools.r8.internal.fa
import org.gradle.api.Project

object EnvConfigs {

    val gparkPool: EnvConfigProperties.CastMap = EnvConfigProperties.CastMap(emptyMap())
    val systemEnvType: String? = System.getenv("party_env") ?: "Online"

    fun check(project: Project) {
        if (gparkPool.isEmpty()) {
            val newGparkPool = EnvConfigProperties.load("envconfig.properties")
            gparkPool.putAll(newGparkPool)
            //使用Gradle参数覆盖默认配置，方便jenkins打包
            gparkPool.putAll(EnvConfigProperties.CastMap(project.properties as Map<String, Any>))
        }
    }

    fun getValFromProperties(flavor: Flavor, name: String): Any? {
        return when (flavor) {
            Flavor.G_PARK -> {
                gparkPool[name]
            }

            else -> {
                null
            }
        }
    }

    val configs = setOf(
        ConfigField(
            itemType = setOf(ItemType.GRADLE_CONFIG, ItemType.BUILD_CONFIG, ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "APPLICATION_ID",
            value = mapOf(
                Flavor.G_PARK to "com.socialplay.gpark.dev",
                Flavor.PARTY to "com.meta.box.party",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.RESOURCE),
            /*buildConfigType = BuildConfigType.STRING,*/
            name = "APP_NAME",
            value = mapOf(Flavor.G_PARK to "GPark-Dev", Flavor.PARTY to "233派对"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "LOG_DEBUG",
            value = mapOf(Flavor.G_PARK to true, Flavor.PARTY to true),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "ENV_TYPE",
            value = mapOf(
                Flavor.G_PARK to "Test",
                Flavor.PARTY to systemEnvType,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "ENV_SCOPE",
            value = mapOf(
                Flavor.G_PARK to """
            {"Test","Dev"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"Test","Dev","Pre","Online"}
        """.trimIndent(),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "DOMAIN_NAME",
            value = mapOf(
                Flavor.G_PARK to """
            {"api.metaworld.fun","dev-api.metaworld.fun"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"test-api.233party.com","dev-api.233party.com","pre-api.233party.com","api.233party.com"}
        """.trimIndent(),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "DOMAIN_URL_PREFIX",
            value = mapOf(
                Flavor.G_PARK to """
            {"https://","http://"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"http://","http://","http://","https://"}
        """.trimIndent(),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "MW_CORE_URL",
            value = mapOf(
                Flavor.G_PARK to """
            {"http://metaverse-api.metaworld.fun","http://metaverse-api.metaworld.fun"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"http://test1010-api.meta-verse.co","http://dev-api.meta-verse.co","http://pre-api.meta-verse.co","https://api.meta-verse.co"}
        """.trimIndent(),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "MW_ROOM_URL",
            value = mapOf(
                Flavor.G_PARK to """
            {"ws://gate-api.metaworld.fun:20011","ws://gate-api.metaworld.fun:20011"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"ws://test-ds.meta-verse.co:20011","ws://dev-ds.meta-verse.co:20011","ws://pre-ds.meta-verse.co:20011","ws://ds.meta-verse.co:20011"}
        """.trimIndent(),
            ),
        ),
        // 签名证书文件
        ConfigField(
            itemType = setOf(ItemType.GRADLE_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APK_SIGNING_FILE_PATH",
            value = mapOf(Flavor.G_PARK to "app-dev.jks" /*Flavor.PARTY to ""*/),
        ),
        ConfigField(
            itemType = setOf(ItemType.GRADLE_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APK_SIGNING_KEY_ALIAS",
            value = mapOf(Flavor.G_PARK to "gachu-dev" /*Flavor.PARTY to ""*/),
        ),
        ConfigField(
            itemType = setOf(ItemType.GRADLE_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APK_SIGNING_PASSWORD",
            value = mapOf(Flavor.G_PARK to "gachu-dev" /*Flavor.PARTY to ""*/),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "USER_AGREEMENT",
            value = mapOf(
                Flavor.G_PARK to "https://gpark.fun/userprivacy/gpark/user_agreement.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=TERMS+OF+SERVICE&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/fzawdwgv2o1dnfxqvtlcueavnrh.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=233%E6%B4%BE%E5%AF%B9%E7%94%A8%E6%88%B7%E5%8D%8F%E8%AE%AE&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "PRIVACY_AGREEMENT",
            value = mapOf(
                Flavor.G_PARK to "https://gpark.fun/userprivacy/gpark/privacy_policy.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=PRIVACY+POLICY&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/qn6xd7ll6o01i9x2xo6cnb7dnkq.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=233%E6%B4%BE%E5%AF%B9%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_CHILDREN_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/c6zzdkfddoxb7cxtnn7cli2yn5c.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=233派对儿童隐私保护指引&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_DISCLAIMER_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/cwildx3zmo9x2fxegw0cahkknhc.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=免责声明&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_DESTROY_ACCOUNT_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/gshedecqdob0xrx2adxcgd6rnqf.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=账号注销协议&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_CREATOR_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/o17cdskbqolahrxciricotf7nzd.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=创作者协议&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_COMPLAINING_INFRINGEMENT_GUIDELINES",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/ihrzdkzstofh9ax3hllcdpb0nsf.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=侵权投诉指引&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_THIRD_SDK_LIST_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/iyd8dcox8o6jnnxhl3gce4trn3c.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=第三方SDK目录及相关说明&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_REALNAME_NEED_PROTOCOL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/qhgvdfcscozpgzxbcwbcbcsxn2q.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=233小游戏隐私政策摘要&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_WEB_URL_RECORD",
            value = mapOf(
                Flavor.PARTY to "https://beian.miit.gov.cn?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=ICP%2FIP%E5%9C%B0%E5%9D%80%2F%E5%9F%9F%E5%90%8D%E4%BF%A1%E6%81%AF%E5%A4%87%E6%A1%88%E5%85%B3%E7%B3%BB%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "H5_CONFIG_ID_WEB_URL_YOUTHS_LIMIT_NOTICE",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-a1.233leyuan.com/useragreement/use_newteengers.html",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "RECHARGE_URL",
            value = mapOf(
                Flavor.G_PARK to "https://testtest-intra-mpa-mobile.233nan.cn/",
                Flavor.PARTY to "https://testtest-intra-mpa-mobile.233nan.cn/",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "RECHARGE_DIALOG_URL",
            value = mapOf(
                Flavor.G_PARK to "http://testtest-intra-mpa-mobile.233nan.cn/balance",
                Flavor.PARTY to "http://testtest-intra-mpa-mobile.233nan.cn/balance",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "RECHARGE_PROTOCOL_URL",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/LYmqddE9RoS5MaxuygjcXJDJnef.html?sys_webview_top_position=T2&sys_is_render_title=true&sys_is_title_menu=false&sys_title_bg_color=FFFFFFFF&sys_title_content=用户充值协议&sys_tabbar_bg_color=FFFFFFFF",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "GPARK_PLUS",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/proxy/subscription/?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "http://testgpark-test-intra-mpa-mobile.233nan.cn/proxy/subscription",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "GPARK_PLUS_STATUS",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/proxy/premium/?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://app-v7.233leyuan.com/operation/arealist/?sys_webview_top_position=T1&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APP_DOWNLOAD",
            value = mapOf(
                Flavor.G_PARK to "https://app-external.gpark.fun/proxy/share/g-download?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://m.233party.com/?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
            ),
        ),
        // TODO 从未用到过
        ConfigField(
            itemType = setOf(/*ItemType.BUILD_CONFIG*/),
            buildConfigType = BuildConfigType.STRING,
            name = "COMMUNITY_RULE_URL",
            value = mapOf(
                Flavor.G_PARK to "https://web-static-mir-01-qn.jaxine.xyz/fs-doc/gpark/docx/GayodCTRZoZOufxBdj8cPL7gnwe.html",
                Flavor.PARTY to "https://web-static-mir-01-qn.jaxine.xyz/fs-doc/gpark/docx/GayodCTRZoZOufxBdj8cPL7gnwe.html",
            ),
        ),
        // TODO 从未用到过
        ConfigField(
            itemType = setOf(/*ItemType.BUILD_CONFIG*/),
            buildConfigType = BuildConfigType.STRING,
            name = "POST_RULE_IMAGE_URL",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/jCIiZuoDyCra1724830028066.webp",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/jCIiZuoDyCra1724830028066.webp",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "GPARK_ACTIVITY_ENTRANCE",
            value = mapOf(
                Flavor.G_PARK to "https://app-pdd-mobile.gpark.fun/proxy/turntable/?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://app-intra.233leyuan.com/proxy/userTitle?sys_webview_top_position=T1&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "DAILY_SIGN_WEB_URL",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/proxy/welfare-task/?sys_webview_top_position=T0&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=00000000&isTranslucentTop=false&____isTranslucentTop=false&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://testtest-feat-gpark-daily-task-intra-mpa-mobile.metaworld.link/proxy/welfare-task",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SPARK_INSTRUCTION",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/light-up/spark-usage-instructions.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://web-static-mir-01-qn.gpark.fun/fs-doc/gpark/docx/EwiYdg7Mboa3sGxQER8cKyvOn6g.html",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SPARK_ACCOUNT",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/proxy/light-up/?sys_webview_top_position=T1&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://b2k3oke.freeshare.store/proxy/light-up/",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "TRUST_QR_HOST_DEFAULT",
            value = mapOf(Flavor.G_PARK to "*.metaworld.fun", Flavor.PARTY to "*.233party.com"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_BG_AVATAR",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1679627888143_614.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1679627888143_614.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_DEFAULT_AVATAR",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1680486399117_461.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1680486399117_461.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_CHOOSE_ROLE_DEFAULT_PORTRAIT",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1681097039887_621.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1681097039887_621.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_CHOOSE_ROLE_DEFAULT_WHOLE",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1681097039803_748.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1681097039803_748.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CND_CREATE_BG",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1688695600611_498.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1688695600611_498.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CND_CREATE_BG_CENTER",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1688695453549_638.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1688695453549_638.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROOM_DETAIL",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1689066477021_217.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1689066477021_217.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROOM_ITEM_BG",
            value = mapOf(
                Flavor.G_PARK to "http://test7niu.233xyx.com/1692081102711_797.png",
                Flavor.PARTY to "http://test7niu.233xyx.com/1692081102711_797.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_DEFAULT_FULL_BODY_IMG",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/PeKQyqkgmkG61698134120013.png",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/PeKQyqkgmkG61698134120013.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_CREATE_V2_BG",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/ORykG6k0A4oM1700205186463.png",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/ORykG6k0A4oM1700205186463.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_1_PORTRAIT",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/lXZRRvrVh5tV1726825056705.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/cdJYPBkzcpIT1731378236580.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_1_WHOLE",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/p7w6HsR4gXbt1726825055603.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/J1JMz55jEqJq1731378237589.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_1_WHOLE2",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/HqOwvULIv4bE1726828623400.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/J1JMz55jEqJq1731378237589.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_2_PORTRAIT",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/7tPd3GyKaZyf1726825057220.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/UiaFJTEOstaq1731378237108.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_2_WHOLE",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/STncrvmuFH3q1726825056193.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/inRLfteQYmLg1731378238574.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_ROLE_V2_DEFAULT_2_WHOLE2",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/A1TJYlHSYY0z1726828622651.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/inRLfteQYmLg1731378238574.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_AI_BOT_GENDER_NON",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/73a711fd9f1b43c79c9f0c5ec1654266_148811.webp",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/73a711fd9f1b43c79c9f0c5ec1654266_148811.webp",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_AI_BOT_GENDER_FEMALE",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/5bc53e4a0583428db3b0b26982efb4c6_148812.webp",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/5bc53e4a0583428db3b0b26982efb4c6_148812.webp",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_AI_BOT_GENDER_MALE",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/e146d7cf0a4d4d92a641d08182b1466c_148813.webp",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/athena/upload_file/test/e146d7cf0a4d4d92a641d08182b1466c_148813.webp",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CND_AB_BOT_UGC_BOTTOM_BG",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/X8ZvlvWYrd5L1725247293366.png",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/X8ZvlvWYrd5L1725247293366.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CND_AB_BOT_UGC_TOP_BG",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/3UmrefNjVzjl1725247292764.png",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/3UmrefNjVzjl1725247292764.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GAME_DETAIL_LONG_IMAGE_BG",
            value = mapOf(
                Flavor.G_PARK to "https://qn-check-basic-content.metaworld.fun/test1010/Z1QF7psRMfTk1724822989881.png",
                Flavor.PARTY to "https://qn-check-basic-content.metaworld.fun/test1010/Z1QF7psRMfTk1724822989881.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SELECT_MODE_GAME_ID",
            value = mapOf(
                Flavor.G_PARK to "stHqwMnyOKeJfQsJV2Nd",
                Flavor.PARTY to "stHqwMnyOKeJfQsJV2Nd",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "IS_FORCE_LOGIN",
            value = mapOf(Flavor.G_PARK to false, Flavor.PARTY to true),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SCHEME_URI",
            value = mapOf(Flavor.G_PARK to "gpark", Flavor.PARTY to "party233"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG, ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "SCHEME_HOST",
            value = mapOf(Flavor.G_PARK to "gpark.fun", Flavor.PARTY to "233party.com"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "PANDORA_APP_KEY",
            value = mapOf(Flavor.G_PARK to "cDEwMDQy", Flavor.PARTY to "cDEwMTMz"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "CRASH_SHOW",
            value = mapOf(Flavor.G_PARK to false, Flavor.PARTY to false),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "LEAK_CANARY_ENABLE",
            value = mapOf(Flavor.G_PARK to false, Flavor.PARTY to false),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "OPEN_FACEBOOK_ANALYTICS",
            value = mapOf(Flavor.G_PARK to true, Flavor.PARTY to false),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "OPEN_FIREBASE_ANALYTICS",
            value = mapOf(Flavor.G_PARK to true, Flavor.PARTY to false),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "OPEN_PANDORA_ANALYTICS",
            value = mapOf(Flavor.G_PARK to true, Flavor.PARTY to true),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "NEED_SHOW_GUIDE",
            value = mapOf(Flavor.G_PARK to true, Flavor.PARTY to true),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SHUMEI_PRIVATE_AUTH_CODE",
            value = mapOf(Flavor.G_PARK to "im-text", Flavor.PARTY to "im-text"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SHUMEI_ROOM_AUTH_CODE",
            value = mapOf(Flavor.G_PARK to "game-room-text", Flavor.PARTY to "game-room-text"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "BULGY_H_DEBUG",
            value = mapOf(Flavor.PARTY to "1ae7925381"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "BULGY_H",
            value = mapOf(Flavor.PARTY to "7f3539becd"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "BULGY_M",
            value = mapOf(Flavor.PARTY to "cacebecd11"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "BULGY_R",
            value = mapOf(Flavor.PARTY to "e2f97c9f2e"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_GAME_DETAIL_API",
            value = mapOf(
                Flavor.G_PARK to "https://api.233lyly.com/game/v1/info/detail",
                Flavor.PARTY to "https://api.233lyly.com/game/v1/info/detail",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_VERSION",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_VERSION,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_VERSION,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_EXT",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_EXT,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_EXT,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_FILE",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_FILE_NAME,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_FILE_NAME,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "DEF_HOTFIX_COMPRESS_TYPE",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_COMPRESS_TYPE,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_COMPRESS_TYPE,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_VERSION_FILE",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_VERSION_FILE,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_VERSION_FILE,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_VERSION_CODE_FILE",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_VERSION_CODE_FILE,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_VERSION_CODE_FILE,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "MW_HOTFIX_OBB_FILE_NAME",
            value = mapOf(
                Flavor.G_PARK to Version.GPARK_MW_ENGINE_OBB_FILE_NAME,
                Flavor.PARTY to Version.PARTY_MW_ENGINE_OBB_FILE_NAME,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING_ARRAY,
            name = "HOST_RESOLUTION_ADDRESSES",
            value = mapOf(
                Flavor.G_PARK to """
            {"https://baidu.com/","https://example.com/"}
        """.trimIndent(),
                Flavor.PARTY to """
            {"https://baidu.com/","https://example.com/"}
        """.trimIndent(),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CHECK_NET_URL",
            value = mapOf(
                Flavor.G_PARK to "https://www.baidu.com",
                Flavor.PARTY to "https://www.baidu.com",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "DEV_SHARE_TEXT",
            value = mapOf(
                Flavor.G_PARK to "https://233xyx.com/",
                Flavor.PARTY to "https://233xyx.com/",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "GAME_SCREEN_RECORD_DISCORD_ID",
            value = mapOf(
                Flavor.G_PARK to "https://discord.gg/jNbWbtBadY",
                Flavor.PARTY to "",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APP_MARKET_URI",
            value = mapOf(
                Flavor.G_PARK to "market://details?id={}",
                Flavor.PARTY to "market://details?id={}",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "APP_MARKET_URL",
            value = mapOf(
                Flavor.G_PARK to "https://play.google.com/store/apps/details?id={}",
                Flavor.PARTY to "https://233party.com",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "OFFICIAL_WEBSITE",
            value = mapOf(
                Flavor.G_PARK to "https://gpark.fun/",
                Flavor.PARTY to "https://233party.com",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "EDITOR_OFFICIAL_WEBSITE",
            value = mapOf(
                Flavor.G_PARK to "https://gpark.fun/",
                Flavor.PARTY to "https://creator.ark.online/",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG, ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "QQ_APP_ID",
            value = mapOf(Flavor.PARTY to "102104369"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG, ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "WECHAT_APP_ID",
            value = mapOf(Flavor.PARTY to "wxb16e36f41bb44551"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG, ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "LE_YUAN_APP_KEY",
            value = mapOf(Flavor.PARTY to "zjqbz9349g34mg9i34mgxvg"),
        ),
        ConfigField(
            itemType = setOf(ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "MGS_PROVIDER_ID",
            value = mapOf(
                Flavor.G_PARK to "com.socialplay.gpark.dev",
                Flavor.PARTY to "com.meta.box.party",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "KWAI_APP_ID",
            value = mapOf(Flavor.PARTY to "ks673288145445343808"),
        ),
        ConfigField(
            itemType = setOf(ItemType.MANIFEST),
            buildConfigType = BuildConfigType.STRING,
            name = "KWAI_SCOPE",
            value = mapOf(Flavor.PARTY to "user_info"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "XHS_KEY",
            value = mapOf(Flavor.PARTY to "814ab172f59d70a299bd6e991231e488"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "DOU_YIN_KEY",
            value = mapOf(Flavor.PARTY to "awab283m0xob0x2n"),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.BOOLEAN,
            name = "IS_NEED_LEGAL",
            value = mapOf(
                Flavor.G_PARK to false,
                Flavor.PARTY to true
            )
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "OAID_CERT_DEFAULT",
            value = mapOf(
                Flavor.PARTY to """
-----BEGIN CERTIFICATE-----
MIIFmDCCA4CgAwIBAgIDATayMA0GCSqGSIb3DQEBCwUAMIGAMQswCQYDVQQGEwJD
TjEQMA4GA1UECAwHQmVpamluZzEMMAoGA1UECgwDTVNBMREwDwYDVQQLDAhPQUlE
X1NESzEeMBwGA1UEAwwVY29tLmJ1bi5taWl0bWRpZC5zaWduMR4wHAYJKoZIhvcN
****************************************************************
MTExWjCBiTELMAkGA1UEBhMCQ04xEDAOBgNVBAgMB0JlaWppbmcxEDAOBgNVBAcM
B0JlaWppbmcxEDAOBgNVBAoMB2JqbWVpdGExGzAZBgNVBAMMEmNvbS5tZXRhLmJv
eC5wYXJ0eTEnMCUGCSqGSIb3DQEJARYYeGlhbmdqaWUubGlAYXBwc2hhaGUuY29t
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAshgvU9xluAUrL51Tam/P
h5Z4yQEL0z3ex9d5tX/eOblLFGGDGmmbiAmI2+Knn2VMkIMsQVE2HwTk6RO3HHXD
aGYAqdOUU54N9M75HOKY2sCSJDXiAtjRpTe0arjuCmv9Oi0rquTfm3y+/BNDMGQI
p6MUrMH/8jWOUdSydm9yOwZ1u61Um+ymwdEOM+SpridOIT1ZewbUnX1Z750VUwQ0
Zv3DIMrqRJfgghlvB1Ho8zx3R3h0Sta8e37sECPuY4DJg2ETV2Q+AJidLwpbq0Ee
WrzdsTrsQfB27QfpUIrbDZ0Ar2GNV6eLOxbX5k+icwzUP/70YF8WKzmlRX6GP7oK
zvkn2bano/pnSkdyoDmgAO+bfM3CXzOlhq2zyZd8R3Nm4Q+JBsEZEwHBye1kDiK1
s/OQjwnyKGIfJ2ZQZbAjWpiSYej5dpwLZp3zLQ7mGSaP9kCuifyhbcsLGCHpFOn4
kbCWmrpwX+bA/SMW7yXEUQfh0QrMkimBIPM+15YfdHx4pn1IE9BreOP6Pp8z4ewq
vQMunVrTMWBce/GdXqWqTiXVDaWViBE/MEB64iZmydfE5YnRkQFvOZFtpGtnK+Lu
hHevCUGvpzfpG0tHQ3rrzgRjrIqqgsuWngNKM/PB0VliIGtNjy02WxLOD3WKzcx1
giEo55N+CqcMMWCVJ+mactcCAwEAAaMQMA4wDAYDVR0TAQH/BAIwADANBgkqhkiG
9w0BAQsFAAOCAgEAV+wor16P+0Uu+/whSZlp+y7KgbFVb2CrjjRAsDHRZCKRF8Cx
YRArLBHBAklNXlzk3O5YrnhdZQgLf7zG+hu6Nrj4j6+e9yR15VJqLEcomjzvrFzO
nvsP0ZOIOzLJ4YYWTTRybg3+6egTb1DplvlB7/Gt1wfOcJQYeidbXqr2507VM99V
FOiNWg3H2mijvPGThKDdlavGsq9nE8OXqr8W48TcC+oXi1MFZXpSHrsmG9M2GzjI
MdB4XBUISQ67h4Y7wCu1KvH5ivHjax3JTmseABCD4keEA4AyzGJRgJjAmiIDEwI4
Gy1s93kdIPaK7W6z+LvspoXeF0jN4UhNmmxscY1VIYdH56AjSF5Y5Hh1EE4Sf9RO
I21dDICHODTJ2+i2nEwyOB0JpaJqNAjLtLZl6h7g6l9W5lhSmpwo8GJ63xGRhS6a
aRC3tMfQJKU/R5uILSvrId8z59ZFuqk9ESQhPuNLXJZT4YakNPuIAJo7wD0anczq
BwTbpsAn7DJcDmP7n16jKGslb4qEIgr5TpjnRFq9oIKH/OTPaE2on0m4C0s7XI2l
/eiQI2CNxpgIccS7UKvrJmtYYWDMLcd+drC4mMEByI1PG2mI0+J+J0jcTMGwLzKn
1BLoKahzX0BjDp+egjuApZJEtYFnhreLtZ5c3VSfo4yE9jTlTD5j5k40UhU=
-----END CERTIFICATE-----
                    """.trimIndent().replace("\n", "\\n\"\n+\"")
            )
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_MAP",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/PvrFLB35RqHq1742813667118.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/qBn4GfqozMDM1742813637215.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_DESIGN",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/FWOOst0ku99X1742813691075.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/vDjp4Q3zXdVz1742813708550.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_MODULE",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/JIrRTwDeMgsA1742865368233.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/gflqBdb12Aim1742865349089.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_DRAW",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/MuCnYevlNObX1744105665383.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/d9FAiV3IgRdW1744105634335.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "DEFAULT_QUICK_INPUT_JSON_CONFIG",
            value = mapOf(
                Flavor.G_PARK to """
{
    "partyDetail": [
        {
            "content": "Game Ingenuity",
            "tagId": 1001
        },
        {
            "content": "Anticipating Updates",
            "tagId": 1002
        },
        {
            "content": "Dev Appreciation",
            "tagId": 1003
        },
        {
            "content": "Worldbuilding",
            "tagId": 1004
        },
        {
            "content": "Friendship Highlights",
            "tagId": 1005
        },
        {
            "content": "Level Strategy",
            "tagId": 1006
        },
        {
            "content": "Visual Impressions",
            "tagId": 1007
        },
        {
            "content": "Story Theories",
            "tagId": 1008
        }
    ],
    "libraryDetail": [
        {
            "content": "Feature Wishlist",
            "tagId": 2001
        },
        {
            "content": "Creator Shoutout",
            "tagId": 2002
        },
        {
            "content": "Creation Showcase",
            "tagId": 2003
        },
        {
            "content": "Asset Recommendation",
            "tagId": 2004
        },
        {
            "content": "Whimsical Ideas",
            "tagId": 2005
        }
    ]
}
""".trimIndent().replace("\n".toRegex(), "")
                    // 移除双引号外部的空格, 保留双引号内部的空格
                    .replace("""\s+(?=(?:[^"]*"[^"]*")*[^"]*$)""".toRegex(), "")
                    .replace("\"", "\\\""),
                Flavor.PARTY to """
{
    "partyDetail": [
        {
            "content": "游戏巧思",
            "tagId": 1001
        },
        {
            "content": "期待更新",
            "tagId": 1002
        },
        {
            "content": "鼓励作者",
            "tagId": 1003
        },
        {
            "content": "场景搭建",
            "tagId": 1004
        },
        {
            "content": "好友趣事",
            "tagId": 1005
        },
        {
            "content": "关卡攻略",
            "tagId": 1006
        },
        {
            "content": "画面感受",
            "tagId": 1007
        },
        {
            "content": "剧情讨论",
            "tagId": 1008
        }
    ],
    "libraryDetail": [
        {
            "content": "期待更新",
            "tagId": 2001
        },
        {
            "content": "鼓励作者",
            "tagId": 2002
        },
        {
            "content": "作品分享",
            "tagId": 2003
        },
        {
            "content": "推荐素材",
            "tagId": 2004
        },
        {
            "content": "搞怪想法",
            "tagId": 2005
        }
    ]
}
""".trimIndent().replace("\n".toRegex(), "")
                    // 移除双引号外部的空格, 保留双引号内部的空格
                    .replace("""\s+(?=(?:[^"]*"[^"]*")*[^"]*$)""".toRegex(), "")
                    .replace("\"", "\\\""),
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_APP_ICON_144",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/6Q6Vnj2w3wTS1744960986033.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/pwm8qKh24zlB1744960958855.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "GROUP_CHAT_AGREEMENT",
            value = mapOf(
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/VTQkdm6DXoEyl8xAwQkcMNEpnvf.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=%E7%BE%A4%E8%81%8A%E6%9C%8D%E5%8A%A1%E8%A7%84%E8%8C%83&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.G_PARK to "",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.INT,
            name = "MAX_GROUP_MEMBERS_COUNT",
            value = mapOf(
                Flavor.PARTY to 200,
                Flavor.G_PARK to 200,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.INT,
            name = "MAX_CREATE_GROUP_COUNT",
            value = mapOf(
                Flavor.PARTY to 3,
                Flavor.G_PARK to 3,
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_SHOT",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/4FFs0qOx7LL71746670686711.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/eqBXTD1jfWMc1746670632630.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_CAR",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/qWqLaWHfBWZ81746670686711.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/aC5BteRo7g0v1746670643975.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "CDN_GUIDE_DEFAULT_BANNER_OBBY",
            value = mapOf(
                Flavor.G_PARK to "https://qn-basic-content.gpark.io/online/RMiWjjTmsKgT1746670686711.png",
                Flavor.PARTY to "https://release.233leyuan.com/online/DJPSovRJT1bD1746670653424.png",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SEND_FLOWER_FEATURE_URL",
            value = mapOf(
                Flavor.G_PARK to "https://web-static-mir-01-qn.gpark.fun/fs-doc/gpark/docx/RwwCdIGIdolvfixN85DcH5aenSc.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=Map+Flower+Gifting+Policy&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/Evq9dQoiaoS36kxtu9gceRdJnjc.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=%E9%80%A0%E7%89%A9%E5%B2%9B%E5%9C%B0%E5%9B%BE%E9%80%81%E8%8A%B1%E8%AF%B4%E6%98%8E&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SEND_FLOWER_NOTICE_URL",
            value = mapOf(
                Flavor.G_PARK to "https://web-static-mir-01-qn.gpark.fun/fs-doc/gpark/docx/E5jUd7zGLochiWxuHiJcL4LLnQb.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=Flower+Gifting+Policy&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/Evq9dQoiaoS36kxtu9gceRdJnjc.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=%E9%80%A0%E7%89%A9%E5%B2%9B%E5%9C%B0%E5%9B%BE%E9%80%81%E8%8A%B1%E8%AF%B4%E6%98%8E&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SEND_FLOWER_FLOWER_RANK_README_URL",
            value = mapOf(
                Flavor.G_PARK to "https://web-static-mir-01-qn.gpark.io/qn-static/html/flower.readme.en.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=Flower+Ranking+List+Function+Intro&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/static/233-party/html/flower.readme.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=%E9%B2%9C%E8%8A%B1%E6%A6%9C%E5%8D%95%E5%8A%9F%E8%83%BD%E8%AF%B4%E6%98%8E&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "SEND_FLOWER_README_URL",
            value = mapOf(
                Flavor.G_PARK to "https://web-static-mir-01-qn.gpark.fun/fs-doc/gpark/docx/E5jUd7zGLochiWxuHiJcL4LLnQb.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=Flower+Gifting+Policy&sys_title_content_color=000000&sys_is_title_menu=false",
                Flavor.PARTY to "https://wstatic-01-ali.233party.com/fs-doc/pd/docx/Evq9dQoiaoS36kxtu9gceRdJnjc.html?sys_webview_top_position=T2&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=true&____isNativeTitleShow=true&isNativeTitleShow=true&sys_title_bg_color=ffffff&sys_title_content=%E9%80%A0%E7%89%A9%E5%B2%9B%E5%9C%B0%E5%9B%BE%E9%80%81%E8%8A%B1%E8%AF%B4%E6%98%8E&sys_title_content_color=000000&sys_is_title_menu=false",
            ),
        ),
        // 交易明细的兜底 url 地址
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.STRING,
            name = "TRANSACTION_DETAILS_URL",
            value = mapOf(
                Flavor.G_PARK to "https://app-intra.gpark.fun/proxy/wallet/details?sys_webview_top_position=T1&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
                Flavor.PARTY to "https://ppp.233party.com/proxy/wallet/details?sys_webview_top_position=T1&____landscape=false&sys_landscape=false&sys_tabbar_bg_color=ffffff&isTranslucentTop=true&____isTranslucentTop=true&sys_is_render_title=false&____isNativeTitleShow=false&isNativeTitleShow=false",
            ),
        ),
        ConfigField(
            itemType = setOf(ItemType.BUILD_CONFIG),
            buildConfigType = BuildConfigType.LONG,
            name = "CUSTOMIZE_FLOWER_GIFT_PRODUCT_ID",
            value = mapOf(
                Flavor.G_PARK to 123606,
                Flavor.PARTY to 123606,
            ),
        ),
    )
}