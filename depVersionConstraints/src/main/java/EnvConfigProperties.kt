import com.international.gradle.plugin.DepVersionConstraintPlugin
import java.io.File
import java.util.Properties

object EnvConfigProperties {

    fun load(propertiesFileName: String): CastMap {
        val file = propertiesFile(propertiesFileName)
        val properties = Properties()
        file.inputStream().use {
            properties.load(it)
        }
        return CastMap(properties as Map<String, Any>)
    }

    private fun propertiesFile(propertiesFileName: String): File {
        val path = System.getProperty("EnvConfigPropertiesPath")
        return if (path == null || path.isEmpty()) {
            File(DepVersionConstraintPlugin.rootProject.rootDir, propertiesFileName)
        } else {
            File(path)
        }.also {
            if (!it.exists()) {
                error("file($it) not exists")
            } else {
                println("propertiesFile: $it")
            }
        }
    }

    class CastMap(m: Map<String, Any>) : HashMap<String, Any>(m) {
        override fun get(key: String): Any? {
            val value = super.get(key)
            if (value == "false") {
                return false
            } else if (value == "true") {
                return true
            }
            kotlin.runCatching { Integer.parseInt(value.toString()) }.getOrNull()?.let {
                return it
            }
            return value
        }
    }
}