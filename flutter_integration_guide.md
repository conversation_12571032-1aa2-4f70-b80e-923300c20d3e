# Flutter集成实施指南

## 1. 项目结构调整

### 1.1 在现有项目中添加Flutter模块
```
MetaAppInternational-2/
├── android/                    # 现有Android项目
├── flutter_modules/            # 新增Flutter模块
│   ├── android/
│   ├── lib/
│   ├── pubspec.yaml
│   └── ...
├── build.gradle
├── settings.gradle.kts
└── ...
```

### 1.2 修改settings.gradle.kts
```kotlin
// settings.gradle.kts
include(":dev")
include(":run-party")
include(":run-gpark-dev")
include(":libs3rdSdk")
include(":libs3rdSdkDanmaku")
include(":asset_engine_obb")
include(":depVersionConstraints")

// 添加Flutter模块
setBinding(Binding(gradle))
include(":flutter_modules")
project(":flutter_modules").projectDir = File("flutter_modules/android")
```

### 1.3 修改主模块build.gradle
```kotlin
// dev/build.gradle.kts
dependencies {
    // 现有依赖...
    
    // 添加Flutter依赖
    implementation project(':flutter_modules')
    implementation 'io.flutter:flutter_embedding_debug:1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314'
    
    // 如果是release版本
    // implementation 'io.flutter:flutter_embedding_release:1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314'
}
```

## 2. Flutter模块初始化

### 2.1 在Application中初始化Flutter
```kotlin
// MetaApplication.kt
@ExperimentalPagingApi
class MetaApplication : Application() {

    private lateinit var applicationLifecycle: ApplicationLifeCycle

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(MetaLanguages.attachContext(base))
        HomeImageShowAnalytics.startBootTime = System.currentTimeMillis()
        AppLaunchAnalytics.handleAppOnAttachBefore()
        applicationLifecycle = MetaApplicationLifecycle(this)
        applicationLifecycle.attachContext()
        
        // 初始化Flutter引擎
        FlutterBridgeManager.getInstance().initialize(this)
        
        AppLaunchAnalytics.handleAppOnAttachAfter()
    }

    override fun onCreate() {
        AppLaunchAnalytics.handleAppOnCreateBefore()
        super.onCreate()
        applicationLifecycle.onCreate()
        AppLaunchAnalytics.handleAppOnCreateAfter()
    }
}
```

### 2.2 在启动项目中添加Flutter初始化
```kotlin
// StartupProject.kt
@ExperimentalPagingApi
val attachContextProject = project("attachContext") {
    timber()
    val mmkv = mmkv()
    mmkv.await()
    initBuildConfig()
    val koin = koin()
    val language = language()
    appTimeInit()
    koin.await() + language.await()
    val pandora = analyticsPreInit()
    if (noSpace()) {
        return@project
    }
    leakCanaryInit()
    val hermesEventBus = hermesEventBus()
    installIPC()
    
    // 添加Flutter初始化
    flutterInit()
    
    pandora.await()
    hermesEventBus.await()
    fixAndroidPWebView()
    maverick()
    isNeedAgreeProtocol = MetaProtocol.needLegal()
    if (!isNeedAgreeProtocol) {
        deviceId()
    }
    AppStartupInit.onAttachContext(this)
}

// 添加Flutter初始化任务
fun Project.flutterInit() = taskAsync("flutterInit", H + M) {
    FlutterBridgeManager.getInstance().initialize(application)
}
```

## 3. 功能开关配置

### 3.1 添加Pandora开关
```kotlin
// PandoraToggle.kt
object PandoraToggle {
    // 现有开关...
    
    // Flutter模块开关
    fun isFlutterSettingsEnabled(): Boolean {
        return pandora.getBoolean("flutter_settings_enabled", false)
    }
    
    fun isFlutterProfileEnabled(): Boolean {
        return pandora.getBoolean("flutter_profile_enabled", false)
    }
    
    fun isFlutterFriendsEnabled(): Boolean {
        return pandora.getBoolean("flutter_friends_enabled", false)
    }
    
    fun isFlutterPaymentEnabled(): Boolean {
        return pandora.getBoolean("flutter_payment_enabled", false)
    }
    
    fun isFlutterNotificationEnabled(): Boolean {
        return pandora.getBoolean("flutter_notification_enabled", false)
    }
}
```

### 3.2 修改现有Fragment使用Flutter
```kotlin
// SettingFragment.kt
class SettingFragment : BaseFragment<FragmentSettingBinding>() {
    
    override fun initView() {
        // 检查Flutter开关
        if (PandoraToggle.isFlutterSettingsEnabled()) {
            navigateToFlutterSettings()
        } else {
            setupNativeSettings()
        }
    }
    
    private fun navigateToFlutterSettings() {
        val intent = FlutterSettingsActivity.createIntent(requireContext())
        startActivity(intent)
        requireActivity().finish()
    }
    
    private fun setupNativeSettings() {
        // 保持原有实现
        binding.apply {
            // 原有的设置逻辑...
        }
    }
}
```

## 4. 数据同步机制

### 4.1 扩展MetaKV支持Flutter设置
```kotlin
// MetaKV.kt - 添加Flutter相关设置
class AppKV(private val mmkv: MMKV) {
    // 现有属性...
    
    // Flutter设置相关
    var notificationsEnabled: Boolean
        get() = mmkv.decodeBool("notifications_enabled", true)
        set(value) = mmkv.encode("notifications_enabled", value)
    
    var soundEnabled: Boolean
        get() = mmkv.decodeBool("sound_enabled", true)
        set(value) = mmkv.encode("sound_enabled", value)
    
    var vibrationEnabled: Boolean
        get() = mmkv.decodeBool("vibration_enabled", true)
        set(value) = mmkv.encode("vibration_enabled", value)
    
    var language: String
        get() = mmkv.decodeString("app_language", "auto") ?: "auto"
        set(value) = mmkv.encode("app_language", value)
    
    var theme: String
        get() = mmkv.decodeString("app_theme", "auto") ?: "auto"
        set(value) = mmkv.encode("app_theme", value)
    
    var autoUpdate: Boolean
        get() = mmkv.decodeBool("auto_update", true)
        set(value) = mmkv.encode("auto_update", value)
    
    var debugMode: Boolean
        get() = mmkv.decodeBool("debug_mode", false)
        set(value) = mmkv.encode("debug_mode", value)
    
    var friendRequestNotification: Boolean
        get() = mmkv.decodeBool("friend_request_notification", true)
        set(value) = mmkv.encode("friend_request_notification", value)
    
    var gameInviteNotification: Boolean
        get() = mmkv.decodeBool("game_invite_notification", true)
        set(value) = mmkv.encode("game_invite_notification", value)
    
    var systemNotification: Boolean
        get() = mmkv.decodeBool("system_notification", true)
        set(value) = mmkv.encode("system_notification", value)
}
```

### 4.2 创建Flutter桥接管理器
```kotlin
// FlutterBridgeManager.kt
package com.socialplay.gpark.flutter

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import org.koin.core.context.GlobalContext
import com.socialplay.gpark.flutter.bridge.*

class FlutterBridgeManager private constructor() {
    
    companion object {
        private const val ENGINE_ID = "gpark_flutter_engine"
        
        @Volatile
        private var INSTANCE: FlutterBridgeManager? = null
        
        fun getInstance(): FlutterBridgeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FlutterBridgeManager().also { INSTANCE = it }
            }
        }
    }
    
    private var flutterEngine: FlutterEngine? = null
    private var isInitialized = false
    
    fun initialize(context: Context) {
        if (isInitialized) return
        
        try {
            // 创建Flutter引擎
            flutterEngine = FlutterEngine(context).apply {
                dartExecutor.executeDartEntrypoint(
                    DartExecutor.DartEntrypoint.createDefault()
                )
            }
            
            // 缓存引擎
            FlutterEngineCache.getInstance().put(ENGINE_ID, flutterEngine!!)
            
            // 设置桥接
            setupBridges()
            
            isInitialized = true
            Timber.d("Flutter engine initialized successfully")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize Flutter engine")
        }
    }
    
    private fun setupBridges() {
        val messenger = flutterEngine!!.dartExecutor.binaryMessenger
        
        // 账户桥接
        MethodChannel(messenger, "com.socialplay.gpark/account")
            .setMethodCallHandler(AccountBridge(GlobalContext.get().get()))
        
        // 数据桥接
        MethodChannel(messenger, "com.socialplay.gpark/data")
            .setMethodCallHandler(SettingsBridge())
        
        // 导航桥接
        MethodChannel(messenger, "com.socialplay.gpark/navigation")
            .setMethodCallHandler(NavigationBridge())
        
        // 分析桥接
        MethodChannel(messenger, "com.socialplay.gpark/analytics")
            .setMethodCallHandler(AnalyticsBridge())
    }
    
    fun getFlutterEngine(): FlutterEngine? = flutterEngine
}
```

## 5. 灰度发布策略

### 5.1 用户分组策略
```kotlin
// FlutterFeatureManager.kt
object FlutterFeatureManager {
    
    fun shouldUseFlutterSettings(userId: String): Boolean {
        // 基于用户ID的哈希值进行分组
        val hash = userId.hashCode()
        val group = Math.abs(hash % 100)
        
        // 获取灰度比例（0-100）
        val rolloutPercentage = PandoraToggle.getFlutterSettingsRolloutPercentage()
        
        return group < rolloutPercentage
    }
    
    fun shouldUseFlutterProfile(userId: String): Boolean {
        val hash = userId.hashCode()
        val group = Math.abs(hash % 100)
        val rolloutPercentage = PandoraToggle.getFlutterProfileRolloutPercentage()
        return group < rolloutPercentage
    }
}
```

### 5.2 动态切换机制
```kotlin
// 在Fragment中使用动态切换
class SettingFragment : BaseFragment<FragmentSettingBinding>() {
    
    override fun initView() {
        val accountInteractor: AccountInteractor = GlobalContext.get().get()
        val userId = accountInteractor.accountLiveData.value?.uuid ?: ""
        
        val useFlutter = PandoraToggle.isFlutterSettingsEnabled() && 
                        FlutterFeatureManager.shouldUseFlutterSettings(userId)
        
        if (useFlutter) {
            navigateToFlutterSettings()
        } else {
            setupNativeSettings()
        }
    }
}
```

## 6. 监控和分析

### 6.1 性能监控
```kotlin
// FlutterPerformanceMonitor.kt
object FlutterPerformanceMonitor {
    
    fun trackFlutterPageLoad(pageName: String, loadTime: Long) {
        Analytics.track("flutter_page_load") {
            put("page_name", pageName)
            put("load_time_ms", loadTime)
            put("flutter_version", getFlutterVersion())
        }
    }
    
    fun trackFlutterError(error: String, stackTrace: String) {
        Analytics.track("flutter_error") {
            put("error_message", error)
            put("stack_trace", stackTrace)
            put("flutter_version", getFlutterVersion())
        }
    }
    
    private fun getFlutterVersion(): String {
        return "3.16.0" // 从BuildConfig或其他地方获取
    }
}
```

### 6.2 用户体验监控
```dart
// lib/core/analytics/flutter_analytics.dart
class FlutterAnalytics {
  static void trackPageView(String pageName) {
    BridgeService.trackEvent('flutter_page_view', {
      'page_name': pageName,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  static void trackUserAction(String action, Map<String, dynamic> properties) {
    BridgeService.trackEvent('flutter_user_action', {
      'action': action,
      ...properties,
    });
  }
  
  static void trackError(String error, String stackTrace) {
    BridgeService.trackEvent('flutter_error', {
      'error': error,
      'stack_trace': stackTrace,
    });
  }
}
```

## 7. 部署和测试

### 7.1 CI/CD集成
```yaml
# .github/workflows/flutter_build.yml
name: Flutter Build

on:
  push:
    paths:
      - 'flutter_modules/**'
  pull_request:
    paths:
      - 'flutter_modules/**'

jobs:
  flutter_test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - name: Install dependencies
        run: |
          cd flutter_modules
          flutter pub get
      - name: Run tests
        run: |
          cd flutter_modules
          flutter test
      - name: Build AAR
        run: |
          cd flutter_modules
          flutter build aar
```

### 7.2 集成测试脚本
```bash
#!/bin/bash
# scripts/test_flutter_integration.sh

echo "Testing Flutter integration..."

# 构建Flutter模块
cd flutter_modules
flutter pub get
flutter test
flutter build aar --release

# 构建Android项目
cd ..
./gradlew assembleDebug

# 运行集成测试
./gradlew connectedAndroidTest

echo "Flutter integration test completed!"
```

这个集成指南提供了：

1. **完整的项目结构调整** - 如何在现有项目中集成Flutter
2. **渐进式迁移策略** - 通过功能开关控制迁移进度
3. **数据同步机制** - 确保原生和Flutter数据一致性
4. **灰度发布支持** - 安全的功能发布策略
5. **监控和分析** - 完整的性能和用户体验监控
6. **CI/CD集成** - 自动化构建和测试流程

您希望我详细实现其他模块（如个人资料或朋友系统），还是需要调整当前的架构方案？
