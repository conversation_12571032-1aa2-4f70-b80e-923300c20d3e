name: gpark_flutter_modules
description: Flutter modules for GPark app

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # 路由管理
  go_router: ^12.1.3
  
  # 网络请求
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # 依赖注入
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # UI组件
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  
  # 工具库
  freezed_annotation: ^2.4.1
  equatable: ^2.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码生成
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  injectable_generator: ^2.4.1
  
  # 代码规范
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

flutter:
  module:
    androidX: true
    androidPackage: com.socialplay.gpark.flutter_modules
    iosBundleIdentifier: com.socialplay.gpark.flutterModules
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
