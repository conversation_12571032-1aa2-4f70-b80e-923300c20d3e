import 'package:flutter/material.dart';

class SettingsModel {
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String language;
  final String theme;
  final bool autoUpdate;
  final bool debugMode;
  final bool friendRequestNotification;
  final bool gameInviteNotification;
  final bool systemNotification;

  const SettingsModel({
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.language = 'auto',
    this.theme = 'auto',
    this.autoUpdate = true,
    this.debugMode = false,
    this.friendRequestNotification = true,
    this.gameInviteNotification = true,
    this.systemNotification = true,
  });

  SettingsModel copyWith({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? language,
    String? theme,
    bool? autoUpdate,
    bool? debugMode,
    bool? friendRequestNotification,
    bool? gameInviteNotification,
    bool? systemNotification,
  }) {
    return SettingsModel(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      autoUpdate: autoUpdate ?? this.autoUpdate,
      debugMode: debugMode ?? this.debugMode,
      friendRequestNotification: friendRequestNotification ?? this.friendRequestNotification,
      gameInviteNotification: gameInviteNotification ?? this.gameInviteNotification,
      systemNotification: systemNotification ?? this.systemNotification,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'language': language,
      'theme': theme,
      'autoUpdate': autoUpdate,
      'debugMode': debugMode,
      'friendRequestNotification': friendRequestNotification,
      'gameInviteNotification': gameInviteNotification,
      'systemNotification': systemNotification,
    };
  }

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      language: json['language'] ?? 'auto',
      theme: json['theme'] ?? 'auto',
      autoUpdate: json['autoUpdate'] ?? true,
      debugMode: json['debugMode'] ?? false,
      friendRequestNotification: json['friendRequestNotification'] ?? true,
      gameInviteNotification: json['gameInviteNotification'] ?? true,
      systemNotification: json['systemNotification'] ?? true,
    );
  }
}

class SettingItem {
  final String key;
  final String title;
  final String? subtitle;
  final SettingType type;
  final dynamic value;
  final List<SettingOption>? options;
  final VoidCallback? onTap;

  const SettingItem({
    required this.key,
    required this.title,
    this.subtitle,
    required this.type,
    this.value,
    this.options,
    this.onTap,
  });
}

class SettingOption {
  final String label;
  final dynamic value;

  const SettingOption({
    required this.label,
    required this.value,
  });
}

enum SettingType {
  toggle,
  selection,
  navigation,
  action,
}

// 扩展方法用于获取显示标签
extension SettingsModelExtension on SettingsModel {
  String getLanguageLabel() {
    switch (language) {
      case 'zh':
        return '中文';
      case 'en':
        return 'English';
      case 'auto':
      default:
        return '自动';
    }
  }
  
  String getThemeLabel() {
    switch (theme) {
      case 'light':
        return '浅色';
      case 'dark':
        return '深色';
      case 'auto':
      default:
        return '跟随系统';
    }
  }
}
