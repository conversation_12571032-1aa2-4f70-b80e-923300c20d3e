import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';
import '../../../../core/bridge/bridge_service.dart';

class SettingsDataSource {
  static const String _settingsKey = 'app_settings';
  
  Future<SettingsModel> getSettings() async {
    try {
      // 首先尝试从原生端获取设置
      final nativeSettings = await BridgeService.getSettings();
      if (nativeSettings != null) {
        return SettingsModel.fromJson(nativeSettings);
      }
      
      // 如果原生端没有，从本地存储获取
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        return SettingsModel.fromJson(json.decode(settingsJson));
      }
      
      // 返回默认设置
      return const SettingsModel();
    } catch (e) {
      print('Error getting settings: $e');
      return const SettingsModel();
    }
  }
  
  Future<bool> updateSettings(SettingsModel settings) async {
    try {
      // 同步到原生端
      await BridgeService.updateSettings(settings.toJson());
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, json.encode(settings.toJson()));
      
      return true;
    } catch (e) {
      print('Error updating settings: $e');
      return false;
    }
  }
  
  Future<bool> updateSetting(String key, dynamic value) async {
    try {
      final currentSettings = await getSettings();
      final updatedSettings = _updateSettingValue(currentSettings, key, value);
      return await updateSettings(updatedSettings);
    } catch (e) {
      print('Error updating setting: $e');
      return false;
    }
  }
  
  SettingsModel _updateSettingValue(SettingsModel settings, String key, dynamic value) {
    switch (key) {
      case 'notificationsEnabled':
        return settings.copyWith(notificationsEnabled: value as bool);
      case 'soundEnabled':
        return settings.copyWith(soundEnabled: value as bool);
      case 'vibrationEnabled':
        return settings.copyWith(vibrationEnabled: value as bool);
      case 'language':
        return settings.copyWith(language: value as String);
      case 'theme':
        return settings.copyWith(theme: value as String);
      case 'autoUpdate':
        return settings.copyWith(autoUpdate: value as bool);
      case 'debugMode':
        return settings.copyWith(debugMode: value as bool);
      case 'friendRequestNotification':
        return settings.copyWith(friendRequestNotification: value as bool);
      case 'gameInviteNotification':
        return settings.copyWith(gameInviteNotification: value as bool);
      case 'systemNotification':
        return settings.copyWith(systemNotification: value as bool);
      default:
        return settings;
    }
  }
}
