import '../models/settings_model.dart';
import '../datasources/settings_datasource.dart';

class SettingsRepository {
  final SettingsDataSource _dataSource;
  
  SettingsRepository(this._dataSource);
  
  Future<SettingsModel> getSettings() async {
    return await _dataSource.getSettings();
  }
  
  Future<bool> updateSettings(SettingsModel settings) async {
    return await _dataSource.updateSettings(settings);
  }
  
  Future<bool> updateSetting(String key, dynamic value) async {
    return await _dataSource.updateSetting(key, value);
  }
}
