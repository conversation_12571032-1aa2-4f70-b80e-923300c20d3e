import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/settings_model.dart';
import '../../data/repositories/settings_repository.dart';
import '../../data/datasources/settings_datasource.dart';

// 数据源提供者
final settingsDataSourceProvider = Provider<SettingsDataSource>((ref) {
  return SettingsDataSource();
});

// 仓库提供者
final settingsRepositoryProvider = Provider<SettingsRepository>((ref) {
  return SettingsRepository(ref.watch(settingsDataSourceProvider));
});

// 设置状态提供者
final settingsProvider = StateNotifierProvider<SettingsNotifier, AsyncValue<SettingsModel>>((ref) {
  return SettingsNotifier(ref.watch(settingsRepositoryProvider));
});

// 设置项列表提供者
final settingItemsProvider = Provider<List<SettingItem>>((ref) {
  final settingsAsync = ref.watch(settingsProvider);
  
  return settingsAsync.when(
    data: (settings) => _buildSettingItems(settings),
    loading: () => [],
    error: (_, __) => [],
  );
});

class SettingsNotifier extends StateNotifier<AsyncValue<SettingsModel>> {
  final SettingsRepository _repository;
  
  SettingsNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    try {
      final settings = await _repository.getSettings();
      state = AsyncValue.data(settings);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
  
  Future<void> updateSetting(String key, dynamic value) async {
    final currentState = state;
    if (currentState is AsyncData<SettingsModel>) {
      // 乐观更新
      final currentSettings = currentState.value;
      final updatedSettings = _updateSettingValue(currentSettings, key, value);
      state = AsyncValue.data(updatedSettings);
      
      // 实际更新
      try {
        final success = await _repository.updateSetting(key, value);
        if (!success) {
          // 如果失败，回滚状态
          state = AsyncValue.data(currentSettings);
        }
      } catch (error, stackTrace) {
        // 如果出错，回滚状态
        state = AsyncValue.data(currentSettings);
        rethrow;
      }
    }
  }
  
  SettingsModel _updateSettingValue(SettingsModel settings, String key, dynamic value) {
    switch (key) {
      case 'notificationsEnabled':
        return settings.copyWith(notificationsEnabled: value as bool);
      case 'soundEnabled':
        return settings.copyWith(soundEnabled: value as bool);
      case 'vibrationEnabled':
        return settings.copyWith(vibrationEnabled: value as bool);
      case 'language':
        return settings.copyWith(language: value as String);
      case 'theme':
        return settings.copyWith(theme: value as String);
      case 'autoUpdate':
        return settings.copyWith(autoUpdate: value as bool);
      case 'debugMode':
        return settings.copyWith(debugMode: value as bool);
      case 'friendRequestNotification':
        return settings.copyWith(friendRequestNotification: value as bool);
      case 'gameInviteNotification':
        return settings.copyWith(gameInviteNotification: value as bool);
      case 'systemNotification':
        return settings.copyWith(systemNotification: value as bool);
      default:
        return settings;
    }
  }
}

List<SettingItem> _buildSettingItems(SettingsModel settings) {
  return [
    // 通知设置
    const SettingItem(
      key: 'notification_header',
      title: '通知设置',
      type: SettingType.navigation,
    ),
    SettingItem(
      key: 'notificationsEnabled',
      title: '推送通知',
      subtitle: '接收应用推送通知',
      type: SettingType.toggle,
      value: settings.notificationsEnabled,
    ),
    SettingItem(
      key: 'friendRequestNotification',
      title: '好友请求',
      subtitle: '接收好友请求通知',
      type: SettingType.toggle,
      value: settings.friendRequestNotification,
    ),
    SettingItem(
      key: 'gameInviteNotification',
      title: '游戏邀请',
      subtitle: '接收游戏邀请通知',
      type: SettingType.toggle,
      value: settings.gameInviteNotification,
    ),
    
    // 声音和震动
    const SettingItem(
      key: 'sound_header',
      title: '声音和震动',
      type: SettingType.navigation,
    ),
    SettingItem(
      key: 'soundEnabled',
      title: '声音',
      subtitle: '启用应用声音',
      type: SettingType.toggle,
      value: settings.soundEnabled,
    ),
    SettingItem(
      key: 'vibrationEnabled',
      title: '震动',
      subtitle: '启用震动反馈',
      type: SettingType.toggle,
      value: settings.vibrationEnabled,
    ),
    
    // 显示设置
    const SettingItem(
      key: 'display_header',
      title: '显示设置',
      type: SettingType.navigation,
    ),
    SettingItem(
      key: 'language',
      title: '语言',
      subtitle: settings.getLanguageLabel(),
      type: SettingType.selection,
      value: settings.language,
      options: const [
        SettingOption(label: '自动', value: 'auto'),
        SettingOption(label: '中文', value: 'zh'),
        SettingOption(label: 'English', value: 'en'),
      ],
    ),
    SettingItem(
      key: 'theme',
      title: '主题',
      subtitle: settings.getThemeLabel(),
      type: SettingType.selection,
      value: settings.theme,
      options: const [
        SettingOption(label: '跟随系统', value: 'auto'),
        SettingOption(label: '浅色', value: 'light'),
        SettingOption(label: '深色', value: 'dark'),
      ],
    ),
    
    // 其他设置
    const SettingItem(
      key: 'other_header',
      title: '其他',
      type: SettingType.navigation,
    ),
    SettingItem(
      key: 'autoUpdate',
      title: '自动更新',
      subtitle: '自动下载应用更新',
      type: SettingType.toggle,
      value: settings.autoUpdate,
    ),
    const SettingItem(
      key: 'about',
      title: '关于',
      type: SettingType.navigation,
    ),
    const SettingItem(
      key: 'logout',
      title: '退出登录',
      type: SettingType.action,
    ),
  ];
}
