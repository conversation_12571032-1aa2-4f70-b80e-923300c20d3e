import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/settings_provider.dart';
import '../widgets/setting_item.dart';
import '../widgets/setting_section.dart';
import '../../../../core/bridge/bridge_service.dart';
import '../../data/models/settings_model.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);
    final settingItems = ref.watch(settingItemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _handleBack(context),
        ),
      ),
      body: settingsAsync.when(
        data: (settings) => _buildSettingsList(context, ref, settingItems),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorView(context, error),
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context, WidgetRef ref, List<SettingItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        
        if (item.type == SettingType.navigation && item.key.endsWith('_header')) {
          return SettingSection(title: item.title);
        }
        
        return SettingItemWidget(
          item: item,
          onChanged: (value) => _handleSettingChanged(ref, item.key, value),
          onTap: () => _handleSettingTap(context, ref, item),
        );
      },
    );
  }

  Widget _buildErrorView(BuildContext context, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            '加载设置失败',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _handleBack(context),
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }

  void _handleSettingChanged(WidgetRef ref, String key, dynamic value) {
    ref.read(settingsProvider.notifier).updateSetting(key, value);
    
    // 发送分析事件
    BridgeService.trackEvent('setting_changed', {
      'setting_key': key,
      'setting_value': value.toString(),
    });
  }

  void _handleSettingTap(BuildContext context, WidgetRef ref, SettingItem item) {
    switch (item.key) {
      case 'language':
        _showLanguageSelector(context, ref, item);
        break;
      case 'theme':
        _showThemeSelector(context, ref, item);
        break;
      case 'about':
        _navigateToAbout(context);
        break;
      case 'logout':
        _showLogoutDialog(context);
        break;
    }
  }

  void _showLanguageSelector(BuildContext context, WidgetRef ref, SettingItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildOptionSelector(
        context,
        ref,
        '选择语言',
        item.key,
        item.options ?? [],
        item.value,
      ),
    );
  }

  void _showThemeSelector(BuildContext context, WidgetRef ref, SettingItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildOptionSelector(
        context,
        ref,
        '选择主题',
        item.key,
        item.options ?? [],
        item.value,
      ),
    );
  }

  Widget _buildOptionSelector(
    BuildContext context,
    WidgetRef ref,
    String title,
    String key,
    List<SettingOption> options,
    dynamic currentValue,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ...options.map((option) => ListTile(
            title: Text(option.label),
            trailing: currentValue == option.value
                ? const Icon(Icons.check, color: Colors.blue)
                : null,
            onTap: () {
              _handleSettingChanged(ref, key, option.value);
              Navigator.pop(context);
            },
          )),
        ],
      ),
    );
  }

  void _navigateToAbout(BuildContext context) {
    // 导航到关于页面
    BridgeService.navigateToNative('/about');
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleLogout(context);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _handleLogout(BuildContext context) async {
    try {
      await BridgeService.logout();
      BridgeService.navigateToNative('/login');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('退出登录失败: $e')),
      );
    }
  }

  void _handleBack(BuildContext context) {
    // 返回原生页面
    BridgeService.finishFlutterPage();
  }
}
