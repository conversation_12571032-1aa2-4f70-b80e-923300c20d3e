import 'package:flutter/material.dart';
import '../../data/models/settings_model.dart';

class SettingItemWidget extends StatelessWidget {
  final SettingItem item;
  final ValueChanged<dynamic>? onChanged;
  final VoidCallback? onTap;

  const SettingItemWidget({
    super.key,
    required this.item,
    this.onChanged,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    switch (item.type) {
      case SettingType.toggle:
        return _buildToggleItem(context);
      case SettingType.selection:
        return _buildSelectionItem(context);
      case SettingType.navigation:
        return _buildNavigationItem(context);
      case SettingType.action:
        return _buildActionItem(context);
    }
  }

  Widget _buildToggleItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: Switch(
        value: item.value as bool? ?? false,
        onChanged: onChanged,
      ),
      onTap: () => onChanged?.call(!(item.value as bool? ?? false)),
    );
  }

  Widget _buildSelectionItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildNavigationItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildActionItem(BuildContext context) {
    final isDestructive = item.key == 'logout';
    
    return ListTile(
      title: Text(
        item.title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      onTap: onTap,
    );
  }
}
