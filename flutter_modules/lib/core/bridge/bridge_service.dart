import 'package:flutter/services.dart';

class BridgeService {
  // Method Channels
  static const MethodChannel _accountChannel = 
      MethodChannel('com.socialplay.gpark/account');
  static const MethodChannel _navigationChannel = 
      MethodChannel('com.socialplay.gpark/navigation');
  static const MethodChannel _dataChannel = 
      MethodChannel('com.socialplay.gpark/data');
  static const MethodChannel _analyticsChannel = 
      MethodChannel('com.socialplay.gpark/analytics');
  static const MethodChannel _testChannel = 
      MethodChannel('com.socialplay.gpark/test');
  
  // Event Channels
  static const EventChannel _userStatusChannel = 
      EventChannel('com.socialplay.gpark/user_status');
  static const EventChannel _friendUpdateChannel = 
      EventChannel('com.socialplay.gpark/friend_update');
  
  // 测试方法
  static Future<String> getPlatformVersion() async {
    try {
      final version = await _testChannel.invokeMethod('getPlatformVersion');
      return version ?? 'Unknown';
    } catch (e) {
      print('Error getting platform version: $e');
      return 'Error: $e';
    }
  }
  
  // 账户相关方法
  static Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      final result = await _accountChannel.invokeMethod('getUserProfile');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }
  
  static Future<bool> updateUserProfile({
    String? nickname,
    String? signature,
  }) async {
    try {
      final result = await _accountChannel.invokeMethod('updateUserProfile', {
        if (nickname != null) 'nickname': nickname,
        if (signature != null) 'signature': signature,
      });
      return result == true;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }
  
  static Future<bool> isLoggedIn() async {
    try {
      final result = await _accountChannel.invokeMethod('isLoggedIn');
      return result == true;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }
  
  // 设置相关方法
  static Future<Map<String, dynamic>?> getSettings() async {
    try {
      final result = await _dataChannel.invokeMethod('getSettings');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      print('Error getting settings: $e');
      return null;
    }
  }
  
  static Future<bool> updateSettings(Map<String, dynamic> settings) async {
    try {
      final result = await _dataChannel.invokeMethod('updateSettings', settings);
      return result == true;
    } catch (e) {
      print('Error updating settings: $e');
      return false;
    }
  }
  
  // 导航相关方法
  static Future<void> navigateToNative(String route, [Map<String, dynamic>? params]) async {
    try {
      await _navigationChannel.invokeMethod('navigateToNative', {
        'route': route,
        'params': params,
      });
    } catch (e) {
      print('Error navigating to native: $e');
    }
  }
  
  static Future<void> finishFlutterPage() async {
    try {
      await _navigationChannel.invokeMethod('finishFlutterPage');
    } catch (e) {
      print('Error finishing flutter page: $e');
    }
  }
  
  // 分析相关方法
  static Future<void> trackEvent(String eventName, Map<String, dynamic> properties) async {
    try {
      await _analyticsChannel.invokeMethod('trackEvent', {
        'eventName': eventName,
        'properties': properties,
      });
    } catch (e) {
      print('Error tracking event: $e');
    }
  }
  
  // 登出方法
  static Future<bool> logout() async {
    try {
      final result = await _accountChannel.invokeMethod('logout');
      return result == true;
    } catch (e) {
      print('Error logging out: $e');
      return false;
    }
  }
  
  // 事件流
  static Stream<Map<String, dynamic>> get userStatusStream {
    return _userStatusChannel.receiveBroadcastStream()
        .map((event) => Map<String, dynamic>.from(event));
  }
  
  static Stream<Map<String, dynamic>> get friendUpdateStream {
    return _friendUpdateChannel.receiveBroadcastStream()
        .map((event) => Map<String, dynamic>.from(event));
  }
}
