1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.socialplay.gpark.flutter_modules" >
4
5    <uses-sdk android:minSdkVersion="21" />
6
7    <!--
8         Flutter needs it to communicate with the running application
9         to allow setting breakpoints, to provide hot reload, etc.
10    -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\Projects\Android\MetaAppInternational-2\flutter_modules\.android\src\main\AndroidManifest.xml:7:5-66
11-->F:\Projects\Android\MetaAppInternational-2\flutter_modules\.android\src\main\AndroidManifest.xml:7:22-64
12
13</manifest>
