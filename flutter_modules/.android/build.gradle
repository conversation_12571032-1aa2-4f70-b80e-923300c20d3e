// Flutter模块的Android库配置
// 暂时作为普通Android库，等Flutter SDK配置好后再启用Flutter功能

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

android {
    namespace 'com.socialplay.gpark.flutter_modules'
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.22"
    // 暂时注释掉Flutter相关依赖，等Flutter SDK安装后再启用
    // implementation 'io.flutter:flutter_embedding_debug:1.0.0'
}
