package com.socialplay.gpark.overseabridge.bridge

import android.content.Context
import com.socialplay.gpark.function.overseabridge.bridge.ISolarEngineBridge
import com.socialplay.gpark.function.overseabridge.bridge.SolarEngineConfigBridge
import org.json.JSONArray
import org.json.JSONObject

class SolarEngineSdkBridgeImpl : ISolarEngineBridge{
    override fun preInit(context: Context, appKey: String) {
    }

    override fun initialize(
        context: Context,
        appKey: String,
        config: SolarEngineConfigBridge,
        initializeCallback: ISolarEngineBridge.IInitializationCallbackBridge
    ) {
        initializeCallback.onInitializationCompleted(0)
    }

    override fun getAttribution(): JSONObject? {
        return JSONObject()
    }

    override fun getDistinctId(): String? {
        return null
    }

    override fun setChannel(channel: String?) {
    }

    override fun setGaid(gaid: String?) {
    }

    override fun logEvent(
        customEventName: String,
        preEventData: JSONObject?,
        customEventData: JSONObject?
    ) {
    }

    override fun reportEventImmediately() {
    }

    override fun userInit(properties: JSONObject) {
    }

    override fun userUpdate(properties: JSONObject) {
    }

    override fun userAdd(properties: JSONObject) {
    }

    override fun userUnset(vararg keys: String) {
    }

    override fun userAppend(properties: JSONObject) {
    }

    override fun userDelete(userDeleteType: ISolarEngineBridge.UserDeleteTypeBridge) {
    }

    override fun setVisitorID(visitorID: String) {
        
    }

    override fun getVisitorID(): String? {
        return null
    }

    override fun login(accountID: String) {
        
    }

    override fun getAccountID(): String? {
        return null
    }

    override fun logout() {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: Int) {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: Long) {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: Float) {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: Double) {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: Boolean) {
        
    }

    override fun setSuperProperties(context: Context, key: String, value: String) {
        
    }

    override fun setSuperProperties(context: Context, key: String, values: JSONArray) {
        
    }

    override fun setSuperProperties(context: Context, key: String, values: JSONObject) {
        
    }

    override fun unsetSuperProperty(context: Context, key: String) {
        
    }

    override fun clearSuperProperties(context: Context) {
        
    }
}