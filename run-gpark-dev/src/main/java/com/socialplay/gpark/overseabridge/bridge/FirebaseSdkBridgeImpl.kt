package com.socialplay.gpark.overseabridge.bridge

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LoginFromType
import com.socialplay.gpark.function.overseabridge.bridge.IFirebaseSdkBridge

class FirebaseSdkBridgeImpl : IFirebaseSdkBridge {

    override fun isEnable(): <PERSON>olean {
        return false
    }

    override val analytics: IFirebaseSdkBridge.IAnalyticsBridge by lazy {
        object : IFirebaseSdkBridge.IAnalyticsBridge {
            override fun setUserProperty(name: String, value: String) {
            }

            override fun logEvent(kind: String, params: Bundle) {
            }

            override fun setUserId(uuid: String?) {
            }
        }
    }
    override val firebaseIdLiveData: LiveData<String?> = MutableLiveData()
    override val appInstanceIdLiveData: LiveData<String?> = MutableLiveData()

    override val auth: IFirebaseSdkBridge.IAuthBridge by lazy {
        object : IFirebaseSdkBridge.IAuthBridge {
            override fun signOut() {
            }

            override suspend fun awaitSignInWithCredential(loginFromType: LoginFromType?, token: String) {
            }

            override suspend fun getIdToken(customToken: String?): FireBaseIdToken? {
                return null
            }

            override suspend fun unLinkWithCredential(loginFromType: LoginFromType) {
            }

            override suspend fun linkWithCredential(loginFromType: LoginFromType, token: String) {
            }
        }
    }

    override fun getFirebaseId(context: Context) {
    }

    override fun getAppInstanceId(context: Context) {
    }

    override fun parseDynamicLinks(intent: Intent, activity: Activity, succeed: (Uri?) -> Unit, failed: (Exception) -> Unit) {
        succeed(intent.data)
    }

    override fun createDynamicLink(deepLink: Uri): Uri {
        return deepLink
    }
}
