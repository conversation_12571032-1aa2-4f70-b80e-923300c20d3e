# Flutter集成架构方案

## 1. 整体架构设计

### 1.1 混合架构模式
```
┌─────────────────────────────────────────────────────────────┐
│                    Android Native App                       │
├─────────────────────────────────────────────────────────────┤
│  Core Modules (保持原生)                                     │
│  ├── 游戏引擎 (MW/MetaVerse)                                │
│  ├── 编辑器核心 (Editor Core)                               │
│  ├── 启动流程 (Startup)                                     │
│  └── 核心数据层 (Repository/Database)                       │
├─────────────────────────────────────────────────────────────┤
│  Flutter Bridge Layer (桥接层)                              │
│  ├── Method Channel                                         │
│  ├── Event Channel                                          │
│  ├── Platform Views                                         │
│  └── Data Synchronization                                   │
├─────────────────────────────────────────────────────────────┤
│  Flutter Modules (迁移模块)                                 │
│  ├── 设置页面 (Settings)                                    │
│  ├── 个人资料 (Profile)                                     │
│  ├── 朋友系统 (Friends)                                     │
│  ├── 通知系统 (Notifications)                               │
│  ├── 支付系统 (Payment)                                     │
│  └── IM聊天 (Chat)                                          │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择
- **Flutter版本**: 3.16+ (稳定版)
- **状态管理**: Riverpod 2.x (类型安全，性能优秀)
- **路由管理**: Go Router (官方推荐)
- **网络请求**: Dio + Retrofit Generator
- **本地存储**: Hive/SharedPreferences
- **依赖注入**: GetIt + Injectable

## 2. 桥接层设计

### 2.1 Method Channel 设计
```kotlin
// Android端 - FlutterBridgeManager.kt
class FlutterBridgeManager {
    companion object {
        const val CHANNEL_ACCOUNT = "com.socialplay.gpark/account"
        const val CHANNEL_NAVIGATION = "com.socialplay.gpark/navigation"
        const val CHANNEL_DATA = "com.socialplay.gpark/data"
        const val CHANNEL_ANALYTICS = "com.socialplay.gpark/analytics"
    }
    
    // 账户相关桥接
    private val accountChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_ACCOUNT)
    
    // 导航相关桥接
    private val navigationChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAVIGATION)
    
    // 数据同步桥接
    private val dataChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_DATA)
}
```

### 2.2 数据同步机制
```kotlin
// 数据同步接口
interface FlutterDataBridge {
    // 用户信息同步
    suspend fun syncUserProfile(): UserProfileData
    
    // 好友列表同步
    suspend fun syncFriendsList(): List<FriendData>
    
    // 设置信息同步
    suspend fun syncSettings(): SettingsData
    
    // 通知状态同步
    suspend fun syncNotificationStatus(): NotificationStatus
}
```

## 3. 模块迁移策略

### 3.1 第一阶段：设置页面迁移

**原生模块**: `ui.account.setting.SettingFragment`
**Flutter模块**: `lib/modules/settings/`

**迁移步骤**:
1. 创建Flutter设置模块
2. 实现数据桥接
3. 保持原生入口，内容用Flutter渲染
4. 逐步测试和优化

### 3.2 第二阶段：个人资料迁移

**原生模块**: `ui.profile.*`
**Flutter模块**: `lib/modules/profile/`

**关键考虑**:
- 头像上传和编辑
- 个人信息同步
- 权限管理

### 3.3 第三阶段：朋友系统迁移

**原生模块**: `ui.friend.*`, `function.friend.*`
**Flutter模块**: `lib/modules/friends/`

**复杂度分析**:
- 好友列表管理
- 实时状态更新
- 消息通知集成

## 4. 技术实现细节

### 4.1 Flutter Engine 集成
```kotlin
// FlutterEngineManager.kt
class FlutterEngineManager {
    private var flutterEngine: FlutterEngine? = null
    
    fun initializeFlutterEngine(context: Context) {
        flutterEngine = FlutterEngine(context).apply {
            // 预热Dart VM
            dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
            
            // 注册插件
            registerPlugins()
            
            // 设置桥接通道
            setupMethodChannels()
        }
    }
    
    private fun registerPlugins() {
        // 注册必要的Flutter插件
        flutterEngine?.plugins?.add(SharedPreferencesPlugin())
        flutterEngine?.plugins?.add(PathProviderPlugin())
        // ... 其他插件
    }
}
```

### 4.2 路由集成策略
```kotlin
// FlutterRouterBridge.kt
class FlutterRouterBridge {
    fun navigateToFlutterModule(
        activity: Activity,
        moduleName: String,
        params: Map<String, Any>? = null
    ) {
        val intent = FlutterActivity
            .withCachedEngine("main_engine")
            .build(activity)
            
        // 传递路由参数
        intent.putExtra("initial_route", "/$moduleName")
        params?.let {
            intent.putExtra("route_params", Gson().toJson(it))
        }
        
        activity.startActivity(intent)
    }
}
```

### 4.3 状态同步机制
```dart
// Flutter端 - BridgeService.dart
class BridgeService {
  static const MethodChannel _accountChannel = 
      MethodChannel('com.socialplay.gpark/account');
  
  static const MethodChannel _dataChannel = 
      MethodChannel('com.socialplay.gpark/data');
  
  // 获取用户信息
  Future<UserProfile> getUserProfile() async {
    final result = await _accountChannel.invokeMethod('getUserProfile');
    return UserProfile.fromJson(result);
  }
  
  // 更新用户信息
  Future<bool> updateUserProfile(UserProfile profile) async {
    return await _accountChannel.invokeMethod(
      'updateUserProfile', 
      profile.toJson()
    );
  }
}
```

## 5. 性能优化策略

### 5.1 Flutter Engine 预热
- 应用启动时预初始化Flutter Engine
- 使用缓存引擎减少启动时间
- 按需加载Flutter模块

### 5.2 内存管理
- 合理管理Flutter Engine生命周期
- 及时释放不用的资源
- 监控内存使用情况

### 5.3 包体积优化
- 使用Flutter的分包机制
- 按需引入Flutter依赖
- 代码混淆和压缩

## 6. 测试策略

### 6.1 单元测试
- Flutter模块独立测试
- 桥接层接口测试
- 数据同步测试

### 6.2 集成测试
- 原生-Flutter交互测试
- 端到端功能测试
- 性能基准测试

### 6.3 兼容性测试
- 不同Android版本测试
- 不同设备规格测试
- 网络环境测试

## 7. 风险控制

### 7.1 回滚机制
- 保持原生模块作为备选
- 实现动态开关控制
- 灰度发布策略

### 7.2 监控告警
- 性能监控
- 崩溃率监控
- 用户体验指标

### 7.3 兼容性保证
- API版本兼容
- 数据格式兼容
- 用户体验一致性
