<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <FrameLayout
        android:id="@+id/fl_web_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusPlacedHolder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:isRightIconVisible="true"
        app:layout_constraintTop_toBottomOf="@id/statusPlacedHolder"
        app:rightIcon="@drawable/ic_web_more_menu" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/v_loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/fl_web_container"
        app:layout_constraintTop_toTopOf="@id/fl_web_container" />

</androidx.constraintlayout.widget.ConstraintLayout>