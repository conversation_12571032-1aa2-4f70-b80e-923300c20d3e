package com.meta.web.preload

import com.meta.web.model.BasicWebArgs
import com.meta.web.model.TypedMap
import com.socialplay.gpark.function.analytics.resid.ResIdBean

internal data class PreloadWebArgs(
    override val extras: TypedMap = TypedMap(),
    override val url: String,
    override val resIdBean: ResIdBean,
    override val textZoom: Int,
    override val from: String?,
    override val preloadUniqueId: String?,
    override val releaseWhenNavigateUp: Boolean
) : BasicWebArgs