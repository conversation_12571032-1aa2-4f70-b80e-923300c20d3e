package com.meta.web.constans

import android.annotation.SuppressLint
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.function.analytics.EventDesc

@SuppressLint("ChineseStringLiteral")
internal object EventConstants {

    @EventDesc("web通用弹窗展示")
    val EVENT_WEB_COMMON_POP_SHOW = Event("event_web_common_pop_show")

    @EventDesc("web通用弹窗点击")
    val EVENT_WEB_COMMON_POP_CLICK = Event("event_web_common_pop_click")

    @EventDesc("Web页面展示")
    val EVENT_WEB_SHOW = Event("event_web_show")


    @EventDesc("web资源替换结果（页面不可见时上报）")
    val EVENT_PRELOADING_REPLACE_RESULT = Event("event_preloading_replace_result")


}