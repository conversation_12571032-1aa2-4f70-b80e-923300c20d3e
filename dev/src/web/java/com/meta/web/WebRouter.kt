package com.meta.web

import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.clearFragmentResultListener
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.NavController
import androidx.navigation.NavDeepLinkRequest
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.meta.web.model.EmbeddedWebArgsBuilder
import com.meta.web.model.FullScreenWebDialogArgsBuilder
import com.meta.web.model.StandaloneWebArgsBuilder
import com.meta.web.model.WebDialogArgsBuilder
import com.meta.web.ui.EmbeddedWebCoreFragment
import com.meta.web.ui.FullScreenWebCoreDialog
import com.meta.web.ui.WebCoreDialog
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.util.extension.toBundle
import com.socialplay.gpark.util.extension.toUri

object WebRouter {

    private const val URL_BASE = "meta://com.meta.web"
    private const val URL_FRAGMENT_WEB_STANDALONE = "$URL_BASE/fragment_web_standalone"

    fun createEmbeddedWebFragment(
        url: String,
        resIdBean: ResIdBean,
        builder: EmbeddedWebArgsBuilder.() -> Unit = {}
    ): EmbeddedWebCoreFragment {

        val paramBuilder = EmbeddedWebArgsBuilder()
        val args = builder(paramBuilder).let { paramBuilder.build(url, resIdBean) }

        return EmbeddedWebCoreFragment.newInstance(args)
    }

    fun showStandaloneFragment(
        fragment: Fragment,
        url: String,
        resIdBean: ResIdBean,
        navOptions: NavOptions? = null,
        builder: StandaloneWebArgsBuilder.() -> Unit = {}
    ) {
        val navController = fragment.findNavController()
        showStandaloneFragment(navController, url, resIdBean, navOptions, builder)
    }

    fun showStandaloneFragment(
        navController: NavController,
        url: String,
        resIdBean: ResIdBean,
        navOptions: NavOptions? = null,
        builder: StandaloneWebArgsBuilder.() -> Unit = {}
    ) {

        val paramBuilder = StandaloneWebArgsBuilder()
        val args = builder(paramBuilder).let { paramBuilder.build(url, resIdBean) }

        val request = NavDeepLinkRequest.Builder
            .fromUri(URL_FRAGMENT_WEB_STANDALONE.toUri(args))
            .build()
        navController.navigate(request, navOptions)
    }

    fun showDialog(
        fragmentManager: FragmentManager,
        url: String,
        resIdBean: ResIdBean,
        builder: WebDialogArgsBuilder.() -> Unit = {}
    ): DialogFragment {

        val paramsBuilder = WebDialogArgsBuilder()

        val args = builder(paramsBuilder).let { paramsBuilder.build(url, resIdBean) }

        val dialogFragment = WebCoreDialog()
        dialogFragment.arguments = args.toBundle()

        dialogFragment.show(fragmentManager, WebCoreDialog::class.java.simpleName)

        return dialogFragment
    }

    fun showFullScreenDialog(
        fragmentManager: FragmentManager,
        url: String,
        resIdBean: ResIdBean,
        fragmentResultCallback: (dialog: FullScreenWebCoreDialog, data: Bundle) -> Unit,
        argsBuilder: FullScreenWebDialogArgsBuilder.() -> Unit = {}
    ): FullScreenWebCoreDialog {

        val paramsBuilder = FullScreenWebDialogArgsBuilder()

        val args = argsBuilder(paramsBuilder).let { paramsBuilder.build(url, resIdBean) }

        val dialogFragment = FullScreenWebCoreDialog()
        dialogFragment.arguments = args.toBundle()

        dialogFragment.show(fragmentManager, FullScreenWebCoreDialog::class.java.simpleName)

        dialogFragment.setFragmentResultListener(FullScreenWebCoreDialog.RESULT_KEY) { key, bundle ->
            dialogFragment.clearFragmentResult(FullScreenWebCoreDialog.RESULT_KEY)
            dialogFragment.clearFragmentResultListener(FullScreenWebCoreDialog.RESULT_KEY)

            fragmentResultCallback(dialogFragment, bundle)
        }

        return dialogFragment
    }

}