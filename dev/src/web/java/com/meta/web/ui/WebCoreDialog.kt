package com.meta.web.ui

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import com.meta.biz.web.assets.WebAssetsBiz
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.WebCoreFactory
import com.meta.lib.web.core.WebCoreOptions
import com.meta.lib.web.core.container.IWebContainer
import com.meta.lib.web.core.container.Insets
import com.meta.lib.web.core.helper.CustomViewHelper
import com.meta.lib.web.core.helper.FileChooseHelper
import com.meta.lib.web.core.preload.WebCorePreloadOptions
import com.meta.web.container.AbsFragmentContainerImpl
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.CommonFragmentWebCoreHelper
import com.meta.web.helper.LoadingViewHelper
import com.meta.web.helper.WebNavigationHelper
import com.meta.web.jsb.AbstractNativeInterface
import com.meta.web.jsb.WebDialogNativeInterfaceImpl
import com.meta.web.model.WebDialogArgs
import com.meta.web.ui.contract.IWebDialogInterceptor
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogWebBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.extension.navParams
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import org.koin.core.context.GlobalContext

/**
 * create by: bin on 2022/6/15
 */
open class WebCoreDialog : BaseDialogFragment(), IFragmentWebContainer {

    override val binding by viewBinding(DialogWebBinding::inflate)

    private val platformContract: IWebPlatformContract by lazy { GlobalContext.get().get() }

    private var webDialogInterceptor: IWebDialogInterceptor? = null

    private val args: WebDialogArgs by lazy {
        arguments?.navParams() ?: throw IllegalArgumentException("args is null")
    }

    override lateinit var webCore: WebCore
    override val fragment: Fragment get() = this

    private var webContainer: WebDialogContainerImpl? = null

    private val commonFragmentWebCoreHelper by lazy { CommonFragmentWebCoreHelper(this) }

    private val navigationHelper: WebNavigationHelper by lazy {
        WebNavigationHelper(webCore, this) {
            if(webDialogInterceptor?.onNavigateUp() == true) return@WebNavigationHelper
            dismissAllowingStateLoss()
        }
    }

    private lateinit var _fileChooseHelper: FileChooseHelper

    protected inner class WebDialogContainerImpl : AbsFragmentContainerImpl(platformContract, this, args) {

        override val loadingViewHelper: LoadingViewHelper get() = LoadingViewHelper(webCore, binding.vLoading)

        private val _customViewHelper: CustomViewHelper by lazy { CustomViewHelper(webCore, this@WebCoreDialog) }
        private val _nativeInterface: AbstractNativeInterface by lazy {
            WebDialogNativeInterfaceImpl(
                webCore,
                platformContract,
                this@WebCoreDialog,
                args,
                navigationHelper
            )
        }

        override fun getCustomViewHelper(): CustomViewHelper = _customViewHelper
        override fun getNativeInterface(webCore: WebCore): AbstractNativeInterface = _nativeInterface
        override fun getFileChooseHelper(): FileChooseHelper = _fileChooseHelper


        override fun getInsets(webCore: WebCore) = emptyList<Insets>()

        override fun updateInsets(webCore: WebCore, insets: Insets) {}

        override fun setWebViewTopPosition(webCore: WebCore, pos: String) {}

        override fun onAttachWebView(webCore: WebCore, webView: WebView) {
            webView.setBackgroundColor(resources.getColor(R.color.transparent))
            super.onAttachWebView(webCore, webView)
        }

        override fun getWebContainerView(webCore: WebCore): FrameLayout = binding.flWebContainer

        override fun requestOrientation(webCore: WebCore, orientation: Int) {}

        override fun setTitle(webCore: WebCore, value: String) {
        }

        override fun setTitleMenuVisible(webCore: WebCore, visible: Boolean) {
        }

        override fun onWebViewCreateError(webCore: WebCore, ex: Throwable) {
            toast(resources.getString(R.string.open_webview_excption))
        }

        override fun unregisterContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
            navigationHelper.unregisterContainerNavigationListener(listener)
        }

        override fun registerContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
            navigationHelper.registerContainerNavigationListener(listener)
        }

        override fun createLegacyJsApi(
            webCore: WebCore,
            webView: WebView
        ): IWebPlatformContract.LegacyJsApi {
            return platformContract.createLegacyJsApi(
                webCore,
                this@WebCoreDialog,
                webView,
                args,
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val container = WebDialogContainerImpl().also { webContainer = it }
        webCore = WebCoreFactory.create(
            requireContext(), container, args.url,
            urlOptionsDefaultValues = platformContract.getUrlOptionDefaultValues(),
            options = WebCoreOptions(textZoom = args.textZoom, debugMode = platformContract.debugMode),
            preloadOptions = args.preloadUniqueId?.let { WebCorePreloadOptions(preloadUniqueId = it) },
            assetsProvider = WebAssetsBiz.assetsProvider,
        )
        _fileChooseHelper = FileChooseHelper(webCore, this)
        commonFragmentWebCoreHelper.onFragmentCreated()
        webCore.init()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webCore.attach()
    }

    override fun onResume() {
        webCore.resume()
        super.onResume()
    }

    override fun onPause() {
        webCore.pause()
        super.onPause()
    }

    override fun onDestroyView() {
        webCore.detach()
        super.onDestroyView()
    }

    override fun onDestroy() {
        commonFragmentWebCoreHelper.onFragmentDestroyed()
        webCore.release()
        super.onDestroy()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun init() {}

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun isHideNavigation(): Boolean {
        return true
    }

    override fun loadFirstData() {}

    override fun onDismiss(dialog: DialogInterface) {
        webDialogInterceptor?.onDismiss()
        super.onDismiss(dialog)
    }

    fun setWebDialogInterceptor(interceptor: IWebDialogInterceptor?) {
        webDialogInterceptor = interceptor
    }

    override fun onBackPressed(): Boolean {
        if(webDialogInterceptor?.onBackPressed() == true) return true
        return super.onBackPressed()
    }

    override fun isBackPressedDismiss(): Boolean {
        return args.autoDismiss
    }

    override fun isClickOutsideDismiss(): Boolean {
        return args.autoDismiss
    }
}