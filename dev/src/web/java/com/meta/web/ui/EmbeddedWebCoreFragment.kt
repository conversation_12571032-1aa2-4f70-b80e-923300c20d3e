package com.meta.web.ui

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.FrameLayout
import com.bin.cpbus.CpEventBus
import androidx.fragment.app.Fragment
import com.meta.biz.web.assets.WebAssetsBiz
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.WebCoreFactory
import com.meta.lib.web.core.WebCoreOptions
import com.meta.lib.web.core.container.IWebContainer
import com.meta.lib.web.core.container.Insets
import com.meta.lib.web.core.helper.CustomViewHelper
import com.meta.lib.web.core.helper.FileChooseHelper
import com.meta.lib.web.core.model.State
import com.meta.lib.web.core.preload.WebCorePreloadOptions
import com.meta.lib.web.core.webview.ContentCacheableWebView
import com.meta.web.helper.LoadingViewHelper
import com.meta.web.constans.PageNameConstants
import com.meta.web.constans.EventConstants
import com.meta.web.container.AbsFragmentContainerImpl
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.CommonFragmentWebCoreHelper
import com.meta.web.helper.FragmentSystemUIHelper
import com.meta.web.jsb.AbstractNativeInterface
import com.meta.web.jsb.EmbeddedNativeInterfaceImpl
import com.meta.web.legacy.LegacyOptionUtil
import com.meta.web.model.EmbeddedWebFragmentArgs
import com.meta.web.ui.contract.IWebContent
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.event.ScrollToTopEvent
import com.socialplay.gpark.databinding.FragmentEmbeddedWebBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.util.extension.navParams
import com.socialplay.gpark.util.extension.toBundle
import com.socialplay.gpark.util.extension.toast
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.context.GlobalContext
import java.util.concurrent.CopyOnWriteArrayList


/**
 * 嵌入到其他页面的WBebFragment，用于作为页面中的一部分如首页或详情页，如果需要独立的web页面，请使用[StandaloneWebCoreFragment]
 * 这个默认是lazy的，必须等到fragment resumed之后才会触发首次webview加载
 * 限制：
 * 1、这种内嵌的web页面通常是作为页面的一部分，所以web指定将内容显示到t1或t2这些地方，只能由嵌入它的容器来控制，是否存在其他的Tn由容器来控制，默认的这个不支持
 * 2、不支持关闭当前的页面相关函数
 * 3、状态栏变色可能有使用场景，如将web容器放置到页面最顶部，TODO 稍后实现
 */
open class EmbeddedWebCoreFragment : BaseFragment<FragmentEmbeddedWebBinding>(
    R.layout.fragment_embedded_web
), IWebContent, IFragmentWebContainer {

    companion object{
        fun newInstance(args: EmbeddedWebFragmentArgs): EmbeddedWebCoreFragment {
            return EmbeddedWebCoreFragment().apply {
                arguments = args.toBundle()
            }
        }
    }

    private val platformContract: IWebPlatformContract by lazy { GlobalContext.get().get() }

    private val contentLoadListeners: CopyOnWriteArrayList<IWebContent.IWebContentLoadListener> = CopyOnWriteArrayList()

    private val args: EmbeddedWebFragmentArgs by lazy {
        arguments?.navParams() ?: throw IllegalArgumentException("args is null")
    }

    override lateinit var webCore: WebCore
    override val fragment: Fragment get() = this

    private val systemUIHelper: FragmentSystemUIHelper by lazy { FragmentSystemUIHelper(this) }

    private lateinit var _fileChooseHelper: FileChooseHelper

    private val commonFragmentWebCoreHelper by lazy { CommonFragmentWebCoreHelper(this) }

    protected inner class EmbeddedFragmentContainerImpl : AbsFragmentContainerImpl(platformContract, this, args) {

        override val loadingViewHelper: LoadingViewHelper get() = LoadingViewHelper(webCore,binding.vLoading)

        private val _customViewHelper: CustomViewHelper by lazy { CustomViewHelper(webCore, this@EmbeddedWebCoreFragment) }
        private val _nativeInterface: AbstractNativeInterface by lazy {
            EmbeddedNativeInterfaceImpl(
                webCore,
                platformContract,
                this@EmbeddedWebCoreFragment,
                args,
            )
        }

        override fun getNativeInterface(webCore: WebCore): AbstractNativeInterface = _nativeInterface

        override fun getCustomViewHelper(): CustomViewHelper = _customViewHelper

        override fun getFileChooseHelper(): FileChooseHelper = _fileChooseHelper

        override fun getInsets(webCore: WebCore) = emptyList<Insets>()

        override fun updateInsets(webCore: WebCore, insets: Insets) {
        }

        override fun setWebViewTopPosition(webCore: WebCore, pos: String) {}

        override fun getWebContainerView(webCore: WebCore): FrameLayout = binding.flWebContainer

        override fun onAttachWebView(webCore: WebCore, webView: WebView) {
            super.onAttachWebView(webCore, webView)
            if(webView is ContentCacheableWebView){
                webView.setWebContentsCacheEnabled(platformContract.embeddedWebContentCacheEnabled)
            }
        }

        override fun requestOrientation(webCore: WebCore, orientation: Int) {
        }

        override fun setTitle(webCore: WebCore, value: String) {
        }

        override fun setTitleMenuVisible(webCore: WebCore, visible: Boolean) {
        }

        override fun onWebViewCreateError(webCore: WebCore, ex: Throwable) {
            toast(resources.getString(R.string.open_webview_excption))
        }

        override fun onWebContentLoadFinish(
            webCore: WebCore,
            success: Boolean,
            errorCode: Int,
            msg: String?
        ) {
            contentLoadListeners.forEach { it.onContentLoadFinish(success) }
        }

        override fun unregisterContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
        }

        override fun registerContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
        }

        override fun createLegacyJsApi(
            webCore: WebCore,
            webView: WebView
        ): IWebPlatformContract.LegacyJsApi {
            return platformContract.createLegacyJsApi(
                webCore,
                this@EmbeddedWebCoreFragment,
                webView,
                args,
            )
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        systemUIHelper.restoreInstance(savedInstanceState)

        val webContainer = EmbeddedFragmentContainerImpl()
        webCore = WebCoreFactory.create(
            requireContext(), webContainer, args.url,
            LegacyOptionUtil.toUrlOptions(args),
            urlOptionsDefaultValues = platformContract.getUrlOptionDefaultValues(),
            options = WebCoreOptions(textZoom = args.textZoom, debugMode = platformContract.debugMode),
            preloadOptions = args.preloadUniqueId?.let { WebCorePreloadOptions(preloadUniqueId = it) },
            assetsProvider = WebAssetsBiz.assetsProvider,
        )
        _fileChooseHelper = FileChooseHelper(webCore, this)
        commonFragmentWebCoreHelper.onFragmentCreated()
        webCore.init()
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEmbeddedWebBinding? {
        return FragmentEmbeddedWebBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webCore.attach()
        CpEventBus.register(this)

    }


    override fun onPause() {
        webCore.pause()
        super.onPause()
    }

    override fun onResume() {
        webCore.resume()
        super.onResume()

        Analytics.track(
            EventConstants.EVENT_WEB_SHOW,
            mapOf("pageName" to getPageName()) + getPageExtraParams()
        )

    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        systemUIHelper.saveInstance(outState)
    }

    override fun getPageName(): String {
        val sb = StringBuffer(PageNameConstants.FRAGMENT_NAME_WEB)
        if (!args.from.isNullOrEmpty() && args.from != "inner") {
            sb.append("-").append(args.from)
        }
        if (!args.title.isNullOrEmpty()) {
            sb.append("-").append(args.title)
        }
        return sb.toString()
    }

    override fun getPageExtraParams(): Map<String, String> {
        val uri = kotlin.runCatching { Uri.parse(args.url) }.getOrNull()
        return mapOf(
            "path" to (uri?.path ?: ""),
            "host" to (uri?.host ?: ""),
        )
    }

    override fun onDestroyView() {
        webCore.detach()
        CpEventBus.unregister(this)
        super.onDestroyView()
    }

    override fun onDestroy() {
        commonFragmentWebCoreHelper.onFragmentDestroyed()
        webCore.release()
        super.onDestroy()
    }

    override fun invalidate() {}

    override fun checkReload() {
        if(webCore.webContentLoadStatus is State.Error){
            webCore.reload()
        }
    }

    override fun reload() {
        webCore.reload()
    }

    override fun registerContentLoadListener(listener: IWebContent.IWebContentLoadListener) {
        contentLoadListeners.add(listener)
    }

    override fun unregisterContentLoadListener(listener: IWebContent.IWebContentLoadListener) {
        contentLoadListeners.remove(listener)
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onScrollEvent(event: ScrollToTopEvent) {
        if (isResumed) {
            webCore.webInterface.scrollPage(null)
        }
    }
}