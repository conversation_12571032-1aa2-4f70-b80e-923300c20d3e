package com.meta.web.ui

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.activity.OnBackPressedCallback
import androidx.activity.addCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.meta.biz.web.assets.WebAssetsBiz
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.WebCoreFactory
import com.meta.lib.web.core.WebCoreOptions
import com.meta.lib.web.core.container.IWebContainer
import com.meta.lib.web.core.container.Insets
import com.meta.lib.web.core.container.WebViewTopPosition
import com.meta.lib.web.core.helper.CustomViewHelper
import com.meta.lib.web.core.helper.FileChooseHelper
import com.meta.lib.web.core.model.State
import com.meta.lib.web.core.preload.WebCorePreloadOptions
import com.meta.web.helper.LoadingViewHelper
import com.meta.web.constans.PageNameConstants
import com.meta.web.constans.EventConstants
import com.meta.web.model.StandaloneWebFragmentArgs
import com.meta.web.helper.WebNavigationHelper
import com.meta.web.container.AbsFragmentContainerImpl
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.CommonFragmentWebCoreHelper
import com.meta.web.helper.FragmentSystemUIHelper
import com.meta.web.jsb.AbstractNativeInterface
import com.meta.web.jsb.StandaloneNativeInterfaceImpl
import com.meta.web.legacy.LegacyOptionUtil
import com.meta.web.ui.contract.IWebContent
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentStandaloneWebBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.navParams
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import java.util.concurrent.CopyOnWriteArrayList


/**
 * 独立打开的WebFragment，用于单独的web页面，如果需要将WebFragment嵌入到其他的Fragment，请使用[EmbeddedWebCoreFragment]
 */
open class StandaloneWebCoreFragment : BaseFragment<FragmentStandaloneWebBinding>(
    R.layout.fragment_standalone_web
), IWebContent, IFragmentWebContainer {

    private val platformContract: IWebPlatformContract by lazy { GlobalContext.get().get() }

    private val args: StandaloneWebFragmentArgs by lazy {
        arguments?.navParams() ?: throw IllegalArgumentException("args is null")
    }

    override lateinit var webCore: WebCore
    override val fragment: Fragment get() = this

    private val navigationHelper: WebNavigationHelper by lazy {
        WebNavigationHelper(webCore, this) { findNavController().navigateUp() }
    }

    private lateinit var _fileChooseHelper: FileChooseHelper

    private var onBackPressedCallback: OnBackPressedCallback? = null

    private val systemUIHelper: FragmentSystemUIHelper by lazy { FragmentSystemUIHelper(this) }

    private val contentLoadListeners: CopyOnWriteArrayList<IWebContent.IWebContentLoadListener> = CopyOnWriteArrayList()

    private val commonFragmentWebCoreHelper by lazy { CommonFragmentWebCoreHelper(this) }

    protected inner class StandaloneFragmentContainerImpl : AbsFragmentContainerImpl(platformContract, this, args) {

        override val loadingViewHelper: LoadingViewHelper get() = LoadingViewHelper(webCore,binding.vLoading)
        private val _customViewHelper: CustomViewHelper by lazy { CustomViewHelper(webCore, this@StandaloneWebCoreFragment)}
        private val _nativeInterface: AbstractNativeInterface by lazy {
            StandaloneNativeInterfaceImpl(
                webCore,
                platformContract,
                this@StandaloneWebCoreFragment,
                args,
                navigationHelper,
            )
        }

        override fun getCustomViewHelper(): CustomViewHelper = _customViewHelper
        override fun getFileChooseHelper(): FileChooseHelper = _fileChooseHelper
        override fun getNativeInterface(webCore: WebCore): AbstractNativeInterface = _nativeInterface

        private val insetsList: MutableList<Insets> by lazy {
            val statusBarHeight = StatusBarUtil.getStatusBarHeight(requireContext())
            val statusBar = Insets(
                WebViewTopPosition.T1,
                top = 0,
                height = statusBarHeight,
                visible = true,
                backgroundColor = resources.getColor(R.color.transparent),
                textColor = resources.getColor(R.color.black)
            )

            val titleBarHeight =
                resources.getDimensionPixelOffset(R.dimen.title_bar_height)
            val titleBar = Insets(
                WebViewTopPosition.T2,
                top = statusBar.top + statusBar.height,
                height = titleBarHeight,
                visible = true,
                backgroundColor = resources.getColor(R.color.white),
                textColor = resources.getColor(R.color.black)
            )

            mutableListOf(statusBar, titleBar)
        }

        override fun getInsets(webCore: WebCore) = insetsList

        override fun updateInsets(webCore: WebCore, insets: Insets) {
            val indexOfInsets = this.insetsList.indexOfFirst { it.position == insets.position }

            if (indexOfInsets != -1) {
                this.insetsList[indexOfInsets] = insets
            }

            when (insets.position) {
                WebViewTopPosition.T1 -> {
                    systemUIHelper.backupSystemUiSettingsIfNeeded(systemUiVisibility = activity?.window?.decorView?.systemUiVisibility)

                    binding.statusPlacedHolder.visible(insets.visible)
                    binding.statusPlacedHolder.setBackgroundColor(insets.backgroundColor)


                    if ((insets.textColor and 0x00FFFFFF) == 0xFFFFFF) { // 白色文字，设置标题文字为白色
                        StatusBarUtil.setDarkMode(requireActivity())
                        statusBarViewModel.update(false)
                    } else if ((insets.textColor and 0x00FFFFFF) == 0x000000) { // 黑色文字，设置标题文字为黑色
                        StatusBarUtil.setLightMode(requireActivity())
                        statusBarViewModel.update(true)
                    }
//
//                    activity?.window?.let {
//                        if(insets.visible) {
//
//                            it.decorView.systemUiVisibility = it.decorView.systemUiVisibility  xor (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_FULLSCREEN)
//                        }else{
//                            it.decorView.systemUiVisibility = it.decorView.systemUiVisibility or (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_FULLSCREEN)
//                        }
//                    }

                    systemUIHelper.setSystemUiSettings(systemUiVisibility = activity?.window?.decorView?.systemUiVisibility)
                }

                WebViewTopPosition.T2 -> {
                    binding.tbl.visibility = if (insets.visible) View.VISIBLE else View.GONE
                    binding.tbl.setBarBackgroundColor(insets.backgroundColor)
                    binding.tbl.setTintColor(insets.textColor)
                }
            }
        }

        override fun setWebViewTopPosition(webCore: WebCore, pos: String) {
            val layoutParams = binding.flWebContainer.layoutParams as ConstraintLayout.LayoutParams
            when (pos) {
                WebViewTopPosition.T0 -> {
                    layoutParams.topMargin = 0
                }
                WebViewTopPosition.T1 -> {
                    layoutParams.topMargin = StatusBarUtil.getStatusBarHeight(requireContext())
                }
                WebViewTopPosition.T2 -> {
                    layoutParams.topMargin = StatusBarUtil.getStatusBarHeight(requireContext()) + resources.getDimensionPixelOffset(R.dimen.title_bar_height)
                }
            }
            binding.flWebContainer.layoutParams = layoutParams
        }

        override fun getWebContainerView(webCore: WebCore): FrameLayout = binding.flWebContainer

        override fun onWebViewCreateError(webCore: WebCore, ex: Throwable) {
            toast(resources.getString(R.string.open_webview_excption))
            navigationHelper.navigateToPreviousPage()
            return
        }

        override fun requestOrientation(webCore: WebCore, orientation: Int) {
            systemUIHelper.backupSystemUiSettingsIfNeeded(orientation = activity?.requestedOrientation)
            systemUIHelper.setSystemUiSettings(orientation = orientation)

            requireActivity().requestedOrientation = orientation
        }

        override fun setTitle(webCore: WebCore, value: String) {
            binding.tbl.setTitle(value)
        }

        override fun setTitleMenuVisible(webCore: WebCore, visible: Boolean) {
            binding.tbl.setRightIconVisibility(visible)
        }

        override fun onWebContentLoadFinish(webCore: WebCore, success: Boolean, errorCode: Int, msg: String?) {
            contentLoadListeners.forEach { it.onContentLoadFinish(success) }
        }

        override fun unregisterContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
            navigationHelper.unregisterContainerNavigationListener(listener)
        }

        override fun registerContainerNavigationListener(listener: IWebContainer.ContainerNavigationListener) {
            navigationHelper.registerContainerNavigationListener(listener)
        }

        override fun createLegacyJsApi(webCore: WebCore, webView: WebView): IWebPlatformContract.LegacyJsApi {
            return platformContract.createLegacyJsApi(
                webCore,
                this@StandaloneWebCoreFragment,
                webView,
                args,
            )
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        systemUIHelper.restoreInstance(savedInstanceState)

        val webContainer = StandaloneFragmentContainerImpl()
        webCore = WebCoreFactory.create(
            requireContext(), webContainer, args.url,
            LegacyOptionUtil.toUrlOptions(args),
            urlOptionsDefaultValues = platformContract.getUrlOptionDefaultValues(),
            options = WebCoreOptions(textZoom = args.textZoom, debugMode = platformContract.debugMode),
            preloadOptions = args.preloadUniqueId?.let { WebCorePreloadOptions(preloadUniqueId = it) },
            assetsProvider = WebAssetsBiz.assetsProvider,
        )
        _fileChooseHelper = FileChooseHelper(webCore, this)
        commonFragmentWebCoreHelper.onFragmentCreated()
        webCore.init()
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStandaloneWebBinding? {
        return FragmentStandaloneWebBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webCore.attach()
        registerListeners()
    }

    override fun onPause() {
        webCore.pause()
        super.onPause()
        onBackPressedCallback?.remove()
    }

    override fun onResume() {
        webCore.resume()
        super.onResume()

        val map = mutableMapOf("pageName" to getPageName())
        if (!args.categoryId.isNullOrEmpty()){
            args.categoryId?.let {
                map.put("show_categoryID", it)
            }
        }
        if (!args.shareId.isNullOrEmpty()){
            args.shareId?.let {
                map.put("shareid2", it)
            }
        }
        Analytics.track(
            EventConstants.EVENT_WEB_SHOW,
            map  + getPageExtraParams()
        )

        //返回按键监听
        onBackPressedCallback = requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            onBackPressed = {
                navigationHelper.goBack(true, 1)
            }
        )
    }

    private fun registerListeners() {
        binding.tbl.setOnBackClickedListener {
            navigationHelper.goBack(true, 1)
        }
        binding.tbl.setOnRightIconClickedListener {
            WebMenuDialog.show(
                childFragmentManager,
                url = args.url,
                onRefresh = {
                    webCore.reload()
                },
                onFeedback = {
                    platformContract.openFeedbackPage(this@StandaloneWebCoreFragment, args.url)
                },
                onCopyLink = {
                    val url = webCore.stripCustomQueryParams(webCore.currentPageUrl)
                    lifecycleScope.launch {
                        ClipBoardUtil.setClipBoardContent(url, requireContext(), "URL")
                    }
                    toast(R.string.copy_link_success)
                }
            )
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        systemUIHelper.saveInstance(outState)
    }

    override fun getPageName(): String {
        val sb = StringBuffer(PageNameConstants.FRAGMENT_NAME_WEB)
        if (!args.from.isNullOrEmpty() && args.from != "inner") {
            sb.append("-").append(args.from)
        }
        if (!args.title.isNullOrEmpty()) {
            sb.append("-").append(args.title)
        }
        return sb.toString()
    }

    override fun getPageExtraParams(): Map<String, String> {
        val uri = kotlin.runCatching { Uri.parse(args.url) }.getOrNull()
        return mapOf(
            "path" to (uri?.path ?: ""),
            "host" to (uri?.host ?: ""),
        )
    }


    override fun onDestroyView() {
        webCore.detach()
        super.onDestroyView()
    }

    override fun onDestroy() {
        commonFragmentWebCoreHelper.onFragmentDestroyed()
        webCore.release()
        super.onDestroy()
    }

    override fun invalidate() {}

    override fun checkReload() {
        if(webCore.webContentLoadStatus is State.Error){
            webCore.reload()
        }
    }

    override fun reload() {
        webCore.reload()
    }

    override fun registerContentLoadListener(listener: IWebContent.IWebContentLoadListener) {
        contentLoadListeners.add(listener)
    }

    override fun unregisterContentLoadListener(listener: IWebContent.IWebContentLoadListener) {
        contentLoadListeners.remove(listener)
    }
}