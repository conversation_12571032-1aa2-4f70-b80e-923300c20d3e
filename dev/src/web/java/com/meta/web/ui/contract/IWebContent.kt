package com.meta.web.ui.contract

/**
 * 用来兼容老WebFragment的接口

 */
interface IWebContent {

    /**
     * 检查是否需要重新加载，如果需要重新加载则会重新加载
     * 在加载失败的时候，可以调用此方法进行重新加载
     */
    fun checkReload()


    /**
     * 强制触发重新加载
     */
    fun reload()

    fun registerContentLoadListener(listener: IWebContentLoadListener)

    fun unregisterContentLoadListener(listener: IWebContentLoadListener)

    interface IWebContentLoadListener {
        fun onContentLoadFinish(success: <PERSON><PERSON><PERSON>)
    }

}