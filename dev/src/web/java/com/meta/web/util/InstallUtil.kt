package com.meta.web.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri
import com.socialplay.gpark.util.InstallUtil

/**
 * created by xuanxin
 *
 */


internal object InstallUtil {

    const val PACKAGE_DOUYIN = "com.ss.android.ugc.aweme"
    const val PACKAGE_DOUYIN_LITE = "com.ss.android.ugc.aweme.lite"
    const val PACKAGE_DOUYIN_LIVE = "com.ss.android.ugc.aweme.live"
    const val PACKAGE_KUAISHOU = "com.smile.gifmaker"
    const val PACKAGE_KUAISHOU_LITE = "com.kuaishou.nebula"

    /**
     * 检测是否安装支付宝
     *
     * @param context
     * @return
     */
    fun isInstalledAliPay(context: Context): Boolean {
        val uri = Uri.parse("alipays://platformapi/startApp")
        val intent = Intent(Intent.ACTION_VIEW, uri)
        val componentName = intent.resolveActivity(context.packageManager)
        return componentName != null
    }

    /**
     * 判断是否安装微信
     *
     * @param context
     * @return
     */
    fun isInstalledWX(context: Context): Boolean {
        return InstallUtil.isInstalledWX(context)
    }

    /**
     * 判断是否安装qq
     *
     * @param context
     * @return
     */
    fun isInstalledQQ(context: Context): Boolean {
        return InstallUtil.isInstalledQQ(context)
    }

    /**
     * 判断是否安装抖音
     */
    fun isInstalledDouYin(context: Context): Boolean {
        return isAppInstalled(context, PACKAGE_DOUYIN)
    }

    /**
     * 判断是否安装抖音极速版
     */
    fun isInstalledDouYinLite(context: Context): Boolean {
        return isAppInstalled(context, PACKAGE_DOUYIN_LITE)
    }

    /**
     * 判断是否安装抖音火山版
     */
    fun isInstalledDouYinLive(context: Context): Boolean {
        return isAppInstalled(context, PACKAGE_DOUYIN_LIVE)
    }

    fun isInstalledDouyinFamilies(context: Context): Pair<Boolean, String?> {
        return if (isInstalledDouYin(context)) {
            true to PACKAGE_DOUYIN
        } else if (isInstalledDouYinLite(context)) {
            true to PACKAGE_DOUYIN_LITE
        } else if (isInstalledDouYinLive(context)) {
            true to PACKAGE_DOUYIN_LIVE
        } else {
            false to null
        }
    }

    fun isInstalledDouyinFamilies4ShareSdk(context: Context): Pair<Boolean, String?> {
        return if (isInstalledDouYin(context)) {
            true to PACKAGE_DOUYIN
        } else if (isInstalledDouYinLite(context)) {
            true to PACKAGE_DOUYIN_LITE
        } else {
            false to null
        }
    }

    /**
     * 判断是否安装快手
     * @param includeNebula 是否包含快手极速版
     */
    fun isInstalledKuaiShou(context: Context, includeNebula: Boolean = false): Boolean {
        return isAppInstalled(context, PACKAGE_KUAISHOU) || (includeNebula && isInstalledKuaiShouNebula(context))
    }

    /**
     * 判断是否安装快手极速版
     */
    fun isInstalledKuaiShouNebula(context: Context): Boolean {
        return isAppInstalled(context, PACKAGE_KUAISHOU_LITE)
    }

    fun isInstalledKuaishouFamilies(context: Context): Pair<Boolean, String?> {
        return if (isInstalledKuaiShou(context)) {
            true to PACKAGE_KUAISHOU
        } else if (isInstalledKuaiShouNebula(context)) {
            true to PACKAGE_KUAISHOU_LITE
        } else {
            false to null
        }
    }

    /**
     * 判断是否安装西瓜视频
     */
    fun isInstalledXiGuaVideo(context: Context): Boolean {
        val pkgName = "com.ss.android.article.video"
        return isAppInstalled(context, pkgName)
    }


    /**
     * 判断是否安装西瓜视频
     */
    fun isInstalledBiliBili(context: Context): Boolean {
        val pkgName = "tv.danmaku.bili"
        return isAppInstalled(context, pkgName)
    }

    /**
     * 判断是否安装小红书
     */
    fun isInstalledXhs(context: Context): Boolean {
        val pkgName = "com.xingin.xhs"
        return isAppInstalled(context, pkgName)
    }

    /**
     * 判断某个应用是否安装
     *
     * @param context
     * @param packagename
     * @return
     */
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        val packageInfo: PackageInfo? = try {
            context.packageManager.getPackageInfo(packageName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            null
        }catch (e: Throwable){
            null
        }
        return packageInfo != null
    }

    /**
     * 打开微信
     */
    fun launchWeChat(context: Context?) {
        launchAppByPkgName(context, "com.tencent.mm")
    }

    /**
     * 通过包名打开外部app
     *
     * @param context     上下文
     * @param packageName 应用名称
     */
    fun launchAppByPkgName(context: Context?, packageName: String?) {
        if (context == null || packageName.isNullOrEmpty()) {
            return
        }
        if (isAppInstalled(context, packageName)) {
            try {
                val manager = context.packageManager
                val intent = manager.getLaunchIntentForPackage(packageName)
                if (context !is Activity) {
                    intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun getLaunchIntentByPkgName(context: Context?, packageName: String?): Intent? {
        if (context == null || packageName.isNullOrEmpty()) {
            return null
        }
        if (!isAppInstalled(context, packageName)) {
            return null
        }

        return kotlin.runCatching {
            val packageManager = context.packageManager
            packageManager.getLaunchIntentForPackage(packageName)
        }.getOrNull()
    }

    fun launchOutWeb(activity: Activity, linkValue: String): Boolean {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val uri = kotlin.runCatching { Uri.parse(linkValue) }.getOrNull() ?: return false
        intent.data = uri
        return kotlin.runCatching {
            activity.startActivity(intent)
            true
        }.getOrNull() ?: return false
    }

}