package com.meta.web.container

import android.content.res.Configuration
import android.graphics.Rect
import android.net.Uri
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.meta.lib.web.core.WebCore
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.model.BasicWebArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil

abstract class AbsFragmentContainerImpl(
    platformContract: IWebPlatformContract,
    private val fragment: Fragment,
    private val webArgs: BasicWebArgs
) : AbstractWebContainer(platformContract, fragment.requireContext(), webArgs) {

    override fun getFullScreenViewBottomMargins(webCore: WebCore): Rect {
        val orientation = fragment.resources.configuration.orientation
        val navigationBarHeight =
            ScreenUtil.getCurrentNavigationBarHeight(fragment.requireActivity())

        return if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            Rect(0, 0, 0, navigationBarHeight)
        } else {
            Rect(0, 0, navigationBarHeight, 0)
        }
    }

    override fun jumpByScheme(webCore: WebCore, uri: Uri): Boolean {
        if (platformContract.jumpByScheme(uri, fragment)) {
            return true
        }
        return super.jumpByScheme(webCore, uri)
    }

    override fun requestCameraPermission(
        webCore: WebCore,
        denied: (() -> Unit)?,
        granted: () -> Unit
    ) {
        PermissionRequest.with(fragment.requireActivity())
            .permissions(Permission.CAMERA)
            .des(fragment.resources.getString(R.string.web_camera_permission_apply))
            .denied {
                fragment.lifecycleScope.launchWhenCreated {
                    ToastUtil.showShort(R.string.web_camera_permission_denied)
                }
                denied?.invoke()
            }
            .granted(granted)
            .request()
    }
}