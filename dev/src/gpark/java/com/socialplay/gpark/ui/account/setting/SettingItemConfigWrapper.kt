package com.socialplay.gpark.ui.account.setting

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.MineActionItem
import com.socialplay.gpark.data.model.MineActionJump
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.Source
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.feedback.FeedbackFragmentArgs
import java.util.ArrayList

object SettingItemConfigWrapper {
    fun getItemList(
        application: Context,
        h5PageConfigInteractor: H5PageConfigInteractor,
        metaKV: MetaKV
    ): java.util.ArrayList<MineActionItem> {

        val list = ArrayList<MineActionItem>()
        // 账户设置
        list.add(
            MineActionItem(
                R.string.mine_item_account,
                jump = MineActionJump.AccountSettingActionItem(EventConstants.EVENT_MINE_ACCOUNT_CLICK)
            )
        )
        if (!NotificationManagerCompat.from(application).areNotificationsEnabled()) {
            list.add(
                MineActionItem(
                    R.string.notifications,
                    jump = MineActionJump.GraphNav(
                        R.id.notification_setting,
                        EventConstants.EVENT_NOTIFICATION_SETTING_CLICK
                    )
                )
            )
        }
        // 钱包
//        if (PandoraToggle.isRechargeToggle) {
//            list.add(
//                MineActionItem(
//                    R.string.mine_item_wallet,
//                    jump = MineActionJump.Url(
//                        h5PageConfigInteractor.getH5PageUrl(
//                            H5PageConfigInteractor.RECHARGE
//                        ), EventConstants.EVENT_MINE_WALLET_CLICK, Source.SOURCE_MINE, false
//                    )
//                )
//            )
//        }

        // 用户协议
        list.add(
            MineActionItem(
                R.string.mine_item_user_agreement,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.USER_AGREEMENT),
                    EventConstants.EVENT_USER_AGREEMENT_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )
        // 隐私设置
        list.add(
            MineActionItem(
                R.string.privacy_settings,
                jump = MineActionJump.GraphNav(R.id.privacySetting),
                showLabelRedDot = metaKV.appKV.iShowScreenshotSettingRedHot
            )
        )
        // 隐私协议
        list.add(
            MineActionItem(
                R.string.mine_item_privacy_agreement,
                jump = MineActionJump.Url(
                    h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.PRIVACY_AGREEMENT),
                    EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK,
                    Source.SOURCE_MINE,
                    useBabel = false,
                    useCompatParams = false
                )
            )
        )
        // 订阅
        if (PandoraToggle.isVipPlusOpen()) {
            list.add(
                MineActionItem(
                    R.string.mine_item_subscriptions,
                    jump = MineActionJump.Url(
                        h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.VIP_PLUS),
                        EventConstants.EVENT_SETTING_SUBSCRIBE_CLICK,
                        Source.SOURCE_MINE, false, false
                    ),
                )
            )
        }

        // 意见反馈
        list.add(
            MineActionItem(
                R.string.feedback,
                jump = MineActionJump.GraphNav(
                    R.id.feedback,
                    EventConstants.EVENT_SETTING_FEEDBACK_CLICK,
                    FeedbackFragmentArgs(
                        null,
                        SubmitNewFeedbackRequest.SOURCE_APP_NUMBER,
                        null,
                        needBackRole = false,
                        needBackGame = false,
                        fromGameId = null
                    ).asMavericksArgs()
                )
            )
        )

//        // 语言设置
//        list.add(
//            MineActionItem(
//                R.string.language_cap,
//                jump = MineActionJump.GraphNav(
//                    R.id.languageSetting,
//                    EventConstants.EVENT_SETTINGS_LANGUAGE_CLICK
//                )
//            )
//        )
        // 关于我们
        list.add(
            MineActionItem(
                R.string.about_us,
                jump = MineActionJump.GraphNav(R.id.about_us, EventConstants.EVENT_ABOUT_US_CLICK)
            )
        )


        list.add(
            MineActionItem(
                R.string.logout,
                jump = MineActionJump.LogoutActionItem(EventConstants.EVENT_LOGOUT_CLICK)
            )
        )
        return list
    }
}