package com.socialplay.gpark.function

import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.function.deeplink.DeeplinkAnalysisUtil
import com.socialplay.gpark.function.overseabridge.bridge.IFirebaseSdkBridge
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.router.MetaRouter.Control
import com.socialplay.gpark.ui.account.ConnectedAccountsFragmentArgs
import com.socialplay.gpark.ui.account.SignUpFragmentArgs
import org.koin.core.context.GlobalContext

class MetaRouterWrapper {
    object Account {
        fun signUp(fragment: Fragment, loginSource: String) {
            Control.navigate(
                fragment,
                R.id.sign_up,
                SignUpFragmentArgs(loginSource).toBundle()
            )
        }

        fun connectedAccounts(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            Control.navigate(
                fragment,
                R.id.connected_accounts,
                ConnectedAccountsFragmentArgs(source.source, gameId).toBundle()
            )
        }

        fun loginByPhone(fragment: Fragment, loginSource: String, phoneNumber: String?, onlyLogin: Boolean, successToMain: Boolean) {
            throw NotImplementedError("GPark not support loginByPhone")
        }
    }

    object Main {
        fun convertUri(uri: Uri): Uri {
            return uri
        }

        fun buildLocalJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)
            val encoded = com.socialplay.gpark.function.deeplink.BundleConverter.encode(bundle)
            val args = Uri.encode(encoded)
            val deepLink = Uri.parse(BuildConfig.SCHEME_URI + "://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return deepLink
        }

        fun buildLocalJumpUri(map: Map<String, Any?>, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)

            val builder = Uri.Builder()
                .scheme(BuildConfig.SCHEME_URI)
                .authority(BuildConfig.SCHEME_HOST)
                .path("/")
                .appendQueryParameter("action", action)
                .appendQueryParameter("dest", dest)

            map.entries.forEach {
                if (it.value != null) {
                    builder.appendQueryParameter(it.key, it.value.toString())
                }
            }
            return builder.build()
        }

        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)
            val encoded = com.socialplay.gpark.function.deeplink.BundleConverter.encode(bundle)
            val args = Uri.encode(encoded)
            val deepLink = Uri.parse("https://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(deepLink)
        }


        fun buildLocalJumpUri(action: String, data: Map<String, Any?>): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)

            val builder = Uri.Builder()
                .scheme(BuildConfig.SCHEME_URI)
                .authority(BuildConfig.SCHEME_HOST)
                .appendQueryParameter("action", action)

            data.forEach { (k, v) ->
                if (v != null) {
                    builder.appendQueryParameter(k, v.toString())
                }
            }

            return builder.build()
        }

        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(action: String, data: Map<String, Any?>): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)

            val builder = Uri.Builder()
                .scheme("https")
                .authority(BuildConfig.SCHEME_HOST)
                .appendQueryParameter("action", action)

            data.forEach { (k, v) ->
                if (v != null) {
                    builder.appendQueryParameter(k, v.toString())
                }
            }

            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(builder.build())
        }


        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(json: String, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)
            val args = Uri.encode(json)
            val deepLink = Uri.parse("https://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(deepLink)
        }

        /**
         * 不需要翻墙的deeplink
         */
        fun buildLocalJumpUri(json: String, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)
            val args = Uri.encode(json)
            val deepLink = Uri.parse(BuildConfig.SCHEME_URI + "://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return deepLink
        }
    }

    object Web {
        fun convertWebUri(url: String): Uri? = null

        fun goProtocolWeb(context: Context, url: String, title: String? = null) {
            MetaRouter.Web.navigate(context = context, fragment = null, title = title, url = url, showTitle = true)
        }
    }

    object RealName {
        fun openRealName(fragment: Fragment) {
//            navigate(fragment, R.id.real_name_party)
            throw NotImplementedError("GPark not support openRealName")
        }

        fun showRealName(fragment: Fragment) {
//            navigate(fragment, R.id.real_name_show)
            throw NotImplementedError("GPark not support showRealName")
        }

        private fun navigate(
            fragment: Fragment,
            graphDestId: Int,
            navData: Bundle? = null,
            navOptions: NavOptions? = null
        ) {
            navOptions?.shouldRestoreState()
            fragment.findNavController().navigate(graphDestId, navData, navOptions)
        }
    }

    object ExternalLink {
        fun jump(fragment: Fragment, profileLinkInfo: ProfileLinkInfo, source: String) {
            kotlin.runCatching {
                MetaRouter.Scheme.jumpScheme(
                    fragment,
                    Uri.parse(profileLinkInfo.url),
                    source
                )
            }
        }
    }
}