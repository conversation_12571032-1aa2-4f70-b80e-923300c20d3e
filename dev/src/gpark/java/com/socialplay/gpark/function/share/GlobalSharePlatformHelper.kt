package com.socialplay.gpark.function.share

import android.content.Context
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.util.InstallUtil

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalSharePlatformHelper {

    const val ENABLE_VIDEO_FEED_LANDING_PAGE = true

    fun checkInstallation(context: Context, platform: String): Boolean {
        return when (platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                InstallUtil.isInstalledYoutube(context)
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                InstallUtil.isInstalledTiktokFamilies(context)
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                InstallUtil.isInstalledSnapchat(context)
            }

            else -> {
                true
            }
        }
    }

    fun profile(isMe: <PERSON>olean, platforms: ArrayList<SharePlatform>) {
        if (isMe) {
            platforms.add(SharePlatform.friend())
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        } else {
            platforms.add(SharePlatform.friend())
        }
    }

    fun pgc(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.post())
        platforms.add(SharePlatform.friend())
        val hasImage = raw.hasImage
        if (hasImage) {
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
        }
        platforms.add(SharePlatform.longImage())
        if (hasImage) {
            platforms.add(SharePlatform.save())
        }
        platforms.add(SharePlatform.link())
        if (hasImage) {
            platforms.add(SharePlatform.system())
        }
    }

    fun pgcLongImage(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.tiktok())
        platforms.add(SharePlatform.snapchat())
        platforms.add(SharePlatform.save())
    }

    fun ugc(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.post())
        platforms.add(SharePlatform.friend())
        val hasImage = raw.hasImage
        if (hasImage) {
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
        }
        platforms.add(SharePlatform.longImage())
        if (hasImage) {
            platforms.add(SharePlatform.save())
        }
        platforms.add(SharePlatform.link())
        if (hasImage) {
            platforms.add(SharePlatform.system())
        }
    }

    fun ugcLongImage(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.tiktok())
        platforms.add(SharePlatform.snapchat())
        platforms.add(SharePlatform.save())
    }

    fun postDetail(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.friend())
        if (raw.hasMedia) {
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
        }
        platforms.add(SharePlatform.link())
        platforms.add(SharePlatform.system())
    }

    fun videoFeed(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.friend())
        platforms.add(SharePlatform.tiktok())
        platforms.add(SharePlatform.snapchat())
        platforms.add(SharePlatform.youtube())
        platforms.add(SharePlatform.save())
        if (ENABLE_VIDEO_FEED_LANDING_PAGE) {
            platforms.add(SharePlatform.link())
        }
        platforms.add(SharePlatform.system())
    }

    fun ocMoment(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        if (raw.hasVideo) {
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
            platforms.add(SharePlatform.youtube())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        } else if (raw.hasImage) {
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        }
    }

    fun screenshot(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        if (raw.hasImage) {
            platforms.add(SharePlatform.friend())
            platforms.add(SharePlatform.tiktok())
            platforms.add(SharePlatform.snapchat())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        }
    }

    fun assetDetail(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.friend())
        platforms.add(SharePlatform.post())
    }

    fun avatarShare(platforms: MutableList<SharePlatform>) {
        platforms.add(SharePlatform.tiktok())
        platforms.add(SharePlatform.snapchat())
        platforms.add(SharePlatform.save())
        platforms.add(SharePlatform.system())
    }
}