package com.socialplay.gpark.function.pay.way

import android.app.Activity
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.GooglePayResultData
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.PayParams
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.util.GsonUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 *
 */
class GooglePayPlatform : BasePayPlatform<PayParams>() {
    private val billingClientBridge: IGoogleSdkBridge.IBillingClientBridge by lazy { GlobalContext.get().get<IGoogleSdkBridge>().billingClientBridge }
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    init {
        billingClientBridge.setPurchasesUpdatedListener(
            onSuccess = {google ->
                analyticsTrack(
                    callbackCode = 1,
                    payParams = getAgentPayParams(),
                    googleResult = google
                )
                if (getAgentPayParams() == null) {
                    return@setPurchasesUpdatedListener
                }
                //支付成功
                getAgentPayParams()?.googleResultData = google
                getAgentPayParams()?.let {
                    onThirdPaySuccess(it)
                }
            },
            onCancel = {message->
                analyticsTrack(
                    callbackCode = 2,
                    payParams = getAgentPayParams(),
                    errorMessage = message
                )
                if (getAgentPayParams() == null) {
                    return@setPurchasesUpdatedListener
                }
                //支付弹窗关闭
                payFailed(message, IPayInteractor.FAIL_CANCEL)
            },
            onFail = { msg ,code ->
                analyticsTrack(
                    callbackCode = 3,
                    payParams = getAgentPayParams(),
                    errorCode = code,
                    errorMessage = msg
                )
                if (getAgentPayParams() == null) {
                    return@setPurchasesUpdatedListener
                }
                //拉起失败
                payFailed(msg, code)
            },
            onConsumeGood = {google->
                analyticsTrack(
                    callbackCode = 4,
                    payParams = getAgentPayParams(),
                    googleResult = google
                )
                Timber.d("onConsumeGood_ %s ",google)
                //未消耗订单重新消耗
                if (accountInteractor.accountLiveData.value?.uuid == google.uuid) {
                    var payParams: PayParams? = null
                    if (google.needUpdate == false) {
                        //只需要提交到后端，页面不需要做任何处理
                        payParams = PayParams()
                        updatePrams(google, payParams)
                    } else {
                        //未消耗的商品需要再次消耗
                        payParams = getAgentPayParams()
                        if (payParams == null) {
                            payParams = PayParams()
                        }
                        updatePrams(google, payParams)
                        setAgentPayParams(payParams)
                    }
                    onThirdPaySuccess(payParams)
                }
            },
            onPayDialogShow = {isScuccess->
                analyticsTrack(
                    callbackCode = 5,
                    payParams = getAgentPayParams(),
                    dialogShow = isScuccess
                )
                onPayDialogShow(isScuccess)
            }
        )
    }

    private fun analyticsTrack(
        callbackCode: Int,
        payParams: PayParams? = null,
        googleResult: GooglePayResultData? = null,
        errorCode: Int? = null,
        errorMessage: String? = null,
        dialogShow: Boolean? = null,
    ) {
        val map = mutableMapOf<String, Any?>()
        map["callback_code"] = callbackCode
        if (errorCode != null) {
            map["error_code"] = errorCode
        }
        if (errorMessage != null) {
            map["error_message"] = errorMessage
        }
        if (dialogShow != null) {
            map["dialog_show"] = if(dialogShow) 1 else 0
        }
        if(payParams!=null){
            map["pay_channel"] = payParams.payChannel
            map["pay_iap_scene"] = payParams.currentIapScene
            map["pay_scene_code"] = payParams.sceneCode
            map["pay_source"] = payParams.currentSource
            map["pay_gameid"] = payParams.currentGameId
            map["pay_product_type"] = payParams.productType
            map["pay_uuid"] = payParams.uuid
            map["pay_plan_id"] = payParams.planId
            map["pay_product_id"] = payParams.productId

            val payBody = payParams.currentRequestBody
            if(payBody!=null){
                map["pay_body_product_code"] = payBody.productCode
                map["pay_body_tunnel"] = payBody.payTunnel
                map["pay_body_channel"] = payBody.payChannel
                map["pay_body_scene_code"] = payBody.sceneCode
            }
            map["pay_order_id"] = payParams.currentOrderId

            val orderResult = payParams.takeOderResult
            if(orderResult!=null){
                map["pay_order_pay_code"] = orderResult.payCode
                map["pay_order_code"] = orderResult.orderCode
            }
        }
        if (googleResult != null) {
            map["google_order_id"] = googleResult.orderId
            map["google_token"] = googleResult.purchaseToken
            map["google_need_update"] = if (googleResult.needUpdate) 1 else 0
            map["google_product_type"] = googleResult.productType
            map["google_g_order_id"] = googleResult.googleOrderId
            map["google_uuid"] = googleResult.uuid
            if (googleResult.consumeAgain != null) {
                map["google_consume_again"] = if (googleResult.consumeAgain) 1 else 0
            }

            val jo =
                GsonUtil.gsonSafeParseCollection<HashMap<String, Any>>(googleResult.originalJson)
            if (jo != null) {
                map["google_jo_product_id"] = jo["productId"]?.toString()
                map["google_jo_purchase_state_str"] = jo["purchaseState"]?.toString()
                map["google_jo_purchase_token"] = jo["purchaseToken"]?.toString()
                map["google_jo_obf_account_id"] = jo["obfuscatedAccountId"]?.toString()
                map["google_jo_obf_profile_id"] = jo["obfuscatedProfileId"]?.toString()
                map["google_jo_quantity"] = jo["quantity"]?.toString()
                map["google_jo_acknowledged"] = jo["acknowledged"]?.toString()
                map["google_jo_order_id"] = jo["orderId"]?.toString()
            }
        }
        Analytics.track(
            EventConstants.EVENT_GPARK_PAY_GOOGLE_CALLBACK,
            map.filterValues { it != null }.mapValues { it.value!! }.toMap()
        )
    }

    private fun updatePrams(google: GooglePayResultData, payParams: PayParams){
        val jo = GsonUtil.gsonSafeParseCollection<HashMap<String, Any?>>(google.originalJson)
        val productId = jo?.getValue("productId") as String?
        if (google.productType == Product.SUBS) {
            payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_SUBS
        } else {
            payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_PG_COIN
        }
        payParams.payChannel = IAPConstants.PAY_CHANNEL_GOOGLE
        payParams.productType = google.productType
        payParams.googleResultData = google
        payParams.productId = productId ?: ""
    }
    override fun platformType(): Int = IAPConstants.PAY_CHANNEL_GOOGLE

    override fun startPay(activity: Activity, payParams: PayParams) {
        billingClientBridge.startPay(activity, payParams)
    }

    override suspend fun consumeGoods(type: String, purchaseToken: String) {
        if (type == Product.INAPP) {
            //一次性商品消耗
            billingClientBridge.consumeAsync(purchaseToken)
        } else {
            billingClientBridge.acknowledgePurchase(purchaseToken)
        }
    }
}