<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/custom_root">

    <activity
        android:id="@+id/qq_callback_activity"
        android:name="com.socialplay.gpark.function.auth.oauth.QQCallbackActivity" />

    <fragment
        android:id="@+id/login_by_phone"
        android:name="com.socialplay.gpark.ui.login.LoginByPhoneFragment"
        tools:layout="@layout/fragment_login_by_phone">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="phoneNumber"
            android:defaultValue=""
            app:nullable="true"
            app:argType="string" />
        <argument
            android:name="onlyLogin"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="successToMain"
            android:defaultValue="true"
            app:argType="boolean" />
    </fragment>

    <dialog
        android:id="@+id/rechargeAmountConfirmDialogFragment"
        android:name="com.socialplay.gpark.ui.recharge.RechargeAmountConfirmDialogFragment"
        tools:layout="@layout/dialog_party_recharge_amount_confirm">

        <argument
            android:name="price"
            app:argType="integer" />

    </dialog>
    <dialog
        android:id="@+id/rechargeChannelConfirmDialogFragment"
        android:name="com.socialplay.gpark.ui.recharge.RechargeChannelConfirmDialogFragment"
        tools:layout="@layout/fragment_recharge_channel_confirm">

    </dialog>
</navigation>
