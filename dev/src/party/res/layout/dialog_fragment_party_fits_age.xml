<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_15" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dp_320"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/bg_white_corner_30"
        android:paddingHorizontal="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_24">


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_24"
            android:gravity="center"
            android:text="@string/appropriate_age"
            android:textColor="@color/color_212121"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginRight="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:src="@drawable/ic_close_16_757575"
            app:layout_constraintEnd_toEndOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="parent"

            />

        <ScrollView
            android:id="@+id/scroll_layout"
            android:layout_width="match_parent"
            android:layout_height="400dp"
            android:layout_marginTop="16dp"
            android:fadeScrollbars="false"
            android:paddingHorizontal="@dimen/dp_20"
            android:scrollbarSize="3dp"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/bg_black_15_s100"
            android:scrollbars="vertical"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <TextView
                android:id="@+id/tv_message"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="vertical"
                android:text="@string/appropriate_age_desc"
                android:textColor="@color/color_212121"
                android:textSize="@dimen/sp_14" />
        </ScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>