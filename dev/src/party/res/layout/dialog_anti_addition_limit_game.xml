<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dp_280"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/party_bg_dialog"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="24dp"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="end|center_vertical"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="@dimen/dp_6"
            android:layout_marginRight="6dp"
            android:padding="@dimen/dp_10"
            android:scaleType="fitXY"
            android:src="@drawable/ic_close_16_757575"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">


            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:maxHeight="264dp"
                android:textColor="@color/color_757575"
                android:textSize="14sp"
                tools:text="善言结善缘，恶语伤人心善言结善缘，\n\n\n恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心" />
        </LinearLayout>


        <TextView
            android:id="@+id/btnRight"
            style="@style/Button.S16.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_24"
            android:gravity="center"
            android:text="@string/party_simple_dialog_confirm"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_content"
            app:layout_goneMarginStart="24dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="24dp"
            app:layout_constraintTop_toBottomOf="@id/btnRight" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>