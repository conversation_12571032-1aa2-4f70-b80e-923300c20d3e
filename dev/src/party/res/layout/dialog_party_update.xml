<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="@dimen/dp_280"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/party_bg_dialog"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="24dp"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="16dp"
            android:fadeScrollbars="false"
            android:scrollbars="vertical"
            app:layout_constraintHeight_max="264dp"
            app:layout_constraintTop_toBottomOf="@id/title">

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadeScrollbars="false"
                android:gravity="left"
                android:scrollbarSize="3dp"
                android:scrollbarStyle="outsideInset"
                android:scrollbars="vertical"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                tools:text="善言结善缘，恶语伤人心善言结善缘，\n\n\n恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心善言结善缘，恶语伤人心" />
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/btnLeft"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/placeholder_corner_360"
            android:backgroundTint="@color/button_primary_02"
            android:gravity="center"
            android:paddingVertical="10dp"
            android:text="@string/party_simple_dialog_cancel"
            android:textColor="@color/buttontext_02"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/btnRight"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_content" />

        <TextView
            android:id="@+id/btnRight"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_24"
            android:background="@drawable/bg_ffef30_round_30"
            android:gravity="center"
            android:text="@string/update_now"
            android:textColor="@color/buttontext_01"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnLeft"
            app:layout_constraintTop_toBottomOf="@id/ll_content"
            app:layout_goneMarginStart="24dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="24dp"
            app:layout_constraintTop_toBottomOf="@id/btnRight" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>