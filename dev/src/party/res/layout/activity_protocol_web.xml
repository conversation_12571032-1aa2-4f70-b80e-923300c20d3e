<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:paddingHorizontal="20dp">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        app:isDividerVisible="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title_text=""
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        app:layout_constraintTop_toBottomOf="@id/tbl"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/fl_web_container"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>