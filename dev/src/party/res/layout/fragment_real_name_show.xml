<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/place_holder_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.TitleBarLayout
            android:id="@+id/iv_close"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:back_icon="@drawable/icon_back_left_arrow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/place_holder_view"
            app:title_text="@string/party_real_name_show_title_bar_title" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_22"
            android:text="@string/party_real_name_show_tip"
            android:textColor="@color/color_666666"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_close" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@color/color_F6F6F6"
            app:layout_constraintTop_toBottomOf="@id/tv_tip" />

        <TextView
            android:id="@+id/tv_real_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/dp_18"
            android:paddingStart="@dimen/dp_16"
            android:text="@string/party_real_name_show_tv_real_name"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tip" />

        <TextView
            android:id="@+id/tv_real_name_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_88"
            android:gravity="end"
            android:paddingVertical="@dimen/dp_18"
            android:paddingEnd="@dimen/dp_16"
            android:text=""
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_real_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Eic" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@color/color_F6F6F6"
            app:layout_constraintTop_toBottomOf="@id/tv_real_name" />

        <TextView
            android:id="@+id/tv_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_1"
            android:paddingVertical="@dimen/dp_18"
            android:paddingStart="@dimen/dp_16"
            android:text="@string/party_real_name_show_tv_id_number"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_real_name" />

        <TextView
            android:id="@+id/tv_card_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_88"
            android:layout_marginTop="@dimen/dp_1"
            android:gravity="end"
            android:paddingVertical="@dimen/dp_18"
            android:paddingEnd="@dimen/dp_16"
            android:text=""
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_card"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="51102484285226682365" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@color/color_F6F6F6"
            app:layout_constraintTop_toBottomOf="@id/tv_card" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.appcompat.widget.LinearLayoutCompat>