<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tool:background="#000">

    <androidx.core.widget.NestedScrollView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_white_corner_32_bg"
        android:backgroundTint="@color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl"
            android:layout_width="327dp"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:src="@drawable/close_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_pay_reward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="21dp"
                android:text="@string/iap_title_recharge_party_coins"
                android:textColor="#757575"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/currencyCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginBottom="2dp"
                android:text="@string/iap_currency_code"
                android:textColor="#000000"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_pay_amount"
                app:layout_constraintEnd_toStartOf="@id/tv_pay_amount"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_pay_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textColor="#212121"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/currencyCode"
                app:layout_constraintTop_toBottomOf="@id/tv_pay_reward"
                tool:text="30.01" />

            <TextView
                android:id="@+id/tvPayChannel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="6dp"
                android:text="@string/iap_select_pay_method"
                android:textColor="#757575"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_pay_amount" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="6dp"
                android:maxHeight="108dp"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvPayChannel"
                tool:layout_height="100dp"
                tool:listitem="@layout/item_recharge_channel_adapter" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="14dp"
                android:background="@drawable/iap_shape_pay_confirm"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="#1A1A1A"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv"
                tool:text="立即支付¥30.01" />


            <TextView
                android:id="@+id/rechargePrivacyPre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:text="@string/iap_recharge_protocol_accept"
                android:textColor="#99000000"
                android:textSize="11sp"
                app:layout_constraintEnd_toStartOf="@id/rechargePrivacy"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_confirm" />

            <TextView
                android:id="@+id/rechargePrivacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/iap_recharge_protocol_clickable_text"
                android:textColor="#0083FA"
                android:textSize="11sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/rechargePrivacyPre"
                app:layout_constraintTop_toTopOf="@id/rechargePrivacyPre" />

            <Space
                android:layout_width="0dp"
                android:layout_height="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rechargePrivacy" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</FrameLayout>
