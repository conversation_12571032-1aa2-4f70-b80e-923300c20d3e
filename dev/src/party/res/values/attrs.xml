<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="MNPasswordEditText">
        <!--密码框的颜色-->
        <attr format="color" name="psw_border_color"/>
        <!--密码框选中的颜色-->
        <attr format="color" name="psw_border_selected_color"/>
        <!--密码文字的颜色,圆形密码颜色-->
        <attr format="color" name="psw_text_color"/>
        <!--密码框的圆角-->
        <attr format="dimension" name="psw_border_radius"/>
        <!--密码框的线的大小-->
        <attr format="dimension" name="psw_border_width"/>
        <!--单独框的宽度-->
        <attr format="dimension" name="psw_single_width"/>
        <!--密码框的每个间隔,只有样式2才起作用-->
        <attr format="dimension" name="psw_item_margin"/>
        <!--密码框背景色-->
        <attr format="color" name="psw_background_color"/>
        <!--密码框输入的模式:4.明文,3.文字,2.图片,1.圆形-->
        <attr format="enum" name="psw_mode">
            <!--圆形默认-->
            <enum name="Circle" value="1"/>
            <!--图片-->
            <enum name="Bitmap" value="2"/>
            <!--文本-->
            <enum name="Text" value="3"/>
            <!--原始-->
            <enum name="OriginalText" value="4"/>
        </attr>
        <!--密码框样式: 1.连在一起 2.分开单独显示  3.下划线形式-->
        <attr format="enum" name="psw_style">
            <!--连在一起-->
            <enum name="StyleDefault" value="1"/>
            <!--单独-->
            <enum name="StyleOneself" value="2"/>
            <!--下划线形式-->
            <enum name="StyleUnderLine" value="3"/>
        </attr>
        <!--密码文字遮盖-->
        <attr format="string" name="psw_cover_text"/>
        <!--密码图片遮盖-->
        <attr format="reference" name="psw_cover_bitmap_id"/>
        <!--密码圆形遮盖颜色-->
        <attr format="color" name="psw_cover_circle_color"/>
        <!--密码圆形遮盖半径-->
        <attr format="dimension" name="psw_cover_circle_radius"/>
        <!--密码图片遮盖长宽-->
        <attr format="dimension" name="psw_cover_bitmap_width"/>
        <!--是否显示光标-->
        <attr format="boolean" name="psw_show_cursor"/>
        <!--光标颜色-->
        <attr format="color" name="psw_cursor_color"/>
        <!--光标高度-->
        <attr format="dimension" name="psw_cursor_height"/>
        <!--光标宽度-->
        <attr format="dimension" name="psw_cursor_width"/>
        <!--光标圆角-->
        <attr format="dimension" name="psw_cursor_corner_radius"/>

    </declare-styleable>
</resources>