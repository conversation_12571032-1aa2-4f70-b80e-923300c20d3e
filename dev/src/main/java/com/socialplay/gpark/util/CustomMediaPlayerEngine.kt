package com.socialplay.gpark.util

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.view.View
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.engine.MediaPlayerEngine
import com.luck.picture.lib.widget.MediaPlayerView
import java.io.IOException

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/09/26
 *     desc   :
 * </pre>
 */
class CustomMediaPlayerEngine : MediaPlayerEngine() {

    override fun onCreateVideoPlayer(context: Context): View {
        return CustomMediaPlayerView(context)
    }
}

class CustomMediaPlayerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : MediaPlayerView(context, attrs, defStyleAttr) {

    override fun start(path: String?) {
        if (path.isNullOrBlank()) return
        try {
            startHelper(path)
        } catch (e: IllegalStateException) {
            mediaPlayer.reset()
            startHelper(path)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private fun startHelper(path: String) {
        if (PictureMimeType.isContent(path)) {
            mediaPlayer.setDataSource(context, Uri.parse(path))
        } else {
            mediaPlayer.setDataSource(path)
        }
        mediaPlayer.prepareAsync()
    }
}