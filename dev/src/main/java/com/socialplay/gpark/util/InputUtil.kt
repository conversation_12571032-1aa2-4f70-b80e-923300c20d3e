package com.socialplay.gpark.util

import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/22
 * desc   :
 * </pre>
 */


object InputUtil {

    fun showSoftBoard(view: View?) {
        view?.requestFocus()
        getInputMethodManager(view?.context)?.showSoftInput(view, 0)
    }

    fun hideKeyboard(view: View?) {
        view?.windowToken?.let {
            getInputMethodManager(view.context)?.hideSoftInputFromWindow(it, 0)
        }
    }

    private fun getInputMethodManager(context: Context?): InputMethodManager? {
        return context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager?
    }
}