package com.socialplay.gpark.util

import android.app.Activity
import android.content.Intent
import com.socialplay.gpark.R
import kotlin.system.exitProcess

object QuitAppUtil {
    private var lastPressedTime = 0L

    fun checkClickBackPressed(
        activity: Activity,
        finishCallback: (() -> Unit)? = null
    ): Boolean {
        if (System.currentTimeMillis() - lastPressedTime < 2000) {
            finishCallback?.invoke()
            gotoHomeLauncher(activity)
            activity.finish()
            return true
        }
        lastPressedTime = System.currentTimeMillis()
        ToastUtil.showShort(activity, R.string.click_exit_again)
        return false
    }

    private fun gotoHomeLauncher(activity: Activity) {
        val intent = Intent()
        intent.action = Intent.ACTION_MAIN
        intent.addCategory(Intent.CATEGORY_HOME)
        try {
            activity.startActivity(intent)
        } catch (e: Throwable) {
            //华为Android7.0等机型跳转到超级省电桌面报异常，怀疑没有exported属性
            e.printStackTrace()
            exitProcess(0)
        }
    }
}