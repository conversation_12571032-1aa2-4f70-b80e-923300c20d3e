package com.socialplay.gpark.util

import android.content.Context
import androidx.recyclerview.widget.LinearSmoothScroller

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/02/03
 *     desc   : 滚动元素到顶部（最左侧）
 * </pre>
 */
open class TopSmoothScroller(context: Context) : LinearSmoothScroller(context) {

    private var offset = 0
    private var listener: Listener? = null

    override fun getHorizontalSnapPreference(): Int {
        return SNAP_TO_START // 对齐最左侧
    }

    override fun getVerticalSnapPreference(): Int {
        return SNAP_TO_START // 对齐顶部
    }

    override fun calculateDtToFit(viewStart: Int, viewEnd: Int, boxStart: Int, boxEnd: Int, snapPreference: Int): Int {
        return boxStart - viewStart + offset
    }

    fun setTargetPositionAddOffset(targetPosition: Int, offset: Int): TopSmoothScroller {
        this.targetPosition = targetPosition
        this.offset = offset
        return this
    }

    override fun onStart() {
        super.onStart()
        listener?.onStart()
    }

    override fun onStop() {
        super.onStop()
        listener?.onStop()
        listener = null
    }

    fun setListener(listener: Listener?): TopSmoothScroller {
        this.listener = listener
        return this
    }

    interface Listener {
        fun onStart() {}
        fun onStop() {}
    }

}