package com.socialplay.gpark.util

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import androidx.core.animation.addListener
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.socialplay.gpark.R
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.aibot.AiBotConversationItem
import com.socialplay.gpark.ui.post.v2.PublishPostFragment
import timber.log.Timber
import java.util.Locale

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/07/02
 *     desc   :
 *
 */
class DeleteDragView : LinearLayout {

    private var deleteTitleIndex = 0
    private var dragView: View? = null
    private var llDelArea:View?=null
    private var relativeY = 0
    private var canDrag = false
    private var canDrop = false
    private val rect = Rect()
    private var tvDeleteTitle: TextView? = null
    private var lavDelete: LottieAnimationView? = null
    private var onDragListener: DragListener? = null


    private var deleteTitle = ""
    private var deleteFullTitle = ""
    private var deleteAnimator: ValueAnimator? = null
    private var direction = DIRECTION_BACKWARD
    companion object{
        private const val DIRECTION_FORWARD = 1
        private const val DIRECTION_BACKWARD = 2

    }
    private val isForward get() = direction == DIRECTION_FORWARD
    constructor(
        context: Context
    ) : super(context) {
        initView(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(attrs)
    }
    fun initView(attrs: AttributeSet?){
        val view = LayoutInflater.from(context).inflate(R.layout.view_drag_delete_view, this)
        deleteTitle = context.getString(R.string.delete_cap)
        deleteFullTitle = context.getString(R.string.release_to_delete)
        lavDelete = view.findViewById(R.id.lav_delete)
        llDelArea  = view.findViewById(R.id.ll_del_area)
        tvDeleteTitle = view.findViewById(R.id.tv_delete_title)
        direction = if (MetaLanguages.getAppCurrentLanguage(context).dynamicImpl.postDeleteForward()) {
            deleteTitleIndex = deleteTitle.length
            DIRECTION_FORWARD
        } else {
            deleteTitleIndex = deleteFullTitle.length - deleteTitle.length
            DIRECTION_BACKWARD
        }
    }

    private val onTouchListener = object : View.OnTouchListener {
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_MOVE -> {
                    if (canDrag) {
                        Timber.d("onTouchListener %s  %s", ((dragView?.y ?: 0.0f) + (dragView?.height ?: 0) ), relativeY)
                        if ((dragView?.y ?: 0.0f) + (dragView?.height ?: 0) > relativeY && relativeY>0) {
                            if (!canDrop) {
                                canDrop = true
                                lavDelete?.progress = 1F
                                animateDeleteTitle(true)
                            }
                        } else {
                            if (canDrop) {
                                canDrop = false
                                lavDelete?.progress = 0.0f
                                animateDeleteTitle(false)
                            }
                        }
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (canDrag) {
                        val result = if (canDrop) {
                            onDragListener?.onRemoveItem()
                            true
                        } else {
                            onDragListener?.onActionUp()
                            false
                        }
                        canDrag = false
                        canDrop = false
                        lavDelete?.progress = 0F
                        animateDeleteTitle(false)
                        llDelArea?.animate()?.translationY(llDelArea!!.height.toFloat())
                        return result
                    } else {
                        removeView()
                    }
                }
            }
            return false
        }
    }
    fun removeView() {
        canDrag = false
        canDrop = false
        this.dragView = null
        lavDelete?.progress = 0F
        animateDeleteTitle(false)
        llDelArea?.animate()?.translationY(llDelArea!!.height.toFloat())
    }
    fun setOnListener(view: View, dragListener: DragListener) {
        this.onDragListener = dragListener
        view.setOnTouchListener(onTouchListener)
    }
    fun unDestroyView(view: View,) {
        onDragListener = null
        dragView = null
        view.setOnTouchListener(null)
    }
    fun getDragView(): View? {
        return dragView
    }
    fun setDragView(itemView: View) {
        dragView = itemView
        itemView.getGlobalVisibleRect(rect)
        if (relativeY <= 0) {
            val srcTop = rect.top
            this.getGlobalVisibleRect(rect)
            val targetTop = rect.top - (llDelArea?.translationY ?: 0).toInt()
            this.relativeY = targetTop - srcTop + itemView.y.toInt()
        }
        canDrag = true
        llDelArea?.animate()?.translationY(0.0f)
        Timber.d("onTouch %s %s %s  %s",relativeY, canDrag, (dragView?.y ?: 0.0f) + (dragView?.height ?: 0), rect.height() == itemView.height)

    }
     fun animateDeleteTitle(full: Boolean) {
        if ((full && tvDeleteTitle?.text == deleteFullTitle) || (!full && tvDeleteTitle?.text == deleteTitle)) {
            return
        }
         deleteAnimator?.cancel()
         deleteAnimator = if (full) {
             ValueAnimator.ofInt(deleteTitleIndex, if (isForward) {
                 deleteFullTitle.length
             } else {
                 0
             })
         } else {
             ValueAnimator.ofInt(deleteTitleIndex, if (isForward) {
                 deleteTitle.length
             } else {
                 deleteFullTitle.length - deleteTitle.length
             })
         }.apply {
             duration = 100
             addUpdateListener {
                 deleteTitleIndex = it.animatedValue as Int
                 tvDeleteTitle?.text = deleteFullTitle.substring(
                     if (isForward) 0 else deleteTitleIndex,
                     if (isForward) deleteTitleIndex else deleteFullTitle.length
                 )
             }
             addListener(onEnd = {
                 deleteAnimator = null
                 if (!full) {
                     tvDeleteTitle?.text = deleteTitle
                 }
             })
             start()
         }
    }
    interface DragListener{
        fun onRemoveItem()
        fun onActionUp()
    }
}