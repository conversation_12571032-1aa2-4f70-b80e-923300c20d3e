package com.socialplay.gpark.util.extension

import android.app.Activity
import android.content.res.Configuration
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.annotation.ColorRes
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.DisposableHandle
import com.socialplay.gpark.R


fun Activity.keepScreenOn(screenOn: Boolean = true) {
    window.keepScreenOn(screenOn)
}

fun Window.keepScreenOn(screenOn: Boolean = true) {
    if (screenOn) {
        addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    } else {
        clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
}

fun LifecycleOwner.runWhenDestroyed(block: () -> Unit) {
    lifecycle.runWhenDestroyed(block)
}

fun Lifecycle.runWhenDestroyed(block: () -> Unit) {
    runWhenStatus(Lifecycle.State.DESTROYED, true) { block() }
}

fun Lifecycle.runWhenStatus(status: Lifecycle.State, once: Boolean, block: (DisposableHandle) -> Unit) {
    if (currentState == Lifecycle.State.DESTROYED) {
        return
    }
    val observer = LifecycleDisposableHandle(this, status, once, block)
    addObserver(observer)
}

private class LifecycleDisposableHandle(
    val lifecycle: Lifecycle,
    val status: Lifecycle.State,
    val once: Boolean,
    val block: (DisposableHandle) -> Unit
) : DisposableHandle, LifecycleEventObserver {

    override fun dispose() {
        lifecycle.removeObserver(this)
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (source.lifecycle.currentState == status) {
            block.invoke(this)
            if (once) {
                dispose()
            }
        }
        if (source.lifecycle.currentState == Lifecycle.State.DESTROYED) {
            dispose()
        }
    }
}

fun Activity.setNavColor(bgColor: Int, dividerColor: Int) {
    window?.let {
        it.navColor = bgColor
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            it.navigationBarDividerColor = dividerColor
        }
    }
}

fun Activity.setNavColor(color: Int) {
    setNavColor(color, color)
}

fun Activity.setNavColorByRes(@ColorRes bgColorRes: Int, @ColorRes dividerColorRes: Int) {
    setNavColor(getColorByRes(bgColorRes), getColorByRes(dividerColorRes))
}

fun Activity.setNavColorByRes(@ColorRes colorRes: Int) {
    val color = getColorByRes(colorRes)
    setNavColor(color)
}


typealias ConfigurationChangeCallback = (newConfig: Configuration?) -> Unit
fun Activity.addConfigurationObserver(block: ConfigurationChangeCallback){
    var view = this.findViewById<View>(R.id.v_configuration_detect_holder)
    if(view == null){
        view = object: View(this){
            override fun onConfigurationChanged(newConfig: Configuration?) {
                super.onConfigurationChanged(newConfig)
                val list = view.getTag(R.id.tag_configuration_detector_callback) as? MutableList<ConfigurationChangeCallback>
                list?.forEach { it(newConfig) }
            }

            override fun onDetachedFromWindow() {
                super.onDetachedFromWindow()
                view.setTag(R.id.tag_configuration_detector_callback, null)
            }
        }.apply {
            id = R.id.v_configuration_detect_holder
        }
        this.addContentView(view, ViewGroup.LayoutParams(0, 0))
    }

    var list = view.getTag(R.id.tag_configuration_detector_callback) as? MutableList<ConfigurationChangeCallback>
    if(list == null){
        list = mutableListOf()
        view.setTag(R.id.tag_configuration_detector_callback, list)
    }
    list.add(block)
}

fun Activity.removeConfigurationObserver(block: ConfigurationChangeCallback){
    val view = this.findViewById<View>(R.id.v_configuration_detect_holder)
    val list = view?.getTag(R.id.tag_configuration_detector_callback) as? MutableList<ConfigurationChangeCallback>
    list?.remove(block)
}