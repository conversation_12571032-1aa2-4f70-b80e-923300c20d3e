package com.socialplay.gpark.util

class LruMap<K, V>(
    private val maxSize: Int
) : LinkedHashMap<K, V>(maxSize + 1, 1.0f, true) {

    override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>?): Bo<PERSON>an {
        return size > maxSize
    }
}

class LruSet<E>(private val maxSize: Int) : MutableSet<E>, AbstractSet<E>() {

    @Transient
    private val map = LruMap<E, Boolean>(maxSize)

    override fun add(element: E) = map.put(element, true) == null

    override val size: Int
        get() = map.size

    override fun addAll(elements: Collection<E>) = false

    override fun clear() {
        map.clear()
    }

    override fun contains(element: E) = map.containsKey(element)

    override fun containsAll(elements: Collection<E>) = false

    override fun isEmpty() = map.isEmpty()

    override fun iterator() = map.keys.iterator()

    override fun remove(element: E) = map.remove(element) == null

    override fun removeAll(elements: Collection<E>) = false

    override fun retainAll(elements: Collection<E>) = false
}