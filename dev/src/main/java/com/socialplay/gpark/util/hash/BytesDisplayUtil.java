package com.socialplay.gpark.util.hash;

/**
 * 2024/8/14
 */
public class BytesDisplayUtil {
    private static final char[] hexDigitsUpper = "0123456789ABCDEF".toCharArray();
    private static final char[] hexDigitsLower = "0123456789abcdef".toCharArray();

    public BytesDisplayUtil() {
    }

    public static String hex(byte[] bytes, boolean lower) {
        if (bytes == null) {
            return null;
        } else {
            char[] hexDigits = lower ? hexDigitsLower : hexDigitsUpper;
            char[] chars = new char[bytes.length * 2];

            for(int i = 0; i < bytes.length; ++i) {
                int index = i * 2;
                chars[index] = hexDigits[bytes[i] >> 4 & 15];
                chars[index + 1] = hexDigits[bytes[i] & 15];
            }

            return new String(chars);
        }
    }
}
