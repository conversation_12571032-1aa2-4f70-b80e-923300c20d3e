package com.socialplay.gpark.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import androidx.core.graphics.ColorUtils
import androidx.core.graphics.get
import androidx.palette.graphics.Palette
import com.socialplay.gpark.ui.aibot.AiBotConversationViewModel.Companion.LOW_FREE_MEMORY_LINE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/8/10
 * Desc:
 */
object PaletteUtil {

    // 选取色块的最小亮度
    private const val LIMIT_LUMINANCE = 0.78F

    // 取色失败后的默认颜色
    const val DEFAULT_RGB = Color.GRAY

    // 最终调制的亮度
    private const val FINAL_LUMINANCE = 0.78F


    /**
     * 公用取色方法（柔和深色）
     * 如果是给图的底部加渐变，建议传图的下半部分
     * @param bitmap 图片的bitmap
     */
    suspend fun getCommonColorSampling(
        bitmap: Bitmap,
        defaultColor: Int = DEFAULT_RGB,
        luminance: Float = 0.2f,
        saturation: Float = 0.3f
    ): Int {
        val samplingColor = getCommonColorSamplingBaseImpl(bitmap) ?: defaultColor
        return blendColor(samplingColor, luminance, saturation)
    }

    private fun randomLuminance(): Float = (Math.random() * 13.0 + 15.0).toInt() / 100.0f

    /**
     * 公用取色方法（柔和深色）
     * 如果是给图的底部加渐变，建议传图的下半部分
     * @param bitmap 图片的bitmap
     */
    suspend fun getCommonColorSamplingBaseImpl(bitmap: Bitmap): Int? {
        return withContext(Dispatchers.IO) {
            return@withContext runCatching {
                val palette = Palette.from(bitmap).generate()
                // 备选色块颜色用darkMuted
                var defaultDarkMuted = palette.lightVibrantSwatch
                // 所有样本种类
                val swatches = palette.swatches
                if (swatches.isEmpty()) {
                    throw IllegalArgumentException("no swatches")
                }
                // 备选色块
                var swatch = defaultDarkMuted
                var maxSwatchPopulation = 0
                swatches.forEach {
                    val luminance = ColorUtils.calculateLuminance(it.rgb)
                    // 选取占比最多的色块
                    if (it.population > maxSwatchPopulation) {
                        maxSwatchPopulation = it.population
                        swatch = it
                    }
                }
                if (maxSwatchPopulation > (defaultDarkMuted?.population ?: 0)) {
                    defaultDarkMuted = swatch
                }
                defaultDarkMuted?.rgb
            }.getOrElse {
                Timber.e("checkcheck_palette, sampling error due to: ${it}")
                null
            }
        }
    }

    suspend fun calculateAverageColor(bitmap: Bitmap): Int? {
        return runCatching {
            var red = 0L
            var green = 0L
            var blue = 0L
            val pixelCount = bitmap.width * bitmap.height

            for (x in 0 until bitmap.width) {
                for (y in 0 until bitmap.height) {
                    val pixel = bitmap[x, y]
                    red += Color.red(pixel)
                    green += Color.green(pixel)
                    blue += Color.blue(pixel)
                }
            }

            Color.rgb(
                (red / pixelCount).toInt(),
                (green / pixelCount).toInt(),
                (blue / pixelCount).toInt()
            )
        }.getOrElse {
            null
        }
    }

    /**
     * 调节亮度和饱和度
     * @return RGB
     * luminance 亮度
     * saturation 饱和度
     */
    fun blendColor(originRGB: Int, luminance: Float = 0.2f, saturation: Float = 0.3f): Int {
        runCatching {
            val floatArray = FloatArray(3)
            Color.colorToHSV(originRGB, floatArray)
            floatArray[1] = saturation
            floatArray[2] = luminance
            return Color.HSVToColor(floatArray)
        }.getOrElse {
            Timber.e("checkcheck_palette, blendColor error due to: ${it}")
            return originRGB
        }
    }

    /**
     * 调节亮度
     * @return RGB
     * luminance 亮度
     * saturation 饱和度
     */
    fun blendLuminance(originRGB: Int, luminance: Float = 0.2f): Int {
        runCatching {
            val floatArray = FloatArray(3)
            Color.colorToHSV(originRGB, floatArray)
            floatArray[2] = luminance
            return Color.HSVToColor(floatArray)
        }.getOrElse {
            Timber.e("checkcheck_palette, blendLuminance error due to: ${it}")
            return originRGB
        }
    }

    /**
     * 获取颜色的亮度,亮度范围[0,1]
     */
    fun getLuminance(originRGB: Int): Float? {
        runCatching {
            val floatArray = FloatArray(3)
            Color.colorToHSV(originRGB, floatArray)
            return floatArray[2]
        }.getOrElse {
            Timber.e("checkcheck_palette, getLuminance error due to: ${it}")
            return null
        }
    }

    /**
     * 给color添加透明度
     * @param alpha 透明度 0f～1f
     * @param baseColor 基本颜色
     * @return
     */
    fun getColorWithAlpha(alpha: Float, baseColor: Int): Int {
        val a = (alpha * 255).toInt() and 0xff
        val r = (baseColor shr 16) and 0xff
        val g = (baseColor shr 8) and 0xff
        val b = baseColor and 0xff
        return a shl 24 or (r shl 16) or (g shl 8) or b
    }

    /* * 减少缓冲区
     */
    fun lowMemory(context: Context): Boolean {
        val totalMemory = DeviceUtil.getAppTotalMemory(context)
        val usedMemory = DeviceUtil.getAppUsedMemory(context)
        val freeMemory = totalMemory - usedMemory
        Timber.d("lowMemory, totalMemory:${totalMemory}, usedMemory:${usedMemory}, freeMemory:${freeMemory}")
        return freeMemory <= LOW_FREE_MEMORY_LINE
    }

}