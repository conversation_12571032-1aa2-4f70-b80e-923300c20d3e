package com.socialplay.gpark.util.extension

import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.View.OnKeyListener
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.res.ResourcesCompat
import androidx.core.widget.TextViewCompat
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.LifecycleOwner

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/05/19
 *     desc   :
 * </pre>
 */

fun TextView.clearCompoundDrawables() {
    clearCompoundDrawables(left = true, top = true, right = true, bottom = true)
}

/**
 * 位置参数访问不到,这里写死位置
 * [LEFT](android.widget.TextView.Drawables#LEFT)
 * [TOP](android.widget.TextView.Drawables#TOP)
 * [RIGHT](android.widget.TextView.Drawables#RIGHT)
 * [BOTTOM](android.widget.TextView.Drawables#BOTTOM)
 */
fun TextView.clearCompoundDrawables(
    left: Boolean = false,
    top: Boolean = false,
    right: Boolean = false,
    bottom: Boolean = false,
) {
    compoundDrawables(
        if (!left) compoundDrawables[0] else null,
        if (!top) compoundDrawables[1] else null,
        if (!right) compoundDrawables[2] else null,
        if (!bottom) compoundDrawables[3] else null
    )
}

fun TextView.compoundDrawables(
    @DrawableRes left: Int? = null,
    @DrawableRes top: Int? = null,
    @DrawableRes right: Int? = null,
    @DrawableRes bottom: Int? = null,
    padding: Int? = null,
) {
    compoundDrawables(
        left?.let { ResourcesCompat.getDrawable(resources, it, null) },
        top?.let { ResourcesCompat.getDrawable(resources, it, null) },
        right?.let { ResourcesCompat.getDrawable(resources, it, null) },
        bottom?.let { ResourcesCompat.getDrawable(resources, it, null) },
        padding
    )
}

fun TextView.compoundDrawables(
    left: Drawable? = null,
    top: Drawable? = null,
    right: Drawable? = null,
    bottom: Drawable? = null,
    padding: Int? = null,
) {
    padding?.also { compoundDrawablePadding = it }
    left?.setBounds(0, 0, left.intrinsicWidth, left.intrinsicHeight)
    top?.setBounds(0, 0, top.intrinsicWidth, top.intrinsicHeight)
    right?.setBounds(0, 0, right.intrinsicWidth, right.intrinsicHeight)
    bottom?.setBounds(0, 0, bottom.intrinsicWidth, bottom.intrinsicHeight)
    setCompoundDrawables(left, top, right, bottom)
}

fun TextView.setTextColorByRes(@ColorRes resId: Int) {
    setTextColor(getColorByRes(resId))
}

fun TextView.setTextColorByStr(colorStr: String) {
    setTextColor(getCslByColorStr(colorStr))
}

fun TextView.setTextWithArgs(@StringRes resId: Int, vararg args: Any?) {
    text = context.getString(resId, *args)
}

fun TextView.setHintWithArgs(@StringRes resId: Int, vararg args: Any?) {
    hint = context.getString(resId, *args)
}

fun TextView.doAfterTextChanged(owner: LifecycleOwner, action: (text: Editable?) -> Unit) {
    var textWatcher: TextWatcher? = null
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            textWatcher = doAfterTextChanged(action)
        }, unregister = {
            if (textWatcher != null) {
                removeTextChangedListener(textWatcher)
                textWatcher = null
            }
        }
    )
}
fun View.setOnKeyListener(owner: LifecycleOwner, onKeyListener: OnKeyListener) {
    var keyListener: OnKeyListener? = null
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            keyListener = onKeyListener
            setOnKeyListener(onKeyListener)
        }, unregister = {
            if (keyListener != null) {
                setOnKeyListener(null)
                keyListener = null
            }
        })
}

fun TextView.addTextChangedListener(
    owner: LifecycleOwner,
    beforeTextChanged: (
        text: CharSequence?,
        start: Int,
        count: Int,
        after: Int
    ) -> Unit = { _, _, _, _ -> },
    onTextChanged: (
        text: CharSequence?,
        start: Int,
        before: Int,
        count: Int
    ) -> Unit = { _, _, _, _ -> },
    afterTextChanged: (text: Editable?) -> Unit = {}
) {
    var textWatcher: TextWatcher? = null
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            textWatcher = addTextChangedListener(beforeTextChanged, onTextChanged, afterTextChanged)
        }, unregister = {
            if (textWatcher != null) {
                removeTextChangedListener(textWatcher)
                textWatcher = null
            }
        }
    )
}

fun TextView.compoundDrawableTintListByRes(resId: Int) {
    TextViewCompat.setCompoundDrawableTintList(this, getCslByRes(resId))
}

fun TextView.compoundDrawableTintListByColor(color: Int) {
    TextViewCompat.setCompoundDrawableTintList(this, getCslByColor(color))
}

@Throws
fun TextView.compoundDrawableTintListByColorStr(colorStr: String) {
    TextViewCompat.setCompoundDrawableTintList(this, getCslByColorStr(colorStr))
}

fun TextView.setTextResFirst(resId: Int?, txt: String?) {
    if (resId != null && resId != 0) {
        setText(resId)
    } else {
        text = txt
    }
}