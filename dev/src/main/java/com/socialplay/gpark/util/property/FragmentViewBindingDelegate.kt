package com.socialplay.gpark.util.property

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType
import java.util.concurrent.ConcurrentHashMap
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty


private val sBindingMethodByClass = ConcurrentHashMap<Class<*>, Method>()

fun getBindMethodFrom(javaClass: Class<*>): Method =
    sBindingMethodByClass.getOrPut(javaClass) {
        val actualTypeOfThis = getSuperclassParameterizedType(javaClass)
        val viewBindingClass = actualTypeOfThis.actualTypeArguments[0] as Class<ViewBinding>
        getBindMethod(viewBindingClass)
    }

fun <T : ViewBinding> getBindMethodFrom(javaClass: Class<*>, viewBindingClass: Class<T>): Method =
    sBindingMethodByClass.getOrPut(javaClass) {
        getBindMethod(viewBindingClass)
    }

private fun <T : ViewBinding> getBindMethod(viewBindingClass: Class<T>): Method =
    viewBindingClass.getDeclaredMethod("bind", View::class.java)
        ?: error("The binder class ${viewBindingClass.canonicalName} should have a method bind(View)")

private fun getSuperclassParameterizedType(klass: Class<*>): ParameterizedType {
    val genericSuperclass = klass.genericSuperclass
    return (genericSuperclass as? ParameterizedType)
        ?: getSuperclassParameterizedType(genericSuperclass as Class<*>)
}

class ActivityViewBindingDelegate<T : ViewBinding>(
    private val bindMethod: Method,
) : ReadOnlyProperty<Activity, T> {

    private var binding: T? = null
    override fun getValue(thisRef: Activity, property: KProperty<*>): T {
        binding?.let { return it }

        binding = bindMethod.invoke(null, getView(thisRef)) as T
        return binding!!
    }

    private fun getView(thisRef: Activity): View {
        return thisRef.findViewById<ViewGroup>(android.R.id.content).getChildAt(0)
    }
}

class FragmentViewBindingDelegate<T : ViewBinding>(
    private val bindMethod: Method,
    private val fragment: Fragment
) : ReadOnlyProperty<Fragment, T> {
    private val clearBindingHandler by lazy(LazyThreadSafetyMode.NONE) { Handler(Looper.getMainLooper()) }
    private var binding: T? = null


    init {
        fragment.lifecycleScope.launch {
            fragment.viewLifecycleOwnerLiveData.observe(fragment) { viewLifecycleOwner ->
                viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
                    override fun onDestroy(owner: LifecycleOwner) {
                        // Lifecycle listeners are called before onDestroyView in a Fragment.
                        // However, we want views to be able to use bindings in onDestroyView
                        // to do cleanup so we clear the reference one frame later.
                        clearBindingHandler.post {
                            if (fragment.view == null) {
                                binding = null
                            }
                        }
                    }
                })
            }
        }
    }

    override fun getValue(thisRef: Fragment, property: KProperty<*>): T {
        // onCreateView may be called between onDestroyView and next Main thread cycle.
        // In this case [binding] refers to the previous fragment view. Check that binding's root view matches current fragment view
        val lifecycle = fragment.viewLifecycleOwnerLiveData.value?.lifecycle
        if (binding != null && binding?.root !== thisRef.view && lifecycle != null && lifecycle.currentState != Lifecycle.State.DESTROYED) {
            binding = null
        }
        binding?.let { return it }

        if (lifecycle == null || !lifecycle.currentState.isAtLeast(Lifecycle.State.INITIALIZED)) {
            error("Cannot access view bindings. View lifecycle is ${lifecycle?.currentState}, mView is null?:${fragment.view == null}!")
        }

        @Suppress("UNCHECKED_CAST")
        binding = bindMethod.invoke(null, thisRef.requireView()) as T
        return binding!!
    }
}

/**
 * Create bindings for a view similar to bindView.
 *
 * To use, just call
 * private val binding: FHomeWorkoutDetailsBinding by viewBinding()
 * with your binding class and access it as you normally would.
 */
fun <T : ViewBinding> Activity.viewBinding() =
    ActivityViewBindingDelegate<T>(getBindMethodFrom(javaClass))

inline fun <reified T : ViewBinding> Fragment.viewBinding() =
    FragmentViewBindingDelegate<T>(getBindMethodFrom(javaClass, T::class.java), this)