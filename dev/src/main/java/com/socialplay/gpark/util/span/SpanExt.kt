package com.socialplay.gpark.util.span

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/03/07
 *     desc   :
 */

fun ClickableSpanWithoutUnderline(color: Int, clickListener: () -> Unit): ClickableSpan {
    return object : ClickableSpan() {
        override fun onClick(widget: View) {
            clickListener.invoke()
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.color = color
            ds.isUnderlineText = false
        }
    }
}
