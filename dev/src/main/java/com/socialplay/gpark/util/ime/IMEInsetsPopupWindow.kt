package com.socialplay.gpark.util.ime

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.*
import android.view.animation.DecelerateInterpolator
import android.widget.PopupWindow
import android.widget.Space

/**
 * 显示一个高度为 match_parent,宽度为 0 的 PopupWindow
 * 然后再 PopupWindow 中设置 OnGlobalLayoutListener,用于获取键盘高度
 */
class IMEInsetsPopupWindow(
    private val rootView: View,
    private val keyboardListener: KeyboardListener
) {
    private val popupContentView: View = Space(rootView.context)
    private var contentHeight = 0
    private var isKeyboardShow = false
    private var keyboardHeight = 0
    private var mPopupWindow: PopupWindow? = null
    private val keyboardAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        interpolator = DecelerateInterpolator()
        duration = 200
        addUpdateListener {
            val value = it.animatedValue as Float
            val current = keyboardHeight * value
            keyboardListener.onUpdate(current.toInt())
        }
        addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                if (isKeyboardShow) {
                    keyboardListener.keyboardShowEnd()
                } else {
                    keyboardListener.keyboardHideEnd()
                }
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }
        })
    }
    private val onGlobalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
        val height = popupContentView.height
        if (height > contentHeight) {
            contentHeight = height
        }
        // 这里也可以比对 rootView 与 popupContentView.height 的高度差来判定键盘是否抬起
        if (height < contentHeight) {
            // 变小的高度超过了屏幕高度的1/5则视为键盘抬起
            if (contentHeight - height > Resources.getSystem().displayMetrics.heightPixels / 5) {
                if (!isKeyboardShow) {
                    // 键盘显示
                    isKeyboardShow = true
                    keyboardHeight = contentHeight - height
                    keyboardListener.keyboardShowStart()
                    keyboardAnimator.start()
                }
            }
        } else {
            if (isKeyboardShow) {
                // 键盘隐藏
                isKeyboardShow = false
                keyboardListener.keyboardHideStart()
                keyboardAnimator.reverse()
            }
        }
    }

    /**
     * 当前方法建议在 Activity 的 onWindowFocusChanged 方法回调时调用
     */
    fun listenKeyboard() {
        val popupWindow = PopupWindow(
            popupContentView,
            0,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        popupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        popupWindow.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        popupContentView.viewTreeObserver.addOnGlobalLayoutListener(onGlobalLayoutListener)
        popupWindow.showAtLocation(rootView, Gravity.NO_GRAVITY, 0, 0)
        mPopupWindow = popupWindow
    }

    fun release() {
        mPopupWindow?.apply {
            this.dismiss()
        }
        mPopupWindow = null
        popupContentView.viewTreeObserver.removeOnGlobalLayoutListener(onGlobalLayoutListener)
    }
}