package com.socialplay.gpark.util

import android.content.Context
import android.graphics.Color
import androidx.core.content.ContextCompat
import com.luck.picture.lib.style.AlbumWindowStyle
import com.luck.picture.lib.style.BottomNavBarStyle
import com.luck.picture.lib.style.PictureSelectorStyle
import com.luck.picture.lib.style.PictureWindowAnimationStyle
import com.luck.picture.lib.style.SelectMainStyle
import com.luck.picture.lib.style.TitleBarStyle
import com.socialplay.gpark.R

/**
 * Created by bo.li
 * Date: 2023/8/29
 * Desc:
 */
object PictureSelectorUtil {
    fun getCommonStyle(context: Context): PictureSelectorStyle {
        return PictureSelectorStyle().apply {
            bottomBarStyle = BottomNavBarStyle().apply {
                bottomPreviewNormalTextColor = Color.WHITE
                bottomPreviewSelectTextColor = Color.WHITE
                bottomSelectNumTextColor = ContextCompat.getColor(context, R.color.colorAccent)
                bottomOriginalTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                bottomEditorTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                bottomSelectNumResources = R.drawable.ps_theme_num_oval
            }
            selectMainStyle = SelectMainStyle().apply {
                previewSelectTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                selectNormalTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                selectTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                adapterSelectTextColor = ContextCompat.getColor(context, R.color.colorPrimary)
                selectBackground = R.drawable.selector_check
                previewSelectBackground = R.drawable.selector_check
            }
            windowAnimationStyle = PictureWindowAnimationStyle(
                com.luck.picture.lib.R.anim.ps_anim_up_in,
                com.luck.picture.lib.R.anim.ps_anim_down_out
            )
        }
    }
}