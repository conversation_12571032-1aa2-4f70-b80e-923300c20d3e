package com.socialplay.gpark.util.glide

import android.app.Application
import android.content.Context
import android.net.Uri
import android.os.Build
import android.widget.ImageView
import coil.ImageLoader
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.load
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.engine.CompressFileEngine
import com.luck.picture.lib.engine.ImageEngine
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener
import com.luck.picture.lib.utils.ActivityCompatHelper
import kotlinx.coroutines.suspendCancellableCoroutine
import org.koin.core.context.GlobalContext
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File
import java.util.ArrayList


/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/10
 *     desc   : 图片选择器选择图片时的图片加载Engine
 */
object GlideEngine : ImageEngine {

    private val context by lazy { GlobalContext.get().get<Application>() }

    private fun isGif(context: Context, url: String?): Boolean {
        if (url.isNullOrEmpty()) return false
        val type = context.contentResolver.getType(Uri.parse(url))
        return PictureMimeType.isHasGif(type) || PictureMimeType.isUrlHasGif(url)
    }

    private val gifImageLoader by lazy {
        ImageLoader.Builder(context).components {
            if (Build.VERSION.SDK_INT >= 28) {
                add(ImageDecoderDecoder.Factory())
            } else {
                add(GifDecoder.Factory())
            }
        }.build()
    }

    /**
     * 加载图片
     *
     * @param context   上下文
     * @param url       资源url
     * @param imageView 图片承载控件
     */
    override fun loadImage(context: Context, url: String, imageView: ImageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        if (isGif(context, url)) {
            imageView.load(url, imageLoader = gifImageLoader)
        } else {
            Glide.with(context)
                .load(url)
                .into(imageView)
        }
    }

    override fun loadImage(
        context: Context,
        imageView: ImageView,
        url: String?,
        maxWidth: Int,
        maxHeight: Int
    ) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        if (isGif(context, url)){
            imageView.load(url, imageLoader = gifImageLoader)
        }else{
            Glide.with(context)
                .load(url)
                .override(maxWidth, maxHeight)
                .into(imageView)
        }
    }

    /**
     * 加载相册目录封面
     *
     * @param context   上下文
     * @param url       图片路径
     * @param imageView 承载图片ImageView
     */
    override fun loadAlbumCover(context: Context, url: String?, imageView: ImageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        if (isGif(context, url)) {
            imageView.load(url, imageLoader = gifImageLoader)
        } else {
            Glide.with(context)
                .load(url)
                .override(180, 180)
                .sizeMultiplier(0.5f)
                .transform(CenterCrop(), RoundedCorners(8))
                .placeholder(com.luck.picture.lib.R.drawable.ps_image_placeholder)
                .into(imageView)
        }
    }

    /**
     * 加载图片列表图片
     *
     * @param context   上下文
     * @param url       图片路径
     * @param imageView 承载图片ImageView
     */
    override fun loadGridImage(context: Context, url: String?, imageView: ImageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        if (isGif(context, url)) {
            imageView.load(url, imageLoader = gifImageLoader)
        } else {
            Glide.with(context)
                .load(url)
                .override(200, 200)
                .centerCrop()
                .placeholder(com.luck.picture.lib.R.drawable.ps_image_placeholder)
                .into(imageView)
        }
    }

    override fun pauseRequests(context: Context) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        Glide.with(context).pauseRequests()
    }

    override fun resumeRequests(context: Context) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return
        }
        Glide.with(context).resumeRequests()
    }

}

/**
 * GIF和视频不压缩
 */
fun needCompress(context: Context?, uri: Uri?): Boolean {
    if (context == null || uri == null) return true
    val type = context.contentResolver.getType(uri)
    return !PictureMimeType.isHasGif(type)
            && !PictureMimeType.isUrlHasGif(uri.path)
            && !PictureMimeType.isHasVideo(type)
            && !PictureMimeType.isUrlHasVideo(uri.path)
}

class LubanCompressEngine(val size: Int = 0) : CompressFileEngine {

    override fun onStartCompress(
        context: Context?,
        source: ArrayList<Uri>?,
        call: OnKeyValueResultCallbackListener?
    ) {
        Luban.with(context).load(source)
            .filter { url -> needCompress(context, Uri.parse(url)) }
            .ignoreBy(size)
            .setCompressListener(
                object : OnNewCompressListener {
                    override fun onStart() {
                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        call?.onCallback(source, compressFile?.absolutePath)
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        call?.onCallback(source, null)
                    }
                }).launch()
    }
}

suspend fun LubanCompressEngine.suspendStartCompress(
    context: Context?,
    file: File?,
): Pair<String?, String?> =
    suspendCancellableCoroutine {
        Luban.with(context).load(file)
            .filter { url -> needCompress(context, Uri.parse(url)) }
            .ignoreBy(size)
            .setCompressListener(
                object : OnNewCompressListener {
                    override fun onStart() {
                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        it.resumeWith(Result.success(source to compressFile?.absolutePath))
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        it.resumeWith(Result.success(source to null))
                    }
                }).launch();
    }