package com.socialplay.gpark.util;

import android.app.Activity;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.widget.FrameLayout;

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/03/23
 *     desc   : https://juejin.cn/post/7150453629021847566
 * </pre>
 */
public final class KeyboardHeightUtilV2 {

    public static           int                                     sDecorViewInvisibleHeightPre;
    private static          ViewTreeObserver.OnGlobalLayoutListener onGlobalLayoutListener;
    private volatile static KeyboardHeightListener                  onKeyboardHeightChangeListener;
    private static          int                                     sDecorViewDelta = 0;

    private static int getDecorViewInvisibleHeight(final Activity activity) {
        Point screenSize = new Point();
        activity.getWindowManager().getDefaultDisplay().getSize(screenSize);

        final View decorView = activity.getWindow().getDecorView();
        if (decorView == null) {
            return sDecorViewInvisibleHeightPre;
        }
        final Rect outRect = new Rect();
        decorView.getWindowVisibleDisplayFrame(outRect);

        int delta = screenSize.y - outRect.bottom;

        if (delta < 0) {
            sDecorViewDelta = -delta;
        }
        return delta > 0 ? delta + sDecorViewDelta : 0;
    }

    public static void registerKeyboardHeightListener(final Activity activity, final KeyboardHeightListener listener) {
        invokeBelow31(activity, listener);
    }

    private static void invokeBelow31(Activity activity, KeyboardHeightListener listener) {
        final int flags = activity.getWindow().getAttributes().flags;

        if ((flags & WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS) != 0) {
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        }

        final FrameLayout contentView = activity.findViewById(android.R.id.content);
        sDecorViewInvisibleHeightPre = getDecorViewInvisibleHeight(activity);

        onKeyboardHeightChangeListener = listener;

        onGlobalLayoutListener = () -> {
            int height = getDecorViewInvisibleHeight(activity);
            if (sDecorViewInvisibleHeightPre != height) {

                if (onKeyboardHeightChangeListener != null) {
                    onKeyboardHeightChangeListener.onKeyboardHeightChanged(height);
                }

                sDecorViewInvisibleHeightPre = height;
            }
        };

        contentView.getViewTreeObserver().addOnGlobalLayoutListener(onGlobalLayoutListener);
    }

    public static void unregisterKeyboardHeightListener(Activity activity) {
        onKeyboardHeightChangeListener = null;
        View contentView = activity.getWindow().getDecorView().findViewById(android.R.id.content);
        if (contentView == null) {
            onGlobalLayoutListener = null;
            return;
        }
        contentView.getViewTreeObserver().removeOnGlobalLayoutListener(onGlobalLayoutListener);
        onGlobalLayoutListener = null;
    }

    public interface KeyboardHeightListener {
        void onKeyboardHeightChanged(int height);
    }

}
