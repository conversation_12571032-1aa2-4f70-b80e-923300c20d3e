package com.socialplay.gpark.util.property

import android.app.Activity
import android.os.Bundle
import android.os.Parcelable
import androidx.fragment.app.Fragment
import java.io.Serializable
import java.lang.IllegalStateException
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/11
 * desc   :
 * </pre>
 */

inline fun <reified T> Activity.bundlePropertyNotNull(def: T, key: String? = null) =
    ActivityBundleProperty(argumentGetterNotNull(def, key))

inline fun <reified T> Activity.bundleProperty(def: T? = null, key: String? = null) =
    ActivityBundleProperty(argumentGetter(def, key))

inline fun <reified T> Fragment.bundlePropertyNotNull(def: T, key: String? = null) =
    FragmentArgumentProperty(argumentGetterNotNull(def, key))

inline fun <reified T> Fragment.bundleProperty(def: T? = null, key: String? = null) =
    FragmentArgumentProperty(argumentGetter(def, key))

inline fun <reified T> argumentGetterNotNull(def: T, k: String?): BundleGetter<T> {
    return BundleGetter { extras, key ->
        if (extras == null) return@BundleGetter def
        val realKey = if (k.isNullOrEmpty()) key else k
        val clz = T::class.java
        return@BundleGetter when (clz) {
            java.lang.Integer::class.java -> {
                extras.getInt(realKey, def as? Int ?: 0)
            }
            java.lang.Boolean::class.java -> {
                extras.getBoolean(realKey, def as? Boolean ?: false)
            }
            java.lang.Float::class.java   -> {
                extras.getFloat(realKey, def as? Float ?: 0F)
            }
            java.lang.Long::class.java    -> {
                extras.getLong(realKey, def as? Long ?: 0)
            }
            java.lang.Double::class.java  -> {
                extras.getDouble(realKey, def as? Double ?: 0.0)
            }
            java.lang.String::class.java  -> {
                extras.getString(realKey, def as? String)
            }
            else                          -> {
                val interfaces = clz.interfaces
                when {
                    interfaces.contains(Parcelable::class.java)   -> {
                        return@BundleGetter extras.getParcelable(realKey) ?: def
                    }
                    interfaces.contains(Serializable::class.java) -> {
                        return@BundleGetter extras.getSerializable(realKey) as? T ?: def
                    }
                    else           -> {
                        throw IllegalStateException("This type is not supported at this time: $clz") // 暂不支持此类型
                    }
                }
            }
        } as? T ?: def
    }
}

inline fun <reified T> argumentGetter(def: T?, k: String?): BundleGetter<T?> {
    return BundleGetter { extras, key ->
        if (extras == null) return@BundleGetter def
        val realKey = if (k.isNullOrEmpty()) key else k
        val clz = T::class.java
        return@BundleGetter when (clz) {
            java.lang.Integer::class.java -> {
                extras.getInt(realKey, def as? Int ?: 0)
            }
            java.lang.Boolean::class.java -> {
                extras.getBoolean(realKey, def as? Boolean ?: false)
            }
            java.lang.Float::class.java   -> {
                extras.getFloat(realKey, def as? Float ?: 0F)
            }
            java.lang.Long::class.java    -> {
                extras.getLong(realKey, def as? Long ?: 0)
            }
            java.lang.Double::class.java  -> {
                extras.getDouble(realKey, def as? Double ?: 0.0)
            }
            java.lang.String::class.java  -> {
                extras.getString(realKey, def as? String)
            }
            else                          -> {
                val interfaces = clz.interfaces
                when {
                    interfaces.contains(Parcelable::class.java)   -> {
                        return@BundleGetter extras.getParcelable(realKey) ?: def
                    }
                    interfaces.contains(Serializable::class.java) -> {
                        return@BundleGetter extras.getSerializable(realKey) as? T ?: def
                    }
                    else           -> {
                        throw IllegalStateException("This type is not supported at this time: $clz") // 暂不支持此类型
                    }
                }
            }
        } as? T ?: def
    }
}

class BundleGetter<T>(private val block: (extras: Bundle?, key: String) -> T) {
    fun get(extras: Bundle?, key: String): T = block.invoke(extras, key)
}

class ActivityBundleProperty<T>(private val getter: BundleGetter<T>) : ReadOnlyProperty<Activity, T> {
    override fun getValue(thisRef: Activity, property: KProperty<*>): T {
        return getter.get(thisRef.intent?.extras, property.name)
    }
}

class FragmentArgumentProperty<T>(private val getter: BundleGetter<T>) : ReadOnlyProperty<Fragment, T> {
    override fun getValue(thisRef: Fragment, property: KProperty<*>): T {
        return getter.get(thisRef.arguments, property.name)
    }
}