package com.socialplay.gpark.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import org.koin.core.context.GlobalContext

/**
 * 简易震动工具类
 *
 * create by: bin on 2023/3/7
 */
object SimpleVibratorUtil {
    private val vibrator by lazy {
        GlobalContext.get().get<Context>().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
    }

    /**
     * 普通点击震动
     */
    @SuppressLint("InlinedApi")
    fun vibrateClick(timeMills: Long = 30){
        vibrate(timeMills, VibrationEffect.EFFECT_CLICK)
    }

    /**
     * tick振动，小一点
     */
    @SuppressLint("InlinedApi")
    fun vibrateTick(timeMills: Long = 30) {
        vibrate(timeMills,  VibrationEffect.EFFECT_TICK)
    }

    /**
     * 振动 timeMills 毫秒
     */
    fun vibrate(timeMills: Long) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createOneShot(timeMills, VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            vibrator.vibrate(timeMills)
        }
    }

    /**
     * effectId 高级特性 sdk > 29
     * @see android.os.VibrationEffect.createPredefined
     */
    fun vibrate(timeMills: Long, effectId:Int){
        kotlin.runCatching {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                vibrator.vibrate(VibrationEffect.createPredefined(effectId))
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(timeMills, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                vibrator.vibrate(timeMills)
            }
        }
    }

}