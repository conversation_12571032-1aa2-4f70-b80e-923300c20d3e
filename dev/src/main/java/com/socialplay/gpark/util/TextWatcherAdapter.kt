package com.socialplay.gpark.util

import android.text.Editable
import android.text.TextWatcher

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2021/11/19 5:29 下午
 * @describe:
 */
open class TextWatcherAdapter : TextWatcher {
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

    }

    override fun afterTextChanged(s: Editable?) {

    }
}