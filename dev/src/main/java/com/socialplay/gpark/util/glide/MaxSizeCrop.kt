package com.socialplay.gpark.util.glide

import android.graphics.Bitmap
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.util.Util
import java.nio.ByteBuffer
import java.security.MessageDigest

/**
 * 按最大尺寸裁剪图片
 */
class MaxSizeCrop(
    private val startX: Int = 0,
    private val maxWidth: Int = Integer.MAX_VALUE,
    private val startY: Int = 0,
    private val maxHeight: Int = Integer.MAX_VALUE
) : BitmapTransformation() {
    companion object {
        private const val ID = "com.socialplay.gpark.util.glide.MaxSizeCrop"
        private val ID_BYTES = ID.toByteArray(CHARSET)
    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap {
        val startXTemp = startX.coerceAtMost(toTransform.width).coerceAtLeast(0)
        val startYTemp = startY.coerceAtMost(toTransform.height).coerceAtLeast(0)

        var maxWidthTemp = maxWidth.coerceAtMost(toTransform.width)
        if (maxWidthTemp < 0) {
            maxWidthTemp = toTransform.width
        }
        var maxHeightTemp = maxHeight.coerceAtMost(toTransform.height)
        if (maxHeightTemp < 0) {
            maxHeightTemp = toTransform.height
        }
        return Bitmap.createBitmap(
            toTransform,
            startXTemp,
            startYTemp,
            (toTransform.width - startXTemp).coerceAtMost(maxWidthTemp).coerceAtLeast(0),
            (toTransform.height - startYTemp).coerceAtMost(maxHeightTemp).coerceAtLeast(0)
        )
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(ID_BYTES)
        val boundData = ByteBuffer.allocate(16)
            .putInt(startX)
            .putInt(maxWidth)
            .putInt(startY)
            .putInt(maxHeight)
            .array()
        messageDigest.update(boundData)
    }

    override fun equals(other: Any?): Boolean {
        if (other is MaxSizeCrop) {
            return startX == other.startX
            return maxWidth == other.maxWidth
            return startY == other.startY
            return maxHeight == other.maxHeight
        }
        return false
    }

    override fun hashCode(): Int {
        var hashCode = Util.hashCode(ID.hashCode(), Util.hashCode(startX))
        hashCode = Util.hashCode(hashCode, Util.hashCode(maxWidth))
        hashCode = Util.hashCode(hashCode, Util.hashCode(startY))
        hashCode = Util.hashCode(hashCode, Util.hashCode(maxHeight))
        return hashCode
    }
}