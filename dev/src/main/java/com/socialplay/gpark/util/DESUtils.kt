package com.socialplay.gpark.util

import android.util.Base64
import timber.log.Timber
import java.security.InvalidParameterException
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.DESKeySpec

/**
 * Created by bo.li
 * Date: 2023/3/14
 * Desc:
 */
object DESUtils {
    /**
     * DES加解密
     */
    private const val ALGORITHM = "DES"

    /**
     * 工作模式：ECB
     */
    private const val TRANSFORM_ECB_PKCS5 = "DES/ECB/PKCS5Padding"


    /**
     * 基于ECB工作模式的DES加密字符串
     * @param value 待加密字符串
     * @param key 秘钥，如果不填则使用默认值
     * @return java.lang.String
     */
    fun encryptEcbMode(value: String, key: String): String? {
        if (key.length != 8) {
            throw InvalidParameterException("key length must is 8")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.ENCRYPT_MODE, keySpec)
            //使用DES加密
            val encrypted = encipher.doFinal(value.toByteArray())
            //然后转成BASE64返回
            return String(Base64.encode(encrypted, Base64.NO_WRAP))
        } catch (e: Exception) {
            Timber.d("encrypt failed due to ${e.message}")
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于ECB工作模式的DES解密字符串
     * @param encryptedStr DES加密之后的字符串
     * @param key 秘钥，如果不填则使用默认值
     * @return java.lang.String
     */
    fun decryptEcbMode(encryptedStr: String, key: String): String? {
        if (key.length != 8) {
            throw InvalidParameterException("key length must is 8")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            //Base64解码
            val decodedBytes: ByteArray = Base64.decode(encryptedStr, Base64.NO_WRAP)

            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec)
            //然后再DES解密
            val originalBytes = encipher.doFinal(decodedBytes)
            //返回字符串
            return String(originalBytes)
        } catch (e: Exception) {
            Timber.d("decrypt failed due to ${e.message}")
            e.printStackTrace()
        }
        return null
    }

    /**
     * 生成加密秘钥
     * @param key 明文秘钥
     * @return SecretKeySpec
     */
    private fun getSecretKey(key: String): SecretKey? {
        //生成指定算法密钥
        try {
            val dks = DESKeySpec(key.toByteArray())
            val keyFactory = SecretKeyFactory.getInstance(ALGORITHM)
            return keyFactory.generateSecret(dks)
        } catch (ex: NoSuchAlgorithmException) {
            Timber.d("generate key failed due to ${ex.message}")
            ex.printStackTrace()
        }
        return null
    }
}