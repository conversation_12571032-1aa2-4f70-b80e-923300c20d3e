package com.socialplay.gpark.util

import android.app.ActivityManager
import android.app.ActivityManager.RunningAppProcessInfo
import android.app.ActivityManager.RunningTaskInfo
import android.content.Context
import android.os.Build.VERSION
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/30
 *     desc   :
 *
 */
object SystemUtils {
    fun isInBackground(context: Context?): <PERSON><PERSON>an {
        return if (context == null) {
            true
        } else {
            var isInBackground = true
            val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningProcesses: List<*>?
            if (VERSION.SDK_INT > 20) {
                runningProcesses = am.runningAppProcesses
                if (runningProcesses == null) {
                    return true
                }
                val var4 = runningProcesses.iterator()
                while (true) {
                    var processInfo: RunningAppProcessInfo
                    do {
                        if (!var4.hasNext()) {
                            return isInBackground
                        }
                        processInfo = var4.next() as RunningAppProcessInfo
                    } while (processInfo.importance != 100)
                    val var6 = processInfo.pkgList
                    val var7 = var6.size
                    for (var8 in 0 until var7) {
                        val activeProcess = var6[var8]
                        if (activeProcess == context.packageName) {
                            Timber.d("SystemUtils %s", "the process is in foreground:$activeProcess")
                            return false
                        }
                    }
                }
            } else {
                runningProcesses = am.getRunningTasks(1)
                val componentInfo = (runningProcesses[0] as RunningTaskInfo).topActivity
                if (componentInfo?.packageName == context.packageName) {
                    isInBackground = false
                }
            }
            isInBackground
        }
    }

    fun isHarmonyOs(): Boolean {
        return kotlin.runCatching {
            val buildExClass = Class.forName("com.huawei.system.BuildEx")
            val osBrand = buildExClass.getMethod("getOsBrand").invoke(buildExClass)
            "Harmony".equals(osBrand.toString(), ignoreCase = true)
        }.getOrDefault(false)
    }

}