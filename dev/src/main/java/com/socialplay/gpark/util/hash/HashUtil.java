package com.socialplay.gpark.util.hash;



import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Iterator;
/**
 * 2024/8/14
 */
public class HashUtil {
    private static final int DEFAULT_BUFF_LENGTH = 16384;
    public static final HashCalcTemplate MD5 = new HashCalcTemplate("MD5");
    public static final HashCalcTemplate SHA = new HashCalcTemplate("SHA");
    public static final HashCalcTemplate SHA1 = new HashCalcTemplate("SHA1");
    public static final HashCalcTemplate SHA256 = new HashCalcTemplate("SHA-256");
    public static final HashCalcTemplate SHA512 = new HashCalcTemplate("SHA-512");

    public HashUtil() {
    }

    public static HashCalcTemplate ANY(String code) {
        Iterator var1 = HashCalcTemplate.ALL.iterator();

        HashCalcTemplate template;
        do {
            if (!var1.hasNext()) {
                return null;
            }

            template = (HashCalcTemplate)var1.next();
        } while(!code.equalsIgnoreCase(template.code));

        return template;
    }

    static class Calc {
        Calc() {
        }

        static byte[] hashBytes(String code, byte[] data) {
            try {
                return MessageDigest.getInstance(code).digest(data);
            } catch (Throwable var3) {
                Throwable e = var3;
                e.printStackTrace();
                return null;
            }
        }

        static String hash(String code, byte[] data, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, data), lower);
        }

        static byte[] hashBytes(String code, String data) {
            return hashBytes(code, data, StandardCharsets.UTF_8);
        }

        static String hash(String code, String data, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, data), lower);
        }

        static byte[] hashBytes(String code, String data, Charset charset) {
            return hashBytes(code, data.getBytes(charset));
        }

        static String hash(String code, String data, Charset charset, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, data, charset), lower);
        }

        static byte[] hashBytes(String code, File data) {
            FileInputStream input = null;

            Object var4;
            try {
                input = new FileInputStream(data);
                byte[] var10 = hashBytes(code, (InputStream)input);
                return var10;
            } catch (Throwable var8) {
                Throwable e = var8;
                e.printStackTrace();
                var4 = null;
            } finally {
                LazyUtil.close(new Closeable[]{input});
            }

            return (byte[])var4;
        }

        static String hash(String code, File data, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, data), lower);
        }

        static byte[] hashBytes(String code, InputStream input) {
            try {
                MessageDigest md = MessageDigest.getInstance(code);
                byte[] buffer = new byte[16384];

                int numRead;
                while((numRead = input.read(buffer)) != -1) {
                    md.update(buffer, 0, numRead);
                }

                return md.digest();
            } catch (Exception var5) {
                return null;
            }
        }

        static String hash(String code, InputStream input, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, input), lower);
        }

        static byte[] hashBytes(String code, File data, long begin, long end) {
            RandomAccessFile raf = null;
            long length = end - begin;

            Object var10;
            try {
                raf = new RandomAccessFile(data, "r");
                raf.seek(begin);
                MessageDigest md = MessageDigest.getInstance(code);
                byte[] buffer = new byte[16384];
                long totalRead = 0L;

                while(true) {
                    int numRead;
                    if ((numRead = raf.read(buffer)) != -1) {
                        totalRead += (long)numRead;
                        if (totalRead <= length) {
                            md.update(buffer, 0, numRead);
                            continue;
                        }

                        md.update(buffer, 0, numRead - (int)(totalRead - length));
                    }

                    byte[] var14 = md.digest();
                    return var14;
                }
            } catch (Throwable var18) {
                Throwable e = var18;
                e.printStackTrace();
                var10 = null;
            } finally {
                LazyUtil.close(new Closeable[]{raf});
            }

            return (byte[])var10;
        }

        static String hash(String code, File data, long begin, long end, boolean lower) {
            return BytesDisplayUtil.hex(hashBytes(code, data, begin, end), lower);
        }

        static boolean valid(String content, int validLength) {
            if (content == null) {
                return false;
            } else if (content.length() != validLength) {
                return false;
            } else {
                int state = 0;

                for(int i = 0; i < content.length(); ++i) {
                    char c = content.charAt(i);
                    if (c < '0' || c > '9') {
                        if (c >= 'A' && c <= 'F') {
                            if (state == 1) {
                                return false;
                            }

                            state = 2;
                        } else {
                            if (c < 'a' || c > 'f') {
                                return false;
                            }

                            if (state == 2) {
                                return false;
                            }

                            state = 1;
                        }
                    }
                }

                return true;
            }
        }
    }
}
