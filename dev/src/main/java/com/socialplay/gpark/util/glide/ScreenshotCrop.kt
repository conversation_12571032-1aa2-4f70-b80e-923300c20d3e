package com.socialplay.gpark.util.glide

import android.graphics.Bitmap
import androidx.core.graphics.scale
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.util.Util
import java.nio.ByteBuffer
import java.security.MessageDigest
import kotlin.math.roundToInt

/**
 * xingxiu.hou
 * 2023/1/3
 * 按照边界裁切Bitmap
 */
class ScreenshotCrop(
    private val screenWidth: Int,
    private val statusBarHeight: Int,
) : BitmapTransformation() {

    companion object {
        private const val ID = "com.socialplay.gpark.util.glide.ScreenshotCrop"
        private val ID_BYTES = ID.toByteArray(CHARSET)
    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int,
    ): Bitmap {
        val scaleTop = (statusBarHeight * (toTransform.width / screenWidth.toFloat())).roundToInt()
        val remainHeight = toTransform.height - scaleTop

        val scale = outHeight / toTransform.height.toFloat()
        val scaleWidth = (toTransform.width * scale).roundToInt()
        val scaleHeight = (remainHeight * scale).roundToInt()

        return Bitmap.createBitmap(
            toTransform,
            0,
            scaleTop,
            toTransform.width,
            toTransform.height - scaleTop
        ).scale(scaleWidth, scaleHeight)
    }


    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(ID_BYTES)
        val boundData = ByteBuffer.allocate(16)
            .putInt(screenWidth)
            .putInt(statusBarHeight)
            .array()
        messageDigest.update(boundData)
    }

    override fun equals(other: Any?): Boolean {
        if (other is ScreenshotCrop) {
            return screenWidth == other.screenWidth &&
                    statusBarHeight == other.statusBarHeight
        }
        return false
    }

    override fun hashCode(): Int {
        var hashCode = Util.hashCode(ID.hashCode(), Util.hashCode(screenWidth))
        hashCode = Util.hashCode(hashCode, Util.hashCode(statusBarHeight))
        return hashCode
    }
}