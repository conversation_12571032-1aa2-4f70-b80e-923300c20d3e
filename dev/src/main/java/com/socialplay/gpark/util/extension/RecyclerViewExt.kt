package com.socialplay.gpark.util.extension

import android.os.Looper
import android.view.View
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.google.android.flexbox.FlexboxLayoutManager

fun RecyclerView.getCurrentOffset(): Triple<Boolean, Int, Int> {
    val llm = layoutManager as? LinearLayoutManager ?: return Triple(false, 0, 0)
    val firstView = llm.getChildAt(0) ?: return Triple(false, 0, 0)
    val firstPos = llm.getPosition(firstView)
    val firstOffset =
        if (llm.orientation == LinearLayoutManager.HORIZONTAL) firstView.left else firstView.top
    return Triple(true, firstPos, firstOffset)
}

fun RecyclerView.scrollWithOffset(position: Int, offset: Int) {
    (layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(position, offset)
}

fun RecyclerView.Adapter<*>.registerAdapterDataObserver(
    owner: LifecycleOwner,
    observer: RecyclerView.AdapterDataObserver
) {
    if (Looper.getMainLooper().thread != Thread.currentThread()) {
        throw IllegalStateException("observe must main thread")
    }
    if (owner.lifecycle.currentState == Lifecycle.State.DESTROYED) {
        return
    }

    registerAdapterDataObserver(observer)
    owner.lifecycle.addObserver(object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            owner.lifecycle.removeObserver(this)
            // 有可能已经被外部注销过了, 重复注销会抛出异常
            runCatching { unregisterAdapterDataObserver(observer) }
        }
    })
}

fun RecyclerView.getVisibleViewHolders(): List<RecyclerView.ViewHolder> {
    val list = ArrayList<RecyclerView.ViewHolder>()
    val lm = layoutManager
    val fp: Int
    val lp: Int
    when (lm) {
        is LinearLayoutManager -> {
            fp = lm.findFirstVisibleItemPosition()
            if (fp == RecyclerView.NO_POSITION) return list
            lp = lm.findLastVisibleItemPosition()
        }

        is FlexboxLayoutManager -> {
            fp = lm.findFirstVisibleItemPosition()
            if (fp == RecyclerView.NO_POSITION) return list
            lp = lm.findLastVisibleItemPosition()
        }

        else -> {
            return list
        }
    }
    if (lp < fp) return list
    for (i in fp..lp) {
        val vh = findViewHolderForAdapterPosition(i)
        vh ?: continue
        list.add(vh)
    }
    return list
}

fun RecyclerView.getVisibleItemViews(): List<View> = getVisibleViewHolders().map { it.itemView }

fun ItemTouchHelper.attachToRecyclerView(
    owner: LifecycleOwner,
    recyclerView: RecyclerView
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            attachToRecyclerView(recyclerView)
        }, unregister = {
            attachToRecyclerView(null)
        }
    )
}

fun RecyclerView.addOnScrollListener(
    owner: LifecycleOwner,
    scrollListener: OnScrollListener
) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addOnScrollListener(scrollListener)
        },
        unregister = {
            removeOnScrollListener(scrollListener)
        }
    )
}

fun EpoxyVisibilityTracker.attach(
    owner: LifecycleOwner,
    recyclerView: RecyclerView
): EpoxyVisibilityTracker {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            attach(recyclerView)
        },
        unregister = {
            detach(recyclerView)
        }
    )
    return this
}

fun EpoxyVisibilityTracker.attachV2(
    owner: LifecycleOwner,
    recyclerView: RecyclerView
): EpoxyVisibilityTracker {
    owner.runOnMainThreadWhenNotDestroyed {
        attach(recyclerView)
        owner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                clearVisibilityStates()
                requestVisibilityCheck()
            }

            override fun onDestroy(owner: LifecycleOwner) {
                owner.lifecycle.removeObserver(this)
                detach(recyclerView)
            }
        })
    }
    return this
}

fun EpoxyController.addModelBuildListener(owner: LifecycleOwner, listener: OnModelBuildFinishedListener) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addModelBuildListener(listener)
        },
        unregister = {
            removeModelBuildListener(listener)
        }
    )
}