package com.socialplay.gpark.util

import org.json.JSONArray
import org.json.JSONObject

/**
 * xingxiu.hou
 * 2022/10/24
 */

fun Map<*, *>.simJson(): String {
    return simJsonObj().toString()
}

fun Map<*, *>.simJsonObj(): JSONObject {
    val obj = JSONObject()
    runCatching {
        onEach {
            when (val value = it.value) {
                is Map<*, *> -> {
                    obj.put(it.key.toString(), value.simJsonObj())
                }
                is Collection<*> -> {
                    obj.put(it.key.toString(), value.simJsonArr())
                }
                else -> {
                    obj.put(it.key.toString(), it.value)
                }
            }
        }
    }
    return obj
}

fun Collection<*>.simJson(): String {
    return simJsonArr().toString()
}

fun Collection<*>.simJsonArr(): JSONArray {
    val array = JSONArray()
    runCatching {
        onEach {
            when (it) {
                is Map<*, *> -> {
                    array.put(it.simJsonObj())
                }
                is Collection<*> -> {
                    array.put(it.simJsonArr())
                }
                else -> {
                    array.put(it)
                }
            }
        }
    }
    return array
}