package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R

/**
 * Created by bo.li
 * Date: 2023/10/18
 * Desc:
 */
object PronounsUtil {

    /**
     * 获取性别代词:1-男 2-女 0-未知
     */
    fun getPossessivePronoun1(context: Context, gender: Int, isMe: <PERSON><PERSON>an): String {
        return when {
            isMe -> {
                context.getString(R.string.my_cap)
            }
            gender == 1 -> {
                context.getString(R.string.his_cap)
            }
            gender == 2 -> {
                context.getString(R.string.her_cap)
            }
            else -> {
                context.getString(R.string.their_cap)
            }
        }
    }

    /**
     * 获取性别代词:1-男 2-女 0-未知
     */
    fun getPossessivePronoun2(context: Context, gender: Int, isMe: Boolean): String {
        return when {
            isMe -> {
                context.getString(R.string.my_cap)
            }
            gender == 1 -> {
                context.getString(R.string.hes_cap)
            }
            gender == 2 -> {
                context.getString(R.string.shes_cap)
            }
            else -> {
                context.getString(R.string.theyre_cap)
            }
        }
    }
}