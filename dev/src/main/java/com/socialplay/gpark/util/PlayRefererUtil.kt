package com.socialplay.gpark.util

import android.content.Context
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import kotlinx.coroutines.InternalCoroutinesApi

import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/10/08
 *     desc   : 获取Play
 */
object PlayRefererUtil {

    private val QUERY_PAIR_REGEX = Regex("([^=&]+=[^=&]+)")

    //仅保留Referer中的这些字段
    private val KEEP_KEYS = listOf("utm_source", "utm_medium", "utm_term", "utm_content", "utm_campaign", "anid")

    suspend fun getReferer(context: Context): ReferrerDetails? {
        return suspendCancellableCoroutine { cancelableCoroutine ->
            val referrerClient: InstallReferrerClient = InstallReferrerClient.newBuilder(context).build()
            referrerClient.startConnection(object : InstallReferrerStateListener {

                @OptIn(InternalCoroutinesApi::class)
                override fun onInstallReferrerSetupFinished(responseCode: Int) {
                    Timber.w("zhuwei:onInstallReferrerSetupFinished %s", responseCode)
                    var referrerDetails: ReferrerDetails? = null

                    when (responseCode) {
                        InstallReferrerClient.InstallReferrerResponse.OK -> {
                            referrerDetails = kotlin.runCatching {
                                val installReferrer = referrerClient.installReferrer
                                referrerClient.endConnection()
                                return@runCatching installReferrer
                            }.getOrNull()
                        }
                    }
                    val token = cancelableCoroutine.tryResume(referrerDetails)
                    token?.run { cancelableCoroutine.completeResume(this) }
                }

                override fun onInstallReferrerServiceDisconnected() {
                    // Try to restart the connection on the next request to
                    // Google Play by calling the startConnection() method.
                    Timber.w("zhuwei:onInstallReferrerServiceDisconnected")
                }
            })
        }
    }

    fun splitReferer(referer: String?): Map<String, String> {
        if (referer.isNullOrEmpty()) return emptyMap()

        return QUERY_PAIR_REGEX.findAll(referer)
            .map {
                val kv = it.value.split("=")
                return@map kv[0] to kv[1]
            }
            .filter { KEEP_KEYS.contains(it.first) }
            .toMap()
    }
}