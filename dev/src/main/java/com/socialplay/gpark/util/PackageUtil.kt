package com.socialplay.gpark.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import com.socialplay.gpark.data.model.ApkInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.security.MessageDigest

/**
 * Created by bo.li
 * Date: 2021/5/28
 * Desc:
 */
object PackageUtil {

    private const val INSTALL_PACKAGE = "com.android.packageinstaller"

    /**
     * 判断启动app的来源是否为桌面/安装界面
     * @return 来源是否符合条件
     */
    fun isLauncher(referrer: String, context: Context) =
        getLauncherPkgName(context).indexOfFirst { referrer.contains(it) } >= 0

    /**
     * 获取手机桌面/安装界面包名
     * @return 包名Array
     */
    fun getLauncherPkgName(context: Context): ArrayList<String> {
        val result = arrayListOf(INSTALL_PACKAGE)
        // 可能有多个桌面
        val list = context.packageManager.queryIntentActivities(Intent(Intent.ACTION_MAIN).addCategory(Intent.CATEGORY_HOME), 0)
        if (list.isNullOrEmpty()) {
            return result
        }
        result.addAll(list.map {
            it.activityInfo.packageName
        })
        return result
    }

    /**
     * 安装app
     */
    fun installApp(context: Context, file: File): Boolean {
        if (file.extension != "apk") {
            return false
        }
        val intent = Intent(Intent.ACTION_VIEW)
        val uriForFile = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            Uri.fromFile(file)
        } else {
            val authority = context.applicationContext.packageName + ".fileprovider"
            FileProvider.getUriForFile(context, authority, file)
        }
        intent.setDataAndType(uriForFile, "application/vnd.android.package-archive")
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        return true
    }

    suspend fun getApkInfo(context: Context, apkFile: File): ApkInfo? {
        if (!apkFile.exists() || apkFile.length() <= 0) {
            return null
        }
        return withContext(Dispatchers.IO) {
            val packageManager = context.packageManager
            val packageArchiveInfo = packageManager.getPackageArchiveInfo(apkFile.absolutePath, PackageManager.GET_ACTIVITIES)
                ?: return@withContext null
            val applicationInfo = packageArchiveInfo.applicationInfo
            applicationInfo.sourceDir = apkFile.absolutePath
            applicationInfo.publicSourceDir = apkFile.absolutePath
            val packageName = applicationInfo.packageName
            val appName = packageManager.getApplicationLabel(applicationInfo).toString()
            val version = packageArchiveInfo.versionName
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageArchiveInfo.longVersionCode
            } else {
                packageArchiveInfo.versionCode.toLong()
            }
            val icon = applicationInfo.loadIcon(packageManager)

            ApkInfo(packageName, appName, version, versionCode, icon, apkFile.length(), apkFile.absolutePath)
        }
    }

    fun getApkVersionCode(context: Context, packageName: String): Long {
        val packageManager = context.packageManager
        val packageInfo = kotlin.runCatching { packageManager.getPackageInfo(packageName, 0) }.getOrNull()
            ?: return 0
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
        } else {
            packageInfo.versionCode.toLong()
        }
    }

    /**
     * 获取应用名称
     */
    fun getAppName(context: Context): String {
        return kotlin.runCatching {
            val packageManager = context.packageManager
            val appInfo = packageManager.getApplicationInfo(context.packageName, 0)
            packageManager.getApplicationLabel(appInfo)
        }.getOrNull().toString()
    }

    /**
     * 当前应用是否由Google签名
     */
    fun isSignedByGoogle(context: Context): Boolean {
        // Google签名 SHA1 "70:DA:BF:6B:38:2D:6D:9A:A8:41:92:C1:AB:B2:6C:EF:74:30:97:5C"
        val googleSignatureSHA = "70DABF6B382D6D9AA84192C1ABB26CEF7430975C"

        try {
            val info = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )

            for (signature in info.signatures) {
                val md = MessageDigest.getInstance("SHA")
                md.update(signature.toByteArray())

                val digest = md.digest().joinToString("") {
                    String.format("%1\$02x", it)
                }.uppercase()

                if (digest == googleSignatureSHA) {
                    return true
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to read package signature.")
        }

        return false
    }
}