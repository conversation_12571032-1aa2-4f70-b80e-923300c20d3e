package com.socialplay.gpark.util.extension

import android.util.DisplayMetrics
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import java.lang.reflect.Field
import kotlin.math.abs

val ViewPager2.recyclerView: RecyclerView?
    get() {
        try {
            val recyclerViewField: Field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true
            return recyclerViewField.get(this) as? RecyclerView
        } catch (ignore: Exception) {
        }

        return null
    }


fun ViewPager2.setTouchSlop(touchSlop: Int) {
    val recyclerView = this.recyclerView ?: return
    try {
        val touchSlopField: Field = RecyclerView::class.java.getDeclaredField("mTouchSlop")
        touchSlopField.isAccessible = true
        touchSlopField.set(recyclerView, touchSlop)
    } catch (ignored: Exception) {
    }
}


fun ViewPager2.smoothScrollTo(dstPos: Int, duration: Long = 500) {
    val rv = recyclerView
    val layoutManager = rv?.layoutManager as? LinearLayoutManager ?: return

    var scrollDistance: Float = rv.height.toFloat()
    if (abs(dstPos - currentItem) > 3) {
        rv.scrollToPosition(if (dstPos > currentItem) dstPos - 3 else dstPos + 3)
        scrollDistance = rv.height * 3F
    }

    val scroller = object : LinearSmoothScroller(rv.context) {
        override fun getVerticalSnapPreference(): Int = SNAP_TO_START
        override fun getHorizontalSnapPreference(): Int = SNAP_TO_START
        override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics?): Float {
            return (duration / scrollDistance)
        }
    }
    scroller.targetPosition = dstPos
    layoutManager.startSmoothScroll(scroller)
}

fun ViewPager2.setCurrentItemIfValid(position: Int) {
    val pages = adapter?.itemCount ?: 0
    if (pages > 0 && position in 0 until pages && currentItem != position) {
        currentItem = position
    }
}