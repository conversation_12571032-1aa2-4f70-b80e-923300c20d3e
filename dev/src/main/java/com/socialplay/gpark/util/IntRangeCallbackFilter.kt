package com.socialplay.gpark.util

import android.text.InputFilter
import android.text.Spanned

class IntRangeCallbackFilter(
    val min: Int,
    val max: Int,
    val callback: (() -> Unit)? = null
) : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence? {
        try {
            // 构建用户输入后的完整文本
            val newVal = (dest.toString().substring(0, dstart)
                    + source.toString()
                    + dest.toString().substring(dend))

            // 如果新文本为空，允许输入（例如，删除所有内容）
            if (newVal.isEmpty()) {
                // null 表示接受输入
                return null
            }

            // 尝试将新文本转换为整数
            val input = newVal.toInt()

            // 检查整数是否在范围内
            if (input >= min && input <= max) {
                // null 表示接受输入
                return null
            }
        } catch (_: NumberFormatException) {
            // 如果转换失败（例如，用户输入非数字字符），则返回空字符串，拒绝输入
            callback?.invoke()
            return ""
        }

        // 如果不符合范围，返回空字符串，拒绝输入
        callback?.invoke()
        return ""
    }
}