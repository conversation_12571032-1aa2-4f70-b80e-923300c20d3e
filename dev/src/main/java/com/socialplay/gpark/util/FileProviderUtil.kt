package com.socialplay.gpark.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import com.socialplay.gpark.BuildConfig
import org.jetbrains.annotations.ApiStatus.Experimental
import java.io.File

object FileProviderUtil {
    fun getUriForFile(context: Context, file: File): Uri {
        return if (Build.VERSION.SDK_INT >= 24) {
            getUriForFile24(context, file)
        } else {
            Uri.fromFile(file)
        }
    }

    private fun getUriForFile24(context:Context, file: File) :Uri{
        val authority = BuildConfig.APPLICATION_ID + ".fileprovider"
        return  FileProvider.getUriForFile(context,authority,file)
    }


    fun setIntentDataAndType(context:Context, intent: Intent, type:String, file:File, writeAble:Boolean) {
        if (Build.VERSION.SDK_INT >= 24) {
            intent.setDataAndType(getUriForFile(context, file), type)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            if (writeAble) {
                intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
            }
        } else {
            intent.setDataAndType(Uri.fromFile(file), type)
        }
    }

    fun getValidPath(context: Context, path: String?, vararg packages: String): String? {
        path ?: return null
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
            && !WebUtil.isHttpOrHttpsScheme(path)
        ) {
            val uri = kotlin.runCatching {
                FileProvider.getUriForFile(
                    context,
                    "${BuildConfig.APPLICATION_ID}.fileprovider",
                    File(path)
                )
            }.getOrNull()
            if (uri != null) {
                for (p in packages) {
                    context.grantUriPermission(
                        p,
                        uri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION
                    )
                }
                uri.toString()
            } else {
                path
            }
        } else {
            path
        }
    }

    @Experimental
    fun getUriForFileV2(context: Context, file: File): Uri {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            FileProvider.getUriForFile(
                context,
                context.packageName.toString() + ".fileprovider",
                file
            )
        } else {
            Uri.fromFile(file)
        }
    }

    fun getValidPathV2(context: Context, path: String?, vararg packages: String): String? {
        path ?: return null
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
            && !WebUtil.isHttpOrHttpsScheme(path)
        ) {
            val uri = kotlin.runCatching {
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    File(path)
                )
            }.getOrNull()
            if (uri != null) {
                for (p in packages) {
                    context.grantUriPermission(
                        p,
                        uri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION
                    )
                }
                uri.toString()
            } else {
                path
            }
        } else {
            path
        }
    }
}