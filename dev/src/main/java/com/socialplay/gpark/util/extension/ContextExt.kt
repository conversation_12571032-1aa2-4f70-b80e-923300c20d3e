package com.socialplay.gpark.util.extension

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.ContextWrapper
import android.os.Looper
import androidx.fragment.app.DialogFragment
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/29
 * desc   :
 * </pre>
 */


inline fun Context.activityManager(): ActivityManager {
    return getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
}

fun Context.toast(msg: String, isLong: Boolean = false) {
    if (Looper.getMainLooper() == Looper.myLooper()) {
        if (isLong) {
            ToastUtil.showLong(this, msg)
        } else {
            ToastUtil.showShort(this, msg)
        }
    } else {
        GlobalScope.launch(Dispatchers.Main) {
            if (isLong) {
                ToastUtil.showLong(this@toast, msg)
            } else {
                ToastUtil.showShort(this@toast, msg)
            }
        }
    }
}
fun Context.toast(msgResId: Int) = toast(getString(msgResId))

fun Context?.wrappedActivity(): Activity? {
    return when (this) {
        is Activity -> {
            this
        }
        is ContextWrapper -> {
            baseContext.wrappedActivity()
        }
        else              -> {
            null
        }
    }
}

val ViewBinding.context: Context get() = root.context

fun Job?.safeCancel() {
    this ?: return
    if (isActive) {
        cancel()
    }
}

fun DialogFragment.dismissSafely() {
    activity?.let {
        if (!it.isFinishing) {
            dismissAllowingStateLoss()
        }
    }
}