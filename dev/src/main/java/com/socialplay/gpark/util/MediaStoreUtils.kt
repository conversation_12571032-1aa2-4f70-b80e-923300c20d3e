package com.socialplay.gpark.util

import android.content.ContentValues
import android.content.Context
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.fragment.app.Fragment
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileNotFoundException
import java.io.IOException

/**
 * 2024/3/8
 */
typealias SaveAlbumCallback = (Boolean, List<Uri>) -> Unit

object MediaStoreUtils : CoroutineScope by CoroutineScope(Dispatchers.IO) {

    fun savePathToAlbumWithPermissionCheck(
        fragment: Fragment,
        path: String,
        callback: SaveAlbumCallback? = null
    ) {
        savePathsToAlbumWithPermissionCheck(fragment, listOf(path), callback)
    }

    fun savePathsToAlbumWithPermissionCheck(
        fragment: Fragment,
        paths: List<String>,
        callback: SaveAlbumCallback? = null
    ) {
        saveFilesToAlbumWithPermissionCheck(fragment, paths.map { File(it) }, callback)
    }

    fun saveFileToAlbumWithPermissionCheck(
        fragment: Fragment,
        file: File,
        callback: SaveAlbumCallback? = null
    ) {
        saveFilesToAlbumWithPermissionCheck(fragment, listOf(file), callback)
    }

    fun saveFilesToAlbumWithPermissionCheck(
        fragment: Fragment,
        files: List<File>,
        callback: SaveAlbumCallback? = null
    ) {
        val uris = ArrayList<Uri>(files.size)
        val activity = fragment.activity
        if (activity == null) {
            callback?.invoke(false, emptyList())
            return
        }
        val context = activity.applicationContext
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            if (PermissionRequest.checkSelfPermission(
                    activity,
                    *Permission.WRITE_STORAGE.permissions
                )
            ) {
                saveFilesToAlbumWithPermissionCheckHelper(context, files, callback)
            } else {
                PermissionRequest.with(activity)
                    .permissions(Permission.WRITE_STORAGE)
                    .enableGoSettingDialog()
                    .granted {
                        launch {
                            files.forEach {
                                val uri = saveFileToAlbum(context, it)
                                if (uri != null) {
                                    uris.add(uri)
                                }
                            }
                            callback?.invoke(uris.size == files.size, uris)
                        }
                    }
                    .denied {
                        callback?.invoke(false, emptyList())
                    }
                    .branch(PermissionRequest.SCENE_SAVE_IMAGE_VIDEO)
                    .request()
            }
        } else {
            saveFilesToAlbumWithPermissionCheckHelper(context, files, callback)
        }
    }

    private fun saveFilesToAlbumWithPermissionCheckHelper(
        context: Context,
        files: List<File>,
        callback: SaveAlbumCallback?
    ) {
        launch {
            val uris = ArrayList<Uri>(files.size)
            files.forEach {
                val uri = saveFileToAlbum(context, it)
                if (uri != null) {
                    uris.add(uri)
                }
            }
            callback?.invoke(uris.size == files.size, uris)
        }
    }

    suspend fun saveFileToAlbum(context: Context, file: File): Uri? = withContext(Dispatchers.IO) {
        val fileTypes = getFileTypes(file)
        if (fileTypes == null) {
            Timber.d("not support file type ${file.extension} $file")
            return@withContext null
        }
        val resolver = context.contentResolver
        val cv = ContentValues().apply {
            when (fileTypes.mediaTypes) {
                MediaTypes.Video -> put(MediaStore.Video.Media.MIME_TYPE, fileTypes.mimeType)
                MediaTypes.Image -> put(MediaStore.Images.Media.MIME_TYPE, fileTypes.mimeType)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                when (fileTypes.mediaTypes) {
                    MediaTypes.Video -> put(
                        MediaStore.Video.Media.RELATIVE_PATH,
                        Environment.DIRECTORY_MOVIES
                    )

                    MediaTypes.Image -> put(
                        MediaStore.Images.Media.RELATIVE_PATH,
                        Environment.DIRECTORY_DCIM
                    )
                }
            } else {
                when (fileTypes.mediaTypes) {
                    MediaTypes.Video -> {
                        val targetDir =
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES)
                                .also { if (it.exists()) it.mkdirs() }
                        val targetFile =
                            File(targetDir, "${System.currentTimeMillis()}.${fileTypes.extension}")
                        put(MediaStore.Video.Media.DATA, targetFile.absolutePath)
                    }

                    MediaTypes.Image -> {
                        val targetDir =
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
                                .also { if (it.exists()) it.mkdirs() }
                        val targetFile =
                            File(targetDir, "${System.currentTimeMillis()}.${fileTypes.extension}")
                        put(MediaStore.Images.Media.DATA, targetFile.absolutePath)
                    }
                }
            }
        }
        val insertUri = try {
            when (fileTypes.mediaTypes) {
                MediaTypes.Video -> resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, cv)
                MediaTypes.Image -> resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, cv)
            }
        } catch (e: SecurityException) {
            Timber.d("insert failed due to permission denial")
            return@withContext null
        }
        if (insertUri == null) {
            Timber.d("insert failed null uri")
            return@withContext null
        }
        try {
            resolver.openOutputStream(insertUri)?.use { out ->
                try {
                    file.inputStream().copyTo(out)
                } catch (e: IOException) {
                    Timber.d("writing input stream failed due to i/o issue")
                    return@withContext null
                }
                runCatching {
                    MediaScannerConnection
                        .scanFile(context, arrayOf(insertUri.path), arrayOf(fileTypes.mimeType), null)
                }
            }
        } catch (e: FileNotFoundException) {
            Timber.d("file not found")
            return@withContext null
        }
        return@withContext insertUri
    }

    private fun getFileTypes(file: File): FileTypes? {
        return when (val extension = file.extension.lowercase()) {
            "png", "webp", "gif" -> FileTypes(
                MediaTypes.Image,
                "image/$extension",
                file,
                extension
            )

            "jpg", "jpeg", "jpe", "jfif" -> FileTypes(
                MediaTypes.Image,
                "image/jpeg",
                file,
                extension
            )

            "bmp" -> FileTypes(MediaTypes.Image, "image/x-ms-bmp", file, extension)

            "mp4", "m4v" -> FileTypes(MediaTypes.Video, "video/mp4", file, extension)
            "mov" -> FileTypes(MediaTypes.Video, "video/quicktime", file, extension)
            "avi" -> FileTypes(MediaTypes.Video, "video/x-msvideo", file, extension)
            "3gp", "3gpp" -> FileTypes(MediaTypes.Video, "video/3gpp", file, extension)
            "3g2", "3gpp2" -> FileTypes(MediaTypes.Video, "video/3gpp2", file, extension)
            "flv" -> FileTypes(MediaTypes.Video, "video/x-flv", file, extension)
            "wmv" -> FileTypes(MediaTypes.Video, "video/x-ms-wmv", file, extension)
            "m3u8" -> FileTypes(MediaTypes.Video, "application/x-mpegURL", file, extension)

            else -> null
        }
    }

    private data class FileTypes(
        val mediaTypes: MediaTypes,
        val mimeType: String,
        val file: File,
        val extension: String,
    )

    private enum class MediaTypes {
        Image, Video
    }


}