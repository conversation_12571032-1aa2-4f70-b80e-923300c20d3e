package com.socialplay.gpark.util

import android.content.Context
import android.media.AudioManager
import android.media.SoundPool
import android.os.Build
import timber.log.Timber


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/05/10
 *     desc   :
 *
 */
class SoundPlayer() {
    private var soundPool: SoundPool? = null
    private val soundMap = HashMap<Int, Int>()

    init {
        // 版本兼容
        soundPool = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            SoundPool.Builder().setMaxStreams(MAX_SOUNDS).build()
        } else {
            //第一个参数是可以支持的声音数量，第二个是声音类型，第三个是声音品质
            SoundPool(MAX_SOUNDS, AudioManager.STREAM_MUSIC, 0)
        }
    }

    /**
     * 播放音频
     * @param resId 音频文件 R.raw.xxx
     * @param repeatTime  循环模式：0表示循环一次，-1表示一直循环，其他表示数字+1表示当前数字对应的循环次
     */

    fun play(context: Context, resId: Int, repeatTime: Int) {
        val soundID = soundPool!!.load(context, resId, 1)
        // 该方法防止sample not ready错误
        soundPool!!.setOnLoadCompleteListener { soundPool: SoundPool, sampleId: Int, status: Int ->
            val streamId = soundPool.play(
                soundID,  //声音id
                1f,  //左声道：0.0f ~ 1.0f
                1f,  //右声道：0.0f ~ 1.0f
                1,  //播放优先级：0表示最低优先级
                repeatTime,  //循环模式：0表示循环一次，-1表示一直循环，其他表示数字+1表示当前数字对应的循环次
                1f
            ) //播放速度：1是正常，范围从0~2
            soundMap[resId] = streamId
        }
    }


    /**
     * 暂停
     * @param resId
     */
    fun pause(resId: Int) {
        if (soundPool != null) {
            val mStreamID = soundMap[resId]
            if (mStreamID != null) {
                soundPool?.pause(mStreamID)
            }
        }
    }

    /**
     * 继续
     * @param resId
     */
    fun resume(resId: Int) {
        if (soundPool != null) {
            val mStreamID = soundMap[resId]
            if (mStreamID != null) {
                soundPool?.resume(mStreamID)
            }
        }
    }

    /**
     * 停止
     * @param resId
     */
    fun stop(resId: Int) {
        if (soundPool != null) {
            val mStreamID = soundMap[resId]
            if (mStreamID != null) {
                soundPool?.stop(mStreamID)
            }
        }
    }

    /**
     * 资源释放
     */
    fun release() {
        Timber.d(TAG+ "Cleaning resources..")
        if (soundPool != null) {
            soundPool?.autoPause()
            soundPool?.release()
        }
    }

    companion object {
        private const val TAG = "SoundPlayer"
        private const val MAX_SOUNDS = 3
    }
}