package com.socialplay.gpark.util

import android.Manifest
import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.database.sqlite.SQLiteException
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.StrictMode
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.text.TextUtils
import androidx.core.content.ContextCompat
import java.io.File
import java.lang.reflect.Method

/**
 * file和uri相互转化工具类
 */
object FileUriUtil {

    /**
     * 获取分享的图片、文件、音频uri
     * Get file uri
     * @param context context
     * @param shareContentType TEXT IMAGE AUDIO VIDEO FILE
     * @param file file
     * @return Uri
     */
    fun getFileUri(context: Context, shareContentType: String, file: File): Uri? {
        var type = shareContentType
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return null
        }
        var uri: Uri?
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            uri = Uri.fromFile(file)
        } else {
            if (TextUtils.isEmpty(type)) {
                type = "*/*"
            }
            //TODO type需要改成集成分享之后的值
            uri = when (type) {
                "image" -> getImageContentUri(context, file)
                "video" -> getVideoContentUri(context, file)
                "audio" -> getAudioContentUri(context, file)
                "file"  -> getFileContentUri(context, file)
                else    -> {
                    null
                }
            }
        }
        if (uri == null) {
            uri = forceGetFileUri(file)
        }
        return uri
    }

    /**
     * forceGetFileUri
     * @param shareFile shareFile
     * @return Uri
     */
    @SuppressLint("DiscouragedPrivateApi")
    private fun forceGetFileUri(shareFile: File): Uri? {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                val rMethod: Method =
                    StrictMode::class.java.getDeclaredMethod("disableDeathOnFileUriExposure")
                rMethod.invoke(null)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return Uri.parse("file://" + shareFile.absolutePath)
    }


    /**
     * Gets the content:// URI from the given corresponding path to a file
     *
     * @param context context
     * @param imageFile imageFile
     * @return content Uri
     */
    @SuppressLint("Range")
    private fun getImageContentUri(context: Context, imageFile: File): Uri? {
        var uri: Uri? = null
        val filePath: String = imageFile.absolutePath
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI, arrayOf(
                    MediaStore.Images.Media._ID
                ), MediaStore.Images.Media.DATA + "=? ", arrayOf(filePath), null
            )

            if (cursor != null && cursor.moveToFirst()) {
                val id: Int = cursor.getInt(cursor.getColumnIndex(MediaStore.MediaColumns._ID))
                val baseUri: Uri = Uri.parse("content://media/external/images/media")
                uri = Uri.withAppendedPath(baseUri, "" + id)
            }
        } catch (e: SQLiteException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
        if (uri == null) {
            val values = ContentValues()
            values.put(MediaStore.Images.Media.DATA, filePath)
            uri =
                context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
        }
        return uri
    }

    /**
     * Gets the content:// URI from the given corresponding path to a file
     *
     * @param context context
     * @param videoFile videoFile
     * @return content Uri
     */
    @SuppressLint("Range")
    private fun getVideoContentUri(context: Context, videoFile: File): Uri? {
        var uri: Uri? = null
        val filePath: String = videoFile.absolutePath
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI, arrayOf(
                    MediaStore.Video.Media._ID
                ), MediaStore.Video.Media.DATA + "=? ", arrayOf(filePath), null
            )
            if (cursor != null && cursor.moveToFirst()) {
                val id: Int = cursor.getInt(cursor.getColumnIndex(MediaStore.MediaColumns._ID))
                val baseUri: Uri = Uri.parse("content://media/external/video/media")
                uri = Uri.withAppendedPath(baseUri, "" + id)
            }
        } catch (e: SQLiteException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }

        if (uri == null) {
            val values = ContentValues()
            values.put(MediaStore.Video.Media.DATA, filePath)
            uri =
                context.contentResolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, values)
        }
        return uri
    }


    /**
     * Gets the content:// URI from the given corresponding path to a file
     *
     * @param context context
     * @param audioFile audioFile
     * @return content Uri
     */
    @SuppressLint("Range")
    private fun getAudioContentUri(context: Context, audioFile: File): Uri? {
        var uri: Uri? = null
        val filePath: String = audioFile.absolutePath
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, arrayOf(
                    MediaStore.Audio.Media._ID
                ), MediaStore.Audio.Media.DATA + "=? ", arrayOf(filePath), null
            )
            if (cursor != null && cursor.moveToFirst()) {
                val id: Int = cursor.getInt(cursor.getColumnIndex(MediaStore.MediaColumns._ID))
                val baseUri: Uri = Uri.parse("content://media/external/audio/media")
                uri = Uri.withAppendedPath(baseUri, "" + id)
            }
        } catch (e: SQLiteException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }

        if (uri == null) {
            val values = ContentValues()
            values.put(MediaStore.Audio.Media.DATA, filePath)
            uri =
                context.contentResolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values)
        }
        return uri
    }

    /**
     * getFileContentUri
     * @param context context
     * @param file file
     * @return Uri
     */
    @SuppressLint("Range")
    private fun getFileContentUri(context: Context, file: File): Uri? {
        val volumeName = "external"
        val filePath: String = file.absolutePath
        val projection = arrayOf(MediaStore.Files.FileColumns._ID)
        var uri: Uri? = null
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(
                MediaStore.Files.getContentUri(volumeName),
                projection,
                MediaStore.Images.Media.DATA + "=? ",
                arrayOf(filePath),
                null
            )
            if (cursor != null && cursor.moveToFirst()) {
                val id: Int = cursor.getInt(cursor.getColumnIndex(MediaStore.Files.FileColumns._ID))
                uri = MediaStore.Files.getContentUri(volumeName, id.toLong())
            }
        } catch (e: SQLiteException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
        }
        return uri
    }
    /*---------------------------------------------------------以上代码是关于分享的图片，文件，视音频的uri获取--------------------------------------------*/

    /**
     * 根据uri获取filePath
     */
    fun getFilePathByUri(context: Context?, uri: Uri?): String? {
        context ?: return null
        uri ?: return null
        val path: String?
        // 以 file:// 开头的
        if (ContentResolver.SCHEME_FILE == uri.scheme) {
            path = uri.path
            return path
        }
        // 4.4及之后的是以 content:// 开头的，比如 content://com.android.providers.media.documents/document/image%3A235700
        if (DocumentsContract.isDocumentUri(context, uri)) {
            when {
                isExternalStorageDocument(uri) -> {
                    // ExternalStorageProvider
                    val docId: String = DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).toTypedArray()
                    val type = split[0]
                    if ("primary".equals(type, ignoreCase = true)) {
                        path = Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                        return path
                    }
                }
                isDownloadsDocument(uri)       -> {
                    // DownloadsProvider
                    val id: String = DocumentsContract.getDocumentId(uri)
                    val contentUri: Uri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"),
                        id.toLong()
                    )
                    path = getDataColumn(context, contentUri, null, null)
                    return path
                }
                isMediaDocument(uri)           -> {
                    // MediaProvider
                    val docId: String = DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).toTypedArray()
                    val contentUri = when (split[0]) {
                        "image" -> MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        "video" -> MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                        "audio" -> MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                        else    -> null
                    }
                    val selection = "_id=?"
                    val selectionArgs = arrayOf(split[1])
                    contentUri ?: return null
                    path = getDataColumn(context, contentUri, selection, selectionArgs)
                    return path
                }
            }
        }
        return null
    }

    private fun getDataColumn(
        context: Context,
        uri: Uri,
        selection: String?,
        selectionArgs: Array<String>?
    ): String? {
        var cursor: Cursor? = null
        val column = "_data"
        val projection = arrayOf(column)
        try {
            cursor = context.contentResolver.query(uri, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(columnIndex)
            }
        } finally {
            cursor?.close()
        }
        return null
    }

    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }
}