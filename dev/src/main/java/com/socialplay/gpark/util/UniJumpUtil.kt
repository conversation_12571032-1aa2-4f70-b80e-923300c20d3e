package com.socialplay.gpark.util

import android.content.pm.ActivityInfo
import android.net.Uri
import androidx.fragment.app.Fragment
import com.google.android.exoplayer2.MediaItem
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.main.MainViewModel
import org.koin.core.context.GlobalContext

object UniJumpUtil {

    /**
     * 跳转类型
     * 1 文本
     * 2 web跳转
     * 3 游戏id
     * 4 ugc游戏id
     * 5 游戏圈
     * 6 帖子详情
     * 7 视频链接
     */
    const val TYPE_TEXT = 1
    const val TYPE_WEB = 2
    const val TYPE_GAME_DETAIL = 3
    const val TYPE_LAUNCH_UGC_GAME = 4
    const val TYPE_GAME_CIRCLE = 5
    const val TYPE_GAME_CIRCLE_DETAIL = 6
    const val TYPE_VIDEO = 7

    // 角色编辑器 带数据
    const val TYPE_AVATAR_EDITOR = 14

    // 家庭合影
    const val TYPE_FAMILY_PHOTO = 8

    // 角色每日任务
    const val TYPE_DAILY_TASK_REWARD = 20

    // key
    const val KEY_VIDEO_FROM = "videoPlayAnalyticsFrom"
    const val KEY_CATEGORY_ID = "category_id"
    const val KEY_LOCAL_UNI_FLAG = "local_uni_flag"

    /**
     * 使用打开activity的方式跳转scheme
     * @param config 运营位
     * @param source 来源 [com.socialplay.gpark.function.deeplink.LinkData]
     * @param categoryId 资源位，每个运营位的固定值，不跳转游戏的话则用不到
     * @param extras 额外参数，会拼接在uri后面
     *
     * @return [Boolean] 是否成功处理
     */
    fun jump(
        fragment: Fragment,
        config: UniJumpConfig,
        source: String,
        categoryId: Int,
        extras: Map<String, String>?
    ): Boolean {
        when (config.jumpType) {
            TYPE_TEXT -> {
                return true
            }

            TYPE_WEB -> { //web跳转
                return jumpWeb(fragment, config)
            }

            TYPE_VIDEO -> {
                config.param1?.let {
                    return jumpVideoWithUri(
                        fragment,
                        it,
                        config.videoExtra?.isLandscapeVideo == true,
                        extras
                    )
                } ?: return false
            }

            else -> { //其他的默认都是根据Scheme跳转的
                val uri = assembleSchemeUri(config, categoryId, extras)
                uri ?: return false
                MetaRouter.Scheme.jumpScheme(fragment, uri, source)
                return true
            }
        }
    }

    /**
     * 使用直接调用DeepLinkHandler的方式跳转scheme
     * @param config 运营位
     * @param source 来源 [com.socialplay.gpark.function.deeplink.LinkData]
     * @param categoryId 资源位，每个运营位的固定值，不跳转游戏的话则用不到
     * @param extras 额外参数，会拼接在uri后面
     *
     * @return [Boolean] 是否成功处理
     */
    fun jump(
        fragment: Fragment,
        config: UniJumpConfig,
        source: String,
        categoryId: Int,
        mainViewModel: MainViewModel,
        extras: Map<String, String>?
    ): Boolean {
        when (config.jumpType) {
            TYPE_TEXT -> {
                return true
            }

            TYPE_WEB -> { //web跳转
                return jumpWeb(fragment, config)
            }

            TYPE_VIDEO -> {
                config.param1?.let {
                    return jumpVideoWithUri(
                        fragment,
                        it,
                        config.videoExtra?.isLandscapeVideo == true,
                        extras
                    )
                } ?: return false
            }
            TYPE_AVATAR_EDITOR -> {
                MetaRouter.MobileEditor.avatarEditor(
                    context = fragment.requireContext(),
                    categoryId = categoryId,
                    data = config.param1
                )
                return true
            }
            else -> { //其他的默认都是根据Scheme跳转的
                if (config.jumpType == TYPE_DAILY_TASK_REWARD && (config.param1?.startsWith("http") == true)) {
                    val jumpSuccess = jumpWeb(fragment, config)
                    if (jumpSuccess) {
                        return true
                    }
                }
                val uri = assembleSchemeUri(config, categoryId, extras)
                uri ?: return false
                return MetaRouter.Scheme.jumpScheme(fragment, mainViewModel, uri, source)
            }
        }
    }

    /**
     * 组装跳转scheme
     */
    private fun assembleSchemeUri(
        config: UniJumpConfig,
        categoryId: Int,
        extras: Map<String, String>?
    ): Uri? {
        val schemeUri = runCatching {
            val uri = Uri.parse(config.param1)
            val uriBuilder = uri.buildUpon()
            if (uri.getQueryParameter(KEY_CATEGORY_ID).isNullOrEmpty()) {
                uriBuilder.appendQueryParameter(KEY_CATEGORY_ID, "$categoryId")
            }
            if (uri.getQueryParameter(KEY_LOCAL_UNI_FLAG).isNullOrEmpty()) {
                uriBuilder.appendQueryParameter(KEY_LOCAL_UNI_FLAG, "1")
            }
            extras?.keys?.forEach {
                // 遍历增加参数，要注意如果为json的参数需要Uri.encode一下，否则识别不了
                val value = extras[it]
                if (it.isNotEmpty() && !value.isNullOrEmpty() && uri.getQueryParameter(it)
                        .isNullOrEmpty()
                ) {
                    uriBuilder.appendQueryParameter(it, value)
                }
            }
            uriBuilder.build()
        }.getOrElse { Uri.parse(config.param1) }
        return schemeUri
    }

    /**
     * 跳转web
     */
    private fun jumpWeb(
        fragment: Fragment,
        config: UniJumpConfig
    ): Boolean {
        if (!config.param1.isNullOrBlank()) {
            // 是否在app中开启落地页0否，1是
            if (config.param2 == UniJumpConfig.WEB_OUTSIDE) {
                MetaRouter.Web.navigate(
                    fragment,
                    url = config.param1,
                    isWebOutside = true,
                    openInsideWhenOutsideFailed = false
                )
            } else if (config.param2 == UniJumpConfig.WEB_INSIDE) {
                MetaRouter.Web.navigate(fragment, url = config.param1)
            } else {
                return false
            }
            return true
        }
        return false
    }

    /**
     * 跳转视频详情页
     */
    private fun jumpVideoWithUri(
        fragment: Fragment,
        videoUrl: String,
        landscape: Boolean,
        extras: Map<String, String>?
    ): Boolean {
        val playerController = GlobalContext.get().get<SharedVideoPlayerControllerInteractor>()
        val toRestoreUri = MediaItem.fromUri(videoUrl)
        playerController.setMediaItem(toRestoreUri)
        playerController.mute(false)
        val orientation =
            if (landscape) ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE else ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
        val analyticsFrom: String = extras?.get(KEY_VIDEO_FROM) ?: ""
        MetaRouter.Video.fullScreenPlayer(fragment, analyticsFrom)
        return true
    }

}