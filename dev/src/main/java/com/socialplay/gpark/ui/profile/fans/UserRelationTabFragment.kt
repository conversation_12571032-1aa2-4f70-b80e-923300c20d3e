package com.socialplay.gpark.ui.profile.fans

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isInvisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.databinding.FragmentUserRelationTabBinding
import com.socialplay.gpark.databinding.TabIndicatorUserRelationBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

class UserRelationTabFragment :
    BaseFragment<FragmentUserRelationTabBinding>(R.layout.fragment_user_relation_tab) {

    private val vm: UserFansTabViewModel by fragmentViewModel()
    private val args: UserFansTabFragmentDialogArgs by args()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUserRelationTabBinding? {
        return FragmentUserRelationTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        if (args.isMe) {
            binding.ibSetting.visible()
            binding.ibSetting.setOnAntiViolenceClickListener {
                MetaRouter.Control.navigate(this, R.id.privacySetting)
            }
        } else {
            binding.ibSetting.gone()
        }

        vm.oldState.let {
            initTab(it.tabItems, it.count)
            binding.vp.setCurrentItem(getPost(it.type), false)
            vm.cancelJump2Fans()
        }
        vm.onEach(
            UserFansTabModelState::tabItems,
            UserFansTabModelState::count,
        ) { tabItems, count ->
            val titles = getTitle(tabItems, count)
            titles.forEachIndexed { index, title ->
                binding.tl.getTabAt(index)?.apply {
                    customView?.findViewById<TextView>(R.id.tvThin)?.text = title.first
                    customView?.findViewById<TextView>(R.id.tvThinNum)?.text = title.second
                    customView?.findViewById<TextView>(R.id.tvBold)?.text = title.first
                    customView?.findViewById<TextView>(R.id.tvBoldNum)?.text = title.second
                }
            }
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        tab.customView?.findViewById<TextView>(R.id.tvThin)?.isInvisible = select
        tab.customView?.findViewById<TextView>(R.id.tvThinNum)?.isInvisible = select
        tab.customView?.findViewById<TextView>(R.id.tvBold)?.isInvisible = !select
        tab.customView?.findViewById<TextView>(R.id.tvBoldNum)?.isInvisible = !select
    }

    /**
     * 当前选中位置
     */
    private fun getPost(type: Int): Int {
        return if (PandoraToggle.isIMEntrance) {
            type
        } else {
            type - 1
        }
    }

    private fun initTab(tabs: List<CommonTabItem>, count: RelationCountResult) {
        binding.tl.addOnTabSelectedListener(
            viewLifecycleOwner,
            object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab) {
                    setTabSelectUI(tab, true)
                }

                override fun onTabUnselected(tab: TabLayout.Tab) {
                    setTabSelectUI(tab, false)
                }

                override fun onTabReselected(tab: TabLayout.Tab) {}
            })
        val titles = getTitle(tabs, count)
        binding.vp.adapterAllowStateLoss = CommonTabStateAdapter(
            tabs.map { it.fragmentInvoke },
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        TabLayoutMediator(
            binding.tl,
            binding.vp
        ) { tab: TabLayout.Tab, position: Int ->
            val tabBinding = TabIndicatorUserRelationBinding.inflate(layoutInflater)
            tabBinding.tvThin.text = titles[position].first
            tabBinding.tvThinNum.text = titles[position].second
            tabBinding.tvBold.text = titles[position].first
            tabBinding.tvBoldNum.text = titles[position].second

            tab.customView = tabBinding.root
        }.attach(viewLifecycleOwner)
    }

    private fun getTitle(
        tabItems: List<CommonTabItem>,
        count: RelationCountResult
    ): List<Pair<String, String>> {
        val titles = tabItems.map {
            when (it.type) {
                UserFansItemFragment.TYPE_FANS, UserFansItemFragment.TYPE_FOLLOWING -> {
                    getString(it.title) to UnitUtil.formatKMCount(
                        if (it.type == UserFansItemFragment.TYPE_FANS) {
                            count.reverseCount
                        } else {
                            count.forwardCount
                        }
                    )
                }

                UserFansItemFragment.TYPE_FRIEND -> {
                    getString(it.title) to UnitUtil.formatKMCount(count.friendCount)
                }

                else -> {
                    "" to ""
                }
            }
        }
        return titles
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_USER_FANS_TAB
}