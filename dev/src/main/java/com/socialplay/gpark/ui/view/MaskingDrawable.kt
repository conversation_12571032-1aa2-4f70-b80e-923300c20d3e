package com.socialplay.gpark.ui.view

import android.graphics.*
import android.graphics.drawable.Drawable

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/10/11
 * desc   :
 * </pre>
 */


class MaskingDrawable(private val origin: Drawable) : Drawable() {

    private val srcPath = Path()
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    init {
        paint.color = Color.parseColor("#ffffffff")
    }

    fun applyPath(path: Path) {
        srcPath.set(path)
    }

    override fun draw(canvas: Canvas) {
        origin.bounds = bounds
        if (srcPath.isEmpty) {
            origin.draw(canvas)
        } else {
            val saveCount = canvas.saveLayer(0F, 0F, canvas.width.toFloat(), canvas.height.toFloat(), paint)
            origin.draw(canvas)
            paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
            canvas.drawPath(srcPath, paint)
            paint.xfermode = null
            canvas.restoreToCount(saveCount)
        }
    }

    override fun setAlpha(alpha: Int) {
        origin.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        origin.colorFilter = colorFilter
    }

    override fun getOpacity(): Int {
        return origin.opacity
    }
}