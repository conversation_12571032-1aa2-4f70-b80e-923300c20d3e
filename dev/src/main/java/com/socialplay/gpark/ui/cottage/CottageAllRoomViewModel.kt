package com.socialplay.gpark.ui.cottage

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import org.koin.android.ext.android.get

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/10/09
 *     desc   :
 *
 */
data class CottageAllRoomViewModelState(
    val roomList: List<House> = emptyList(),
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val pageNum: Int = 0,
    val totalPage: Int = 1
) : MavericksState

class CottageAllRoomViewModel(
    private val repository: IMetaRepository,
    initialState: CottageAllRoomViewModelState
) : BaseViewModel<CottageAllRoomViewModelState>(initialState) {

    companion object :
        KoinViewModelFactory<CottageAllRoomViewModel, CottageAllRoomViewModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: CottageAllRoomViewModelState
        ): CottageAllRoomViewModel {
            return CottageAllRoomViewModel(get(), state)
        }

    }

    init {
        refresh()
    }

    fun refresh() = withState { oldState ->
        repository.getCottageRoomList(1)
            .execute {
                val data = it.invoke()
                val list = data?.houses ?: emptyList()
                copy(
                    roomList = list,
                    loadMore = it.map {
                        LoadMoreState(data?.currentPage == data?.totalPage)
                    },
                    pageNum = 1,
                    totalPage = data?.totalPage ?: 1
                )
            }
    }

    fun loadMore() = withState { oldState ->
        if (oldState.roomList.isEmpty()) {
            return@withState
        }
        if (oldState.loadMore is Loading) {
            return@withState
        }
        if (oldState.pageNum == oldState.totalPage) {
            return@withState
        }
        repository.getCottageRoomList(oldState.pageNum + 1).execute { result ->
            val data = result.invoke()
            val list =data?.houses ?: emptyList()
            copy(roomList = (oldState.roomList + list), loadMore = result.map {
                LoadMoreState(data?.currentPage == data?.totalPage)
            }, pageNum = oldState.pageNum + 1)
        }

    }
}