package com.socialplay.gpark.ui.mgs.record

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.SystemClock
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.net.toUri
import com.bin.cpbus.CpEventBus
import com.bumptech.glide.Glide
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.SimpleSharePlatInfo
import com.socialplay.gpark.data.model.post.CommonPostPublishReceiveEvent
import com.socialplay.gpark.data.model.post.CommonPostPublishSendEvent
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.DialogScreenRecordEndGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.record.ScreenRecordAnalytics
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.GlobalSharePlatformHelper
import com.socialplay.gpark.function.share.MetaShare
import com.socialplay.gpark.function.share.RecorderShareHelper
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * @des:游戏录屏结束后弹窗 - new
 * @author: lijunjia
 * @date: 2022/1/4 17:34
 */
class GameScreenRecordEndDialog(
    private val videoPath: String,
    private val videoUri: Uri,
    private val gameId: String,
    private val gamePackageName: String,
    private val gameName: String = "",
    private val activity: Activity,
    private val metaApp: Application,
    private val dismissCallback: (() -> Unit)? = null
) : Dialog(activity, android.R.style.Theme_Dialog) {

    private val requestId = "GameScreenRecordEndDialog_${SystemClock.elapsedRealtime()}"

    private lateinit var binding: DialogScreenRecordEndGameBinding
    private val accountInteractor = GlobalContext.get().get<AccountInteractor>()
    private val publishPostInteractor = GlobalContext.get().get<PublishPostInteractor>()

    private val dpDialogWidth by lazy { 314.dp }

    private val metaKV = GlobalContext.get().get<MetaKV>()
    private val scope = MainScope()
    private var job: Job? = null

    init {
        initView()
    }

    companion object {
        const val YOUTUBE_ID = "gpark_official"
        const val TIKTOK_ID = "gpark_official"
        val DISCORD_ID = BuildConfig.GAME_SCREEN_RECORD_DISCORD_ID
        const val CHANNEL_SCREEN_ITEM_COUNT = 5.5F

        fun create(
            videoPath: String,
            videoUri: Uri,
            gameId: String,
            gamePackageName: String,
            activity: Activity,
            metaApp: Application,
            dismissCallback: (() -> Unit)?
        ) =
            GameScreenRecordEndDialog(
                videoPath,
                videoUri,
                gameId,
                gamePackageName,
                "",
                activity,
                metaApp,
                dismissCallback
            )
    }


    private fun initDialogParams() {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        binding = DialogScreenRecordEndGameBinding.inflate(LayoutInflater.from(metaApp))
        val gravity: Int = Gravity.CENTER
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            dimAmount = 0.75f,
            gravity = gravity,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    override fun onDetachedFromWindow() {
        binding.simpleVideoView.release()
        super.onDetachedFromWindow()
    }


    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
        setOnDismissListener {
            dismissCallback?.invoke()
        }

        binding.clParentContent.setSize(
            (ScreenUtil.getScreenWidth(metaApp) * 0.8).toInt()
                .coerceAtMost(ScreenUtil.dp2px(metaApp, 314f)),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        binding.ivClose.setOnAntiViolenceClickListener {
            ScreenRecordAnalytics.eventRecordFinishDialogClose(gameId)
            dismiss()
        }
        initBottomShareInfo()
        initShare()
        initVideoVIew()
        RecorderShareHelper.initBinding(binding)
    }

    private fun initVideoVIew() {
        kotlin.runCatching {
            Glide.with(context).load(videoPath)
//                .apply(RequestOptions().transform(CenterCrop(), RoundedCorners(10.dp)))
                .into(binding.simpleVideoView.ivCover)
        }
        binding.simpleVideoView.clickCallback = {

        }
        binding.simpleVideoView.initVideoPath(videoPath)


    }

    private fun shareToPlatform(shareChannelInfo: ShareWrapper.RecordShareChannelInfo) {
        binding.simpleVideoView.pauseAndShowCover()
        scope.launch(Dispatchers.Main) {
            val platform = shareChannelInfo.sharePlatform
            if (platform != null) {
                Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_SHARE_CLICK) {
                    put("platform", platform.platform)
                    if (!GlobalSharePlatformHelper.checkInstallation(activity, platform.platform)) {
                        ToastUtil.showShort(activity, R.string.not_installed)
                    } else {
                        scope.launch {
                            val tTaiInteractor: TTaiInteractor = GlobalContext.get().get()
                            val config =
                                tTaiInteractor.getSceneShareConfig("inGameRecording").singleOrNull()
                            if (!activity.isFinishing) {
                                MetaShare.share(
                                    activity, ShareData(
                                        requestId,
                                        platform.platform,
                                        ShareHelper.MODE_SINGLE_VIDEO,
                                        videos = listOf(videoPath),
                                        extraTitle = config?.titleByPlatform(platform.platform),
                                        extraContent = config?.descByPlatform(platform.platform),
                                        extraTags = config?.tagsByPlatform(platform.platform),
                                        gameId = MWBizBridge.currentGameId(),
                                        from = ThirdShareParams.FROM_RECORD_END_DIALOG
                                    )
                                )
                            }
                        }
                    }
                }
            } else {
                val componentNames = shareChannelInfo.platform!!
                componentNames.forEachIndexed { index, componentName ->
                    Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_SHARE_CLICK) {
                        put("platform", shareChannelInfo.name)
                    }
                    if (!InstallUtil.isAppInstalled(activity, componentName.packageName)) {
                        if (componentNames.lastIndex == index) {
                            ToastUtil.showShort(activity, R.string.not_installed)
                        }
                    } else {
                        kotlin.runCatching {
                            MetaShare.shareVideo(
                                activity,
                                videoUri,
                                shareChannelInfo,
                                index,
                                getShareText()
                            )
                        }
                        return@launch
                    }
                }
            }
        }
    }

    private fun shareToCommunity() {
        binding.simpleVideoView.pauseAndShowCover()
        Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_SHARE_CLICK) {
            put("platform", "Community")
        }
        CpEventBus.post(
            CommonPostPublishSendEvent(
                requestId,
                PublishPostInteractor.METHOD_CAN_PUBLISH,
                gameId,
                publishPostInteractor.processName
            )
        )
        job?.cancel()
        job = scope.launch {
            delay(1_000)
            goPublishPost()
            job = null
        }
    }

    private fun getShareText(): String {
        val userName = accountInteractor.accountLiveData.value?.nickname ?: ""
        val curDateStr = SimpleDateFormat("yyyy-MM-dd", Locale.ROOT).run {
            format(Date(System.currentTimeMillis()))
        }
        return context.getString(R.string.share_video_user_name_game_name_date, userName, gameName, curDateStr)
    }

    private fun initShare() {
        val channelList: MutableList<ShareWrapper.RecordShareChannelInfo> = ShareWrapper.getRecordShareChannelList()

        binding.rvShare.adapter =
            RecordShareChannelAdapter(getChannelMinWidth(channelList.size)).apply {
                setList(channelList)
                setOnItemClickListener { _, position ->
                    val item = getItem(position)
                    if (item.sharePlatform != null || !item.platform.isNullOrEmpty()) {
                        shareToPlatform(item)
                    } else if (item is ShareWrapper.RecordShareChannelInfo.Community) {
                        shareToCommunity()
                    }
                }
            }
    }

    private fun getChannelMinWidth(size: Int): Int {
        return if (size > CHANNEL_SCREEN_ITEM_COUNT) {
            ((dpDialogWidth - 16.dp).toFloat() / CHANNEL_SCREEN_ITEM_COUNT).toInt()
        } else {
            ((dpDialogWidth - 32.dp).toFloat() / size).toInt()
        }
    }

    private fun getConfigInfo(): SimpleSharePlatInfo? {
        return GsonUtil.gsonSafeParse(metaKV.tTaiKV.recordSharePlatformConfig)
    }

    private fun initBottomShareInfo() {
        val configInfo = getConfigInfo()
        binding.tvContentDiscord.text = configInfo?.discord ?: DISCORD_ID
        binding.tvContentTiktok.text = configInfo?.tiktok ?: TIKTOK_ID
        binding.tvContentYoutube.text = configInfo?.youtube ?: YOUTUBE_ID
        binding.tvContentDiscord.setOnAntiViolenceClickListener {
            scope.launch {
                ClipBoardUtil.setClipBoardContent(binding.tvContentDiscord.text.toString(), activity)
            }
            ToastUtil.showShort(activity, R.string.copy_success)
        }

        binding.tvContentYoutube.setOnAntiViolenceClickListener {
            scope.launch {
                ClipBoardUtil.setClipBoardContent(binding.tvContentYoutube.text.toString(), activity)
            }
            ToastUtil.showShort(activity, R.string.copy_success)
        }
        binding.tvContentTiktok.setOnAntiViolenceClickListener {
            scope.launch {
                ClipBoardUtil.setClipBoardContent(binding.tvContentTiktok.text.toString(), activity)
            }
            ToastUtil.showShort(activity, R.string.copy_success)
        }
    }

    override fun dismiss() {
        binding.rvShare.adapter = null
        super.dismiss()
    }

    override fun onStart() {
        super.onStart()
        registerHermes()
    }

    override fun onStop() {
        job?.cancel()
        job = null
        unregisterHermes()
        super.onStop()
    }

    private fun goPublishPost() {
        scope.launch(Dispatchers.Main) {
            MetaRouter.Main.openPostPublishFromGame(
                context,
                content = null,
                briefMedias = arrayListOf(videoPath.toString()),
                completeCardList = null,
                tagIdList = null,
                clearTopBackFeed = false,
                MWBizBridge.currentGameId(),
                MWBizBridge.currentGamePkg(),
                enableOutfitShare = false
            )
        }
    }

    @Subscribe
    fun onEvent(event: CommonPostPublishReceiveEvent) {
        if (event.processName != publishPostInteractor.processName) return
        if (event.requestId != requestId) return
        if (event.method != PublishPostInteractor.METHOD_CAN_PUBLISH) return
        val result = event.data as? Boolean ?: return
        job?.cancel()
        job = null
        if (result) {
            goPublishPost()
        } else {
            metaApp.toast(R.string.post_publishing_tip)
        }
    }
}