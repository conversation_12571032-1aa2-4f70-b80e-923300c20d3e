package com.socialplay.gpark.ui.im.groupchat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.navArgs
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.databinding.FragmentHeGroupsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.toast

class HeGroupsListFragment :
    BaseRecyclerViewFragment<FragmentHeGroupsBinding>(R.layout.fragment_he_groups) {
    private val vm: GroupChatListViewModel by fragmentViewModel()
    private val args by navArgs<HeGroupsListFragmentArgs>()
    private val moreClickedListener = object : IItemClickedListener {
        override fun onMoreClicked(groupChatInfo: GroupChatInfo) {
            if(groupChatInfo.requestJoinStatus == GroupChatInfo.REQUEST_JOIN_STATUS_LOADING){
                return
            }
            if(groupChatInfo.joinStatus == GroupChatInfo.JOIN_STATUS_CAN_APPLY_JOIN){
                MetaRouter.IM.goGroupJoinRequestFragment(
                    this@HeGroupsListFragment,
                    groupChatInfo
                ) { groupInfo, result ->
                    if (groupInfo != null && result) {
                        vm.updateGroupInfo(groupInfo)
                    }
                }
            }
            if (groupChatInfo.joinStatus == GroupChatInfo.JOIN_STATUS_CAN_JOIN) {
                Analytics.track(
                    EventConstants.GROUP_JOIN,
                    "group_id" to groupChatInfo.id.toString()
                )
                // 无需申请, 可直接加入
                vm.joinGroupChat(groupChatInfo)
            }
        }

        override fun onItemClicked(groupChatInfo: GroupChatInfo) {
            MetaRouter.IM.goGroupProfileGuestFragment(
                this@HeGroupsListFragment,
                groupChatInfo
            ) { groupInfo, result ->
                if (groupInfo != null && result) {
                    vm.updateGroupInfo(groupInfo)
                }
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
    override val destId: Int = R.id.heGroupsPage
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentHeGroupsBinding? {
        return FragmentHeGroupsBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        glide = Glide.with(this)
        binding.titleBar.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.titleBar.setOnBackClickedListener {
            navigateUp()
        }
        binding.loadingView.setRetry {
            vm.getUserInfo(args.targetUid)
            vm.getGroups(args.targetUid, true)
        }

        vm.onEach(
            GroupChatListModelState::gender,
            uniqueOnly()
        ) { gender ->
            binding.titleBar.setTitle(
                getString(
                    if (gender == Gender.Female) {
                        R.string.her_groups_page_title
                    } else {
                        R.string.he_groups_page_title
                    }
                )
            )
        }

        vm.onAsync(GroupChatListModelState::groups, onFail = { _, _ ->
            binding.loadingView.showError()
        }, onLoading = {
            binding.loadingView.showLoading()
        }, onSuccess = {
            binding.loadingView.hide()
        })

        vm.onAsync(
            GroupChatListModelState::joinResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.loading_net_error)
                } else {
                    toast(R.string.toast_join_group_failed)
                }
            },
            onLoading = {
            },
            onSuccess = { joinResult ->
                val result = joinResult.second
                if (result.succeeded && result.data != null && !result.data!!.success.isNullOrEmpty()) {
                    vm.updateGroupInfo(joinResult.first.copy(
                        joinStatus = GroupChatInfo.JOIN_STATUS_JOINED,
                        requestJoinStatus = GroupChatInfo.REQUEST_JOIN_STATUS_DEFAULT
                    ))
                    toast(R.string.toast_join_group_success)
                } else {
                    val data = result.data
                    val defaultToast = if (data == null) {
                        getString(R.string.toast_join_group_failed)
                    } else if (!data.failExistMember.isNullOrEmpty()) {
                        getString(R.string.toast_join_group_failed_exist_member)
                    } else if (!data.failMaxGroupMember.isNullOrEmpty()) {
                        getString(R.string.toast_join_group_failed_members_full)
                    } else if (!data.failMaxJoinLimit.isNullOrEmpty()) {
                        getString(R.string.toast_join_group_failed_maximum_join_limit)
                    } else {
                        getString(R.string.toast_join_group_failed)
                    }
                    // 目前服务器端不给错误信息
                    toast(defaultToast)
                }
            })

        vm.setupRefreshLoading(
            GroupChatListModelState::groups,
            binding.loadingView,
            binding.refreshLayout,
            emptyMsg = getString(R.string.group_page_no_groups)
        ) {
            vm.getUserInfo(args.targetUid)
            vm.getGroups(args.targetUid, true)
        }

        vm.getUserInfo(args.targetUid)
        vm.getGroups(args.targetUid, true)
    }

    override fun epoxyController(): EpoxyController = simpleController(
        vm,
        GroupChatListModelState::refreshFlag,
        GroupChatListModelState::groups,
        GroupChatListModelState::loadMore,
    ) { refreshFlag, groups, loadMore ->
        if (groups is Success) {
            val groupList = groups.invoke()
            groupList.forEach { groupInfo ->
                add {
                    GroupChatItem(
                        groupInfo,
                        false,
                        moreClickedListener
                    ).id("HeGroupItem-$refreshFlag-${groupInfo.id}")
                }
            }
        }
        if (loadMore !is Uninitialized) {
            if (groups is Success && groups.invoke().isNotEmpty()) {
                loadMoreFooter(loadMore, idStr = "LoadMoreFooter-$refreshFlag", endText = "") {
                    vm.getGroups(args.targetUid, false)
                }
            }
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_HE_GROUPS
}