package com.socialplay.gpark.ui.view.shimmer

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.socialplay.gpark.R

class ShimmerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val shimmerDrawable: ShimmerDrawable = ShimmerDrawable()

    init {
        background = shimmerDrawable
        if (attrs != null) {
            var typedArray: TypedArray? = null
            try {

                val defaultShimmerWidth = TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP,
                    50F,
                    resources.displayMetrics
                )

                typedArray = context.obtainStyledAttributes(attrs, R.styleable.ShimmerView)

                val shimmerWidth = typedArray.getDimension(
                    R.styleable.ShimmerView_shimmer_width,
                    defaultShimmerWidth
                )

                shimmerDrawable.setShimmerWidth(shimmerWidth)
            } finally {
                typedArray?.recycle()
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        shimmerDrawable.startShimmer()
    }

    override fun onDetachedFromWindow() {
        shimmerDrawable.stopShimmer()
        super.onDetachedFromWindow()
    }

}