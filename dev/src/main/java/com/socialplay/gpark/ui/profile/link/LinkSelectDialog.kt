package com.socialplay.gpark.ui.profile.link

import android.os.Bundle
import android.os.Parcelable
import androidx.fragment.app.Fragment
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.databinding.DialogLinkSelectBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.deeplink.LinkData.Companion.SOURCE_LOCAL
import com.socialplay.gpark.ui.core.BaseRVBottomSheetDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/09/14
 *     desc   :
 *
 */
@Parcelize
data class LinkSelectArgs(
    val linkList: List<ProfileLinkInfo>,
    val uuid: String,
    val isMe: Boolean,
    val isFromBottom: Boolean,
) : Parcelable

class LinkSelectDialog : BaseRVBottomSheetDialogFragment() {

    companion object {
        fun show(
            fragment: Fragment,
            linkList: List<ProfileLinkInfo>,
            uuid: String,
            isMe: Boolean,
            isFromBottom: Boolean
        ) {
            val dialog = LinkSelectDialog()
            dialog.arguments = LinkSelectArgs(linkList, uuid, isMe, isFromBottom).asMavericksArgs()
            dialog.show(fragment.childFragmentManager, "LinkSelectDialog")
        }
    }

    override val binding by viewBinding(DialogLinkSelectBinding::inflate)
    private val args by args<LinkSelectArgs>()
    private val vm: LinkSelectViewModel by fragmentViewModel()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val itemListener = object : ILinkSelectListener {
        override fun getGlideOrNull(): RequestManager? {
            return glide
        }

        override fun click(item: ProfileLinkInfo) {
            parentFragment?.let {
                MetaRouterWrapper.ExternalLink.jump(
                    it,
                    item,
                    SOURCE_LOCAL
                )
            }
            Analytics.track(
                EventConstants.EVENT_LINK_CLICK,
                mapOf("userid" to args.uuid, "link" to item.url.orEmpty())
            )
        }
    }

    override fun init() {
        skipCollapsed()
        recyclerView.isVerticalScrollBarEnabled = args.linkList.size > 4
        recyclerView.setHeight(dp(80) * args.linkList.size.coerceAtMost(4))
        binding.ivCloseBtn.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        Analytics.track(
            EventConstants.EVENT_HOME_LINK_SHOW,
            "source" to (if (args.isFromBottom) "home" else "profile"),
            "user_type" to (if (args.isFromBottom || args.isMe) "owner" else "visitor")
        )
    }

    override fun epoxyController() = simpleController(
        vm,
        LinkSelectState::linkList
    ) {
        it.forEachIndexed { index, item ->
            add(LinkSelectItem(item, itemListener).id("LinkSelect-${index}-${item.id}"))
        }
    }

    override var heightPercent: Float = 0.0f

    override fun getStyle() = R.style.BottomSheetDialog_NavWhite

    override fun needCountTime() = false

    override fun getPageName() = PageNameConstants.DIALOG_EXTERNAL_LINK_SELECT
}