package com.socialplay.gpark.ui.post.tab

import android.content.ComponentCallbacks
import androidx.annotation.StringRes
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.SearchPostCardRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/10/7
 * Desc:
 */

data class RelatedCardListBundle(
    val pgcDeveloped: RelatedCardListWrap? = null,
    val pgcVisited: RelatedCardListWrap? = null
) {

    val emptyList: Boolean get() = pgcDeveloped?.list.isNullOrEmpty() && pgcVisited?.list.isNullOrEmpty()

    fun pgcEnd(): Boolean {
        return pgcDeveloped?.end == true && pgcVisited?.end == true
    }
}

data class RelatedCardListWrap(
    @StringRes val title: Int,
    val end: Boolean,
    val nextPage: Int,
    val list: List<PostCardInfo>,
    val orderId: String? = null,
)

data class AddCardItemModelState(
    val searchKeyWord: String? = null,
    val searchNextPage: Int = 1,
    val searchList: Async<List<PostCardInfo>> = Uninitialized,
    val searchLoadMore: Async<LoadMoreState> = Uninitialized,
    val relatedBundle: Async<RelatedCardListBundle> = Uninitialized,
    val relatedLoadMore: Async<LoadMoreState> = Uninitialized,
    val currentPage: String = PAGE_RELATED,
) : MavericksState {

    companion object {
        const val PAGE_RELATED = "related"
        const val PAGE_SEARCH = "search"
    }
}

class AddCardItemViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: AddCardItemModelState
) : BaseViewModel<AddCardItemModelState>(initialState) {

    private var lastOrderNum: Long? = null

    init {
        refresh()
    }

    fun refresh() {
        withState {
            refreshPgcPage()
        }
    }

    fun loadMore() {
        withState {
            loadMorePgcPage()
        }
    }

    fun search() {
        withState { oldState ->
            if (oldState.searchKeyWord.isNullOrEmpty()) return@withState
            repository.searchPostCardList(
                SearchPostCardRequest(
                    oldState.searchKeyWord,
                    1,
                    null
                )
            ).map {
                Analytics.track(
                    EventConstants.GAME_CARD_SEARCH_LIST_SHOW
                )
                lastOrderNum = it.lastOrderNum
                it.dataList.distinctBy { it.gameId }
            }.execute { result ->
                val success = result is Success
                copy(
                    searchList = result,
                    searchLoadMore = if (success) Uninitialized else searchLoadMore,
                    searchNextPage = 1 + if (success) 1 else 0,
                    currentPage = AddCardItemModelState.PAGE_SEARCH
                )
            }
        }
    }

    fun loadMoreSearch() {
        withState { oldState ->
            if (oldState.searchKeyWord.isNullOrEmpty() || oldState.searchLoadMore is Loading) return@withState
            repository.searchPostCardList(
                SearchPostCardRequest(
                    oldState.searchKeyWord,
                    oldState.searchNextPage,
                    lastOrderNum
                )
            ).map {
                lastOrderNum = it.lastOrderNum
                it.dataList
            }.execute { result ->
                val success = result is Success
                val list = result()
                copy(
                    searchList = if (list.isNullOrEmpty()) oldState.searchList else {
                        oldState.searchList.map {
                            (it + list).distinctBy { it.gameId }
                        }
                    },
                    searchLoadMore = result.map { LoadMoreState(!success || it.isEmpty()) },
                    searchNextPage = oldState.searchNextPage + if (success) 1 else 0,
                    currentPage = AddCardItemModelState.PAGE_SEARCH
                )
            }
        }
    }

    fun clearSearch() {
        setState {
            copy(
                searchKeyWord = null,
                searchList = Uninitialized,
                searchLoadMore = Uninitialized,
                currentPage = AddCardItemModelState.PAGE_RELATED,
            )
        }
    }

    private fun refreshPgcPage() {
        withState {
            repository.getIDevelopedPgcList(
                MyCreationsV4Request(
                    null,
                    accountInteractor.curUuid,
                    null
                )
            ).map {
                it.copy(dataList = it.dataList.map {
                    it.copy(gameAuthor = accountInteractor.curNameOrNull)
                }.distinctBy {
                    it.gameId
                })
            }.execute { result ->
                copy(
                    relatedBundle = result.map {
                        RelatedCardListBundle(
                            pgcDeveloped = RelatedCardListWrap(
                                end = it.end || it.dataList.isEmpty(),
                                title = R.string.i_developed,
                                nextPage = 1 + if (result is Success) 1 else 0,
                                list = it.dataList,
                                orderId = it.dataList.lastOrNull()?.offset
                            )
                        )
                    }, relatedLoadMore = if (result is Success) {
                        Uninitialized
                    } else {
                        relatedLoadMore
                    }
                )
            }
        }
    }

    private fun loadMorePgcPage() {
        withState { oldState ->
            if (oldState.relatedLoadMore is Loading) return@withState
            val oldList = oldState.relatedBundle() ?: return@withState
            val loadMoreDeveloped = oldList.pgcDeveloped != null && !oldList.pgcDeveloped.end

            val refreshVisited = oldList.pgcVisited == null
            val loadMoreVisited = oldList.pgcVisited != null && !oldList.pgcVisited.end

            if (loadMoreDeveloped) {
                loadMorePgcDeveloped(oldState.relatedBundle, oldList.pgcDeveloped!!)
            } else if (refreshVisited) {
                refreshPgcVisited(oldState.relatedBundle)
            } else if (loadMoreVisited) {
                loadMorePgcVisited(oldState.relatedBundle, oldList.pgcVisited!!)
            } else {
                setState {
                    copy(relatedLoadMore = relatedLoadMore.copyEx(LoadMoreState(true)))
                }
            }
        }
    }

    private fun loadMorePgcDeveloped(
        oldBundle: Async<RelatedCardListBundle>,
        pgcDeveloped: RelatedCardListWrap
    ) {
        val oldList = oldBundle()!!
        repository.getIDevelopedPgcList(
            MyCreationsV4Request(
                oldList.pgcDeveloped!!.orderId,
                accountInteractor.curUuid,
                null
            )
        ).map {
            it.copy(dataList = it.dataList.map {
                it.copy(gameAuthor = accountInteractor.curNameOrNull)
            })
        }.execute { result ->
            val success = result is Success
            val newBundle = result.map {
                val newList = pgcDeveloped.list + it.dataList
                oldList.copy(
                    pgcDeveloped = RelatedCardListWrap(
                        end = it.end || it.dataList.isEmpty(),
                        title = R.string.i_developed,
                        nextPage = pgcDeveloped.nextPage + if (success) 1 else 0,
                        list = newList.distinctBy { it.gameId },
                        orderId = newList.lastOrNull()?.offset
                    )
                )
            }
            copy(
                relatedBundle = oldBundle.copyEx(newBundle()), relatedLoadMore = result.map {
                    LoadMoreState(!success || (newBundle()?.pgcEnd() == true))
                }
            )
        }
    }

    private fun refreshPgcVisited(oldBundle: Async<RelatedCardListBundle>) {
        val oldList = oldBundle()!!
        repository.fetchRecentPlayedPgcCardListV2(PAGE_SIZE, 1, accountInteractor.curUuid)
            .map {
                it.copy(dataList = it.dataList.distinctBy {
                    it.gameId
                })
            }
            .execute { result ->
                val success = result is Success
                val newBundle = result.map {
                    oldList.copy(
                        pgcVisited = RelatedCardListWrap(
                            end = it.end || it.dataList.isEmpty(),
                            title = R.string.recently_visited,
                            nextPage = 1 + if (success) 1 else 0,
                            list = it.dataList,
                        )
                    )
                }
                copy(
                    relatedBundle = oldBundle.copyEx(newBundle()), relatedLoadMore = result.map {
                        LoadMoreState(!success || (newBundle()?.pgcEnd() == true))
                    }
                )
            }
    }

    private fun loadMorePgcVisited(
        oldBundle: Async<RelatedCardListBundle>,
        pgcVisited: RelatedCardListWrap
    ) {
        val oldList = oldBundle()!!
        repository.fetchRecentPlayedPgcCardListV2(
            PAGE_SIZE,
            oldList.pgcVisited!!.nextPage,
            accountInteractor.curUuid
        ).execute { result ->
            val success = result is Success
            val newBundle = result.map {
                val newList = pgcVisited.list + it.dataList
                oldList.copy(
                    pgcVisited = RelatedCardListWrap(
                        end = it.end || it.dataList.isEmpty(),
                        title = R.string.recently_visited,
                        nextPage = pgcVisited.nextPage + if (success) 1 else 0,
                        list = newList.distinctBy { it.gameId },
                    )
                )
            }
            copy(
                relatedBundle = oldBundle.copyEx(newBundle()), relatedLoadMore = result.map {
                    LoadMoreState(!success || (newBundle()?.pgcEnd() == true))
                }
            )
        }
    }

    fun changeKeyWord(keyword: String) {
        setState {
            copy(searchKeyWord = keyword)
        }
    }

    companion object :
        KoinViewModelFactory<AddCardItemViewModel, AddCardItemModelState>() {

        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: AddCardItemModelState
        ): AddCardItemViewModel {
            return AddCardItemViewModel(get(), get(), state)
        }
    }
}