package com.socialplay.gpark.ui.home.adapter

import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.databinding.AdapterPlayedBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.util.DownloadProgressUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.addIf
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber
import kotlin.math.ceil

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */


class GameItemPlayedAdapter(private val glide: GlideGetter) : BasePagingDataAdapter<MyPlayedGame, AdapterPlayedBinding>(CALLBACK) {

    companion object {

        private const val CHANGED_NAME = "CHANGED_NAME"
        private const val CHANGED_ICON = "CHANGED_ICON"
        private const val CHANGED_LOAD_PERCENT = "CHANGED_LOAD_PERCENT"

        val CALLBACK = object : DiffUtil.ItemCallback<MyPlayedGame>() {
            override fun areItemsTheSame(oldItem: MyPlayedGame, newItem: MyPlayedGame): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: MyPlayedGame, newItem: MyPlayedGame): Boolean {
                return oldItem.icon == newItem.icon &&
                        oldItem.downloadPercent == newItem.downloadPercent &&
                        oldItem.name == newItem.name
            }

            override fun getChangePayload(oldItem: MyPlayedGame, newItem: MyPlayedGame): Any? {
                return arrayListOf<String>()
                    .addIf(CHANGED_LOAD_PERCENT) { oldItem.downloadPercent != newItem.downloadPercent }
                    .addIf(CHANGED_ICON) { oldItem.icon != newItem.icon }
                    .addIf(CHANGED_NAME) { oldItem.name != newItem.name }
                    .ifEmptyNull()
            }
        }
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterPlayedBinding {
        return AdapterPlayedBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterPlayedBinding>, item: MyPlayedGame, position: Int) {
        if (ScreenUtil.isPad(context)) {
            val layoutParams = holder.binding.root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 88f)
            holder.binding.root.layoutParams = layoutParams
            Timber.d("ScreenUtil.isPad")
        } else {
            val layoutParams = holder.binding.root.layoutParams
            layoutParams.width = ceil(
                (ScreenUtil.screenWidth - ScreenUtil.dp2px(
                    context,
                    46f
                )).toFloat() / (4.3)
            ).toInt()
            holder.binding.root.layoutParams = layoutParams
            Timber.d("ScreenUtil.isPad_false")
        }
        updateName(holder, item)
        updateIcon(holder, item)
        updatePercent(holder, item.downloadPercent, item.id)
    }

    override fun convert(holder: BindingViewHolder<AdapterPlayedBinding>, item: MyPlayedGame, position: Int, payloads: MutableList<Any>) {

        val payload = payloads[0]
        if (payload is List<*> && payload.isNotEmpty()) {
            payload.forEach {
                when (it) {
                    CHANGED_NAME         -> {
                        updateName(holder, item)
                    }
                    CHANGED_ICON         -> {
                        updateIcon(holder, item)
                    }
                    CHANGED_LOAD_PERCENT -> {
                        updatePercent(holder, item.downloadPercent, item.id)
                    }
                }
            }
        }
    }

    private fun updatePercent(holder: BindingViewHolder<AdapterPlayedBinding>, percent: Float, gameId: String) {
        val matrix = ColorMatrix()
        if (percent > 0f) {
            matrix.setSaturation(1f)
        } else {
            matrix.setSaturation(0f)
        }
        val filter = ColorMatrixColorFilter(matrix)
        holder.binding.iv.colorFilter = filter
        holder.binding.loading.visible(percent > 0f && percent < 1f)
        holder.binding.loading.progress = DownloadProgressUtil.getShowProgress(percent * 100).toInt()
    }

    private fun updateIcon(holder: BindingViewHolder<AdapterPlayedBinding>, item: MyPlayedGame) {
        glide()?.run {
            load(item.icon).placeholder(R.drawable.placeholder_corner_16)
                .transform(CenterCrop(), RoundedCorners(ScreenUtil.dp2px(context, 16f)))
                .into(holder.binding.iv)
        }
    }

    private fun updateName(holder: BindingViewHolder<AdapterPlayedBinding>, item: MyPlayedGame) {
        holder.binding.tvGameName.text = item.name
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<AdapterPlayedBinding>) {
        super.onViewAttachedToWindow(holder)
        peek(holder.bindingAdapterPosition)?.apply {
            Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                put("gameid", id)
                put("packagename", packageName ?: "")
                put("game_type", typeToString())
                putAll(
                    ResIdUtils.getAnalyticsMap(
                        ResIdBean().setGameId(id).setCategoryID(CategoryId.RECOMMEND_MY_GAMES)
                            .setParam1(holder.bindingAdapterPosition + 1)
                            .setTsTypeIfUnset(if (isUgcGame) ResIdBean.TS_TYPE_UCG else ResIdBean.TS_TYPE_NORMAL)
                            .setIconTypeIfEmpty(if (isUgcGame) ResIdBean.ICON_TYPE_UGC else ResIdBean.ICON_TYPE_PGC)
                    )
                )
            }
        }
    }
}