package com.socialplay.gpark.ui.base

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AccelerateInterpolator
import androidx.annotation.StyleRes
import androidx.core.animation.doOnEnd
import androidx.fragment.app.DialogFragment
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.BaseDialogFragmentContainerBinding
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

/**
 * 改进版的BaseDialogFragment，解决弹框跳动问题
 * 使用全屏布局，内容固定在底部，避免弹框高度变化导致的跳动
 */
abstract class BaseDialogFragmentExt : BaseDialogFragment() {

    private val containerBinding by viewBinding(BaseDialogFragmentContainerBinding::inflate)

    // 子类需要实现的绑定，用于提供实际内容
    protected abstract val contentBinding: ViewBinding

    // 覆盖父类的binding，返回容器binding
    override val binding: ViewBinding
        get() = containerBinding

    // 内容视图的底部外边距，默认为0
    open fun contentMarginBottom(): Int = 0

    // 是否使用容器的左右边距，默认为true
    open fun useContainerHorizontalMargin(): Boolean = true

    // 是否允许点击背景关闭弹框，默认为true
    open fun isBackgroundClickDismiss(): Boolean = true

    // 是否使用全屏布局，默认为true
    override fun isFullScreen(): Boolean = true

    // 设置对话框样式，使用渐变动画
    @StyleRes
    override fun getStyle(): Int = R.style.DialogFadeStyle

    // 背景动画时长，默认300ms
    protected open fun backgroundAnimDuration(): Long = 300L

    // 背景淡出动画时长，默认250ms
    protected open fun backgroundFadeOutDuration(): Long = 250L

    // 是否使用背景动画，默认为true
    protected open fun useBackgroundAnimation(): Boolean = true

    // 是否使用嵌套对话框特殊处理，默认为true
    protected open fun useNestedDialogHandling(): Boolean = true

    // 嵌套对话框的初始透明度，默认为0.4f
    protected open fun nestedDialogInitialDimAmount(): Float = 0.1f

    // 是否是从另一个对话框打开的
    protected fun isOpenedFromAnotherDialog(): Boolean {
        return parentFragment is DialogFragment
    }

    // 背景动画器
    private var backgroundAnimator: ValueAnimator? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 使用父类的onCreateView创建容器视图
        val rootView = super.onCreateView(inflater, container, savedInstanceState)

        // 将内容视图添加到容器中
        containerBinding.dialogContentContainer.addView(contentBinding.root)

        // 设置内容视图的外边距
        val params = containerBinding.dialogContentContainer.layoutParams as ViewGroup.MarginLayoutParams

        // 设置底部外边距
        val marginBottom = contentMarginBottom()
        if (marginBottom > 0) {
            params.bottomMargin = marginBottom
        }

        // 设置左右外边距
        if (!useContainerHorizontalMargin()) {
            params.leftMargin = 0
            params.rightMargin = 0
        }

        containerBinding.dialogContentContainer.layoutParams = params

        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置背景点击事件
        if (isBackgroundClickDismiss()) {
            containerBinding.dialogBackground.setOnAntiViolenceClickListener {
                dismissAllowingStateLoss()
            }
        }

        // 如果需要使用背景动画
        if (useBackgroundAnimation()) {
            if (isOpenedFromAnotherDialog() && useNestedDialogHandling()) {
                // 如果是从另一个对话框打开的，并且需要使用嵌套对话框特殊处理
                // 设置初始透明度为10%，然后渐变到目标透明度
                val initialDimAmount = nestedDialogInitialDimAmount()
                dialog?.window?.setDimAmount(initialDimAmount) // 初始时背景半透明

                // 创建并启动背景淡入动画
                animateBackgroundDim(initialDimAmount, dimAmount(), backgroundAnimDuration())
            } else {
                // 如果不是从另一个对话框打开的
                dialog?.window?.setDimAmount(0f) // 初始时背景透明

                // 创建并启动背景淡入动画
                animateBackgroundDim(0f, dimAmount(), backgroundAnimDuration())
            }
        }
    }

    override fun dismiss() {
        // 如果需要使用背景动画，则先渐变背景，再关闭对话框
        if (useBackgroundAnimation()) {
            if (isOpenedFromAnotherDialog() && useNestedDialogHandling()) {
                // 如果是从另一个对话框打开的，并且需要使用嵌套对话框特殊处理
                // 渐变到初始透明度，然后关闭对话框
                animateBackgroundDimAndDismiss(dimAmount(), nestedDialogInitialDimAmount()) { super.dismiss() }
            } else {
                // 如果不是从另一个对话框打开的
                animateBackgroundDimAndDismiss(dimAmount(), 0f) { super.dismiss() }
            }
        } else {
            super.dismiss()
        }
    }

    override fun dismissAllowingStateLoss() {
        // 如果需要使用背景动画，则先渐变背景，再关闭对话框
        if (useBackgroundAnimation()) {
            if (isOpenedFromAnotherDialog() && useNestedDialogHandling()) {
                // 如果是从另一个对话框打开的，并且需要使用嵌套对话框特殊处理
                // 渐变到初始透明度，然后关闭对话框
                animateBackgroundDimAndDismiss(dimAmount(), nestedDialogInitialDimAmount()) { super.dismissAllowingStateLoss() }
            } else {
                // 如果不是从另一个对话框打开的
                animateBackgroundDimAndDismiss(dimAmount(), 0f) { super.dismissAllowingStateLoss() }
            }
        } else {
            super.dismissAllowingStateLoss()
        }
    }

    override fun onDestroyView() {
        // 清理动画器，避免内存泄漏
        backgroundAnimator?.cancel()
        backgroundAnimator = null
        super.onDestroyView()
    }

    /**
     * 动画背景半透明度
     * @param fromDim 起始半透明度
     * @param toDim 目标半透明度
     * @param duration 动画时长
     */
    private fun animateBackgroundDim(fromDim: Float, toDim: Float, duration: Long) {
        // 取消当前正在运行的动画
        backgroundAnimator?.cancel()

        // 创建新的动画器
        backgroundAnimator = ValueAnimator.ofFloat(fromDim, toDim).apply {
            this.duration = duration
            interpolator = if (fromDim < toDim) {
                // 淡入使用加速减速插值器
                AccelerateDecelerateInterpolator()
            } else {
                // 淡出使用加速插值器
                AccelerateInterpolator()
            }

            addUpdateListener { animator ->
                val value = animator.animatedValue as Float
                dialog?.window?.setDimAmount(value)
            }

            start()
        }
    }

    /**
     * 动画自定义背景的透明度
     * @param fromAlpha 起始透明度
     * @param toAlpha 目标透明度
     * @param duration 动画时长
     */
    private fun animateCustomBackground(fromAlpha: Float, toAlpha: Float, duration: Long) {
        // 取消当前正在运行的动画
        backgroundAnimator?.cancel()

//        // 获取自定义背景视图
//        val dimBackground = containerBinding.dialogDimBackground
//
//        // 设置初始透明度
//        dimBackground.alpha = fromAlpha

        // 创建新的动画器
        backgroundAnimator = ValueAnimator.ofFloat(fromAlpha, toAlpha).apply {
            this.duration = duration
            interpolator = if (fromAlpha < toAlpha) {
                // 淡入使用加速减速插值器
                AccelerateDecelerateInterpolator()
            } else {
                // 淡出使用加速插值器
                AccelerateInterpolator()
            }

            addUpdateListener { animator ->
                val value = animator.animatedValue as Float
//                dimBackground.alpha = value
            }

            start()
        }
    }

    /**
     * 动画背景半透明度并在完成后执行关闭操作
     * @param fromDim 起始半透明度
     * @param toDim 目标半透明度
     * @param dismissAction 关闭操作
     */
    private fun animateBackgroundDimAndDismiss(fromDim: Float, toDim: Float, dismissAction: () -> Unit) {
        // 取消当前正在运行的动画
        backgroundAnimator?.cancel()

        // 创建新的动画器
        backgroundAnimator = ValueAnimator.ofFloat(fromDim, toDim).apply {
            this.duration = backgroundFadeOutDuration()
            interpolator = AccelerateInterpolator()

            addUpdateListener { animator ->
                val value = animator.animatedValue as Float
                dialog?.window?.setDimAmount(value)
            }

            // 动画结束后执行关闭操作
            doOnEnd {
                dismissAction.invoke()
                backgroundAnimator = null
            }

            start()
        }
    }


}
