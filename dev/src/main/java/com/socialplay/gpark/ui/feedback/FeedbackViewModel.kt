package com.socialplay.gpark.ui.feedback

import android.content.ComponentCallbacks
import android.content.Context
import androidx.annotation.StringRes
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get
import timber.log.Timber
import java.io.File

data class FeedbackTypeWrapper(
    val id: String,
    @StringRes
    val typeNameRes: Int,
    @StringRes
    val optionTitleRes: Int,
    // 展示顺序，position越小越靠前
    val position: Int,
    // 已选option
    val selectedIdSet: Set<String> = HashSet(),
    // 是否选中
    val selected: Boolean = false,
    // type的各种配置
    val config: FeedbackConfigItem = FeedbackConfigItem(
        "",
        feedbackTxtNotNull = true,
        ifShowTextBox = true,
        optionConfigList = null,
        optionNotNull = true,
        chooseWay = FeedbackConfigItem.CHOOSE_WAY_MULTI
    ),
) {

    companion object {
        val avatar = FeedbackTypeWrapper(
            SubmitNewFeedbackRequest.NET_SOURCE_AVATAR,
            R.string.feedback_type_avatar,
            R.string.avatar_feedback_type,
            1
        )
        val experience = FeedbackTypeWrapper(
            SubmitNewFeedbackRequest.NET_SOURCE_GAME,
            R.string.feedback_type_experience,
            R.string.mandatory_feedback_type,
            2
        )
        val suggestion = FeedbackTypeWrapper(
            SubmitNewFeedbackRequest.NET_SOURCE_SUGGESTION,
            R.string.feedback_type_suggestions,
            R.string.mandatory_feedback_type,
            3
        )
        val reportBugs = FeedbackTypeWrapper(
            SubmitNewFeedbackRequest.NET_SOURCE_APP,
            R.string.feedback_type_report_bugs,
            R.string.mandatory_feedback_type,
            4
        )
        val buy = FeedbackTypeWrapper(
            SubmitNewFeedbackRequest.NET_SOURCE_BUY,
            R.string.feedback_type_buy,
            R.string.mandatory_feedback_type,
            5
        )

        fun assembleListByConfig(list: List<FeedbackConfigItem>?): List<FeedbackTypeWrapper>? {
            return list?.mapNotNull { convertApiType(it) }?.sortedBy { it.position }
        }

        private fun convertApiType(origin: FeedbackConfigItem): FeedbackTypeWrapper? {
            return when (origin.originCode) {
                reportBugs.id -> reportBugs
                experience.id -> experience
                avatar.id -> avatar
                suggestion.id -> suggestion
                buy.id -> buy
//                evaluate.id -> evaluate
                else -> null
            }.let {
                it?.copy(config = origin)
            }
        }
    }
}

data class FeedbackAttachment(val localMedia: LocalMedia)

data class FeedbackResult(val result: Boolean, val uploadImageErrorMessage: String?)

/**
 * Created by bo.li
 * Date: 2023/8/24
 * Desc:
 */
data class FeedbackModelState(
    val gameId: String?, // 游戏id
    val source: String, // 来源
    val defaultSelectType: String?, // 默认选中type
    val typeList: Async<List<FeedbackTypeWrapper>> = Uninitialized, // 反馈选项
    val result: Async<FeedbackResult> = Uninitialized, // 提交结果
    val pictureMap: Map<String, String>? = null, // 已选图片对应cdn
    val submitEnable: Boolean = false, // 当前是否可以提交
    val desc: String? = null, // 描述
    val attachments: List<FeedbackAttachment>? = null, // 附件：本地文件、对应url
    val feedbackDiscord: String? = null, // discord链接
) : MavericksState {

    // 附件个数
    val attachmentSize: Int = attachments?.size ?: 0

    // 当前选项的id
    val selectedType = typeList()?.firstOrNull { it.selected }

    // 当前选项的id
    val currentId = selectedType?.id

    // 当前选项的配置
    val currentConfig = selectedType?.config

    // 当前选项的已选项
    val currentSelectedIdSet = selectedType?.selectedIdSet ?: hashSetOf()

    // 当前选项的所有可选项
    val currentOptionList = currentConfig?.optionConfigList

    // 当前选项是否展示描述
    val ifShowTextBox = currentConfig?.ifShowTextBox ?: true

    // 当前选项描述是否可空 true:不可空
    val feedbackTxtNotNull = ifShowTextBox && (currentConfig?.feedbackTxtNotNull ?: true)

    // 当前选项的选择项是否可以为空 true不可空 false可空
    val optionNotNull = if (currentConfig == null) {
        true
    } else {
        !currentConfig.optionConfigList.isNullOrEmpty() && currentConfig.optionNotNull
    }

    constructor(args: FeedbackFragmentArgs) : this(args.gameId, args.source, args.defaultSelectType)
}

class FeedbackViewModel(
    private val context: Context,
    private val repository: IMetaRepository,
    private val uploadFileInteractor: UploadFileInteractor,
    initialState: FeedbackModelState
) : BaseViewModel<FeedbackModelState>(initialState) {
    init {
        fillType(initialState.defaultSelectType)
        fetchDiscord()
        observeSubmit()
    }

    /**
     * 赋值按钮可点击字段
     */
    private fun observeSubmit() {
        onEach(
            FeedbackModelState::currentConfig,
            FeedbackModelState::desc,
            FeedbackModelState::currentSelectedIdSet
        ) { currentConfig, currentDesc, currentSelectedIdSet ->
            setState {
                copy(
                    submitEnable = currentConfig != null
                            && (!currentConfig.optionNotNull || currentConfig.optionConfigList.isNullOrEmpty() || currentSelectedIdSet.isNotEmpty())
                            && (!currentConfig.feedbackTxtNotNull || !currentConfig.ifShowTextBox || (currentDesc.orEmpty().length in 1..FeedbackFragment.DESC_MAX_COUNT))
                )
            }
        }
    }

    private fun fillType(defaultSelectType: String?) = withState {
        repository.getFeedbackOptionList().map { list ->
            val configList =
                FeedbackTypeWrapper.assembleListByConfig(list?.distinctBy { it.originCode })
            if (configList.isNullOrEmpty()) {
                throw ApiResultCodeException(
                    -1,
                    context.getString(R.string.common_error),
                    List::class
                )
            }
            configList.map {
                it.copy(selected = it.id == defaultSelectType)
            }
        }.execute {
            copy(typeList = it.map { it() ?: emptyList() })
        }
    }

    private fun fetchDiscord() {
        repository.getFeedbackDiscordLink().execute {
            copy(feedbackDiscord = it.invoke())
        }
    }

    fun checkType(id: String) {
        setState {
            copy(typeList = typeList.map {
                it.map { wrapper ->
                    wrapper.copy(selected = if (!wrapper.selected && id == wrapper.id) true else false)
                }
            })
        }
    }

    fun checkOption(id: String) {
        val state = oldState
        val selected = state.selectedType
        val currentConfig = selected?.config
        if (currentConfig == null || currentConfig.optionConfigList.isNullOrEmpty()) {
            return
        }
        val newSelectedIdSet: Set<String> = HashSet(selected.selectedIdSet).apply {
            if (!contains(id)) {
                val canChoose =
                    currentConfig.chooseWay == FeedbackConfigItem.CHOOSE_WAY_MULTI || (currentConfig.chooseWay == FeedbackConfigItem.CHOOSE_WAY_SINGLE && isEmpty())
                if (canChoose) {
                    add(id)
                } else {
                    return
                }
            } else {
                remove(id)
            }
        }
        setState {
            val newType = selected.copy(selectedIdSet = newSelectedIdSet)
            copy(
                typeList = typeList.map {
                    it.map { type ->
                        if (type.id == selected.id) {
                            newType
                        } else {
                            type
                        }
                    }
                }
            )
        }
    }

    fun updateDesc(desc: String) {
        if (desc == oldState.desc) return
        setState {
            copy(desc = desc)
        }
    }

    private fun getAttachmentPath(attachment: FeedbackAttachment): String? {
        return attachment.localMedia.compressPath ?: attachment.localMedia.realPath
        ?: attachment.localMedia.path
    }

    fun submitInfo() {
        withState { state ->
            if (state.result is Loading || state.selectedType == null) return@withState
            val needUploadFileList = kotlin.runCatching {
                state.attachments?.map {
                    File(getAttachmentPath(it) ?: "")
                }?.filter { file ->
                    state.pictureMap?.get(file.absolutePath)
                        .isNullOrEmpty() && file.exists()
                }
            }.getOrElse {
                Timber.e("checkcheck_feedback, fetch map error, ${it}")
                null
            }

            if (needUploadFileList.isNullOrEmpty()) {
                flow {
                    emit(
                        repository.submitNewFeedback(
                            SubmitNewFeedbackRequest(
                                state.gameId,
                                state.selectedType.selectedIdSet.toList(),
                                state.desc,
                                state.pictureMap?.values?.toList(),
                                state.selectedType.id
                            )
                        )
                    )
                }.map {
                    FeedbackResult(it, null)
                }.execute { uploadResult ->
                    copy(result = uploadResult)
                }
            } else {
                uploadFileInteractor.uploadList(
                    UploadFileInteractor.BIZ_CODE_FEEDBACK,
                    needUploadFileList
                )
                    .map { resultList ->
                        val map = hashMapOf<String, String>().apply {
                            state.pictureMap?.let { putAll(it) }
                            resultList.mapNotNull { it.data }.forEach {
                                put(it.filePath, it.url)
                            }
                        }
                        val allSuccess = !resultList.any { !it.succeeded }
                        Triple(map, allSuccess, resultList.find { !it.succeeded }?.message)
                    }.map { uploadPair ->
                        val uploadSuccess = uploadPair.second
                        val onlineUrlMap = uploadPair.first
                        val errorMsg = uploadPair.third
                        setState {
                            copy(pictureMap = hashMapOf<String, String>().apply {
                                pictureMap?.let { putAll(it) }
                                putAll(onlineUrlMap)
                            })
                        }
                        if (uploadSuccess) {
                            FeedbackResult(
                                repository.submitNewFeedback(
                                    SubmitNewFeedbackRequest(
                                        state.gameId,
                                        state.selectedType.selectedIdSet.toList(),
                                        state.desc,
                                        onlineUrlMap.values.toList(),
                                        state.selectedType.id
                                    )
                                ),
                                null
                            )
                        } else {
                            FeedbackResult(false, errorMsg)
                        }
                    }.execute {
                        copy(result = it)
                    }
            }
        }
    }

    fun addAttachment(list: List<LocalMedia>) {
        withState {
            if (it.result is Loading) return@withState
            val oldList = it.attachments ?: emptyList()
            val newList: List<FeedbackAttachment> = (oldList + list.map { localMedia ->
                FeedbackAttachment(localMedia.apply { id += System.currentTimeMillis() })
            }).distinctBy { it.localMedia.id }
            setState {
                copy(attachments = newList)
            }
        }
    }

    fun removeAttachment(attachment: FeedbackAttachment) {
        withState {
            if (it.result is Loading) return@withState
            val oldList = it.attachments ?: return@withState
            val newList: List<FeedbackAttachment> = oldList.filter {
                it.localMedia.id != attachment.localMedia.id
            }.distinctBy { it.localMedia.id }
            val newMap = hashMapOf<String, String>().apply {
                it.pictureMap?.let { it1 -> putAll(it1) }
                remove(getAttachmentPath(attachment))
            }
            setState {
                copy(attachments = newList, pictureMap = newMap)
            }
        }
    }

    companion object : KoinViewModelFactory<FeedbackViewModel, FeedbackModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: FeedbackModelState
        ): FeedbackViewModel {
            return FeedbackViewModel(get(), get(), get(), state)
        }
    }
}