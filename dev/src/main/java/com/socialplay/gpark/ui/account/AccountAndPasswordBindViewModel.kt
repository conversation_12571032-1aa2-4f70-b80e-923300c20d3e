package com.socialplay.gpark.ui.account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.user.ValueCheckedResult
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.util.AccountUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch


class AccountAndPasswordBindViewModel(val repository: IMetaRepository, val accountInteractor: AccountInteractor) : ViewModel() {

    private val _passwordVisibilityFlow = MutableStateFlow(false)
    val passwordVisibilityFlow: Flow<Boolean> = _passwordVisibilityFlow

    private val _passwordVisibilityFlow2 = MutableStateFlow(false)
    val passwordVisibilityFlow2: Flow<Boolean> = _passwordVisibilityFlow2

    private val _accountFlow = MutableStateFlow<String?>(null)
    val accountFlow: Flow<String?> = _accountFlow

    private val _passwordFlow = MutableStateFlow<String?>(null)
    val passwordFlow: Flow<String?> = _passwordFlow

    private val _passwordFlow2 = MutableStateFlow<String?>(null)
    val passwordFlow2: Flow<String?> = _passwordFlow2

    val emailAndVerifyCodeValidFlow: Flow<Boolean> = passwordFlow.combine(passwordFlow2) { v1, v2 -> isPasswordEnable(v1) && v1 == v2 }

    private val _resultFlow = MutableSharedFlow<ValueCheckedResult?>()
    val resultFlow: Flow<ValueCheckedResult?> = _resultFlow

    var source: String? = null

    fun togglePasswordVisibility() {
        _passwordVisibilityFlow.value = !_passwordVisibilityFlow.value
    }

    fun togglePasswordVisibility2() {
        _passwordVisibilityFlow2.value = !_passwordVisibilityFlow2.value
    }

    fun postAccountValueChanged(email: String?) {
        _accountFlow.value = email
    }

    fun postPasswordValueChanged(code: String?) {
        _passwordFlow.value = code
    }

    fun postPassword2ValueChanged(code: String?) {
        _passwordFlow2.value = code
    }

    private fun isAccountValid(account: String?): Boolean {
        return AccountUtil.isAccountEnableWithoutEmail(account)
    }

    private fun isPasswordEnable(password: String?): Boolean {
        return PasswordViewModel.isPasswordEnable(password)
    }

    fun bindAccountAndPassword(account: String, password: String, loginType: LoginType) = viewModelScope.launch {
        if (accountInteractor.isVisitorLogin()) {
            // 注册
            accountInteractor.accountSignup(account, password, loginType).collect {
                _resultFlow.emit(ValueCheckedResult(it.succeeded, if (it is LoginState.Failed) it.message else "", ValueCheckedResult.TRIGGER_U13))
                trackResult(it.succeeded)
            }
        } else {
            // 绑定账密
            accountInteractor.bindPasswordByGparkId(account, password).collect {
                _resultFlow.emit(ValueCheckedResult(it.data == true, it.message))
                trackResult(it.data == true)
            }
        }
    }

    fun trackResult(result: Boolean) {
        Analytics.track(EventConstants.EVENT_SET_ACCOUNT) {
            put("source", source ?: "")
            put("result", if (result) 1 else 2)
        }
    }
}