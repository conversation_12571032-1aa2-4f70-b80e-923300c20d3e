package com.socialplay.gpark.ui.account

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.user.ValueCheckedResult
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.AccountUtil
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/10/25
 *     desc   :
 * </pre>
 */
data class AccPwdV7State(
    val pwdVis: Boolean = false,
    val pwdVis2: Boolean = false,
//    val acc: String? = null,
//    val accVerify: Boolean = true,
    val pwd: String? = null,
    val pwd2: String? = null,
    val pwdVerify: Boolean = true,
    val result: Async<ValueCheckedResult> = Uninitialized
) : MavericksState

class AccPwdV7ViewModel(
    initialState: AccPwdV7State,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor
) : BaseViewModel<AccPwdV7State>(initialState) {

    //    val accNotEmpty get() = !oldState.acc.isNullOrEmpty()
    val pwdNotEmpty get() = !oldState.pwd.isNullOrEmpty()
    val pwd2NotEmpty get() = !oldState.pwd2.isNullOrEmpty()
//    val accVerify get() = oldState.accVerify
    val pwdVerify get() = oldState.pwdVerify

//    fun updateAcc(acc: String?) = withState { s ->
//        val newAcc = acc?.trim()
//        if (newAcc != s.acc) {
//            setState { copy(acc = newAcc, accVerify = true) }
//        }
//    }

    fun updatePwd(pwd: String?) = withState { s ->
        val newPwd = pwd?.trim()
        if (newPwd != s.pwd) {
            setState { copy(pwd = newPwd, pwdVerify = true) }
        }
    }

    fun updatePwd2(pwd2: String?) = withState { s ->
        val newPwd = pwd2?.trim()
        if (newPwd != s.pwd2) {
            setState { copy(pwd2 = newPwd, pwdVerify = true) }
        }
    }

    fun signup() = withState { s ->
//        val account = s.acc
        val password = s.pwd
//        var accOk = true
        var pwdOk = true
//        if (!AccountUtil.validateAccount(account)) {
//            accOk = false
//        }
        if (!AccountUtil.validatePassword(password)) {
            pwdOk = false
        }
        if (!pwdOk) {
            setState { copy(pwdVerify = pwdOk) }
            return@withState
        }

        val account = accountInteractor.getUserInfoFromCache()?.userNumber
        if (accountInteractor.isVisitorLogin()) {
            // 注册
            accountInteractor.accountSignup(account!!, password!!, LoginType.BindAccount).map {
                ValueCheckedResult(it.succeeded, if (it is LoginState.Failed) it.message else "")
            }.execute {
                copy(result = it)
            }
        } else {
            // 绑定账密
            accountInteractor.bindPasswordByGparkId(account!!, password!!).map {
                ValueCheckedResult(it.data == true, it.message)
            }.execute {
                copy(result = it)
            }
        }
    }

    fun switchPwdVis() {
        setState { copy(pwdVis = !pwdVis) }
    }

    fun switchPwdVis2() {
        setState { copy(pwdVis2 = !pwdVis2) }
    }

    companion object : KoinViewModelFactory<AccPwdV7ViewModel, AccPwdV7State>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: AccPwdV7State
        ): AccPwdV7ViewModel {
            return AccPwdV7ViewModel(state, get(), get())
        }
    }

}