package com.socialplay.gpark.ui.aibot.ugc

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewbinding.ViewBinding
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.aibot.AIBotCreateRequest
import com.socialplay.gpark.data.model.aibot.AIBotStyle
import com.socialplay.gpark.data.model.aibot.AIBotUserInfo
import com.socialplay.gpark.data.model.aibot.AiBotCreateResultEvent
import com.socialplay.gpark.databinding.FragmentAiBotCreateBinding
import com.socialplay.gpark.databinding.TabViewAiBotBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel.Companion.SELECT_IMAGE
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel.Companion.SELECT_TEXT
import com.socialplay.gpark.ui.aibot.ugc.adapter.AIBotStyleAdapter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.property.viewBinding
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import kotlin.math.min

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/23
 *     desc   :
 *
 */
class AIBotUGCCreateFragment : BaseFragment<FragmentAiBotCreateBinding>() {
    private val viewModel by viewModel<AIBotUGCCreateViewModel>()
    private val adapter :AIBotStyleAdapter by lazy { AIBotStyleAdapter() }
    val args = navArgs<AIBotUGCCreateFragmentArgs>()
    var tabLayoutMediator : TabLayoutMediator?=null
    val tabTitles = listOf(
        R.string.ai_bot_image_to_avatar,
        R.string.ai_bot_text_to_avatar
    )
    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            updateSelTab(position)
            viewModel.updateSelect(position)
        }
    }
    private val tabListener by lazy {
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabPos = tab.position
                viewModel.updateSelect(tabPos)
                updateSelTab(tabPos)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {

            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotCreateBinding? {
        return FragmentAiBotCreateBinding.inflate(inflater)
    }

    override fun init() {
        initView()
        initData()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }
    private fun initView(){
        binding.tl.addOnTabSelectedListener(tabListener)
        initPager()
        binding.imgBack.setOnAntiViolenceClickListener {
            navigateUp()
        }
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_TOP_BG).into(binding.bgLineAiBotUgcBack)

        binding.tvGenerate.setOnAntiViolenceClickListener {
           viewModel.sendText()
        }

        binding.ryStyle.adapter = adapter
        adapter.setOnItemClickListener { view, position ->
            viewModel.updateStyle(adapter.getItem(position))
        }

    }
    private fun generate(){
        if (viewModel.selectStyle.value == null) {
            return
        }
        if (!viewModel.isEnoughAiBotCreate()) {
            Analytics.track(EventConstants.EVENT_AI_BOT_GENDER_LIMIT)
            ToastUtil.showShort(R.string.ai_bot_limit_tip)
            navigateUp()
            return
        }
        val content = if (viewModel.selectItem.value == SELECT_IMAGE) "" else {
            if (viewModel.uploadText.value.isNullOrEmpty()) "" else viewModel.uploadText.value
        }
        val path = if (viewModel.selectItem.value == SELECT_IMAGE) viewModel.uploadResultImage.value else ""
        val type = if (viewModel.selectItem.value == SELECT_IMAGE) {
            AIBotCreateRequest.TYPE_IMAGE
        } else {
            AIBotCreateRequest.TYPE_TEXT
        }
        val aiBotCreateRequest = AIBotCreateRequest(type, path, content)
        aiBotCreateRequest.style = viewModel.selectStyle.value
        aiBotCreateRequest.userInfo = AIBotUserInfo(args.value.gender)
        MetaRouter.AiBot.gotoAIBotCreateResult(this,
            GsonUtil.safeToJson(aiBotCreateRequest)
        )
        viewModel.uploadResult(null)
        Analytics.track(EventConstants.EVENT_AI_BOT_AVATAR_CLICK, map = mapOf("generate_type" to getGenerateType()))
    }

    @Subscribe
    fun onEvent(event: AiBotCreateResultEvent) {
        Timber.d("AiBotCreateResultEvent")
        this.findNavController().popBackStack()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
    private fun getGenerateType(): String {
        return if (viewModel.selectItem.value == SELECT_IMAGE) {
            "image 2 image"
        } else {
            "text 2 image"
        }
    }
    private fun initData(){
        viewModel.styleLiveData.observe(viewLifecycleOwner){
            adapter.setList(it)
        }
        viewModel.uploadImage.observe(viewLifecycleOwner){
            if (it != null) {
                checkGenerate()
            }
        }
        viewModel.uploadText.observe(viewLifecycleOwner){
            checkGenerate()
        }
        viewModel.selectItem.observe(viewLifecycleOwner){
            checkGenerate()
        }
        viewModel.sendLivedata.observe(viewLifecycleOwner) {
            if (it != null && it == true) {
                generate()
            }
        }
    }
    private fun initPager() {
        viewModel.configFragments()
        val pagerAdapter = CommonTabStateAdapter(viewModel.tabItems.value ?: arrayListOf(), childFragmentManager, viewLifecycleOwner.lifecycle)
        binding.vpChoose.adapterAllowStateLoss = pagerAdapter
        binding.vpChoose.registerOnPageChangeCallback(vpCallback)
//        binding.vpChoose.adapter = adapter

        tabLayoutMediator = TabLayoutMediator(binding.tl, binding.vpChoose) { tab, position ->
            tab.customView = createTab(position)
        }
        tabLayoutMediator?.attach()
    }



    private fun createTab(index: Int): View {
        val tabViewBinding = TabViewAiBotBinding.inflate(layoutInflater)
        tabViewBinding.tvName.text =getString( tabTitles.get(index))
        return tabViewBinding.root
    }
    private fun updateSelTab(pos:Int){
    }


    /**
     * 文字内容变化，图片内容变化，选择tab变化
     */
    private fun checkGenerate() {
        if (viewModel.selectStyle.value == null) {
            binding.tvGenerate.alpha = 0.5f
            binding.tvGenerate.isEnabled = false
            return
        }
        if (viewModel.selectItem.value == SELECT_IMAGE) {
            //图生图
            if (viewModel.uploadImage.value?.data.isNullOrEmpty()) {
                binding.tvGenerate.alpha = 0.5f
                binding.tvGenerate.isEnabled = false
            } else {
                binding.tvGenerate.alpha = 1f
                binding.tvGenerate.isEnabled = true
            }
        } else {
            //文生图
            if (viewModel.selectItem.value == SELECT_TEXT) {
                binding.tvGenerate.alpha = 1f
                binding.tvGenerate.isEnabled = true
            }
        }
    }


    override fun loadFirstData() {
        viewModel.getStyleList()
    }
    override fun onDestroyView() {
        binding.vpChoose.adapterAllowStateLoss = null
        binding.vpChoose.unregisterOnPageChangeCallback(vpCallback)
        binding.tl.removeOnTabSelectedListener(tabListener)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        super.onDestroyView()
    }
    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_AI_BOT_CREATE
}