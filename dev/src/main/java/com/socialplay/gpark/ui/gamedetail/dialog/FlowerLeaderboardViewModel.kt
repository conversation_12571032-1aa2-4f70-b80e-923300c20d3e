package com.socialplay.gpark.ui.gamedetail.dialog

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardData
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardItemData
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

data class FlowerLeaderboardModelState(
    val gameId: String,
    val isOwner: <PERSON><PERSON>an,
    val refresh: Async<FlowerLeaderboardData> = Uninitialized,
    val currentUser: FlowerLeaderboardItemData? = null
) : MavericksState {
    val list: List<FlowerLeaderboardItemData> = refresh.invoke()?.rankings ?: emptyList()
    val isInTop: Boolean = refresh.invoke()?.inTop ?: false

    constructor(args: FlowerLeaderboardDialogArgs) : this(
        args.gameId,
        args.isOwner
    )
}

/**
 * 花朵排行榜ViewModel
 */
class FlowerLeaderboardViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: FlowerLeaderboardModelState
) : BaseViewModel<FlowerLeaderboardModelState>(initialState) {

    init {
//        // 使用模拟数据进行测试
//        if (BuildConfig.DEBUG) {
//            createMockData()
//        } else {
        refreshList()
//        }
    }

    /**
     * 创建模拟数据
     */
    private fun createMockData() {
        viewModelScope.launch {
            // 创建模拟用户列表
            val mockUsers = listOf(
                FlowerLeaderboardItemData(
                    uid = "user_1",
                    nickname = "Flower Queen",
                    portrait = "https://randomuser.me/api/portraits/women/1.jpg",
                    tippingCnt = 999,
                    rank = 1
                ),
                FlowerLeaderboardItemData(
                    uid = "user_2",
                    nickname = "Flower King",
                    portrait = "https://randomuser.me/api/portraits/men/2.jpg",
                    tippingCnt = 888,
                    rank = 2
                ),
                FlowerLeaderboardItemData(
                    uid = "user_3",
                    nickname = "Flower Princess",
                    portrait = "https://randomuser.me/api/portraits/women/3.jpg",
                    tippingCnt = 777,
                    rank = 3
                ),
                FlowerLeaderboardItemData(
                    uid = "user_4",
                    nickname = "Flower Prince",
                    portrait = "https://randomuser.me/api/portraits/men/4.jpg",
                    tippingCnt = 666,
                    rank = 4
                ),
                FlowerLeaderboardItemData(
                    uid = "user_5",
                    nickname = "Flower Knight",
                    portrait = "https://randomuser.me/api/portraits/men/5.jpg",
                    tippingCnt = 555,
                    rank = 5
                ),
                FlowerLeaderboardItemData(
                    uid = "user_6",
                    nickname = "Flower Mage",
                    portrait = "https://randomuser.me/api/portraits/women/6.jpg",
                    tippingCnt = 444,
                    rank = 6
                ),
                FlowerLeaderboardItemData(
                    uid = "user_7",
                    nickname = "Flower Warrior",
                    portrait = "https://randomuser.me/api/portraits/men/7.jpg",
                    tippingCnt = 333,
                    rank = 7
                ),
                FlowerLeaderboardItemData(
                    uid = "user_8",
                    nickname = "Flower Archer",
                    portrait = "https://randomuser.me/api/portraits/women/8.jpg",
                    tippingCnt = 222,
                    rank = 8
                ),
                FlowerLeaderboardItemData(
                    uid = "user_9",
                    nickname = "Flower Thief",
                    portrait = "https://randomuser.me/api/portraits/men/9.jpg",
                    tippingCnt = 111,
                    rank = 9
                ),
                FlowerLeaderboardItemData(
                    uid = "user_10",
                    nickname = "Flower Bard",
                    portrait = "https://randomuser.me/api/portraits/women/10.jpg",
                    tippingCnt = 100,
                    rank = 10
                )
            )

            // 创建当前用户
            val currentUser = FlowerLeaderboardItemData(
                uid = accountInteractor.curUuid ?: "current_user",
                nickname = "Current User",
                portrait = "https://randomuser.me/api/portraits/men/99.jpg",
                tippingCnt = 50,
                rank = 15
            )

            // 创建模拟数据
            val mockData = FlowerLeaderboardData(
                inTop = true,
                rankings = mockUsers
            )

            // 打印模拟数据
            Timber.d("Mock data created: $mockData")

            // 更新状态
            setState {
                copy(
                    refresh = Success(mockData),
                    currentUser = if (!isOwner) currentUser else null
                )
            }
        }
    }

    /**
     * 刷新排行榜列表
     */
    fun refreshList() {
        withState { state ->
            setState { copy(refresh = Loading()) }
            Timber.d("Refreshing list for gameId: ${state.gameId}, isOwner: ${state.isOwner}")
            repository.queryFlowerLeaderboard(state.gameId).execute { result ->
                if (result.complete) {
                    val response = result.invoke()
                    val flowerData = response?.data
                    Timber.d("API response: $response, flowerData: $flowerData")
                    if (flowerData != null) {
                        // 设置排名
                        val rankingsList = flowerData.rankings?.mapIndexed { index, item ->
                            item.copy(rank = index + 1, uid = item.uid, nickname = item.nickname ?: "", portrait = item.portrait ?: "", tippingCnt = item.tippingCnt ?: 0)
                        }

                        // 查找当前用户
                        val currentUid = accountInteractor.curUuid
                        var currentUser = rankingsList?.find { it.uid == currentUid }

                        if (currentUser == null) {
                            val currentUserInfo = accountInteractor.getUserInfoFromCache()
                            currentUser = FlowerLeaderboardItemData(
                                currentUserInfo?.uuid ?: "",
                                currentUserInfo?.nickname,
                                currentUserInfo?.portrait,
                                flowerData.userFlowerCnt,
                            )
                        }
                        // 创建新的FlowerLeaderboardData对象
                        val newFlowerData = FlowerLeaderboardData(
                            inTop = flowerData.inTop,
                            rankings = rankingsList
                        )

                        copy(
                            refresh = Success(newFlowerData),
                            currentUser = currentUser
                        )
                    } else {
                        Timber.e("Query flower leaderboard failed: data is null")
                        copy(refresh = Success(FlowerLeaderboardData()))
                    }
                } else {
                    Timber.e("Query flower leaderboard failed")
                    // 当请求失败时，使用空的FlowerLeaderboardData
                    copy(refresh = Loading())
                }
            }
        }
    }

    companion object : KoinViewModelFactory<FlowerLeaderboardViewModel, FlowerLeaderboardModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: FlowerLeaderboardModelState
        ): FlowerLeaderboardViewModel {
            return FlowerLeaderboardViewModel(get(), get(), state)
        }
    }
}
