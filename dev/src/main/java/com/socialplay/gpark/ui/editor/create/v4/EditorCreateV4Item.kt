package com.socialplay.gpark.ui.editor.create.v4

import android.graphics.Color
import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.GenericTransitionOptions
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.FormWorkV4Info
import com.socialplay.gpark.databinding.AdapterEditorCreateV4FormworkGameBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/06
 *     desc   :
 * </pre>
 */
interface IBuildTemplateListener : IBaseEpoxyItemListener {
    fun click(item: FormWorkV4Info, position: Int)
    fun show(item: FormWorkV4Info)
}

fun MetaEpoxyController.mapTemplateItem(
    item: FormWorkV4Info,
    position: Int,
    listener: IBuildTemplateListener,
) {
    add {
        MapTemplateItem(
            item,
            position,
            listener
        ).id("MapTemplate-$it-${item.gameCode}-${item.archiveId}")
    }
}

data class MapTemplateItem(
    val item: FormWorkV4Info,
    val position: Int,
    val listener: IBuildTemplateListener
) : ViewBindingItemModel<AdapterEditorCreateV4FormworkGameBinding>(
    R.layout.adapter_editor_create_v4_formwork_game,
    AdapterEditorCreateV4FormworkGameBinding::bind
) {
    override fun AdapterEditorCreateV4FormworkGameBinding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.formworkImg).placeholder(R.drawable.placeholder_corner_12)
                .transition(GenericTransitionOptions.withNoTransition())
                .into(ivCover)
        }
        val rate = runCatching {
            item.level?.toFloat() ?: 1.0f
        }.getOrElse {
            3.0f
        }.coerceIn(1.0f, 3.0f)
        ratingbar.rating = rate
        ratingbar.ratingCount = rate.toInt()
        tvTitle.text = item.title

        if (item.tagModel?.name.isNullOrEmpty()) {
            tvTag.invisible()
        } else {
            tvTag.visible()
            tvTag.text = item.tagModel?.name
            item.tagConfig?.let {
                runCatching {
                    tvTag.background.setTint(Color.parseColor(it))
                }
            }
        }

        root.setOnAntiViolenceClickListener {
            listener.click(item, position)
        }
    }

    override fun AdapterEditorCreateV4FormworkGameBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.show(item)
        }
    }
}