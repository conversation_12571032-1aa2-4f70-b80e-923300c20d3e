package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/07/07
 *     desc   : https://www.cnblogs.com/xunevermore/p/16064261.html
 * </pre>
 */
open class VerticalCoordinatorLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : CoordinatorLayout(context, attrs, defStyleAttr) {

    override fun onStartNestedScroll(child: View, target: View, axes: Int, type: Int): <PERSON><PERSON><PERSON> {
        if ((axes and ViewCompat.SCROLL_AXIS_HORIZONTAL) != 0) {
            return false
        }
        return super.onStartNestedScroll(child, target, axes, type)
    }

}