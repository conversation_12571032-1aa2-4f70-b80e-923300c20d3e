package com.socialplay.gpark.ui.gamedetail.cover

import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/10 2:39 下午
 * @describe:
 */
object DetailVideoAnalytics {

    var gameId: String? = null

    fun videoShow() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_SHOW) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoPlaySuc() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_PLAY_SUC) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoPause() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_PAUSE) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoGoOnPlay() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_GO_ON_PLAY) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoSeekClick() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_SEEK_CLICK) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoScreenClick() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_CLICK_SCREEN) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoVolumeOpen() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_VOLUME_OPEN) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoVolumeClose() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_VOLUME_CLOSE) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoPlayFinish() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_PLAY_FINISH) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoFullScreenClick() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_FULL_SCREEN_CLICK) {
            gameId?.let { put("gameid", it) }
        }
    }

    fun videoFullScreenBack() {
        Analytics.track(EventConstants.EVENT_GAME_DETAIL_VIDEO_FULL_SCREEN_BACK) {
            gameId?.let { put("gameid", it) }
        }
    }
}