package com.socialplay.gpark.ui.post.v2

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.databinding.AdapterPostCommentV2Binding
import com.socialplay.gpark.databinding.AdapterPostMediaBinding
import com.socialplay.gpark.databinding.AdapterPostReplyMoreV2Binding
import com.socialplay.gpark.databinding.AdapterPostReplyV2Binding
import com.socialplay.gpark.databinding.AdapterPublishNewPostTagBinding
import com.socialplay.gpark.databinding.AdapterPublishPostGameBinding
import com.socialplay.gpark.databinding.AdapterPublishPostMediaBinding
import com.socialplay.gpark.databinding.AdapterPublishPostTagBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.DateUtil.formatCommentDate
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.imageTintListByRes
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.unsetOnClickAndClickable
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/28
 *     desc   :
 * </pre>
 */
interface IPostListener: IBaseEpoxyItemListener {
    fun openMedia(media: PostMediaResource, position: Int) {}
    fun openGame(game: PostCardInfo) {}
    fun clickTag(tag: PostTag) {}
    fun deleteTag(tag: PostTag) {}
    fun delMedia(media: PostMediaResource) {}
    fun delGame(game: PostCardInfo) {}
}

interface IPublishPostListener: IPostListener {
    fun openAlbum()
}

interface IPostDetailListener : IPostListener {
    fun goUserPage(uid: String?)
    fun operateComment(view: View, comment: PostComment, commentPosition: Int)
    fun likeComment(comment: PostComment, commentPosition: Int)
    fun reply2Comment(comment: PostComment, commentPosition: Int)
    fun operateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int
    )
    fun reply2Reply(reply: PostReply, commentPosition: Int)
    fun loadMoreReply(replyId: String, commentId: String, commentPosition: Int)
    fun clickLabel(data: Pair<Int, LabelInfo?>)
    fun isMe(uuid: String?): Boolean
}

fun MetaModelCollector.publishPostMediaItem(
    item: PostMediaResource,
    position: Int,
    listener: IPostListener
) {
    add {
        PublishPostMediaItem(item, position, listener).apply {
            id("PublishPostMediaItem-${item.itemId}")
        }
    }
}

data class PublishPostMediaItem(
    val item: PostMediaResource,
    val position: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostMediaBinding>(
    R.layout.adapter_publish_post_media,
    AdapterPublishPostMediaBinding::bind
) {
    override fun AdapterPublishPostMediaBinding.onBind() {
        val url = if (item.isImage) {
            ivPlay.gone()
            item.thumbnail
        } else if (item.isVideo) {
            ivPlay.visible()
            ivPlay.setImageResource(R.drawable.ic_post_play)
            item.thumbnail
        } else {
            ivPlay.gone()
            ""
        }
        ivDelBtn.visible()
        listener.getGlideOrNull()?.run {
            load(url).placeholder(R.drawable.bg_f6f6f6_round_6)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivCover)
        }
        root.setOnAntiViolenceClickListener { listener.openMedia(item, position) }
        ivDelBtn.setOnAntiViolenceClickListener { listener.delMedia(item) }
    }

    override fun AdapterPublishPostMediaBinding.onUnbind() {
        root.unsetOnClick()
        ivDelBtn.unsetOnClick()
    }
}

fun MetaModelCollector.publishPostMediaAddItem(
    listener: IPublishPostListener
) {
    add {
        PublishPostMediaAddItem(listener).apply {
            id("PublishPostMediaAddItem-$it")
        }
    }
}

data class PublishPostMediaAddItem(
    val listener: IPublishPostListener
) : ViewBindingItemModel<AdapterPublishPostMediaBinding>(
    R.layout.adapter_publish_post_media,
    AdapterPublishPostMediaBinding::bind
) {
    override fun AdapterPublishPostMediaBinding.onBind() {
        ivCover.setImageResource(R.color.color_F6F6F6)
        ivPlay.visible()
        ivPlay.setImageResource(R.drawable.ic_post_add)
        ivDelBtn.gone()
        root.setOnAntiViolenceClickListener { listener.openAlbum() }
    }

    override fun AdapterPublishPostMediaBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaModelCollector.postMediaItem(
    item: PostMediaResource,
    position: Int,
    totalCount: Int,
    listener: IPostListener
) {
    add {
        PostMediaItem(item, position, totalCount, listener).id("PostMediaItem-$it")
    }
}

data class PostMediaItem(
    val item: PostMediaResource,
    val position: Int,
    val totalCount: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPostMediaBinding>(
    R.layout.adapter_post_media,
    AdapterPostMediaBinding::bind
) {
    override fun AdapterPostMediaBinding.onBind() {
        val target = if (totalCount == 1) {
            val itemWidth = ScreenUtil.screenWidth - 32.dp
            ivAuto.visible()
            ivFixed.gone()
            ivAuto.setHeight(
                if (item.resourceWidth > 0 && item.resourceHeight > 0) {
                    ((itemWidth) * (item.resourceHeight / item.resourceWidth.toFloat())).toInt()
                } else { // 不知道宽或高的情况下自适应
                    ViewGroup.LayoutParams.WRAP_CONTENT
                }
            )
            ivAuto.maxHeight = when {
                item.isVideo -> (itemWidth * 1.3F).toInt()
                item.isHorizontal -> 350.dp
                else -> 442.dp
            }
            ivAuto
        } else {
            ivAuto.gone()
            ivFixed.visible()
            ivFixed
        }
        listener.getGlideOrNull()?.run {
            load(item.thumbnail).placeholder(R.drawable.placeholder_corner_6)
                .into(target)
        }
        ivPlay.visible(item.isVideo)
        root.setOnAntiViolenceClickListener { listener.openMedia(item, position) }
    }

    override fun AdapterPostMediaBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun getSpanSize(totalSpanCount: Int, position: Int, itemCount: Int): Int {
        return when (itemCount) {
            1 -> 6
            2 -> 3
            else -> 2
        }
    }
}

fun MetaModelCollector.postGameItem(
    item: PostCardInfo,
    margin: Int,
    enableClick: Boolean,
    listener: IPostListener
) {
    add {
        PostGameItem(
            item,
            margin,
            enableClick,
            listener
        ).id("PostGameItem-${item.resourceType}-${item.gameId}")
    }
}

data class PostGameItem(
    val item: PostCardInfo,
    val margin: Int,
    val enableClick: Boolean,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostGameBinding>(
    R.layout.adapter_publish_post_game,
    AdapterPublishPostGameBinding::bind
) {
    override fun AdapterPublishPostGameBinding.onBind() {
        root.setMargin(top = margin)
        listener.getGlideOrNull()?.run {
            load(item.gameIcon).error(R.drawable.placeholder)
                .into(includeGameInfo.ivIcon)
        }
        includeGameInfo.tvGameTitle.text = item.gameName
        includeGameInfo.tvGameAuthor.setTextWithArgs(R.string.creator_cap_with_param, item.gameAuthor)
        when (item.resourceType) {
            PostCardInfo.TYPE_PGC -> {
                includeGameInfo.tvGamePeople.gone()
                includeGameInfo.tvGameLike.visible()
                includeGameInfo.tvGameLike.text = item.likeCountStr
                includeGameInfo.tvGameScore.gone()
            }

            PostCardInfo.TYPE_UGC -> {
                includeGameInfo.tvGamePeople.visible()
                includeGameInfo.tvGamePeople.text = UnitUtil.formatPlayerCount(item.player)
                includeGameInfo.tvGameLike.gone()
                includeGameInfo.tvGameScore.gone()
            }

            else -> {
                includeGameInfo.tvGamePeople.gone()
                includeGameInfo.tvGameLike.gone()
                includeGameInfo.tvGameScore.gone()
            }
        }
        tvView.visible(enableClick)
        ivDelBtn.visible(!enableClick)
        if (enableClick) {
            root.setOnAntiViolenceClickListener { listener.openGame(item) }
            ivDelBtn.unsetOnClick()
        } else {
            root.unsetOnClickAndClickable()
            ivDelBtn.setOnClickListener { listener.delGame(item) }
        }
    }

    override fun AdapterPublishPostGameBinding.onUnbind() {
        root.unsetOnClickAndClickable()
        ivDelBtn.unsetOnClick()
    }
}

fun MetaModelCollector.postTagItem(
    item: PostTag,
    margin: Int,
    enableDelete: Boolean,
    color: Int,
    listener: IPostListener
) {
    add {
        PostTagItem(
            item,
            margin,
            enableDelete,
            color,
            listener
        ).id("AddTagItem-${item.tagId}")
    }
}
fun MetaModelCollector.postNewTagItem(
    item: PostTag,
    listener: IPostListener
) {
    add {
        PostNewTagItem(
            item,
            listener
        ).id("AddTagItem-${item.tagId}")
    }
}

data class PostTagItem(
    val item: PostTag,
    val margin: Int,
    val enableDelete: Boolean,
    val color: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostTagBinding>(
    R.layout.adapter_publish_post_tag,
    AdapterPublishPostTagBinding::bind
) {
    override fun AdapterPublishPostTagBinding.onBind() {
        root.setMargin(left = margin, top = margin)
        tvTopicTitle.text = item.tagName
        if (color != 0) {
            tvTopicTitle.setTextColor(color)
        }
        root.setOnAntiViolenceClickListener { listener.clickTag(item) }
        if (enableDelete) {
            ivTopicDel.visible()
            ivTopicDel.setOnAntiViolenceClickListener { listener.deleteTag(item) }
        } else {
            ivTopicDel.gone()
            ivTopicDel.unsetOnClick()
        }
    }

    override fun AdapterPublishPostTagBinding.onUnbind() {
        root.unsetOnClick()
        ivTopicDel.unsetOnClick()
    }
}
data class PostNewTagItem(
    val item: PostTag,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishNewPostTagBinding>(
    R.layout.adapter_publish__new_post_tag,
    AdapterPublishNewPostTagBinding::bind
) {
    override fun AdapterPublishNewPostTagBinding.onBind() {
        tvTopicTitle.text = PublishPostFragment.TAG_RULE + item.tagName

        tvViews.text = getItemView().context.getString(R.string.x_views, UnitUtil.formatKMCount2(item.viewCount))
        root.setOnAntiViolenceClickListener { listener.clickTag(item) }
    }

    override fun AdapterPublishNewPostTagBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaModelCollector.postCommentItem(
    item: PostComment,
    commentListRefreshTime: Long,
    position: Int,
    authorHeight: Int,
    selectMore: Boolean,
    listener: IPostDetailListener
) {
    add {
        PostCommentItem(
            item,
            position,
            authorHeight,
            selectMore,
            listener
        ).id("PostCommentItem-${commentListRefreshTime}-${item.commentId}")
    }
}

data class PostCommentItem(
    val item: PostComment,
    val position: Int,
    val authorHeight: Int,
    val selectMore: Boolean,
    val listener: IPostDetailListener
) : ViewBindingItemModel<AdapterPostCommentV2Binding>(
    R.layout.adapter_post_comment_v2,
    AdapterPostCommentV2Binding::bind
) {
    override fun AdapterPostCommentV2Binding.onBind() {
        guideUserInfo.updateLayoutParams<ConstraintLayout.LayoutParams> {
            guideBegin = authorHeight
        }

        listener.getGlideOrNull()?.run {
            load(item.avatar).error(R.drawable.placeholder)
                .into(ivAuthorAvatar)
        }
        tvAuthorName.text = item.nickname
        ivOfficial.show(
            item.tagIds ?: item.user?.tagIds,
            item.userLabelInfo ?: item.user?.labelInfo,
            isMe = listener.isMe(item.uid),
            isOfficial = item.user?.isOfficial == true,
            isUnderReview = item.underReview,
            glide = listener.getGlideOrNull()
        )
        tvTime.text = item.commentTime.formatCommentDate(root.context)
        tvContent.text = item.content
        if (item.isLike) {
            ivLikeCount.setImageResource(R.drawable.icon_post_like_selected)
            tvLikeCount.setTextColorByRes(R.color.textColorPrimary)
        } else {
            ivLikeCount.setImageResource(R.drawable.icon_post_like_unselected)
            tvLikeCount.setTextColorByRes(R.color.textColorSecondary)
        }
        tvLikeCount.text = UnitUtil.formatKMCount(item.likeCount)
        tvCommentCount.text = UnitUtil.formatKMCount(item.replyTotalCount)
        vTimeline.visible(item.hasReply)
        if (selectMore) {
            ivMoreBtn.imageTintListByRes(R.color.color_4AB4FF)
        } else {
            ivMoreBtn.imageTintList = null
        }

        vAuthorClick.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        ivMoreBtn.setOnAntiViolenceClickListener { listener.operateComment(it, item, position) }
        vLikeClick.setOnAntiViolenceClickListener { listener.likeComment(item, position) }
        tvContent.setOnClickListener { listener.reply2Comment(item, position) }
        vCommentClick.setOnClickListener { listener.reply2Comment(item, position) }
        ivOfficial.setListener {
            listener.clickLabel(it)
        }
    }

    override fun AdapterPostCommentV2Binding.onUnbind() {
        vAuthorClick.unsetOnClick()
        ivMoreBtn.unsetOnClick()
        vLikeClick.unsetOnClick()
        tvContent.unsetOnClick()
        vCommentClick.unsetOnClick()
        ivOfficial.setListener(null)
    }
}

fun MetaModelCollector.postReplyItem(
    item: PostReply,
    replyPosition: Int,
    commentPosition: Int,
    atColor: Int,
    selectMore: Boolean,
    listener: IPostDetailListener
) {
    add {
        PostReplyItem(
            item,
            replyPosition,
            commentPosition,
            atColor,
            selectMore,
            listener
        ).id("PostReplyItem-${item.replyId}")
    }
}

data class PostReplyItem(
    val item: PostReply,
    val replyPosition: Int,
    val commentPosition: Int,
    val atColor: Int,
    val selectMore: Boolean,
    val listener: IPostDetailListener
) : ViewBindingItemModel<AdapterPostReplyV2Binding>(
    R.layout.adapter_post_reply_v2,
    AdapterPostReplyV2Binding::bind
) {
    override fun AdapterPostReplyV2Binding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.avatar).error(R.drawable.placeholder)
                .into(ivAuthorAvatar)
        }
        tvAuthorName.text = item.nickname
        ivOfficial.show(
            item.tagIds,
            item.labelInfo ?: item.user?.labelInfo,
            isMe = listener.isMe(item.uid),
            isOfficial = item.user?.isOfficial == true,
            glide = listener.getGlideOrNull()
        )
        tvTime.text = item.replyTime.formatCommentDate(root.context)
        tvContent.text =  if (item.replyUid.isNullOrBlank()) {
            item.content
        } else {
            tvContent.movementMethod = InterceptClickEventLinkMovementMethod(tvContent)
            SpannableHelper.Builder()
                .text("@${item.replyName}:")
                .click(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        listener.goUserPage(item.replyUid)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = false
                        ds.color = atColor
                    }
                })
                .text(" " + item.content.orEmpty())
                .build()
        }
        if (selectMore) {
            ivMoreBtn.imageTintListByRes(R.color.color_4AB4FF)
        } else {
            ivMoreBtn.imageTintList = null
        }

        vAuthorClick.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        tvContent.setOnClickListener {
            listener.reply2Reply(item, commentPosition)
        }
        ivMoreBtn.setOnAntiViolenceClickListener {
            listener.operateReply(
                it,
                item,
                replyPosition,
                commentPosition
            )
        }
        ivOfficial.setListener {
            listener.clickLabel(it)
        }
    }

    override fun AdapterPostReplyV2Binding.onUnbind() {
        tvContent.movementMethod = null
        tvContent.unsetOnClick()
        vAuthorClick.unsetOnClick()
        ivMoreBtn.unsetOnClick()
        ivOfficial.setListener(null)
    }
}

fun MetaModelCollector.postReplyMoreItem(
    replyId: String,
    commentId: String,
    commentPosition: Int,
    listener: IPostDetailListener
) {
    add {
        PostReplyMoreItem(
            replyId,
            commentId,
            commentPosition,
            listener
        ).id("PostReplyMoreItem-${replyId}")
    }
}

data class PostReplyMoreItem(
    val replyId: String,
    val commentId: String,
    val commentPosition: Int,
    val listener: IPostDetailListener
) : ViewBindingItemModel<AdapterPostReplyMoreV2Binding>(
    R.layout.adapter_post_reply_more_v2,
    AdapterPostReplyMoreV2Binding::bind
) {
    override fun AdapterPostReplyMoreV2Binding.onBind() {
        tvMoreReply.setOnAntiViolenceClickListener {
            listener.loadMoreReply(replyId, commentId, commentPosition)
        }
    }

    override fun AdapterPostReplyMoreV2Binding.onUnbind() {
        tvMoreReply.unsetOnClick()
    }
}