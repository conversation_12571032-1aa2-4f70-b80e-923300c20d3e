package com.socialplay.gpark.ui.mgs.listener

import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsPlayerInfo

/**
 * Created by bo.li
 * Date: 2021/8/16
 * Desc: MGS用户资料卡片
 */
interface OnMgsPlayerInfoDialogListener {
    fun onClickAddFriend(data: MgsPlayerInfo)
    fun onClickDressUp()
    fun onClickJumpGame(itemGameId: String)
    fun onDismissDialog()
    fun onChangeVoiceState(isOpen: Boolean, openId: String, from: String)
    fun getMemberInfo(uuid: String): Member?
    fun onClickJumpGameDetail(ugcId: String, gameid: String)
}