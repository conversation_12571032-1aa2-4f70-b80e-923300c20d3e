package com.socialplay.gpark.ui.kol.game

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.model.creator.label.UgcKolSpecificLabel
import com.socialplay.gpark.data.model.creator.moreugc.LocalCreatorUgcWrapper
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc:kol更多ugc游戏：推荐
 */
class RecommendKolMoreUgcViewModel(
    private val repository: IMetaRepository,
    initialState: KolMoreUgcModelState
) : BaseKolMoreUgcViewModel(initialState) {

    init {
        refresh()
    }

    companion object :
        KoinViewModelFactory<RecommendKolMoreUgcViewModel, KolMoreUgcModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: KolMoreUgcModelState
        ): RecommendKolMoreUgcViewModel {
            return RecommendKolMoreUgcViewModel(get(), state)
        }
    }

    private fun KolMoreUgcModelState.getPageNum(): Int {
        return rollId?.toIntOrNull() ?: 1
    }

    override fun refresh() {
        withState { oldState ->
            if (oldState.asyncList is Loading) return@withState
            repository.getKolRecommendUgcGameListByPage(null).map {
                LocalCreatorUgcWrapper(it.dataList?.map { it.copy(localLabel = UgcKolSpecificLabel.buildLabelById(it.gameShowTag, it.isNewGame))  }, it.end || it.dataList.isNullOrEmpty())
            }.execute { result ->
                val end = result()?.end == true || result()?.gameList.isNullOrEmpty()
                copy(
                    asyncList = result.map {
                        it.gameList?.distinctBy { it.id } ?: emptyList()
                    },
                    rollId = (1 + if (result is Success) 1 else 0).toString(),
                    loadMore = result.map { LoadMoreState(end) },
                )
            }
        }

    }

    override fun loadMore() {
        withState { oldState ->
            if (oldState.asyncList is Loading) return@withState
            repository.getKolRecommendUgcGameListByPage(oldState.getPageNum()).map {
                LocalCreatorUgcWrapper(it.dataList?.map { it.copy(localLabel = UgcKolSpecificLabel.buildLabelById(it.gameShowTag, it.isNewGame))  }, it.end || it.dataList.isNullOrEmpty())
            }.execute { result ->
                val end = result()?.end == true || result()?.gameList.isNullOrEmpty()
                copy(
                    asyncList = if (result is Success) {
                        val oldList = asyncList.invoke()
                        result.map { wrapper ->
                            if (oldList.isNullOrEmpty()) {
                                wrapper.gameList ?: emptyList()
                            } else {
                                oldList + (wrapper.gameList ?: emptyList())
                            }.distinctBy { it.id }
                        }
                    } else {
                        asyncList
                    },
                    rollId = (getPageNum() + if (result is Success) 1 else 0).toString(),
                    loadMore = result.map { LoadMoreState(end) },
                )
            }
        }
    }
}