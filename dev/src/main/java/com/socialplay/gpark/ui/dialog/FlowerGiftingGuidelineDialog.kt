package com.socialplay.gpark.ui.dialog

import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.view.Gravity
import androidx.annotation.StyleRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.databinding.DialogFlowerGiftingGuidelineBinding
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.router.MetaRouter
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import com.socialplay.gpark.ui.base.BaseDialogFragmentExt
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

/**
 * 送花排行榜指南弹框
 * 点击送花排行榜标题旁边的问号按钮时显示
 */
class FlowerGiftingGuidelineDialog : BaseDialogFragmentExt(), KoinComponent {

    companion object {
        fun show(fragmentManager: FragmentManager, onConfirmCallback: (() -> Unit)? = null) {
            FlowerGiftingGuidelineDialog().apply {
                this.onConfirmCallback = onConfirmCallback
            }.show(fragmentManager, "FlowerGiftingGuidelineDialog")
        }
    }

    @StyleRes
    override fun getStyle() = R.style.DialogStyleFloating

    private var onConfirmCallback: (() -> Unit)? = null

    // 是否是通过点击按钮关闭的
    private var isDismissByButton = false

    // H5页面配置
    private val h5PageConfigInteractor: H5PageConfigInteractor by inject()

    private val _contentBinding by viewBinding(DialogFlowerGiftingGuidelineBinding::inflate)

    override val contentBinding: ViewBinding
        get() = _contentBinding

    override fun init() {
        _contentBinding.tvContent.text = createContent()
        _contentBinding.tvContent.movementMethod = LinkMovementMethod.getInstance()

        _contentBinding.btnKnow.setOnAntiViolenceClickListener {
            // 设置标志，表示是通过点击按钮关闭的
            isDismissByButton = true
            dismissAllowingStateLoss()
            onConfirmCallback?.invoke()
            onConfirmCallback = null
        }
    }

    private fun createContent(): SpannableStringBuilder {
        val color = ContextCompat.getColor(requireContext(), R.color.color_9242FF)
        val builder = SpannableHelper.Builder()

        // 共用的部分
        val part1 = getString(R.string.flower_gifting_guideline_part1)
        val flowerGiftingGuidelines = getString(R.string.flower_gifting_guidelines)
        val part3 = getString(R.string.flower_gifting_guideline_part3)

        builder.text(part1)

        if (EnvConfig.isParty()) {
            // Party版本显示支付协议和送花指南
            val paymentAgreement = getString(R.string.payment_agreement)
            val part2 = getString(R.string.flower_gifting_guideline_part2)

            // 支付协议
            builder.text(" ")
                .text(paymentAgreement).click {
                    val item = PayProvider.getBuyCoinsPageRechargeH5ConfigItem()
                    MetaRouter.Web.navigate(this, item.title, item.url)
                }.color(color)
                .underline()
                .bold(true)
                .text(part2)
        } else {
            // GPark版本只显示送花指南
            builder.text(" ")
        }

        // 送花指南
        builder.text(flowerGiftingGuidelines)
            .click {
                val item =
                    h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_NOTICE_URL)
                MetaRouter.Web.navigate(this, item.title, item.url)
            }.color(color)
            .underline()
            .bold(true)
            .text(part3)

        return builder.build()
    }

    override fun loadFirstData() {
        // Not needed
    }

    override fun gravity(): Int = Gravity.BOTTOM

    override fun contentMarginBottom(): Int = resources.getDimensionPixelSize(R.dimen.dp_42)

    // 如果是从 FlowerLeaderboardDialog 打开的，使用嵌套对话框特殊处理
    override fun useNestedDialogHandling(): Boolean {
        return isOpenedFromAnotherDialog()
    }

    // 如果是通过点击按钮关闭的，不使用背景动画
    override fun useBackgroundAnimation(): Boolean {
        return !isDismissByButton
    }
}
