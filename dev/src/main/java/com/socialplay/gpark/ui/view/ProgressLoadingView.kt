package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.sp
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.min

/**
 * 2023/10/26
 */
class ProgressLoadingView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {

    private val bgColor = Color.parseColor("#535353")
    private val progressColor = Color.WHITE
    private val textColor = Color.WHITE

    private val bgWidth = 4.dp * 1F
    private val progressWidth = 4.dp * 1F
    private val textSize = 14.sp

    private val max = AtomicLong(10000L)
    private val progress = AtomicLong(5000L)

    private val paint = Paint()

    fun setMax(value: Long) {
        max.set(value)
        postInvalidate()
    }

    fun setProgress(value: Long) {
        progress.set(value)
        postInvalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.apply {
            drawBg(this)
            drawProgress(this)
            drawText(this)
        }
    }

    private fun drawBg(canvas: Canvas) {
        paint.reset()
        paint.isAntiAlias = true
        paint.color = bgColor
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = bgWidth
        val x = canvas.width / 2F
        val y = canvas.height / 2F
        val radius = min(x, y) - progressWidth / 2F
        canvas.drawCircle(x, y, radius, paint)
    }

    private fun drawProgress(canvas: Canvas) {
        paint.reset()
        paint.isAntiAlias = true
        paint.color = progressColor
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = progressWidth
        paint.strokeCap = Paint.Cap.ROUND
        val sweepAngle = progress.get() * 360F / max.get()
        val rectF = RectF(0F, 0F, canvas.width * 1F, canvas.height * 1F)
        rectF.inset(progressWidth / 2F, progressWidth / 2F)
        canvas.drawArc(rectF, -90F, sweepAngle, false, paint)
    }

    private fun drawText(canvas: Canvas) {
        val text = getText()
        paint.reset()
        paint.isAntiAlias = true
        paint.color = textColor
        paint.textSize = textSize.toFloat()
        val centerX = canvas.width / 2F
        val centerY = canvas.height / 2F
        val measureText = paint.measureText(text)
        val x = centerX - measureText / 2
        canvas.drawText(text, x, centerY + textSize / 3, paint)
    }

    private fun getText(): String {
        val p = (progress.get() * 100F / max.get()).toInt()
        return "$p%"
    }

}