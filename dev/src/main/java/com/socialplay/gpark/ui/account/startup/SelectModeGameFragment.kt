package com.socialplay.gpark.ui.account.startup

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.databinding.FragmentStartupSelectModeGameBinding
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.extension.navigateUp
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/27
 *     desc   :
 * </pre>
 */
class SelectModeGameFragment :
    BaseFragment<FragmentStartupSelectModeGameBinding>(R.layout.fragment_startup_select_mode_game) {

    private val viewModel by sharedViewModel<MainViewModel>()
    private val vm: SelectModeGameViewModel by fragmentViewModel()
    private val metaKV: MetaKV by inject()
    private val tsLaunch by lazy { TSLaunch() }
    private val gameStartScenes by lazy { MWGameStartScenes(this) }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStartupSelectModeGameBinding? {
        return FragmentStartupSelectModeGameBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        gameStartScenes.show(horizontal = true, autoHide = false)

        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchGameEnd { _, _ ->
                vm.hide(delay = true)
            }
        }
        vm.onEach(SelectModeGameState::hide) {
            if (it) {
                gameStartScenes.hide()
                navigateUp()
            }
        }

        val gameInfo = viewModel.modeGameLiveData.value
        if (gameInfo == null) {
            viewModel.fetchModeGameInfo()
            viewModel.modeGameLiveData.observe(viewLifecycleOwner) {
                launchModeGame(it)
            }
        } else {
            launchModeGame(gameInfo)
        }
    }

    private fun launchModeGame(gameInfo: GameDetailInfo?) {
        if (gameInfo == null) {
            vm.hide()
            return
        }
        viewModel.resetModeGameInfo()
        val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.SELECT_MODE_WORLD_MAP)
        if (!tsLaunch.isLaunching(gameInfo.id)) {
            metaKV.analytic.saveClickLaunchTime(gameInfo.packageName, System.currentTimeMillis())
            val params = TSLaunchParams(gameInfo, resIdBean)
            tsLaunch.launch(requireContext(), params)
        }
    }

    override fun onDestroyView() {
        gameStartScenes.hide()
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
        if (!gameStartScenes.isShowing()) {
            navigateUp()
        }
    }

    override fun invalidate() {}

    override fun getPageName() = ""
}