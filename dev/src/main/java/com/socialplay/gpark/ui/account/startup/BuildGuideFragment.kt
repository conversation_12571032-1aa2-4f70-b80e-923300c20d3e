package com.socialplay.gpark.ui.account.startup

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.view.doOnAttach
import androidx.core.view.isInvisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.databinding.FragmentStartupBuildGuideBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.view.BuildGuideBtnBg
import com.socialplay.gpark.ui.view.BuildGuideCheckBtn
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber
import kotlin.math.max

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/31
 *     desc   :
 * </pre>
 */
@Parcelize
data class BuildGuideFragmentArgs(
    val showBack: Boolean
) : Parcelable

class BuildGuideFragment :
    BaseFragment<FragmentStartupBuildGuideBinding>(R.layout.fragment_startup_build_guide) {

    override var navColorRes = R.color.navigation_bar_color

    private val vm: BuildGuideViewModel by fragmentViewModel()
    private val mainVM by sharedViewModel<MainViewModel>()
    private val args by args<BuildGuideFragmentArgs>()

    private var animFlag = true
    private var animator: ValueAnimator? = null
    private var animatorSet: AnimatorSet? = null

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStartupBuildGuideBinding? {
        return FragmentStartupBuildGuideBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tbl.isInvisible = !args.showBack
        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.vBgPlayMap.setOnAntiViolenceClickListener {
            if (animator != null) return@setOnAntiViolenceClickListener
            vm.selectMode(true)
        }
        binding.vBgBuildMap.setOnAntiViolenceClickListener {
            if (animator != null) return@setOnAntiViolenceClickListener
            vm.selectMode(false)
        }
        binding.tvNextBtn.setOnAntiViolenceClickListener {
            if (vm.isBanned()) {
                vm.banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_EDIT_PROFILE, this)
            } else {
                val oldState = vm.oldState
                val playOrBuild = oldState.playOrBuild ?: return@setOnAntiViolenceClickListener
                if (playOrBuild) {
                    Analytics.track(
                        EventConstants.GO_PLAY_THE_GAME_CLICK
                    )
                    mainVM.buildMode = 0
                    MetaRouter.Startup.createAvatar(this, true, fromBuildGuide = true)
                } else {
                    Analytics.track(
                        EventConstants.MAKE_A_GAME_CLICK
                    )
                    mainVM.buildMode = 1
                    val (avatar, portraitRes) = oldState.getAvatarInfo()
                    MetaRouter.Startup.enterName(this, avatar, portraitRes, skipAvatar = true)
                }
            }
        }

        vm.onEach(BuildGuideState::playOrBuild, deliveryMode = uniqueOnly()) {
            when (it) {
                true -> {
                    animPress(
                        binding.clPlayMapContainer,
                        binding.vBgPlayMap,
                        binding.ivPlayMap,
                        binding.tvPlayMap,
                        binding.cbPlayMap,
                        binding.clBuildMapContainer,
                        binding.vBgBuildMap,
                        binding.ivBuildMap,
                        binding.tvBuildMap,
                        binding.cbBuildMap
                    )

                    binding.tvNextBtn.enableWithAlpha(true)
                }

                false -> {
                    animPress(
                        binding.clBuildMapContainer,
                        binding.vBgBuildMap,
                        binding.ivBuildMap,
                        binding.tvBuildMap,
                        binding.cbBuildMap,
                        binding.clPlayMapContainer,
                        binding.vBgPlayMap,
                        binding.ivPlayMap,
                        binding.tvPlayMap,
                        binding.cbPlayMap
                    )

                    binding.tvNextBtn.enableWithAlpha(true)
                }

                else -> {
                    binding.vBgPlayMap.reset()
                    binding.cbPlayMap.reset()

                    binding.vBgBuildMap.reset()
                    binding.cbBuildMap.reset()

                    binding.tvNextBtn.enableWithAlpha(false)
                }
            }
        }

        if (animFlag) {
            animFlag = false
            binding.root.doOnAttach {
                val trans = max(screenWidth, dp(1250)).toFloat()
                val x1 = 0.95f
                val x2 = 0.5f
                val y11 = 1.1f
                val y12 = 1.15f
                val y13 = 1.2f

                val pi1 = PathInterpolator(x1, y11, x2, 1.0f)
                val pi2 = PathInterpolator(x1, y12, x2, 1.0f)
                val pi3 = PathInterpolator(x1, y13, x2, 1.0f)
                val pia = PathInterpolator(1.0f, 0.0f, 1.0f, 1.0f)

                binding.tvGuideTitle.translationX = trans
                val vaTitle = ValueAnimator.ofFloat(trans, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val transX = it.animatedValue as Float
                        binding.tvGuideTitle.translationX = transX

                        val fraction = pia.getInterpolation(it.animatedFraction)
                        binding.tvGuideTitle.alpha = fraction
                    }
                    interpolator = pi1
                    duration = 692
                }

                binding.tvDesc.translationX = trans
                val vaContent = ValueAnimator.ofFloat(trans, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val transX = it.animatedValue as Float
                        binding.tvDesc.translationX = transX

                        val fraction = pia.getInterpolation(it.animatedFraction)
                        binding.tvDesc.alpha = fraction
                    }
                    interpolator = pi2
                    duration = 705
                    startDelay = 50
                }

                binding.clPlayMapContainer.translationX = trans
                binding.clBuildMapContainer.translationX = trans

                val vaPlay = ValueAnimator.ofFloat(trans, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val transX = it.animatedValue as Float
                        binding.clPlayMapContainer.translationX = transX

                        val fraction = pia.getInterpolation(it.animatedFraction)
                        binding.vBgPlayMap.alpha = fraction
                        binding.ivPlayMap.alpha = fraction
                        binding.cbPlayMap.alpha = fraction
                    }
                    interpolator = pi1
                    duration = 729
                    startDelay = 100
                }
                val vaBuild = ValueAnimator.ofFloat(trans, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val transX = it.animatedValue as Float
                        binding.clBuildMapContainer.translationX = transX

                        val fraction = pia.getInterpolation(it.animatedFraction)
                        binding.vBgBuildMap.alpha = fraction
                        binding.ivBuildMap.alpha = fraction
                        binding.cbBuildMap.alpha = fraction
                    }
                    interpolator = pi2
                    duration = 729
                    startDelay = 150
                }

                binding.tvNextBtn.translationX = trans
                val vaNext = ValueAnimator.ofFloat(trans, 0.0f).apply {
                    addUpdateListener(viewLifecycleOwner) {
                        val transX = it.animatedValue as Float
                        binding.tvNextBtn.translationX = transX

                        val fraction = pia.getInterpolation(it.animatedFraction) * 0.5f
                        binding.tvNextBtn.alpha = fraction
                    }
                    interpolator = pi3
                    duration = 705
                    startDelay = 200
                }

                val animatorSet = AnimatorSet()
                animatorSet.doOnEnd {
                    if (it == this.animatorSet) {
                        this.animatorSet = null
                    }
                }
                animatorSet.playTogether(vaTitle, vaContent, vaPlay, vaBuild, vaNext)
                animatorSet.start()
                this.animatorSet = animatorSet
            }
        } else {
            binding.tvGuideTitle.alpha = 1.0f
            binding.tvDesc.alpha = 1.0f
            binding.vBgPlayMap.alpha = 1.0f
            binding.ivPlayMap.alpha = 1.0f
            binding.cbPlayMap.alpha = 1.0f
            binding.vBgBuildMap.alpha = 1.0f
            binding.ivBuildMap.alpha = 1.0f
            binding.cbBuildMap.alpha = 1.0f
            when (vm.oldState.playOrBuild) {
                true -> {
                    switchPress(
                        binding.clPlayMapContainer,
                        binding.vBgPlayMap,
                        binding.ivPlayMap,
                        binding.tvPlayMap,
                        binding.cbPlayMap,
                        binding.clBuildMapContainer,
                        binding.vBgBuildMap,
                        binding.ivBuildMap,
                        binding.tvBuildMap,
                        binding.cbBuildMap
                    )

                    binding.tvNextBtn.enableWithAlpha(true)
                }

                false -> {
                    switchPress(
                        binding.clBuildMapContainer,
                        binding.vBgBuildMap,
                        binding.ivBuildMap,
                        binding.tvBuildMap,
                        binding.cbBuildMap,
                        binding.clPlayMapContainer,
                        binding.vBgPlayMap,
                        binding.ivPlayMap,
                        binding.tvPlayMap,
                        binding.cbPlayMap
                    )

                    binding.tvNextBtn.enableWithAlpha(true)
                }

                else -> {
                    binding.clPlayMapContainer.alpha = 1.0f
                    binding.clPlayMapContainer.scaleX = 1.0f
                    binding.clPlayMapContainer.scaleY = 1.0f
                    binding.vBgPlayMap.reset()
                    binding.cbPlayMap.reset()

                    binding.clBuildMapContainer.alpha = 1.0f
                    binding.clBuildMapContainer.scaleX = 1.0f
                    binding.clBuildMapContainer.scaleY = 1.0f
                    binding.vBgBuildMap.reset()
                    binding.cbBuildMap.reset()

                    binding.tvNextBtn.enableWithAlpha(false)
                }
            }
        }

        Analytics.track(
            EventConstants.BUILD_NEWBIE_GUIDE_SHOW
        )
    }

    private fun switchPress(
        container: ViewGroup,
        bg: BuildGuideBtnBg,
        icon: View,
        text: View,
        check: BuildGuideCheckBtn,
        containerOther: ViewGroup,
        bgOther: BuildGuideBtnBg,
        iconOther: View,
        textOther: View,
        checkOther: BuildGuideCheckBtn,
    ) {
        container.alpha = 1.0f
        container.scaleX = 1.0f
        container.scaleY = 1.0f
        bg.updateValue(1.0f)
        check.updateValue(1.0f)

        containerOther.alpha = 0.3f
        containerOther.scaleX = 1.0f
        containerOther.scaleY = 1.0f
        bgOther.reset()
        checkOther.reset()
    }

    private fun animPress(
        container: ViewGroup,
        bg: BuildGuideBtnBg,
        icon: View,
        text: View,
        check: BuildGuideCheckBtn,
        containerOther: ViewGroup,
        bgOther: BuildGuideBtnBg,
        iconOther: View,
        textOther: View,
        checkOther: BuildGuideCheckBtn,
    ) {
        animator?.cancel()

        container.alpha = 1.0f

        containerOther.scaleX = 1.0f
        containerOther.scaleY = 1.0f
        bgOther.reset()
        checkOther.reset()

        val scaleInterpolator1 = PathInterpolator(0.17f, 0.17f, 0.38f, 1.0f)
        val scaleInterpolator2 = PathInterpolator(0.62f, 2.4f, 0.38f, 1.0f)
        val fragInterpolator = PathInterpolator(1.0f, 0.0f, 0.07f, 1.0f)
        val dur = 750
        val checkDelay = 234

        animator = ValueAnimator.ofInt(0, dur).apply {
            addUpdateListener(viewLifecycleOwner) {
                val curVal = it.animatedValue as Int
                val scale = if (curVal <= 167) {
                    1.0f + 0.1f * scaleInterpolator1.getInterpolation(curVal / 167.0f)
                } else {
                    1.1f - 0.1f * scaleInterpolator2.getInterpolation((curVal - 167) / 300.0f)
                }

                container.scaleX = scale
                container.scaleY = scale

                val curFraction = it.animatedFraction
                val alphaFactor = (4.9f * curFraction).coerceAtMost(0.7f)
                val antiAlpha = 1.0f - alphaFactor
                containerOther.alpha = antiAlpha

                bg.updateValue(fragInterpolator.getInterpolation(curFraction))

                if (curVal > checkDelay) {
                    check.updateValue((curVal - checkDelay) / (dur - checkDelay).toFloat())
                }
            }
            doOnEnd {
                if (it == animator) {
                    animator = null
                }
            }
            duration = dur.toLong()
            start()
        }
    }

    override fun onDestroyView() {
        animatorSet?.cancel()
        animatorSet = null
        animator?.cancel()
        animator = null
        super.onDestroyView()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_BUILD_GUIDE
}