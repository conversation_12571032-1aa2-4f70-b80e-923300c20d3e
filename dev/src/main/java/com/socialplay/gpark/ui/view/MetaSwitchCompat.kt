package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.AttrRes
import androidx.appcompat.widget.SwitchCompat
import com.socialplay.gpark.util.ScreenUtil
import java.lang.reflect.Field

/**
 * Created by bo.li
 * Date: 2021/12/9
 * Desc: 自定义switch宽度
 */
class MetaSwitchCompat : SwitchCompat {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private fun init() {

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        kotlin.runCatching {
            val switchWidth: Field = SwitchCompat::class.java.getDeclaredField("mSwitchWidth")
            switchWidth.isAccessible = true

            // Using 120 below as example width to set
            // We could use attr to pass in the desire width
            switchWidth.setInt(this, ScreenUtil.dp2px(context, 45f))
        }
    }
}