package com.socialplay.gpark.ui.profile

import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.PopUpProfileBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * created by liyanfeng on 2022/7/25 2:32 下午
 * @describe: ProfileTab 底栏主页
 */
class MeProfileFragment : BaseProfilePage() {

    companion object {
        private const val TAG = "Profile-ME"

        const val FROM_BOTTOM_TAB = 0
        const val FROM_OTHER = 1
    }

    private val args by navArgs<MeProfileFragmentArgs>()

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpProfileBinding.inflate(layoutInflater) }
    private val WALLETE_SHOW_MESSAGE = 100
    private val h5PageConfig by inject<H5PageConfigInteractor>()
    private val payInteractor: IPayInteractor = GlobalContext.get().get()

    private val from: String
        get() = if (isFromBottomTab) "tab" else if (isFromOther) args.from else "4"

    private val isFromBottomTab: Boolean
        get() = args.entry == FROM_BOTTOM_TAB

    private val isFromOther: Boolean
        get() = args.entry == FROM_OTHER

    override fun isBottomTab(): Boolean = true
    override fun otherUuid(): String = accountInteractor.curUuid
    override fun isMe(): Boolean = true
    override fun onIvRightClick(view: View) {
        popupBinding.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        popupWindow.showAsDropDownByLocation(
            view,
            -popupBinding.cv.measuredWidth + 18.dp,
            -(4).dp
        )
    }

    override fun init() {
        super.init()
        initPopup()
        onProfilePageShow(from)
        accountInteractor.accountLiveData.value?.let {
            Timber.tag(TAG).e("init accountLiveData $it")
            val data = ProfileDisplayBean().createByMetaUserInfo(it)
            initTopUserView(data)
            initTopUserRoleView(data)
        }
        initPremiumEntrance()
        binding.root.setPaddingEx(
            bottom = if (args.isFromBottom) {
                getDimensionPx(R.dimen.tab_layout_height)
            } else {
                0
            }
        )
        accountInteractor.badgeLiveData.observe(viewLifecycleOwner) {
            binding.includeCount.vFollowersNumRedDot.visible(it?.newFollower?.hasNew == true)
        }
        if (PandoraToggle.showProfileGroupChatLayout) {
            binding.groupGroupChat.visible(true)
            binding.vGroupChatBg.setOnAntiViolenceClickListener {
                MetaRouter.IM.goMyGroupsListFragment(
                    this@MeProfileFragment,
                    otherUuid()
                )
            }
        } else {
            binding.groupGroupChat.visible(false)
        }
    }

    override fun loadFirstData() {
        super.loadFirstData()
        viewModel.getSubsProduct()
    }

    private fun initPremiumEntrance() {
        if (PandoraToggle.isVipPlusOpen()) {
            Analytics.track(
                EventConstants.EVENT_SUBSCRIBE_SHOW,
                mapOf("entry_style" to if (viewModel.memberInfo.value?.isActive() == true) "1" else "0")
            )
        }
        binding.groupPremium.visible(PandoraToggle.isVipPlusOpen())
        binding.vBgPremium.setOnAntiViolenceClickListener {
            val url = java.lang.StringBuilder(h5PageConfig.getH5PageUrl(H5PageConfigInteractor.VIP_STATUS)).append("?source=personal").toString()
            MetaRouter.Web.navigate(
                this,
                null,
                url,
                showTitle = false,
                showStatusBar = false
            )
            Analytics.track(
                EventConstants.EVENT_USER_SUBSCRIBE_CLICK,
                mapOf("entry_style" to if (viewModel.memberInfo.value?.isActive() == true) "1" else "0")
            )
        }
        viewModel.memberInfo.observe(viewLifecycleOwner) {
            if (it != null) {
                if (PandoraToggle.isVipStatusOpen() && it.isActive()) {
                    binding.groupPremium.visible()
                }
                binding.tvPremiumMine.visible(binding.groupPremium.isVisible && it.isActive())
                visibleList(
                    binding.tvPremium,
                    binding.tvRewardCoinNum,
                    visible = binding.groupPremium.isVisible && !it.isActive()
                )
                binding.tvPremium.setText(if (it.isActive()) {
                    R.string.mine_premium_tile
                } else {
                    R.string.premium_tile
                })
            } else {
                binding.tvPremiumMine.gone()
                visibleList(
                    binding.tvPremium,
                    binding.tvRewardCoinNum,
                    visible = binding.groupPremium.isVisible
                )
                binding.tvPremium.setText(R.string.premium_tile)
            }
        }
        viewModel.subscribeProductLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.tvRewardCoinNum.text =
                    getString(R.string.premiume_coin_num, it.extendInfo?.rewardCoinNum ?: "0")
            } else {
                binding.tvRewardCoinNum.text =
                    getString(R.string.premiume_coin_num, "0")
            }
        }

    }

    override fun initTopUserView(display: ProfileDisplayBean) {
        Timber.tag(TAG).d("initTopUserView $display")

        showUserProfile(display)
    }

    private fun showUserProfile(display: ProfileDisplayBean) {
        binding.apply {
            glide?.run {
                load(display.portrait).placeholder(R.drawable.icon_default_avatar)
                    .circleCrop()
                    .into(ivAvatar)
            }
            tvUsername.apply {
                isVisible = true
                text = display.nickname
            }
            ulv.show(display.tagIds, display.labelInfo, glide = glide)
            tvId.apply {
                display.userNumber.let { text = it }
                isVisible = true
            }
            tvSignature.apply {
                isVisible = !display.signature.isNullOrEmpty()
                text = display.signature
            }
            tvSignatureAdd.isVisible = display.signature.isNullOrEmpty()
        }
        binding.apply {
            ivAvatar.setOnAntiViolenceClickListener {
                onIvAvatarClick(display.portrait)
            }
            tvSignatureAdd.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_PROFILE_ADD_INFORMATION_CLICK)
                MetaRouter.Account.informationReset(
                    this@MeProfileFragment,
                    accountInteractor.accountLiveData.value?.signature
                )
            }
            updateNumView(display.fansCount, display.followCount, display.likeCount, display.friendTotal)
        }
    }

    override fun initObserve() {
        super.initObserve()
        accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            Timber.tag(TAG).e("accountLiveData $it")
            it ?: return@observe
            val data = ProfileDisplayBean().createByMetaUserInfo(it)
            initTopUserView(data)
            initTopUserRoleView(data)
            val uuid = it.uuid ?: return@observe
            viewModel.updateIfDifferent(uuid) {
                apiStart()
            }
        }
        setFragmentResultListenerByActivity(QRCodeScanFragment.KEY_PROFILE_REQUEST_SCAN_QRCODE, viewLifecycleOwner) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val result = ScanResultData.from(bundle)

            if (result != null && request != null) {
                viewLifecycleOwner.lifecycleScope.launch {
                    GlobalContext.get().get<QrCodeInteractor>()
                        .dispatchQRCodeScanResult(this@MeProfileFragment, request, result)
                }
            }
        }
    }

    private fun initPopup() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationFromRight
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupBinding.mtvScan.setOnAntiViolenceClickListener {
            MetaRouter.IM.goQRCodeScan(
                requireActivity(),
                this,
                QRCodeScanFragment.KEY_PROFILE_REQUEST_SCAN_QRCODE,
                ScanEntry.Profile
            )
            popupWindow.dismiss()
        }
        popupBinding.mtvSettings.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_PROFILE_SET_CLICK)
            Analytics.track(EventConstants.HOMEPAGE_SETTING_CLICK) {
                put("type", 0)
            }
            MetaRouter.AccountSetting.setting(this@MeProfileFragment, LoginPageSource.Profile, null)
            popupWindow.dismiss()
        }
    }

    private fun share(userExtra: ShareRawData.UserExtra?) {
        val user = viewModel.userProfile.value ?: return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.user(user, userExtra)
        )
    }

    override fun onDestroyView() {
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        super.onDestroyView()
    }

}