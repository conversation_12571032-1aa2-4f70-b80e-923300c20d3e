package com.socialplay.gpark.ui.dialog

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.ui.main.MainActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.mutableMapOf

enum class DialogScene {
    MAIN_PAGE,
    GUIDE_LOGIN_PAGE,
    GAME_DETAIL_PAGE,
    MATERIAL_DETAIL_PAGE,
    GAME_SUCCESS,
    LIKE_OPT,
}

enum class DialogShowCount {
    /**
     * 只展示一次
     */
    ONCE,

    /**
     * 可能在一次App生命周期中多次展示
     */
    MULTI,
}

enum class DialogState {
    /**
     * 正在初始化
     */
    INITIALIZE,

    /**
     * 已经初始化完成（此时不一定可以Show，需要等待onResume）
     */
    INITIALIZED,

    /**
     * 正在展示
     */
    SHOWING,

    /**
     * 该次生命周期展示完成了
     */
    FINISHED,
}

interface IDialogManager {
    /**
     * 初始化弹框数据
     * 在注册时就会回调，可以在这里进行网络请求，提前获取一些信息
     * @param finishCallback 初始化完成回调
     */
    suspend fun initData(finishCallback: (Boolean) -> Unit)

    /**
     * 是否需要展示弹框（只进行条件判断，不要进行弹框展示）
     * 会在场景触发的时候，按照优先级从高到低进行询问
     * 支持异步回调，使用needShowCallback进行回调
     * 注意：请务必涵盖所以条件分支，必须调用needShowCallback，如果需要展示，需要调用needShowCallback(true)，否则不需要展示，需要调用needShowCallback(false)
     * 如果某个代码的条件分支没有覆盖，会导致该场景下所有的弹框无法展示
     * @param fragment 当前Fragment
     * @param scene 弹框场景
     * @param args 场景携带的参数，是bundle类型
     * @param needShowCallback 是否需要展示弹框的回调，true代表需要展示，false代表不需要展示
     */
    fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit)

    /**
     * 展示弹框，弹框管理器调用该函数时，进行弹框的展示
     * 注意：请确保弹框已经初始化完成，并且已经调用了needShowCallback(true)
     * 注意：请确保一定会调用needShowCallback(true/false)，若是连续弹框逻辑，则将该回调传递到下一个弹框逻辑中
     * @param fragment 当前Fragment
     * @param onDismissCallback 当弹框消失的时候，需要回调这个方法(true代表消失后还在当前界面，false代表消失后不在当前界面)，请务必调用onDismissCallback，否则该场景下后续所有的弹框无法展示
     * 建议处理方案，一般会在弹框的onDismiss的回调里面进行兜底，其他地方执行过后置空onDismissCallback即可
     */
    fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit)

    /**
     * 若弹框管理器调用该函数，请执行弹框的dismiss操作
     * 一般是在页面销毁的时候调用（现在还没）
     */
    fun exeDismiss()
}

data class DialogManagerInfo(
    val priority: Int = 0,
    val scene: List<DialogScene> = listOf(DialogScene.MAIN_PAGE),
    var dialogState: DialogState = DialogState.INITIALIZE,
    /**
     * 弹框展示次数限制
     * ONCE: 只展示一次
     * MULTI: 可能在一次App生命周期中多次展示
     */
    val showCount: DialogShowCount = DialogShowCount.ONCE,
    /**
     * 弹框运行次数限制(运行次数是指弹框判断是否展示的次数）
     * -1: 不限制
     * 0: 不限制
     * 其他数字：运行次数限制
     */
    val runCount: Int = -1,
)

/**
 * 弹框管理器
 * 弹框管理器会根据场景，按照优先级从高到低进行弹框询问+展示
 * 注意：如果有多个弹框，会按照优先级从高到低弹出. 上一个弹框消失之后 才会继续弹下一个弹框
 *
 * 使用方法：
 * 1. 实现IDialogManager接口，实现对应方法（每个函数都有相应的注释说明）
 * 2. 注册弹框：DialogShowManager.registerDialog(dialog, info)
 *
 *
 * 谨慎注意：
 * 1. 管理器为了防止同时展示多个弹框，会根据场景来做标识，当前弹框展示完成后，才会询问下一个弹框是否展示。所以请务必细心实现IDialogManager.needShow的needShowCallback 以及 showByDialogManager的onDismissCallback
 * 2. 如果某个弹框没有消失，从其他页面回来后，不会进行其他弹框的询问，直到该弹框消失后才会进行下一个弹框的询问
 * 3. 弹框管理器初始化在合规弹框之后才会进行
 */
object DialogShowManager {
    const val TAG = "DialogShowManager"
    private val dialogMap = mutableMapOf<IDialogManager, DialogManagerInfo>()
    private var currentShowDialogMap = mutableMapOf<DialogScene, IDialogManager?>()
    private var shownCountMap: MutableMap<DialogScene, Int> = mutableMapOf()
    private var dialogRunCountMap: MutableMap<IDialogManager, Int> = mutableMapOf()

    // 添加场景检查状态记录
    private val sceneCheckingStatus = ConcurrentHashMap<DialogScene, Long>()
    private const val CHECK_TIMEOUT = 12000L  // 12秒超时，比有些接口10秒超时长一点点

    /**
     * 注册弹框
     * 会在用户同意合规弹框之后，放心进行网络请求
     * @param dialog 弹框
     * @param info 弹框配置：优先级，越大越先展示。 scene 弹框场景
     */
    suspend fun registerDialog(dialog: IDialogManager, info: DialogManagerInfo) {
        Timber.tag(TAG).d("register $dialog, info:$info")
        dialogMap.put(dialog, info)
        dialogRunCountMap[dialog] = 0
        dialog.initData {
            dialogMap[dialog]?.let {
                if (it.dialogState == DialogState.INITIALIZE) {
                    dialogMap[dialog] = it.copy(dialogState = DialogState.INITIALIZED)
                }
            }
        }
    }

    /**
     * 如果有弹框，会走这里弹出.
     * 注意：如果有多个弹框，会按照优先级从高到低弹出. 上一个弹框消失之后 才会继续弹下一个弹框
     */
    fun loopCheck(fragment: Fragment, sortedDialogs: List<Pair<IDialogManager, DialogManagerInfo>>, scene: DialogScene, loopSort: Int, args: Bundle? = null) {
        if (sortedDialogs.size <= loopSort) {
            // 问完了，没有要展示的了
            Timber.tag(TAG).d("loopCheck, $scene loop finished loopSort:$loopSort")
            // 清除场景检查状态
            sceneCheckingStatus.remove(scene)
            return
        }
        if (!fragment.isAdded || fragment.isDetached) {
            Timber.tag(TAG).d("loopCheck, $scene fragment is not added or detached, stop loop check")
            // 清除场景检查状态
            sceneCheckingStatus.remove(scene)
            return
        }
        val dialog = sortedDialogs[loopSort].first
        val info = sortedDialogs[loopSort].second
        if (currentShowDialogMap[scene] == null
            && (info.showCount == DialogShowCount.MULTI || info.dialogState != DialogState.FINISHED)
            && ((dialogRunCountMap[dialog] ?: 0) < info.runCount || info.runCount == -1 || info.runCount == 0)
        ) {
            Timber.tag(TAG).d("currentShowDialog == null, loop check condition scene:$scene dialog:$dialog info:$info loopSort:$loopSort")
            dialogRunCountMap[dialog] = (dialogRunCountMap[dialog] ?: 0) + 1
            dialog.needShow(fragment, scene = scene, args) {
                if (!fragment.isAdded || fragment.isDetached) {
                    // Fragment没了，不执行逻辑了
                    Timber.tag(TAG).d("in needShow, $scene fragment is not added or detached, stop loop check loopSort:$loopSort")
                    // 清除场景检查状态
                    sceneCheckingStatus.remove(scene)
                    return@needShow
                }
                if (it) {
                    Timber.tag(TAG).d("condition is true, show dialog $dialog loopSort:$loopSort")
                    // 要展示
                    showDialog(dialog, fragment, info, sortedDialogs, scene, loopSort + 1, args)
                    shownCountMap[scene] = (shownCountMap[scene] ?: 0) + 1

                } else {
                    Timber.tag(TAG).d("condition is false, loop next dialog loopSort:$loopSort")
                    // 不需要展示，去找下一个问
                    loopCheck(fragment, sortedDialogs, scene, loopSort + 1, args)
                }
            }
        } else if (currentShowDialogMap[scene] != null) {
            // 当前有弹框在展示，就不要继续了
            Timber.tag(TAG).d("currentShowDialog is not null, stop loop check, wait for next trigger loopSort:$loopSort")
            // 清除场景检查状态
            sceneCheckingStatus.remove(scene)
        } else {
            Timber.tag(TAG).d("currentShowDialog is null, loop next dialog loopSort:$loopSort")
            loopCheck(fragment, sortedDialogs, scene, loopSort + 1, args)
        }
    }

    fun triggerMainScene(fragment: Fragment) {
        Timber.tag(TAG).d("trigger main scene")
        val scene = DialogScene.MAIN_PAGE
        checkWrapper(fragment, scene)
    }

    /**
     * @param gameType: 取值为 BaseGameDetailCommonFragment.GAME_TYPE_PGC 或者 BaseGameDetailCommonFragment.GAME_TYPE_UGC
     */
    fun triggerGameDetailScene(
        fragment: Fragment,
        gameId: String,
        gameType: Int,
        isMyGame: Boolean,
        conditionsInfo: SendGiftConditionsInfo? = null
    ) {
        Timber.tag(TAG).d("trigger game detail scene")
        val scene = DialogScene.GAME_DETAIL_PAGE
        checkWrapper(
            fragment, scene, args = bundleOf(
                "gameId" to gameId,
                "gameType" to gameType,
                "isMyGame" to isMyGame,
                "conditionsInfo" to conditionsInfo,
            )
        )
    }

    fun triggerGameSuccess() {
        Timber.tag(TAG).d("trigger game success scene")
        val curActivity = LifecycleInteractor.activityRef?.get() ?: return
        val navHostFragment = if (curActivity is MainActivity) {
            curActivity.findNavHostFragment()
        } else {
            null
        }
        navHostFragment?.lifecycleScope?.launch(Dispatchers.IO) {
            val scene = DialogScene.GAME_SUCCESS
            checkWrapper(navHostFragment, scene)
        }
    }

    fun triggerLike(fragment: Fragment) {
        Timber.tag(TAG).d("trigger like scene")
        val scene = DialogScene.LIKE_OPT
        checkWrapper(fragment, scene)
    }

    fun triggerGuideLoginScene(fragment: Fragment) {
        Timber.tag(TAG).d("trigger guide login scene")
        val scene = DialogScene.GUIDE_LOGIN_PAGE
        checkWrapper(fragment, scene)
    }

    private fun checkWrapper(fragment: Fragment, scene: DialogScene, args: Bundle? = null) {
        val checkStartTime = sceneCheckingStatus[scene]
        if (checkStartTime != null) {
            // 该场景正在检查中，跳过
            Timber.tag(TAG).d("Scene $scene is already being checked, skip")
            return
        }
        // 清理之前的缓存
        currentShowDialogMap.remove(scene)
        // 标记该场景正在检查
        sceneCheckingStatus[scene] = System.currentTimeMillis()
        val sortedDialogs = dialogMap.entries.filter { it.value.scene.contains(scene) }.sortedByDescending { it.value.priority }.map { Pair<IDialogManager, DialogManagerInfo>(it.key, it.value) }
        loopCheck(fragment, sortedDialogs, scene, 0, args)
    }

    private fun showDialog(dialog: IDialogManager, fragment: Fragment, info: DialogManagerInfo, sortedDialogs: List<Pair<IDialogManager, DialogManagerInfo>>, scene: DialogScene, i: Int, args: Bundle? = null) {
        currentShowDialogMap[scene] = dialog
        dialogMap[dialog] = info.copy(dialogState = DialogState.SHOWING)
        fragment.lifecycleScope.launch(Dispatchers.Main) {
            dialog.showByDialogManager(fragment) {
                dialogMap[dialog] = info.copy(dialogState = DialogState.FINISHED)
                currentShowDialogMap.remove(scene)
                Timber.tag(TAG).d("dialog $dialog show finished. flag is $it")
                if (it) {
                    loopCheck(fragment, sortedDialogs, scene, i, args)
                } else {
                    // 如果弹框消失后不在当前界面，清除场景检查状态
                    sceneCheckingStatus.remove(scene)
                }
            }
        }

    }

    /**
     * 根据当前场景获取弹框展示次数
     * 以当前进程生命周期为维度，当前生命周期内累积，下次启动清空重置
     * 计数是在弹框展示之后才会+1，所以当前弹框展示时获取不会包含当前展示次数
     * @param scene 场景
     */
    fun getShownCountByScene(scene: DialogScene): Int {
        return shownCountMap[scene] ?: 0
    }

}