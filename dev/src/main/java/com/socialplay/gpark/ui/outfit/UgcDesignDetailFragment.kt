package com.socialplay.gpark.ui.outfit

import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.os.bundleOf
import androidx.core.view.doOnNextLayout
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.community.PostMedia
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.FragmentUgcDesignDetailBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonCommentBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.divider
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.detail.comment.IUgcCommentListener
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentExpandItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentLoading
import com.socialplay.gpark.ui.editor.detail.comment.ugcReplyItem
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.doOnLayoutChanged
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setFragmentResultByHostFragment
import com.socialplay.gpark.util.extension.setFragmentResultListener
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.toastLong
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/04
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcDesignDetailArgs(
    val itemId: String, val from: Int, val tabId: Int, val targetCommentId: String?, val targetReplyId: String?, val categoryId: Int
) : Parcelable

class UgcDesignDetailFragment : BaseFragment<FragmentUgcDesignDetailBinding>(R.layout.fragment_ugc_design_detail) {

    companion object {
        const val TAG = "UgcDesignDetailFragment"

        const val KEY_ITEM_ID = "itemId"
        const val KEY_LIKE_COUNT = "likeCount"
        const val KEY_IS_LIKE = "isLike"

        const val TRACK_TAG = "ugc_design_detail"
    }

    private val vm: UgcDesignDetailViewModel by fragmentViewModel()
    private val args: UgcDesignDetailArgs by args()

    private val commentController by lazy { buildCommentController() }

    private val itemListener = UgcCommentListener()

    private var likeAnim = false
    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    private var scrollToTop = false
    private var needLocate = false
    private var locatePosition = 0

    private lateinit var popupWindowComment: PopupWindowCompat
    private val popupBindingComment by lazy { PopUpGameDetailCommonCommentBinding.inflate(layoutInflater) }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcDesignDetailBinding? {
        return FragmentUgcDesignDetailBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (contentHeight != 0) {
            binding.clScrollable.minHeight = contentHeight
        }

        likeAnim = false

        binding.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)

        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW, viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (vm.detail?.uuid == uuid) {
                vm.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        setFragmentResultListener(UgcDesignEditFragment.KEY, viewLifecycleOwner) { _, bundle ->
            val title = bundle.getString(UgcDesignEditFragment.KEY_TITLE)
            val desc = bundle.getString(UgcDesignEditFragment.KEY_DESC)
            vm.edit(title, desc)
        }

        binding.lv.showLoading()
        binding.lv.setRetry {
            vm.initDetail()
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            back()
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            back()
        }
        binding.vUserClick.setOnAntiViolenceClickListener {
            val authorId = vm.authorId
            if (!authorId.isNullOrEmpty()) {
                MetaRouter.Profile.other(this, authorId, "ugc_design_detail", checkFollow = true)
            }
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.follow()
        }
        binding.ivEditBtn.setOnAntiViolenceClickListener {
            val s = vm.oldState
            val detail = s.detail() ?: return@setOnAntiViolenceClickListener
            Analytics.track(
                EventConstants.LIBRARY_METARIAL_EDIT_CLICK
            )
            MetaRouter.UgcDesign.edit(
                this, s.itemId, detail.cover, s.title, s.desc, detail.feedType
            )
        }
        binding.ivShareBtn.setOnAntiViolenceClickListener {
            vm.detail?.let {
                GlobalShareDialog.show(
                    childFragmentManager, ShareRawData.ugcDesignDetail(it)
                )
            }
        }
        binding.slDress.setOnAntiViolenceClickListener {
            vm.detail?.let {
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_TRYON_CLICK, "metrialidid" to it.trackId, "authorid" to it.uuid.orEmpty()
                )
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(), FullScreenEditorActivityArgs(
                        categoryId = CategoryId.UGC_DESIGN_DETAIL, tryOn = RoleGameTryOn.create(
                            tryOnUserId = it.uuid.orEmpty(), from = RoleGameTryOn.FROM_HOME_PAGE_CLOTHES, clothesItemId = it.itemId.orEmpty()
                        )
                    )
                )
            }
        }
        binding.tvLikeBtn.setOnAntiViolenceClickListener {
            vm.like()
        }
        binding.tvGetBtn.setOnAntiViolenceClickListener {
            vm.get()
        }
        binding.tvBottomInputHint.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.itemId, vm.detail?.userName))
        }
        binding.vBottomEmojiClick.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.itemId, vm.detail?.userName), true)
        }
        binding.etvOutfitDesc.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
        binding.ivRemixAllowedTips.setOnAntiViolenceClickListener {
            val rect = Rect()
            binding.ivRemixAllowedTips.getGlobalVisibleRect(rect)
            UgcAssetMixableTipsDialog.show(this, rect)
            Analytics.track(
                EventConstants.ASSET_REMIX_OR_NOT_BTN_CLICK
            )
        }
        commentController.addModelBuildListener(viewLifecycleOwner) {
            if (needLocate) {
                needLocate = false
                scrollToTop = false
                binding.abl.setExpanded(false, true)
                binding.rvComment.smoothScrollToPosition(
                    locatePosition.coerceIn(
                        0, commentController.adapter.itemCount - 1
                    )
                )
            } else if (scrollToTop) {
                scrollToTop = false
                binding.rvComment.scrollToPosition(0)
            }
        }

        val statusBarHeight = StatusBarUtil.getStatusBarHeight(requireContext())
        val scrollHeight = (statusBarHeight + dp(80)).toFloat()
        val topHeight = statusBarHeight + getDimensionPx(R.dimen.title_bar_height)
        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, offset ->
            val absOffset = abs(offset)
            binding.vBgTopToolbar.alpha = (absOffset / scrollHeight)
        }
        binding.clScrollable.minimumHeight = topHeight
        binding.lv.setPaddingEx(top = topHeight)

        binding.rvComment.setController(commentController)

        initPopup()

        vm.onAsync(UgcDesignDetailState::detail, onFail = { _, _ ->
            binding.lv.showError()
        }, onLoading = {
            binding.lv.showLoading()
        }) {
            if (it.published) {
                binding.lv.hide()
                initDetail(it)
            } else {
                binding.lv.showEmpty(
                    msg = getString(R.string.current_work_offline), resId = R.drawable.icon_no_recent_activity
                )
            }
        }
        vm.onEach(UgcDesignDetailState::isFollow) {
            it ?: return@onEach
            updateFollowStatus(it)
        }
        vm.onEach(
            UgcDesignDetailState::title, UgcDesignDetailState::desc
        ) { title, desc ->
            updateTitleStatus(title)
            updateDescStatus(desc)
            updateSpaceHeight()
        }
        vm.onEach(
            UgcDesignDetailState::isLike, UgcDesignDetailState::likeCount, UgcDesignDetailState::isMe
        ) { isLike, likeCount, isMe ->
            isLike ?: return@onEach
            likeCount ?: return@onEach
            isMe ?: return@onEach
            if (isMe) {
                binding.tvLikes.text = UnitUtil.formatKMCount(likeCount)
            } else {
                updateLikeStatus(likeCount, isLike)
            }
        }
        vm.onEach(
            UgcDesignDetailState::got, UgcDesignDetailState::isMe
        ) { got, isMe ->
            got ?: return@onEach
            isMe ?: return@onEach
            if (isMe) return@onEach
            updateGetStatus(got)
        }
        vm.onEach(UgcDesignDetailState::commentList) {
            updateCommentCount(it.invoke()?.total)
        }
        vm.onAsync(UgcDesignDetailState::getResult, deliveryMode = uniqueOnly(), onFail = { _ ->
            vm.detail?.let {
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_GET_CLICK, "metrialidid" to it.trackId, "authorid" to it.uuid.orEmpty(), "result" to "1", "type" to it.feedType
                )
            }
        }) {
            vm.detail?.let {
                if (it.isUgcModule) {
                    toastLong(R.string.ugc_module_get_tips)
                } else {
                    toastLong(R.string.add_to_role_inventory_tips)
                }
                Analytics.track(
                    EventConstants.LIBRARY_METARIAL_GET_CLICK, "metrialidid" to it.trackId, "authorid" to it.uuid.orEmpty(), "result" to "0", "type" to it.feedType
                )
            }
        }
        vm.onAsync(
            UgcDesignDetailState::addCommentResult, deliveryMode = uniqueOnly(), onFail = { _, it ->
                it ?: return@onAsync
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK, "metrialidid" to vm.trackId, "count" to vm.commentSuccessCount, "result" to "1", "reviewtype" to 1, "image_num" to it.imageCount, "type" to vm.feedType
                )
            }) {
            vm.commentSuccessCount++
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK, "metrialidid" to vm.trackId, "count" to vm.commentSuccessCount, "result" to "0", "reviewtype" to 1, "image_num" to it.imageCount, "type" to vm.feedType
            )
        }
        vm.onAsync(
            UgcDesignDetailState::addReplyResult, deliveryMode = uniqueOnly(), onFail = { _, it ->
                it ?: return@onAsync
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK, "metrialidid" to vm.trackId, "count" to vm.commentSuccessCount, "result" to "1", "reviewtype" to 2, "image_num" to it.imageCount, "type" to vm.feedType
                )
            }) {
            vm.commentSuccessCount++
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_PUBLISH_CLICK, "metrialidid" to vm.trackId, "count" to vm.commentSuccessCount, "result" to "0", "reviewtype" to 2, "image_num" to it.imageCount, "type" to vm.feedType
            )
        }
        vm.onEach(UgcDesignDetailState::gotCount) {
            if (it != null) {
                binding.tvAcquiredTimes.text = UnitUtil.formatKMCount(it)
            }
        }
        vm.onEach(UgcDesignDetailState::guideInvoke, deliveryMode = uniqueOnly()) {
            it ?: return@onEach
            UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_FIRST_ASSET_INTERACT)
        }
        vm.registerToast(UgcDesignDetailState::toast)
        vm.registerAsyncErrorToast(UgcDesignDetailState::detail)
        vm.registerAsyncErrorToast(UgcDesignDetailState::addCommentResult)
        vm.registerAsyncErrorToast(UgcDesignDetailState::addReplyResult)
        vm.registerAsyncErrorToast(UgcDesignDetailState::getResult)
    }

    private fun initPopup() {
        popupWindowComment = PopupWindowCompat(
            popupBindingComment.root, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        popupBindingComment.root.setOnClickListener {
            popupWindowComment.dismiss()
        }
        popupWindowComment.setOnDismissListener {
            popupBindingComment.vCopyClick.unsetOnClick()
            popupBindingComment.vPinClick.unsetOnClick()
            popupBindingComment.vUnpinClick.unsetOnClick()
            popupBindingComment.vReportClick.unsetOnClick()
            popupBindingComment.vDeleteClick.unsetOnClick()
        }
    }

    private fun initDetail(detail: UgcDesignDetail) {
        if (detail.isUgcModule) {
            binding.slDress.gone()
            binding.tvTryOnTimesLabel.setText(R.string.ugc_asset_popularity)
        } else {
            binding.slDress.visible()
            binding.tvTryOnTimesLabel.setText(R.string.try_ons)
        }
        binding.vBgTopGradient.visible()
        glide?.run {
            load(detail.userIcon).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(binding.ivAvatar)
        }
        binding.tvUsername.text = detail.userName
        binding.ulv.show(null, detail.labelInfo, glide = glide)
        binding.vUserClick.visible()
        binding.ivShareBtn.visible()
        if (detail.isUgcModule) {
            glide?.run {
                load(detail.cover).transform(CenterCrop(), RoundedCorners(dp(12)))
                    .into(binding.ivOutfit)
            }
        } else {
            glide?.run {
                load(detail.cover).fitCenter()
                    .into(binding.ivOutfit)
            }
        }
        binding.tvOutfitPv.text = UnitUtil.formatKMCount(detail.views)
        binding.tvTryOnTimes.text = UnitUtil.formatKMCount(detail.popularity)

        binding.tvOutfitDescUpdateTime.text = detail.createTime.formatWholeDate()

        binding.tvOutfitTag1.goneIfValueEmpty(detail.tags?.getOrNull(0))
        binding.tvOutfitTag2.goneIfValueEmpty(detail.tags?.getOrNull(1))
        binding.tvOutfitTag3.goneIfValueEmpty(detail.tags?.getOrNull(2))

        if (vm.isMe(detail.uuid)) {
            binding.tvFollowBtn.gone()
            binding.ivEditBtn.visible()
            binding.tvDress.setText(R.string.text_dress)
            binding.tvDress.compoundDrawables(top = R.drawable.ic_dress_btn)
            binding.llLikes.visible()
            binding.tvLikes.text = UnitUtil.formatKMCount(detail.favorites)
            binding.tvLikeBtn.gone()
            binding.ivLikeCount.gone()
            binding.lavLikeAnim.gone()
            binding.tvLikeCount.gone()
            binding.tvGetBtn.gone()
        } else {
            binding.tvFollowBtn.visible()
            binding.ivEditBtn.gone()
            binding.tvDress.setText(R.string.head_bar_role_try_on)
            binding.tvDress.compoundDrawables(top = R.drawable.ic_tryon_btn)
            binding.llLikes.gone()
            binding.tvLikeBtn.visible()
            binding.ivLikeCount.visible()
            binding.lavLikeAnim.visible()
            binding.tvLikeCount.visible()
            binding.tvGetBtn.visible()
            if (!detail.isOwned) {
                initGuide(detail)
            }
        }

        if (detail.editable) {
            binding.groupRemixAllowed.visible()
        } else {
            binding.groupRemixAllowed.gone()
        }
        updateSpaceHeight()

        binding.clScrollable.doOnNextLayout {
            binding.clScrollable.minHeight = 0
        }
    }

    private fun initGuide(detail: UgcDesignDetail) {
        if (detail.isUgcModule) {
            if (vm.ugcModelDetailGuide) return
            vm.ugcModelDetailGuide = true
        } else {
            if (vm.ugcDesignDetailGuide) return
            vm.ugcDesignDetailGuide = true
        }
        Analytics.track(
            EventConstants.LIBRARY_USE_STEER_SHOW
        )
        binding.mlGuide.visible()
        binding.ivGuideTri.visible()
        binding.tvGuide.visible()
        if (detail.isUgcModule) {
            binding.tvGetBtn.doOnLayoutSized {
                initModelGuide()
            }
        } else {
            binding.tvDress.doOnLayoutSized {
                initDesignGuide()
            }
        }
    }

    private fun initDesignGuide() {
        navColorRes = R.color.black_60
        requireActivity().window.navColor = getColorByRes(navColorRes)
        val rectRoot = Rect()
        val rect = Rect()
        binding.root.getGlobalVisibleRect(rectRoot)
        binding.tvDress.getGlobalVisibleRect(rect)
        binding.mlGuide.setClipArea(
            rect.left,
            rect.top,
            rect.right,
            rect.bottom,
            rx = dp(14).toFloat(),
            ry = dp(14).toFloat(),
        )
        binding.ivGuideTri.translationY = (rect.bottom + dp(8)).toFloat()
        binding.ivGuideTri.translationX = (rect.right - rectRoot.right - dp(20)).toFloat()
        binding.tvGuide.translationY = (rect.bottom + dp(20)).toFloat()
        binding.tvGuide.translationX = (rect.right - rectRoot.right).toFloat()
        binding.mlGuide.setOnClickListener {
            binding.tvGetBtn.getGlobalVisibleRect(rect)
            val radius = binding.tvGetBtn.height.coerceAtLeast(binding.tvGetBtn.measuredHeight) / 2
            binding.mlGuide.setClipArea(
                rect.left,
                rect.top,
                rect.right,
                rect.bottom,
                rx = radius.toFloat(),
                ry = radius.toFloat(),
            )
            binding.ivGuideTri.translationY = (rect.bottom + dp(8)).toFloat()
            binding.ivGuideTri.translationX = (rect.right - rectRoot.right - dp(57)).toFloat()
            binding.tvGuide.setText(R.string.ugc_design_try_on_tips)
            binding.tvGuide.translationY = (rect.bottom + dp(20)).toFloat()
            binding.tvGuide.translationX = (rect.right - rectRoot.right - dp(15)).toFloat()
            binding.mlGuide.setOnClickListener {
                hideGuide()
            }
        }
    }

    private fun initModelGuide() {
        navColorRes = R.color.black_60
        requireActivity().window.navColor = getColorByRes(navColorRes)
        val rectRoot = Rect()
        val rect = Rect()
        binding.root.getGlobalVisibleRect(rectRoot)
        binding.tvGetBtn.getGlobalVisibleRect(rect)
        updateModuleGuidePosition(rectRoot, rect)
        binding.mlGuide.setOnClickListener {
            hideGuide()
        }
        binding.tvGetBtn.doOnLayoutChanged(viewLifecycleOwner) {
            if (!binding.mlGuide.isVisible) return@doOnLayoutChanged
            it.getGlobalVisibleRect(rect)
            updateModuleGuidePosition(rectRoot, rect)
        }
    }

    private fun updateModuleGuidePosition(rectRoot: Rect, rect: Rect) {
        val radius = binding.tvGetBtn.height.coerceAtLeast(binding.tvGetBtn.measuredHeight) / 2
        binding.mlGuide.setClipArea(
            rect.left,
            rect.top,
            rect.right,
            rect.bottom,
            rx = radius.toFloat(),
            ry = radius.toFloat(),
        )
        binding.ivGuideTri.translationY = (rect.bottom + dp(8)).toFloat()
        binding.ivGuideTri.translationX = (rect.right - rectRoot.right - dp(57)).toFloat()
        binding.tvGuide.setText(R.string.ugc_design_try_on_tips)
        binding.tvGuide.translationY = (rect.bottom + dp(20)).toFloat()
        binding.tvGuide.translationX = (rect.right - rectRoot.right - dp(15)).toFloat()
    }

    private fun updateFollowStatus(isFollow: Boolean) {
        if (isFollow) {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.sp_f5f5f5_r24_eeeeee_s1)
            binding.tvFollowBtn.setText(R.string.following_cap)
        } else {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_ffef30_round_24)
            binding.tvFollowBtn.setText(R.string.follow)
        }
    }

    private fun updateTitleStatus(title: String?) {
        binding.tvOutfitTitle.goneIfValueEmpty(title)
    }

    private fun updateDescStatus(desc: String?) {
        if (desc.isNullOrEmpty()) {
            binding.etvOutfitDesc.gone()
        } else {
            binding.etvOutfitDesc.visible()
            binding.etvOutfitDesc.updateForRecyclerView(
                desc, screenWidth - dp(32), descExpandState
            )
        }
    }

    private fun updateSpaceHeight() {
        if (binding.groupRemixAllowed.isVisible) {
            if (binding.tvOutfitTitle.isVisible || binding.etvOutfitDesc.isVisible) {
                binding.spaceOutfit.visible()
                binding.spaceOutfit.setHeight(dp(8))
            } else {
                binding.spaceOutfit.gone()
            }
        } else {
            binding.spaceOutfit.visible()
            binding.spaceOutfit.setHeight(dp(21))
        }
    }

    private fun updateLikeStatus(likeCount: Long, isLike: Boolean) {
        binding.tvLikeCount.text = UnitUtil.formatKMCount(likeCount)
        if (isLike) {
            binding.tvLikeCount.setTextColorByRes(R.color.color_4AB4FF)
            binding.ivLikeCount.invisible()
            binding.lavLikeAnim.visible()
            if (!likeAnim) {
                binding.lavLikeAnim.progress = 1.0f
            } else {
                binding.lavLikeAnim.progress = 0.0f
                binding.lavLikeAnim.playAnimation()
            }
        } else {
            binding.tvLikeCount.setTextColorByRes(R.color.color_757575)
            binding.ivLikeCount.visible()
            binding.lavLikeAnim.cancelAnimationIfAnimating()
            binding.lavLikeAnim.gone()
        }
        likeAnim = true
    }

    private fun updateGetStatus(got: Boolean) {
        if (got) {
            binding.tvGetBtn.setText(R.string.claimed_cap)
        } else {
            binding.tvGetBtn.setText(R.string.claim_cap)
        }
        binding.tvGetBtn.enableWithAlpha(!got)
    }

    private fun updateCommentCount(count: Long?) {
        binding.tvCommentCount.setTextWithArgs(
            R.string.comment_count_postfix, count?.toString().orEmpty()
        )
    }

    private fun buildCommentController() = simpleController(
        vm, UgcDesignDetailState::commentList, UgcDesignDetailState::commentListLoadMore, UgcDesignDetailState::uniqueTag, UgcDesignDetailState::showCommentPinRedDot
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        when (comments) {
            is Success -> {
                val list = comments().dataList
                if (list.isNullOrEmpty()) {
                    empty(
                        idStr = "UgcAssetDetailCommentEmpty", iconRes = R.drawable.icon_no_recent_activity, descRes = R.string.let_comm_begin_with_your_comment, top = dp(32)
                    ) {
                        vm.getCommentList(true)
                    }
                } else {
                    val dp05 = dp(0.5)
                    val dp8 = dp(8)
                    val dp62 = dp(62)
                    val dp16 = dp(16)
                    val commentContentWidth = screenWidth - 78.dp
                    val replyContentWidth = screenWidth - 112.dp
                    val atColor = getColorByRes(R.color.color_0083FA)
                    list.forEachIndexed { commentPosition, comment ->
                        if (comment.isNewAdd) {
                            comment.isNewAdd = false
                            scrollToTop = true
                        }
                        ugcCommentItem(
                            uniqueTag = uniqueTag, item = comment, position = commentPosition, contentWidth = commentContentWidth, enableImage = true, firstPin = showCommentPinRedDot, listener = itemListener
                        )
                        if (comment.needLocate) {
                            comment.needLocate = false
                            needLocate = true
                            locatePosition = buildItemIndex
                        }
                        var showReplyItem = false
                        if (!comment.collapse) {
                            showReplyItem = (comment.authorReply?.size ?: 0) + (comment.replyCommonPage?.dataList?.size ?: 0) > 0
                            comment.authorReply?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag, reply, replyPosition, commentPosition, true, atColor, replyContentWidth, true, itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag, reply, replyPosition, commentPosition, false, atColor, replyContentWidth, true, itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                        } else {
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                if (reply.forceShow) {
                                    showReplyItem = true
                                    ugcReplyItem(
                                        uniqueTag, reply, replyPosition, commentPosition, false, atColor, replyContentWidth, true, itemListener
                                    )
                                    if (reply.needLocate) {
                                        reply.needLocate = false
                                        needLocate = true
                                        locatePosition = buildItemIndex
                                    }
                                }
                            }
                        }
                        val showReplyButtons = comment.showReplyButtons
                        if (comment.loading) {
                            ugcCommentLoading(
                                uniqueTag, comment
                            )
                        } else if (showReplyButtons) {
                            ugcCommentExpandItem(
                                uniqueTag, comment, commentPosition, showReplyItem, itemListener
                            )
                        }
                        divider(
                            height = dp05, colorRes = R.color.color_E6E6E6, marginLeft = dp62, marginTop = dp16, marginRight = dp16, idStr = "UgcDesignCommentDivider-${uniqueTag}-${comment.commentId}"
                        )
                    }
                    loadMoreFooter(
                        loadMore, idStr = "UgcDesignCommentFooter-${uniqueTag}", endText = getString(R.string.community_article_comment_empty), endTextColorRes = R.color.textColorSecondary
                    ) {
                        vm.getCommentList(false)
                    }
                }
            }

            is Loading -> {
                loadMoreFooter(idStr = "UgcDesignCommentFooterLoading") {}
            }

            is Fail -> {
                empty(
                    idStr = "UgcAssetDetailEmpty-Fail", iconRes = R.drawable.icon_no_recent_activity, descRes = R.string.footer_load_failed, top = dp(32)
                ) {
                    vm.initCommentList()
                }
            }

            else -> {}
        }
    }

    private fun showReplyDialog(target: AddPostCommentReplyTarget, showEmoji: Boolean = false) {
        val replyType: Long
        val reviewId: String
        val type: Int
        if (target.isTargetComment) {
            replyType = 0L
            reviewId = target.asComment.commentId
            type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
        } else if (target.isTargetReply) {
            replyType = 1L
            reviewId = target.asReply.replyId
            type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
        } else {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_DETAIL_PAGE_REVIEW_CLICK, "metrialidid" to vm.trackId
            )
            replyType = -1L
            reviewId = vm.oldState.itemId
            type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
        }
        vm.setReplyTarget(target)
        ArticleCommentInputDialog.show(
            this, target.toNickname, null, null, type, 0.7f, showEmoji, false, getPageName(), ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DESIGN_DETAIL, 1, true, pageName = getPageName()
        ) {
            if (it == null || !it.valid) return@show
            if (target.isTargetPost) {
                vm.addCommentViaNet(it)
            } else {
                vm.addReplyViaNet(it)
            }
        }
    }

    private fun handleOperateComment(
        view: View, comment: PostComment, commentPosition: Int, showRedDot: Boolean
    ) {
        handleOperationHelper(
            view, comment = comment, showRedDot = showRedDot, commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View, reply: PostReply, replyPosition: Int, commentPosition: Int, isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view, reply = reply, commentPosition = commentPosition, replyPosition = replyPosition, isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View, comment: PostComment? = null, showRedDot: Boolean = false, reply: PostReply? = null, commentPosition: Int = 0, replyPosition: Int = 0, isAuthorReply: Boolean = false
    ) {
        val isMe = vm.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vm.isCreator(vm.myUuid)

        if (comment != null && isCreator) {
            if (comment.top == true) {
                visibleList(
                    popupBindingComment.mtvPin, popupBindingComment.vPinClick, popupBindingComment.vPinRedDot, popupBindingComment.vDivider1, visible = false
                )
                visibleList(
                    popupBindingComment.mtvUnpin, popupBindingComment.vUnpinClick, popupBindingComment.vDivider2, visible = true
                )
            } else {
                visibleList(
                    popupBindingComment.mtvPin, popupBindingComment.vPinClick, popupBindingComment.vDivider1, visible = true
                )
                popupBindingComment.vPinRedDot.visible(showRedDot)
                visibleList(
                    popupBindingComment.mtvUnpin, popupBindingComment.vUnpinClick, popupBindingComment.vDivider2, visible = false
                )
            }
        } else {
            visibleList(
                popupBindingComment.mtvPin, popupBindingComment.vPinClick, popupBindingComment.vPinRedDot, popupBindingComment.vDivider1, popupBindingComment.mtvUnpin, popupBindingComment.vUnpinClick, popupBindingComment.vDivider2, visible = false
            )
        }
        visibleList(
            popupBindingComment.mtvReport, popupBindingComment.vReportClick, popupBindingComment.vDivider3, visible = !isMe
        )
        visibleList(
            popupBindingComment.mtvDelete, popupBindingComment.vDeleteClick, popupBindingComment.vDivider4, visible = isMe || isCreator
        )
        val showCopy = if (comment != null && comment.content.isNullOrEmpty()) {
            false
        } else if (reply != null && reply.content.isNullOrEmpty()) {
            false
        } else {
            true
        }
        visibleList(
            popupBindingComment.mtvCopy, popupBindingComment.vCopyClick, visible = showCopy
        )
        if (!showCopy) {
            visibleList(
                popupBindingComment.vDivider1, popupBindingComment.vDivider2, visible = false
            )
        }

        popupBindingComment.vCopyClick.setOnAntiViolenceClickListener {
            val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
            val reviewType: Long = if (comment != null) 1L else 2L
            viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(
                    comment?.content ?: reply?.content, requireContext(), if (comment != null) "Comment" else "Reply"
                )
            }
            Analytics.track(
                EventConstants.LIBRARY_REVIEW_COPY_CLICK, "metrialidid" to vm.trackId, "reviewid" to reviewId, "reviewtype" to reviewType
            )
            toast(R.string.copied_to_clipboard)
            popupWindowComment.dismiss()
        }
        popupBindingComment.vPinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                vm.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vUnpinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                vm.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vReportClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                goReport(comment.commentId, ReportType.UgcClothingComment)
            } else if (reply != null) {
                goReport(reply.replyId, ReportType.UgcClothingReply)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vDeleteClick.setOnAntiViolenceClickListener {
            ConfirmDialog.Builder(this).image(R.drawable.dialog_icon_cry).content(
                getString(
                    R.string.delete_confirm, getString(
                        if (comment != null) {
                            R.string.comment
                        } else {
                            R.string.reply
                        }
                    )
                )
            ).cancelBtnTxt(getString(R.string.dialog_cancel)).confirmBtnTxt(getString(R.string.delete_cap)).isRed(true).confirmCallback {
                if (comment != null) {
                    vm.deleteComment(comment, commentPosition)
                } else if (reply != null) {
                    vm.deleteReply(
                        reply, replyPosition, commentPosition, isAuthorReply
                    )
                }
            }.show()
            popupWindowComment.dismiss()
        }

        popupBindingComment.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        val dp5 = dp(5)
        val dp16 = dp(16)
        val x = -popupBindingComment.cv.measuredWidth + dp(8)
        val rawBottom = rect.top - dp5 + popupBindingComment.cv.measuredHeight
        binding.root.getGlobalVisibleRect(rect)
        val y = if (rawBottom > rect.bottom) {
            -dp16 - popupBindingComment.cv.measuredHeight + dp5
        } else {
            -dp16 - view.height.coerceAtLeast(view.measuredHeight) - dp5
        }
        popupWindowComment.showAsDropDownByLocation(view, x, y, autoHeight = false)
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        MetaRouter.Report.postReport(this, reportId, reportType) {
            if (it) {
                Analytics.track(
                    EventConstants.LIBRARY_REVIEW_REPORT_SUCCESS, "metrialidid" to vm.trackId, "reviewid" to reportId, "reviewtype" to if (reportType == ReportType.UgcClothingComment) 1L else 2L
                )
                val analyticsParams = when (reportType) {
                    ReportType.UgcClothingComment -> {
                        ReportSuccessDialogAnalyticsParams.ClothingComment(
                            feedId = args.itemId,
                            commentId = reportId,
                        )
                    }

                    ReportType.UgcClothingReply -> {
                        ReportSuccessDialogAnalyticsParams.ClothingCommentReply(
                            feedId = args.itemId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
            }
        }
    }

    private fun back() {
        if (binding.mlGuide.isVisible) {
            hideGuide()
        } else {
            navigateUp()
        }
    }

    private fun hideGuide() {
        navColorRes = R.color.white
        requireActivity().window.navColor = getColorByRes(navColorRes)
        binding.mlGuide.unsetOnClick()
        binding.mlGuide.gone()
        binding.ivGuideTri.gone()
        binding.tvGuide.gone()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DESIGN_DETAIL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcDesignDetailState::detail
        )
        commentController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        commentController.onSaveInstanceState(outState)
    }

    override fun onDestroyView() {
        contentHeight = binding.clScrollable.height
        popupWindowComment.dismiss()
        super.onDestroyView()
    }

    override fun onDestroy() {
        if (args.categoryId == CategoryId.UGC_DESIGN_FEED) {
            val state = vm.oldState
            state.likeCount?.let {
                setFragmentResultByHostFragment(
                    TAG, bundleOf(
                        KEY_ITEM_ID to state.itemId,
                        KEY_IS_LIKE to (state.isLike == true),
                        KEY_LIKE_COUNT to it,
                    )
                )
            }
        }
        super.onDestroy()
    }

    private inner class UgcCommentListener : IUgcCommentListener {
        override fun isMe(uid: String?): Boolean {
            return vm.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vm.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vm.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@UgcDesignDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View, comment: PostComment, commentPosition: Int, showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_REVIEW_LIKE_CLICK, "metrialidid" to vm.trackId, "reviewtype" to "1", "fromwho" to comment.uid.orEmpty()
            )
            vm.likeComment(comment, commentPosition)
            DialogShowManager.triggerLike(this@UgcDesignDetailFragment)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_REPLAY_CLICK, "metrialidid" to vm.trackId, "replaytype" to "0", "fromwho" to comment.uid.orEmpty()
            )
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View, reply: PostReply, replyPosition: Int, commentPosition: Int, isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply, replyPosition: Int, commentPosition: Int, isAuthorReply: Boolean
        ) {
            Analytics.track(
                EventConstants.LIBRARY_REVIEW_LIKE_CLICK, "metrialidid" to vm.trackId, "reviewtype" to "2", "fromwho" to reply.uid.orEmpty()
            )
            vm.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
            DialogShowManager.triggerLike(this@UgcDesignDetailFragment)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_REVIEW_REPLAY_CLICK, "metrialidid" to vm.trackId, "replaytype" to "1", "fromwho" to reply.uid.orEmpty()
            )
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            if (comment.isCollapse) {
                Analytics.track(
                    EventConstants.LIBRARY_ITEM_DETAIL_REVIEW_COLLAPSE_CLICK, "metrialidid" to vm.trackId
                )
            }
            vm.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_DETAIL_PAGE_REVIEW_REPLISE_CLICK, "metrialidid" to vm.trackId
            )
            vm.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}

        override fun previewImage(mediaList: List<PostMedia>?, imagePosition: Int) {
            val images = mediaList?.filter { it.isImage }?.map { it.resourceValue }
            if (images.isNullOrEmpty()) return
            ImgPreDialogFragment.show(
                requireActivity(), images.toTypedArray(), TRACK_TAG, imagePosition
            )
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {}

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@UgcDesignDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}