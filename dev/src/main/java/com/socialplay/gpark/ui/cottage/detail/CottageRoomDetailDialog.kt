package com.socialplay.gpark.ui.cottage.detail

import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.databinding.DialogCottageRoomDetailBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.CategoryId.COTTAGE_ROOM_LIST
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.ui.base.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.room.RoomMembersAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.firstOrNull
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/10/10
 *     desc   :
 *
 */

class CottageRoomDetailDialog : BaseBottomSheetDialogFragment() {
    var roomId: String? = null
    var resid: ResIdBean? = null
    val viewModel: CottageRoomDetailViewModel by inject()
    val editorInteractor by inject<EditorInteractor>()
    val accountInteractor by inject<AccountInteractor>()
    var from: String? = null
    companion object {
        const val ROOM_ID = "roomId"
        const val RES_ID = "resIdBean"
        const val FROM = "from"
        fun show(fragment: Fragment, roomId: String, resid: ResIdBean, from: String) {
            val dialog = CottageRoomDetailDialog()
            dialog.arguments = bundleOf(ROOM_ID to roomId, RES_ID to resid, FROM to from)
            dialog.show(fragment.childFragmentManager, "CottageRoomDetailDialog")
        }
    }
    override val binding by viewBinding(DialogCottageRoomDetailBinding::inflate)
    override fun init() {
        initData()
    }

    private fun initData(){
        roomId = arguments?.getString(ROOM_ID)
        from = arguments?.getString(FROM)
        resid = arguments?.getSerializable(RES_ID) as? ResIdBean
        viewModel.roomInfoLieData.observe(viewLifecycleOwner){
            it?.let { it1 -> updateView(it1) }
        }
        viewModel.pkgLiveData.observe(viewLifecycleOwner){
            val roomInfo = viewModel.roomInfoLieData.value?:return@observe
            if (!it.isNullOrBlank()) {
                joinRoom(roomInfo.roomId, roomInfo.gameId, it)
            }
        }

    }
    private fun updateView(cottageRoomInfo: CottageRoomInfo) {
        Glide.with(this).load(cottageRoomInfo.image).placeholder(R.drawable.placeholder).error(R.drawable.placeholder).centerCrop().into(binding.iv)
        Glide.with(this).load(cottageRoomInfo.ownerAvatar)
            .placeholder(R.drawable.icon_default_avatar).circleCrop().into(binding.ivHost)
        binding.tvDialogTitle.text = cottageRoomInfo.description
        binding.tvHostName.text = cottageRoomInfo.ownerNickname
        val sb =
            StringBuilder(getString(R.string.occupancy)).append(cottageRoomInfo.number).append("/")
                .append(cottageRoomInfo.limitNumber)
        binding.tvOccupancy.text = sb.toString()
        binding.rv.adapter = RoomMembersAdapter().apply { setList(cottageRoomInfo.members) }
        binding.tvJoin.setOnAntiViolenceClickListener {
            val roomInfo = viewModel.roomInfoLieData.value?:return@setOnAntiViolenceClickListener
            viewModel.getGamePkg(roomInfo.gameId)
            Analytics.track(EventConstants.EVENT_DSHOME_DETAIL_CLICK, mapOf("show_categoryid" to(from?:""),
                "userid" to roomInfo.ownerUuid,
                "homename" to roomInfo.description,
                "onlinenumber" to roomInfo.number,
                "roomid" to roomInfo.roomId ))
        }
    }
    private fun joinRoom(roomId: String, gameId: String, pkg:String) {
        MgsGameRoomLauncher.enterMgsGame(
            this@CottageRoomDetailDialog,
            pkg,
            gameId,
            MgsBriefRoomInfo(
                roomIdFromCp = roomId,
                roomName = null,
                roomShowNum = null,
                roomTags = null
            ),
            "CottageRoomDetailDialog",
            0,
            accountInteractor.curUuid,
            true,
            resid?.setCategoryID(COTTAGE_ROOM_LIST),
            null
        )
        dismissAllowingStateLoss()
    }
    override var heightPercent: Float = 0f
    override fun getStyle(): Int {
        return R.style.BottomDialogStyle
    }

    override fun loadFirstData() {
        roomId?.let { viewModel.getRoomInfo(it) }
    }

    override fun needCountTime(): Boolean = false

    override fun getPageName(): String = ""
}