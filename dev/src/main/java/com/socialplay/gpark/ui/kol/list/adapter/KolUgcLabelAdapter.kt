package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.databinding.AdapterKolCreatorUgcLabelBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: Kol ugc游戏标签选择器
 */
class KolUgcLabelAdapter(
    private val glide: RequestManager
) : BaseDifferAdapter<UgcPublishLabel, AdapterKolCreatorUgcLabelBinding>(DIFF_CALLBACK) {

    companion object {

        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<UgcPublishLabel>() {
            override fun areItemsTheSame(
                oldItem: UgcPublishLabel,
                newItem: UgcPublishLabel
            ): Boolean {
                return oldItem.tagId == newItem.tagId
            }

            override fun areContentsTheSame(
                oldItem: UgcPublishLabel,
                newItem: UgcPublishLabel
            ): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterKolCreatorUgcLabelBinding>,
        item: UgcPublishLabel
    ) {
        if (item.localSelected) {
            holder.binding.llLabel.setBackgroundResource(R.drawable.bg_corner_360_black)
            holder.binding.tvLabel.setTextColorByRes(R.color.white)
        } else {
            holder.binding.llLabel.setBackgroundResource(R.drawable.bg_f0f0f0_corner_360)
            holder.binding.tvLabel.setTextColorByRes(R.color.neutral_color_3)
        }
        holder.binding.tvLabel.text = item.name
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterKolCreatorUgcLabelBinding {
        return AdapterKolCreatorUgcLabelBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }
}