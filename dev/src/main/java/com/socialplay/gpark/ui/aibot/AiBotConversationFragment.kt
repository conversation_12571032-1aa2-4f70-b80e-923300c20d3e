package com.socialplay.gpark.ui.aibot

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.entity.AIMessageEntity
import com.socialplay.gpark.databinding.FragmentAibotConversationBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.PaletteUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SoftHideKeyBoardUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/06/262
 *     desc   :
 *
 */
@Parcelize
data class AiBotConversationFragmentArgs(val targetId: String, val id:String, val source:String? = null, val reqid:String? = null) : Parcelable
class AiBotConversationFragment :
    BaseRecyclerViewFragment<FragmentAibotConversationBinding>(R.layout.fragment_aibot_conversation) {
    private val viewModel: AiBotConversationViewModel by fragmentViewModel()
    private var softHideKeyBoardUtil: SoftHideKeyBoardUtil? = null
    private val keyboardHeightListener =
        SoftHideKeyBoardUtil.KeyboardHeightListener { height, show ->
            if (show == true) {
                smoothScrollToBottom(viewModel.oldState.list)
            }
        }
    private val modelBuildListener =
        OnModelBuildFinishedListener { smoothScroll(viewModel.oldState.list) }
    override val recyclerView: EpoxyRecyclerView get() = binding.ry


    private var resumeTime =0L
    private var index: Long = 0L
    private var lastPos : Int = 0

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAibotConversationBinding? {
        return FragmentAibotConversationBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    override fun epoxyController(): EpoxyController = buildConversationController()

    private fun initData() {
        viewModel.onEach(AiBotConversationState::botInfo) { bothInfo ->
            binding.tvUserName.text = bothInfo?.name ?: ""
            binding.tvUserDesc.text = UnitUtil.formatKMCount2(bothInfo?.messageCount?:0L)+" "+getString(R.string.ai_bot_connectors)
            Glide.with(requireContext()).load(bothInfo?.icon).into(binding.imgUser)
            Glide.with(requireContext()).load(bothInfo?.chatImage).into(binding.imgCover)
            Glide.with(requireContext()).asBitmap().load(bothInfo?.chatImage)
                .placeholder(R.color.color_EEEEEF)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?
                    ) {
                        viewModel.generateThemeColor(resource)
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {}
                })

        }
        viewModel.onEach(AiBotConversationState::themeColor) { themeColor ->
            updateMaskView(themeColor)
        }
        viewModel.onEach(AiBotConversationState::isFollow) {
            if (it) {
                binding.imgFollow.setImageResource(R.drawable.icon_ai_bot_following)
            } else {
                binding.imgFollow.setImageResource(R.drawable.icon_ai_bot_follow)
            }
        }
        viewModel.onEach(AiBotConversationState::viewStatus) {
            updateSendStatus(it)
        }
        viewModel.onEach(AiBotConversationState::isLoadMoreEnd){
            if (binding.refreshLayout.isRefreshing) {
                binding.refreshLayout.isRefreshing = false
            }
        }
    }
    private fun updateMaskView(themeColor: Int) {
        binding.viewTop.let {
            it.background = runCatching {
                if (it.background is GradientDrawable) {
                    it.background as GradientDrawable
                } else {
                    GradientDrawable()
                }.apply {
                    orientation = GradientDrawable.Orientation.BOTTOM_TOP
                    val endColor = PaletteUtil.getColorWithAlpha(0f, themeColor)
                    colors = intArrayOf(endColor, themeColor)
                    gradientType = GradientDrawable.LINEAR_GRADIENT
                }
            }.getOrElse {

                GradientDrawable()
            }
        }
        binding.viewBottom.let {
            it.background = runCatching {
                if (it.background is GradientDrawable) {
                    it.background as GradientDrawable
                } else {
                    GradientDrawable()
                }.apply {
                    orientation = GradientDrawable.Orientation.BOTTOM_TOP
                    val endColor= PaletteUtil.getColorWithAlpha(0f,themeColor)
                    colors = intArrayOf(themeColor, endColor)
                    gradientType = GradientDrawable.LINEAR_GRADIENT
                }
            }.getOrElse {

                GradientDrawable()
            }
        }
    }

    private fun initView(){
        StatusBarUtil.setLightMode(requireActivity())
        binding.imgSend.setOnAntiViolenceClickListener {
            sendMessage()
        }
        binding.ry.setOnTouchListener { v, event ->
            if (event.action == 0) {
                InputUtil.hideKeyboard(binding.etContent)
                binding.tvReset.gone()
            }
            false
        }
        binding.clUserInfo.setOnAntiViolenceClickListener {
            MetaRouter.AiBot.gotoAiBotDetail(this,viewModel.oldState.args.targetId, viewModel.oldState.args.id,"4")
        }
        binding.imgClose.setOnAntiViolenceClickListener {
            navigateUp()
        }
        binding.etContent.addTextChangedListener(viewLifecycleOwner) {
            if (binding.etContent.text.isEmpty() && viewModel.oldState.viewStatus != AiBotConversationViewModel.RECEIVING) {
                viewModel.updateSendStatus(AiBotConversationViewModel.DEFAULT)
                updateSendStatus(AiBotConversationViewModel.DEFAULT)
            } else if (binding.etContent.text.isNotBlank()) {
                viewModel.updateSendStatus(AiBotConversationViewModel.SENDING)
                updateSendStatus(AiBotConversationViewModel.SENDING)
            } else {


            }
        }
        binding.etContent.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.tvReset.gone()
            }
        }

        binding.tvReset.setOnAntiViolenceClickListener {
            showDeleteConfirmDialog()
        }
        binding.refreshLayout.setOnRefreshListener {
            lastPos = viewModel.oldState.list.size
            viewModel.getAiMessageHistory(false)
        }

        binding.imgFollow.setOnAntiViolenceClickListener {
            viewModel.followBot()
        }
        binding.ry.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        epoxyController.addModelBuildListener(modelBuildListener)
    }
    private fun sendMessage(){
        val args = viewModel.oldState.args
        when(viewModel.oldState.viewStatus){
            AiBotConversationViewModel.DEFAULT -> {
                InputUtil.hideKeyboard(binding.etContent)
                binding.tvReset.visible()
                smoothScrollToBottom(viewModel.oldState.list)
            }
            AiBotConversationViewModel.SENDING -> {
                Analytics.track(
                    EventConstants.COMMUNITY_APP_BOT_CHAT,
                    "botid" to args.targetId,
                    "type" to "1",
                    "message" to binding.etContent.text.toString(),
                    "source" to args.source.toString(),
                    "reqid" to args.reqid.toString()
                )
                InputUtil.hideKeyboard(binding.etContent)
                smoothScrollToBottom(viewModel.oldState.list)
                viewModel.sendAiMessage(binding.etContent.text.toString())
                binding.etContent.setText("")
            }
            AiBotConversationViewModel.RECEIVING -> {
                InputUtil.hideKeyboard(binding.etContent)
                Analytics.track(
                    EventConstants.COMMUNITY_APP_BOT_REPLAY,
                    "botid" to args.targetId,
                    "type" to "2",
                    "source" to args.source.toString(),
                    "reqid" to args.reqid.toString()
                )
                viewModel.stopReceive()
            }
        }
    }
    private fun updateSendStatus(status:Int){
        if (isVisible && !isStateSaved && !isDetached) {
            when (status) {
                AiBotConversationViewModel.DEFAULT   -> {
                    binding.imgSend.setImageResource(R.drawable.icon_ai_bot_add)
                }

                AiBotConversationViewModel.SENDING   -> {
                    binding.imgSend.setImageResource(R.drawable.icon_ai_send)
                    binding.tvReset.gone()
                }

                AiBotConversationViewModel.RECEIVING -> {
                    binding.imgSend.setImageResource(R.drawable.icon_send_stop)
                    binding.tvReset.gone()
                }
            }
        }
    }
    private fun showDeleteConfirmDialog() {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.ai_bot_conversation_rest))
            .cancelBtnTxt(getString(R.string.cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.friend_chat_delete), lightBackground = true)
            .image(R.drawable.dialog_icon_cry)
            .isRed(true)
            .cancelCallback {

            }
            .confirmCallback {
                index = 0
                lastPos = 0
                viewModel.cleanAllMessage(viewModel.oldState.greetingList)
                val map = mapOf(
                    "botid" to viewModel.oldState.args.targetId,
                )
                Analytics.track(EventConstants.EVENT_APPBOT_RESET, map)

            }
            .navigate()
    }
    override fun invalidate() {

    }

    override fun onResume() {
        if (softHideKeyBoardUtil == null) {
            softHideKeyBoardUtil = SoftHideKeyBoardUtil(requireActivity(), keyboardHeightListener)
        } else {
            softHideKeyBoardUtil?.addGlobalLayoutListener()
        }
        super.onResume()
        resumeTime = System.currentTimeMillis()
    }

    override fun onPause() {
        softHideKeyBoardUtil?.removeGlobalLayoutListener()
        val duration = (System.currentTimeMillis() - resumeTime)
        val map = mapOf(
            "playtime" to duration.toString(),
            "botid" to viewModel.oldState.args.targetId,
            "source" to "2"
        )
        Analytics.track(EventConstants.EVENT_APPBOT_PLAYTIME, map)
        viewModel.stopReceive()
        super.onPause()
    }

    private fun buildConversationController() = simpleController(
        viewModel,
        AiBotConversationState::list
    )
    { list ->

        list.forEachIndexed { pos, aiMessageEntity ->
            AiBotMessageItem(
                pos,
                aiMessageEntity,
                viewModel.oldState.botInfo,
                viewModel.oldState.aiColor,
                viewModel.oldState.userName,
                viewModel.oldState.userColor,
                (pos == list.size - 1) && viewModel.oldState.lastAsk.isNotEmpty()
            ) { view ->
                //点击重新回答
                show()
            }
            index ++
            spacer(height = 16.dp, spanCount = 1, idStr = "AiBotMessageItem_spacer$index")
        }
        index ++
        spacer(height = 40.dp, spanCount = 1, idStr = "AiBotMessageItem_spacer_bottom$index")
    }

    fun show() {
        viewModel.refreshAnswer()
        val args = viewModel.oldState.args
        Analytics.track(
            EventConstants.COMMUNITY_APP_BOT_REPLAY,
            "botid" to args.targetId,
            "type" to "1",
            "source" to args.source.toString(),
            "reqid" to args.reqid.toString()
        )
    }


    private fun smoothScroll(list: List<AIMessageEntity>) {
        if (viewModel.oldState.list.size > 1) {
            if (viewModel.oldState.isLoadMore) {
                (binding.ry.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(0, 0)
            } else {
                smoothScrollToBottom(list)
            }
        }
    }
    private fun smoothScrollToBottom(list: List<AIMessageEntity>) {
        (binding.ry.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(list.size - 1, -100000)
    }


    override fun getPageName(): String = PageNameConstants.FRAGMENT_AI_BOT
    override fun onDestroy() {
        epoxyController.removeModelBuildListener(modelBuildListener)
        super.onDestroy()
    }
}

