package com.socialplay.gpark.ui.view

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.animation.DecelerateInterpolator
import androidx.core.animation.doOnEnd

class AnimatedSeekBar @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defaultStyleAttr: Int = 0
) : androidx.appcompat.widget.AppCompatSeekBar(context, attr, defaultStyleAttr) {

    override fun setThumb(thumb: Drawable?) {
        super.setThumb(thumb)
        requestLayout()
    }

    fun setThumb(newThumb: Drawable?, animate: Boolean) {
        val height = this.measuredHeight
        thumb = newThumb

        val shouldPlayAnim = animate && height > 0

        if (shouldPlayAnim) {
            val targetHeight = computeHeight()

            val initialScaleY = height / targetHeight.toFloat()

            val scaleAnim = ObjectAnimator.ofFloat(this, View.SCALE_Y, initialScaleY, 1F)

            val animatorSet = AnimatorSet()
            animatorSet.interpolator = DecelerateInterpolator(3F)
            animatorSet.setDuration(300L)
            animatorSet.playTogether(scaleAnim)
            animatorSet.start()
        }
    }

    private fun computeHeight(): Int {
        var dh = 0
        val thumbHeight = thumb?.intrinsicHeight ?: 0
        val d = progressDrawable
        if (d != null) {
            if (d.intrinsicHeight != -1) {
                dh = d.intrinsicHeight
            }
        }

        dh = Math.max(thumbHeight, dh)
        dh += paddingTop + paddingBottom
        return dh
    }

    private fun computeWidth(): Int {
        var dw = 0
        val thumbWidth = thumb?.intrinsicWidth ?: 0
        val d = progressDrawable
        if (d != null) {
            if (d.intrinsicWidth != -1) {
                dw = d.intrinsicWidth
            }
        }

        dw = Math.max(thumbWidth, dw)
        dw += paddingLeft + paddingRight
        return dw
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val dw = computeWidth()
        val dh = computeHeight()

        setMeasuredDimension(
            resolveSizeAndState(dw, widthMeasureSpec, 0),
            resolveSizeAndState(dh, heightMeasureSpec, 0)
        )
    }
}