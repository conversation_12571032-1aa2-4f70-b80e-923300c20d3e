package com.socialplay.gpark.ui.share

import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.DialogScreenshotTipsBinding
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/27
 *     desc   :
 * </pre>
 */
class ScreenshotTipsDialog : BaseDialogFragment() {

    companion object {
        fun show(
            fm: FragmentManager,
            raw: ShareRawData,
            requestKey: String = "",
            isPortrait: Boolean = true,
            isInGame: Boolean = false,
            features: List<ShareFeature>? = null
        ) {
            if (raw.scene != ShareHelper.SCENE_SCREENSHOT || raw.image.isNullOrBlank()) return
            val dialog = ScreenshotTipsDialog()
            dialog.arguments =
                GlobalShareArgs(
                    requestKey,
                    raw.scene,
                    GsonUtil.safeToJson(raw),
                    isPortrait,
                    isInGame,
                    features
                ).asMavericksArgs()
            dialog.show(fm, "ScreenshotTipsDialog_$requestKey")
        }
    }

    override val binding by viewBinding(DialogScreenshotTipsBinding::inflate)
    private val vm: ScreenshotTipsViewModel by fragmentViewModel()

    private val args: GlobalShareArgs by args()

    override fun init() {
        vm.init()

        binding.root.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        binding.vBg.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
            GlobalShareDialog.show(
                parentFragmentManager,
                vm.oldState.raw,
                requestKey = args.requestKey,
                isPortrait = args.isPortrait,
                isInGame = args.isInGame
            )
        }

        vm.onEach(ScreenshotTipsState::raw) {
            it.image?.let { image ->
                glide?.run {
                    load(image).into(binding.ivPreview)
                }
            }
        }
        vm.onAsync(ScreenshotTipsState::dismiss) {
            if (it) {
                dismissAllowingStateLoss()
            }
        }
    }

    override fun isFullScreen() = true
    override fun isHideNavigation() = true
    override fun isTransparent() = true
    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
}