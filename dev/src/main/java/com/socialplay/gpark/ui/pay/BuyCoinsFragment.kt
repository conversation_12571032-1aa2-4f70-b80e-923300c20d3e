package com.socialplay.gpark.ui.pay

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.databinding.FragmentBuyCoinsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.pay.RechargeProductCompat
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.feedback.FeedbackTypeWrapper
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext
import kotlin.getValue

class BuyCoinsFragment : BaseFragment<FragmentBuyCoinsBinding>(R.layout.fragment_buy_coins) {
    private val vm: BuyCoinsViewModel by fragmentViewModel()
    private val productController by lazy { buildProductController() }
    private val args by navArgs<BuyCoinsFragmentArgs>()
    private val h5PageConfigInteractor: H5PageConfigInteractor = GlobalContext.get().get<H5PageConfigInteractor>()

    /**
     * 是否上传过显示本页面的埋点事件
     */
    private var doPageShowAnalytics = false

    /**
     * 当前用户是否是会员
     */
    private var currentUserIsMember = false

    private val onItemClickedListener = object : IBuyCoinsItemClickedListener {
        override fun onClicked(product: RechargeProductCompat) {
            Analytics.track(
                EventConstants.EVENT_CLIENT_CLICK_PAY,
                // 上级页面名字
                "source" to args.pageSource.toString(),
                "productid" to product.productId,
                "our_productid" to product.ourProductId,
                "currencycode" to product.currencyCode,
                // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                "page_style" to (if (currentUserIsMember) "1" else "0"),
                // 游戏code码
                "gamecode" to args.gameCode.toString(),
                "price" to product.price.toString(),
            )
            vm.startPay(requireActivity(), product, getPageName())
        }
    }

    override val destId: Int = R.id.buyCoinsPage

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentBuyCoinsBinding? {
        return FragmentBuyCoinsBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.ivBackBtn.setOnAntiViolenceClickListener {
            navigateUp()
        }
        binding.loadingView.setRetry {
            vm.loadData()
        }
        binding.ivReportBtn.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.EVENT_CLIENT_WALLET_FEEDBACK_CLICK,
            )
            MetaRouter.Feedback.feedback(
                this,
                null,
                "",
                FeedbackTypeWrapper.buy.id,
                false,
                needBackGame = false,
                fromGameId = null
            )
        }
        // 禁用商品列表的滚动, 依赖外面的NestedScrollView做滚动
        binding.rvProducts.isNestedScrollingEnabled = false
        binding.rvProducts.setController(productController)
        binding.tvCardDetails.setOnAntiViolenceClickListener {
            // MetaRouter.Pay.goTransactionDetailsPage(this)
            // 交易详情已改为由h5实现
            val pageUrl = h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.TRANSACTION_DETAILS)
            MetaRouter.Web.navigate(this, url = pageUrl, showTitle = false)
        }

        if (PayProvider.ENABLE_PREMIUM) {
            binding.tvMoreEconomicalTitle.visible(true)
            binding.clSubscribe.visible(true)
            binding.clSubscribe.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.EVENT_CLIENT_WALLET_SUB_CLICK,
                )
                MetaRouter.Web.navigate(
                    context,
                    this,
                    null,
                    PayProvider.PREMIUM_PAGE_URL,
                    showTitle = false,
                    showStatusBar = false
                )
            }

            vm.onEach(
                BuyCoinsState::isMember,
                BuyCoinsState::rewardCoinNum,
                BuyCoinsState::rewardCoinRatio,
            ) { isMember, rewardCoinNum, rewardCoinRatio ->
                currentUserIsMember = isMember
                if (!doPageShowAnalytics) {
                    doPageShowAnalytics = true
                    Analytics.track(
                        EventConstants.EVENT_CLIENT_WALLET_SHOW,
                        // 上级页面名字
                        "source" to args.pageSource.toString(),
                        // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                        "page_style" to (if (isMember) "1" else "0"),
                        // 游戏code码
                        "gamecode" to args.gameCode.toString(),
                    )
                }
                updateMemberDesc(isMember, rewardCoinRatio)

                if (rewardCoinNum > 0) {
                    binding.tvSubscribeDesc1.visible(true)
                    binding.tvSubscribeDesc1.text = String.format(
                        getString(R.string.buy_coins_page_subscribe_award_desc),
                        UnitUtilWrapper.formatCoinCont(rewardCoinNum),
                    )
                } else {
                    binding.tvSubscribeDesc1.visible(false)
                }
                if (rewardCoinRatio != "0") {
                    binding.tvSubscribeDesc2.visible(true)
                    binding.tvSubscribeDesc2.text = String.format(
                        getString(R.string.buy_coins_page_subscribe_award_desc2),
                        "$rewardCoinRatio%",
                    )
                } else {
                    binding.tvSubscribeDesc2.visible(false)
                }
            }
        } else {
            if (!doPageShowAnalytics) {
                doPageShowAnalytics = true
                Analytics.track(
                    EventConstants.EVENT_CLIENT_WALLET_SHOW,
                    // 上级页面名字
                    "source" to args.pageSource.toString(),
                    // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                    "page_style" to "0",
                    // 游戏code码
                    "gamecode" to args.gameCode.toString(),
                )
            }
            binding.tvMoreEconomicalTitle.visible(false)
            binding.clSubscribe.visible(false)
        }
        binding.tvBottomDesc.movementMethod =
            InterceptClickEventLinkMovementMethod(binding.tvBottomDesc)
        binding.tvBottomDesc.text = SpannableHelper.Builder()
            .text(getString(R.string.buy_coins_page_policy_desc1))
            .colorRes(R.color.color_999999)
            .text(getString(R.string.buy_coins_page_policy_desc2))
            .colorRes(R.color.color_4AB4FF)
            .bold(true)
            .click {
                // 服务条款
                MetaRouter.Web.navigate(
                    this,
                    title = null,
                    url = BuildConfig.USER_AGREEMENT,
                    false,
                )
            }.text(getString(R.string.buy_coins_page_policy_desc3))
            .colorRes(R.color.color_999999)
            .text(getString(R.string.buy_coins_page_policy_desc4))
            .colorRes(R.color.color_4AB4FF)
            .bold(true)
            .click {
                val item = PayProvider.getBuyCoinsPageRechargeH5ConfigItem()
                // 派对跳用户充值协议, GPark 跳用户隐私协议
                MetaRouter.Web.navigate(
                    this,
                    title = item.title,
                    url = item.url,
                    true,
                )
            }
            .text(getString(R.string.buy_coins_page_policy_desc5))
            .colorRes(R.color.color_999999)
            .build()

        vm.onAsync(
            BuyCoinsState::payResult,
            deliveryMode = uniqueOnly("$destId"),
            onFail = { _, _ ->
                binding.rechargeLoading.visible(false)
                ToastUtil.showWithIcon(
                    R.drawable.icon_toast_recharge_failed,
                    getString(
                        R.string.iap_recharge_failed
                    )
                )
            }, onLoading = {
                // 显示充值loading
                binding.rechargeLoading.visible(true)
                binding.rechargeLoading.isClickable = true
                binding.rechargeLoadingIv.startAnimation(
                    AnimationUtils.loadAnimation(
                        context,
                        R.anim.anim_rotation_linear_2
                    )
                )
            }) { payResult ->
            binding.rechargeLoading.visible(false)
            if (payResult.isSuccess) {
                // 充值成功后, 需要刷新余额
                vm.getBalance()
                ToastUtil.showWithIcon(
                    R.drawable.icon_toast_recharge_success,
                    R.string.iap_recharge_success
                )
            } else if (payResult.code == IPayInteractor.FAIL_CANCEL) {
                // 取消支付
                ToastUtil.showWithIcon(
                    R.drawable.icon_toast_recharge_failed,
                    getString(
                        R.string.iap_recharge_failed
                    )
                )
            } else {
                // 支付失败
                ToastUtil.showWithIcon(
                    R.drawable.icon_toast_recharge_failed,
                    payResult.reason.ifEmpty {
                        getString(
                            R.string.iap_recharge_failed
                        )
                    }
                )
            }
        }

        vm.onEach(BuyCoinsState::balance) { balance ->
            val coinBalance = balance?.leCoinNum
            binding.tvCardBalance.text = if (coinBalance != null && coinBalance >= 0) {
                UnitUtilWrapper.formatCoinCont(coinBalance)
            } else {
                "---"
            }
            val todayIncrease = balance?.todayIncrease
            binding.tvCardTodayEarnings.text = if (todayIncrease != null && todayIncrease >= 0) {
                UnitUtilWrapper.formatCoinCont(todayIncrease)
            } else {
                "0"
            }
            val todayDecrease = balance?.todayDecrease
            binding.tvCardTodayExpenses.text = if (todayDecrease != null) {
                UnitUtilWrapper.formatCoinCont(todayDecrease)
            } else {
                "0"
            }
        }
        vm.onAsync(BuyCoinsState::products,
            onFail = { _, _ ->
                binding.loadingView.showError()
            }, onLoading = {
                binding.loadingView.showLoading()
            }, onSuccess = {
                binding.loadingView.hide()
            }
        )

        vm.loadData()
    }

    private fun buildProductController() = simpleController(
        vm,
        BuyCoinsState::products,
    ) { products ->
        if (products is Success) {
            val productsData = products.invoke()
            productsData.forEach { product ->
                add {
                    BuyCoinsItem(product, onItemClickedListener)
                        .id("BuyCoinsItem-${product.parentProductId}-${product.productId}")
                }
            }
        }
    }

    private fun updateMemberDesc(isMember: Boolean, rewardCoinRatio: String) {
        if (!isMember || rewardCoinRatio == "0") {
            binding.tvBuyCoinsMemberDesc.visible(false)
            return
        }
        val discount =
            String.format(
                getString(R.string.buy_coins_page_member_discount_desc2),
                "$rewardCoinRatio%"
            )
        binding.tvBuyCoinsMemberDesc.text = SpannableHelper.Builder()
            .text(getString(R.string.buy_coins_page_member_discount_desc1))
            .colorRes(R.color.color_666666)
            .text(discount)
            .colorRes(R.color.color_9242FF)
            .bold(true)
            .text(getString(R.string.buy_coins_page_member_discount_desc3))
            .colorRes(R.color.color_666666)
            .build()

        binding.tvBuyCoinsMemberDesc.visible(true)
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_BUY_COINS
}