package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import com.socialplay.gpark.util.InputUtil
import timber.log.Timber


class DispatchEventLinearLayout(context: Context?, attrs: AttributeSet?) :
    LinearLayout(context, attrs) {
    constructor(context: Context?) : this(context, null)

     lateinit var curFoucsViewListener:(()->View?)

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        if (event?.action == MotionEvent.ACTION_DOWN) {
            val v: View? = curFoucsViewListener.invoke()
            if (v is EditText) {
                val outRect = Rect()
                v.getGlobalVisibleRect(outRect)
                if (!outRect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                    Timber.d("focus touchevent")
                    v.clearFocus()
                    InputUtil.hideKeyboard(v)
                }
            }
        }
        return super.dispatchTouchEvent(event)
    }



}