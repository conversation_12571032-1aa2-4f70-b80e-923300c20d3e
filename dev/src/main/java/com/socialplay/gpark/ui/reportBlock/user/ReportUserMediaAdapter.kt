package com.socialplay.gpark.ui.reportBlock.user

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.luck.picture.lib.config.PictureMimeType
import com.socialplay.gpark.data.model.reportBlock.ReportAttachment
import com.socialplay.gpark.databinding.ItemFeedbackImageBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.likelyPath

/**
 * Created by bo.li
 * Date: 2024/7/11
 * Desc: 用户举报-媒体资源选项
 */
class ReportUserMediaAdapter(private val glide: RequestManager) :
    BaseAdapter<ReportAttachment, ItemFeedbackImageBinding>() {

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemFeedbackImageBinding {
        return ItemFeedbackImageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemFeedbackImageBinding>,
        item: ReportAttachment,
        position: Int
    ) {
        holder.binding.ivImage.isVisible = !item.isAdd
        holder.binding.ivDelete.isVisible = !item.isAdd
        holder.binding.ivAdd.isVisible = item.isAdd
        holder.binding.ivVideoSign.isVisible = PictureMimeType.isHasVideo(item.localMedia?.mimeType)
        item.localMedia?.likelyPath
        val path = item.localMedia?.likelyPath
        glide.load(path).into(holder.binding.ivImage)
    }
}