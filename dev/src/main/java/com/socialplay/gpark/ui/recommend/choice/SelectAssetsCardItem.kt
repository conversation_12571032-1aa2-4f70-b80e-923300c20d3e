package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import com.airbnb.epoxy.EpoxyModel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemSelectAssetsBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import android.text.TextUtils
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import android.view.Gravity
import com.airbnb.epoxy.Carousel

/**
 * Select Assets card implementation
 * Displays asset list in horizontal scrolling layout
 */
fun MetaModelCollector.choiceSelectAssetsCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList
    if (games.isNullOrEmpty()) return
    
    // Title
    textItem(
        text = card.cardName,
        textSize = 18.0f,
        fontFamily = R.font.poppins_bold_700,
        textColorRes = R.color.textColorPrimary,
        isSingleLine = true,
        ellipsize = TextUtils.TruncateAt.END,
        gravity = Gravity.START or Gravity.CENTER_VERTICAL,
        paddingLeftDp = 16.0f,
        paddingTopDp = 16.0f,
        paddingRightDp = 12.0f,
        paddingBottomDp = 8.0f,
        idStr = "ChoiceSelectAssetsCardTitle-$cardPosition",
        spanSize = spanSize
    )
    
    // Horizontal scrolling list
    carouselNoSnapWrapBuilder {
        id("ChoiceSelectAssetsCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(4)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        
        games.forEachIndexed { position, game ->
            add(
                ChoiceSelectAssetsItem(
                    game,
                    position,
                    card,
                    cardPosition,
                    spanSize,
                    listener
                ).id("ChoiceSelectAssets-$cardPosition-$position-${game.code}")
            )
        }
    }
}

/**
 * Select Assets item - displays asset with price and other info
 */
data class ChoiceSelectAssetsItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemSelectAssetsBinding>(
    R.layout.adapter_choice_card_item_select_assets,
    AdapterChoiceCardItemSelectAssetsBinding::bind
) {

    override fun AdapterChoiceCardItemSelectAssetsBinding.onBind() {
        root.setMargin(right = dp(10))
        
        // Load asset image
        listener.getGlideOrNull()?.let { glide ->
            glide.load(item.imageUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivAssetIcon)
        }
        
        // Set asset title
        tvAssetTitle.text = item.displayName
        
        // Set price (using description field for price info)
        if (!item.description.isNullOrEmpty()) {
            tvAssetPrice.text = item.description
            tvAssetPrice.visibility = View.VISIBLE
        } else {
            tvAssetPrice.visibility = View.GONE
        }
        
        // Set creator info if available
        if (!item.nickname.isNullOrEmpty()) {
            tvCreatorName.text = item.nickname
            tvCreatorName.visibility = View.VISIBLE
        } else {
            tvCreatorName.visibility = View.GONE
        }
        
        // Set like count or other stats
        item.likeCount?.let { count ->
            if (count > 0) {
                tvStats.text = "${count} likes"
                tvStats.visibility = View.VISIBLE
            } else {
                tvStats.visibility = View.GONE
            }
        } ?: run {
            tvStats.visibility = View.GONE
        }
        
        // Set tags if available
        if (!item.tagList.isNullOrEmpty()) {
            val firstTag = item.tagList?.firstOrNull()
            if (!firstTag.isNullOrEmpty()) {
                tvAssetTag.text = firstTag
                tvAssetTag.visibility = View.VISIBLE
            } else {
                tvAssetTag.visibility = View.GONE
            }
        } else {
            tvAssetTag.visibility = View.GONE
        }

        // Show Buy Now button for assets with price
        if (!item.description.isNullOrEmpty()) {
            btnBuyNow.visibility = View.VISIBLE
            btnBuyNow.setOnAntiViolenceClickListener {
                // Handle buy now click - could open purchase dialog
                listener.onItemClick(cardPosition, card, position, item, false)
            }
        } else {
            btnBuyNow.visibility = View.GONE
        }

        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}
