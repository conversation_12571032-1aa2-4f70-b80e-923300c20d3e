package com.socialplay.gpark.ui.search.tab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.SearchGameItem
import com.socialplay.gpark.databinding.FragmentSearchGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.search.SearchHelper
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh2
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.search.SearchAdapter
import com.socialplay.gpark.ui.search.SearchViewModel
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collectLatest
import org.koin.android.ext.android.inject

@FlowPreview
@ExperimentalCoroutinesApi
class SearchGameFragment: BaseEditorFragment<FragmentSearchGameBinding>() {

    private val searchVM: SearchViewModel by viewModels({ requireParentFragment().requireParentFragment() })
    private val adapter = SearchAdapter(::glide)
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }
    private val metaKV: MetaKV by inject()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSearchGameBinding? {
        return FragmentSearchGameBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()

        adapter.setOnItemClickListener { view, position ->
            val peek = safeGetItem(position) ?: return@setOnItemClickListener
            searchVM.postSearchReport(peek.contentId ?: "", if (peek.isUgc) 2 else SearchHelper.PGC_TYPE)
            goGameDetail(peek)
            Analytics.track(
                EventConstants.SEARCH_ITEM_CLICK,
                "id" to peek.id,
                "packagename" to peek.packageName,
                "contenttype" to (if (peek.isUgc) "ugc" else "game"),
                "show_categoryid" to CategoryId.SEARCH_RESULT,
                "show_param1" to position
            )
        }
        adapter.addChildClickViewIds(R.id.tvEnter)
        adapter.setOnItemChildClickListener { _, position ->
            val peek = safeGetItem(position)?:return@setOnItemChildClickListener
            val resIdBean = ResIdBean().setCategoryID(CategoryId.SEARCH_RESULT).setClickGameTime(System.currentTimeMillis())
            searchVM.postSearchReport(peek.contentId?:"", if (peek.isUgc) 2 else SearchHelper.PGC_TYPE)
            if(peek.isUgc) {
                val params = EditorUGCLaunchParams(peek.id,peek.packageName,peek.displayName.toString(),peek.iconUrl ?: "","")
                editorGameLaunchHelper?.startUgcGame(this, params, resIdBean.setTsType(ResIdBean.TS_TYPE_UCG))
            }else{
                if (tsLaunch.isLaunching(peek.id)) {
                    toast(R.string.launching)
                } else {
                    metaKV.analytic.saveClickLaunchTime(peek.packageName, System.currentTimeMillis())
                    val params = TSLaunchParams(GameDetailInfo(peek.id, pkg = peek.packageName), resIdBean)
                    tsLaunch.launch(requireContext(), params)
                }
            }
        }
        adapter.setOnItemShowListener { view, position ->
            val peek = safeGetItem(position) ?: return@setOnItemShowListener
            Analytics.track(
                EventConstants.SEARCH_ITEM_SHOW,
                "id" to peek.id,
                "packagename" to peek.packageName,
                "contenttype" to (if (peek.isUgc) "ugc" else "game"),
                "show_categoryid" to CategoryId.SEARCH_RESULT,
                "show_param1" to position
            )
        }
        binding.rv.adapter = adapter

        adapter.withStatusAndRefresh2(
            viewLifecycleOwner,
            binding.lv2,
            null,
            getString(R.string.search_empty),
            R.drawable.icon_no_recent_activity
        )

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            searchVM.searchResultGameFlow.collectLatest {
                adapter.submitData(it)
            }
        }
    }

    private fun goGameDetail(peek: SearchGameItem) {
        val resIdBean = ResIdBean().setCategoryID(CategoryId.SEARCH_RESULT)
        if (peek.isUgc) {
            MetaRouter.MobileEditor.ugcDetail(
                this,
                peek.id,
                null,
                resIdBean
            )
        } else {
            MetaRouter.GameDetail.navigate(this, peek.id, resIdBean, peek.packageName)
        }
    }

    private fun safeGetItem(position: Int): SearchGameItem? = kotlin.runCatching { adapter.peek(position) }.getOrNull()

    override fun loadFirstData() {
    }

    override fun getFragmentName() = "SearchGameFragment"
}