package com.socialplay.gpark.ui.videofeed.common

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostPublish.Companion.filterEmptyUrl
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.videofeed.common.Comment
import com.socialplay.gpark.data.model.videofeed.common.CommentArgs
import com.socialplay.gpark.data.model.videofeed.common.CommentUIState
import com.socialplay.gpark.data.model.videofeed.common.InsertPosition
import com.socialplay.gpark.data.model.videofeed.common.PlayerReply
import com.socialplay.gpark.data.model.videofeed.common.Reply
import com.socialplay.gpark.data.model.videofeed.common.ReplyExpandCollapseBar
import com.socialplay.gpark.data.model.videofeed.common.ReplyExpandCollapseBarStatus
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.v2.PostDetailState
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/11/13
 *     desc   :
 */

data class CommentViewModelState(
    val args: CommentArgs,
    val items: List<CommentUIState> = emptyList(),
    val articleDetail: PostDetail? = null,
    val refresh: Async<ArticleDetailWithCommentData?> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val replyRefresh: Async<*> = Uninitialized,
    val replyLoadMore: Async<*> = Uninitialized,
    val toastMsg: ToastData = ToastData.EMPTY,
    val scrollToTop: Boolean = false,
    val queryType: Int = PostCommentListRequestBody.QUERY_TYPE_TOP,
    val page: Int = 1,
    val muteStatus: Async<UserMuteStatus> = Uninitialized,
) : MavericksState {

    constructor(args: CommentArgs) : this(args, items = emptyList())
}


data class ArticleDetailWithCommentData(
    val postDetail: PostDetail,
    val commentData: List<PostComment>?,
    val hasMoreDataToLoad: Boolean
)


class CommentViewModel(
    private val app: Application,
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    private val initialState: CommentViewModelState,
) : BaseViewModel<CommentViewModelState>(initialState) {

    companion object : KoinViewModelFactory<CommentViewModel, CommentViewModelState>() {

        //查询数据类型bbs社区
        const val MODULE_COMMUNITY = 1

        const val PAGE_INDEX_FIRST = 1

        //评论默认携带回复数
        const val REPLY_DEFAULT_COUNT = 0

        //回复每次展开数量
        const val REPLY_EXPAND_COUNT = 3

        //每次获取评论的条数
        var COMMENT_NUM = 10


        const val QUERY_TYPE_RECENT = 1
        const val QUERY_TYPE_LIKE = 3

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: CommentViewModelState
        ): CommentViewModel {
            return CommentViewModel(get(), get(), get(), state)
        }
    }

    /**
     * @param overrideCommentCnt 是否使用传入的评论数覆盖掉当前的评论数
     */
    fun refreshComment(overrideCommentCnt: Boolean = false, ) {
        withState { oldState ->
            if (oldState.refresh is Loading) return@withState

            val ts = System.currentTimeMillis()

            val postId = initialState.args.postId

            repository.getPostDetailV2(postId).map {

                val detail = it.copy(
                    mediaList = PostPublish.validateMedias(it.mediaList, ts).filterEmptyUrl(),
                    gameCardList = PostPublish.validateGames(it.gameCardList),
                    tagList = PostPublish.validateTags(it.tagList)
                )

                val commentList = repository.getPostCommentListV2(
                    PostCommentListRequestBody(
                        MODULE_COMMUNITY,
                        postId,
                        oldState.queryType,
                        COMMENT_NUM,
                        PAGE_INDEX_FIRST,
                        REPLY_DEFAULT_COUNT,
                        PostReplyListRequestBody.QUERY_LATEST,
                        null,
                        null
                    )
                ).invoke()

                if (overrideCommentCnt) {
                    ArticleDetailWithCommentData(
                        detail.copy(commentCount = initialState.args.commentCount),
                        commentList.dataList, !commentList.end
                    )
                } else {
                    ArticleDetailWithCommentData(detail, commentList.dataList, !commentList.end)
                }
            }.execute {
                if (it is Success) {
                    val articleDetailWithCommentData = it.invoke()

                    // 对取回的数据去重
                    val articleCommentData = articleDetailWithCommentData.commentData

                    copy(
                        refresh = it.copy(articleDetailWithCommentData.copy(commentData = articleCommentData)),
                        items = articleCommentData?.flatten(articleDetailWithCommentData.postDetail) ?: emptyList(),
                        loadMore = Uninitialized,
                        articleDetail = articleDetailWithCommentData.postDetail,
                        page = PAGE_INDEX_FIRST
                    )
                } else {
                    copy(refresh = it)
                }
            }
        }
    }

    fun loadMoreComment() = viewModelScope.launch {
        withState { oldState ->

            // 需要先刷新才能 loadmore
            val articleDetail = oldState.articleDetail

            val lastComment = oldState.items.last { it is Comment } as? Comment

            if (articleDetail == null || lastComment == null) {
                setState {
                    copy(loadMore = Fail(IllegalStateException(app.getString(R.string.loading_failed_click_to_retry))))
                }
                return@withState
            }

            val pageNum = oldState.page + 1

            repository.getPostCommentListV2(
                PostCommentListRequestBody(
                    MODULE_COMMUNITY,
                    oldState.args.postId,
                    oldState.queryType,
                    COMMENT_NUM,
                    pageNum,
                    REPLY_DEFAULT_COUNT,
                    PostReplyListRequestBody.QUERY_LATEST,
                    null,
                    null
                )
            ).map {
                ArticleDetailWithCommentData(articleDetail, it.dataList, !it.end)
            }.execute {
                when (it) {
                    is Success -> {
                        // 对取回的数据进行去重
                        val loadMoreResult = it.invoke()

                        val articleCommentData = loadMoreResult.commentData?.distinct(this)

                        copy(
                            items = items + (articleCommentData?.flatten(articleDetail) ?: emptyList()),
                            loadMore = Success(LoadMoreState(!loadMoreResult.hasMoreDataToLoad)),
                            page = pageNum
                        )
                    }

                    is Fail -> {
                        copy(loadMore = Fail(it.error))
                    }

                    else -> {
                        Timber.d("PLDebug LoadMore prepostdata else ${oldState.javaClass.simpleName}")
                        copy(loadMore = Loading())
                    }
                }
            }
        }
    }

    fun expandOrLoadMoreReplies(commentId: String) = viewModelScope.launch {
        withState {
            Analytics.track(
                EventConstants.EVENT_POST_SHOW_REPLIES_CLICK, mapOf(
                "location" to 2,
                "postId" to it.args.postId,
                "show_categoryId" to it.args.resId.getCategoryID(),
            ))

            val replyExpandCollapseBar =
                it.items.getReplyExpandCollapseBar(commentId) ?: return@withState
            if (replyExpandCollapseBar.second.status == ReplyExpandCollapseBarStatus.Collapsed) {
                expandReplies(commentId)
            } else {
                loadMoreReply(commentId)
            }
        }
    }

    /**
     * 收起回复
     */
    fun collapseReplies(commentId: String) = viewModelScope.launch {
        withState {
            val indexAndReplyExpandCollapseBar =
                it.items.getReplyExpandCollapseBar(commentId) ?: return@withState
            if (indexAndReplyExpandCollapseBar.second.status != ReplyExpandCollapseBarStatus.Collapsed) {
                val newItems = it.items.toMutableList()

                // 设置状态为 Collapsed
                newItems[indexAndReplyExpandCollapseBar.first] =
                    indexAndReplyExpandCollapseBar.second.copy(status = ReplyExpandCollapseBarStatus.Collapsed)

                // 保留至多“默认加载回复数量”个回复，多余的回复全部删除掉
                newItems.removeReplies(
                    commentId,
                    keepCnt = REPLY_DEFAULT_COUNT
                )

                val indexAndComment = newItems.getComment(commentId)
                indexAndComment?.second?.let { newItems.setupReplyExpandCollapseBarStatus(it) }
                setState { copy(items = newItems) }
            }
        }
    }

    /**
     * 展开回复
     */
    fun expandReplies(commentId: String) {

        withState {

            val newItems = it.items.toMutableList()

            val indexAndReplyExpandCollapseBar =
                newItems.getReplyExpandCollapseBar(commentId) ?: return@withState

            // 设置状态为Expanded
            newItems[indexAndReplyExpandCollapseBar.first] =
                indexAndReplyExpandCollapseBar.second.copy(status = ReplyExpandCollapseBarStatus.Expanded)

            newItems.removeAll { it is Reply && it.owner.playerComment.commentId == commentId }
            val indexAndComment = newItems.getComment(commentId)

            if (indexAndComment != null) {
                val comment = indexAndComment.second
                newItems.addAll(
                    indexAndComment.first + 1,
                    comment.playerComment.replyCommonPage?.dataList?.map { createReply(comment, it) } ?: emptyList()
                )
            }

            indexAndComment?.second?.let { newItems.setupReplyExpandCollapseBarStatus(it) }

            setState { copy(items = newItems) }
        }
    }

    fun setCommentLikeStatus(commentId: String, isLiked: Boolean) {
        withState {
            repository.saveOpinion(OpinionRequestBody.commentLike(commentId, isLiked)).execute {
                when (it) {
                    is Fail -> {
                        //Toast message
                        copy(toastMsg = toastMsg.toMsg(it.error.message ?: ""))
                    }

                    is Success -> {
                        val indexedComment = this.items.getComment(commentId)
                        if (indexedComment != null) {
                            val newItems = this.items.toMutableList()
                            val comment = indexedComment.second

                            newItems[indexedComment.first] = comment.copy(
                                isLiked = isLiked,
                                playerComment = comment.playerComment.copy(likeCount = comment.playerComment.likeCount + (if (isLiked) 1 else -1))
                            )
                            copy(items = newItems)
                        } else {
                            copy()
                        }
                    }

                    else -> {
                        this
                    }
                }
            }
        }
    }


    fun setReplyLikeStatus(commentId: String, replyId: String, isLiked: Boolean) {
        withState {
            repository.saveOpinion(OpinionRequestBody.replyLike(replyId, isLiked)).execute {
                when (it) {
                    is Fail -> {
                        //Toast message
                        copy(toastMsg = toastMsg.toMsg(it.error.message ?: ""))
                    }

                    is Success -> {
                        val indexedReply = this.items.getReply(commentId, replyId)
                        if (indexedReply != null) {
                            val newItems = this.items.toMutableList()
                            val reply = indexedReply.second
                            val likeCount = reply.playerReply.likeCount + (if (isLiked) 1 else -1)
                            newItems[indexedReply.first] = reply.copy(
                                isLiked = isLiked,
                                playerReply = reply.playerReply.copy(likeCount = likeCount)
                            )
                            copy(items = newItems)
                        } else {
                            copy()
                        }
                    }

                    else -> {
                        this
                    }
                }
            }
        }
    }

    //回复评论
    fun replyComment(
        commentId: String,
        content: String,
    ) {
        //TODO 回复是否需要更新评论数
        val userInfo = accountInteractor.accountLiveData.value
        val now = System.currentTimeMillis()

        val requestBody = PostReplyRequestBody(content, userInfo?.uuid.orEmpty(), commentId)

        repository.addPostReply(
            requestBody
        ).execute { result ->
            when (result) {
                is Fail -> {
                    copy(toastMsg = toastMsg.toMsg(result.error.message ?: ""))
                }

                is Success -> {
                    val replyId = result.invoke()
                    val playerReply = requestBody.toPostReply(replyId, userInfo, now)
                    copy(
                        items = items.insertReplies(
                            commentId,
                            listOf(playerReply),
                            InsertPosition.Head
                        )
                    )
                }

                else -> {
                    this
                }
            }
        }
    }

    //回复回复
    fun replyReply(
        commentId: String,
        content: String,
        repliedId: String,
        repliedName: String?,
        repliedUuid: String?,
    ) {

        //TODO 回复是否需要更新评论数
        val postId = initialState.args.postId
        val now = System.currentTimeMillis()

        val userInfo = accountInteractor.accountLiveData.value

        val requestBody = PostReplyRequestBody(
            content = content,
            uid = userInfo?.uuid.orEmpty(),
            commentId = commentId,
            replyUid = repliedUuid,
            replyNickname = repliedName,
            replyContentId = repliedId
        )
        repository.addPostReply(
            requestBody
        ).execute { result ->

            when (result) {
                is Fail -> {
                    copy(toastMsg = toastMsg.toMsg(result.error.message ?: ""))
                }

                is Success -> {
                    val replyId = result.invoke()
                    val playerReply = requestBody.toPostReply(replyId, userInfo, now)
                    copy(items = items.insertReplies(commentId, listOf(playerReply), InsertPosition.Head))
                }

                else -> {
                    this
                }
            }
        }
    }


    fun deleteReply(commentId: String, replyId: String) {
        val postId = initialState.args.postId
        repository.deletePostReply(replyId).execute {
            if (it is Success) {

                val indexedComment = items.getComment(commentId)

                if(indexedComment != null){

                    val comment = indexedComment.second

                    val replies = ArrayList(comment.playerComment.replyCommonPage?.dataList ?: emptyList()).apply {
                        removeAll { it.replyId == replyId }
                    }

                    val newItems = items.toMutableList()

                    val newComment = comment.copy(
                        playerComment = comment.playerComment.copy(
                            replyCommonPage = comment.playerComment.replyCommonPage?.copy(dataList = replies)
                        )
                    )

                    newItems[indexedComment.first] = newComment
                    newItems.removeAll { it is Reply && it.playerReply.replyId == replyId }

                    newItems.setupReplyExpandCollapseBarStatus(newComment)

                    //TODO 回复是否需要更新评论数
                    copy(items = newItems)

                }else{
                    this
                }
            } else {
                this
            }
        }
    }

    fun deleteComment(commentId: String) {
        repository.deletePostComment(commentId).execute {
            if (it is Success) {
                val newItems = items.toMutableList()
                newItems.removeAll {
                    (it is Comment && it.playerComment.commentId == commentId) ||
                            (it is Reply && it.owner.playerComment.commentId == commentId)
                }

                if (newItems.isEmpty()) {
                    // 仅维护需要的字段如评论数发布者即可
                    if (articleDetail != null) {
                        val newArticleDetail = articleDetail.copy(commentCount = 0)

                        // 应该总是不会为空才对
                        val detailWithCommentData = ArticleDetailWithCommentData(
                            postDetail = newArticleDetail,
                            commentData = emptyList(),
                            hasMoreDataToLoad = false
                        )
                        copy(
                            items = newItems,
                            refresh = Success(detailWithCommentData),
                            articleDetail = newArticleDetail
                        )
                    } else {
                        copy(items = newItems)
                    }

                } else {
                    copy(
                        items = newItems,
                        articleDetail = articleDetail?.copy(commentCount = articleDetail.commentCount - 1)
                    )
                }

            } else {
                this
            }
        }
    }

    fun setCommentTextExpandStatus(commentId: String, expand: Boolean) {
        setState {
            val indexedComment = items.getComment(commentId)
            val newItems = items.toMutableList()
            if(indexedComment != null){
                newItems[indexedComment.first] = indexedComment.second.copy(isTextExpanded = expand)
            }
            copy(items = newItems)
        }
    }

    fun setReplyTextExpandStatus(commentId: String, replyId: String, expand: Boolean) {
        setState {
            val indexedComment = items.getComment(commentId)
            val newItems = items.toMutableList()
            if(indexedComment != null){
                val indexedReply = items.getReply(commentId,replyId)
                if(indexedReply != null){
                    newItems[indexedReply.first] = indexedReply.second.copy(isTextExpanded = expand)
                }
            }
            copy(items = newItems)
        }
    }

    /*
           suspend fun getCommentReportParams(comment: Comment): Map<String, String?> {
               val state = awaitState()
               val gameCircleName = state.articleDetail?.gameCircleName

               val resId = initialState.args.postId
               return ReportParamsUtil.getReportCommentParams(
                   reportUuid = comment.playerComment.uuid,
                   reportId = comment.playerComment.commentId,
                   icon = comment.playerComment.avatar ?: "",
                   userName = comment.playerComment.username,
                   belongsId = resId,
                   content = comment.playerComment.content,
                   reporterId = accountInteractor.curUuid ?: "",
                   signature = comment.playerComment.userInfo?.signature ?: "",
                   userType = comment.playerComment.userInfo?.origin ?: "",
                   commentResId = resId,
                   gameCircleName = gameCircleName
               )
           }

           suspend fun getReplyReportParams(reply: Reply): Map<String, String?> {
               val state = awaitState()
               val gameCircleName = state.articleDetail?.gameCircleName

               val resId = initialState.args.postId
               return ReportParamsUtil.getReportCommentParams(
                   reportUuid = reply.playerReply.uuid,
                   reportId     = reply.playerReply.commentId ?: "",
                   icon = reply.playerReply.avatar ?: "",
                   userName = reply.playerReply.username ?: "",
                   belongsId = resId,
                   content = reply.playerReply.content,
                   reporterId = accountInteractor.curUuid ?: "",
                   signature = reply.playerReply.userInfo?.signature ?: "",
                   userType = reply.playerReply.userInfo?.origin ?: "",
                   commentResId = resId,
                   gameCircleName = gameCircleName
               )
           }
           */

    private fun loadMoreReply(commentId: String) = viewModelScope.launch {
        withState {

            val indexAndComment = it.items.getComment(commentId) ?: return@withState

            // Todo Using PostComment#lastReplyId replace this, Be careful add comment and remove commend needed to update this value
            val lastReplyId = indexAndComment.second.playerComment.replyCommonPage?.dataList?.lastOrNull()?.replyId ?: ""

            repository.getPostReplyListV2(
                PostReplyListRequestBody(
                    REPLY_EXPAND_COUNT,
                    commentId,
                    PostReplyListRequestBody.QUERY_LATEST,
                    lastReplyId = lastReplyId
                )
            ).execute {
                if (it is Success) {
                    val dataResult = it.invoke()
                    val replies = dataResult.dataList

                    val newItems = items.toMutableList()

                    // 设置状态为Expanded
                    val indexAndReplyExpandCollapseBar =
                        newItems.getReplyExpandCollapseBar(commentId)
                    if (indexAndReplyExpandCollapseBar != null) {
                        newItems[indexAndReplyExpandCollapseBar.first] =
                            indexAndReplyExpandCollapseBar.second.copy(status = ReplyExpandCollapseBarStatus.Expanded)
                    }

                    copy(
                        items = newItems.insertReplies(commentId, replies),
                        replyLoadMore = it
                    )
                } else {
                    copy(replyLoadMore = it)
                }
            }
        }
    }


    fun setScrollToTop(scrollToTop: Boolean) {
        setState { copy(scrollToTop = scrollToTop) }
    }

    //评论帖子
    fun commentArticle(content: String) {
        val postId = initialState.args.postId

        val requestBody = PostCommentRequestBody(content, MODULE_COMMUNITY, postId)
        val ts = System.currentTimeMillis()
        val userInfo = accountInteractor.accountLiveData.value

        repository.queryUserMuteStatus().map {
            if(it.isMuted == true){
                throw IllegalStateException(app.getString(R.string.community_ban, app.getString(R.string.commenting)))
            }
            return@map repository.addPostComment(requestBody)()
        }.execute {
            if (it is Fail) {
                copy(toastMsg = toastMsg.toMsg(it.error.message ?: ""))
            } else if (it is Success) {

                if(articleDetail == null){
                    return@execute this
                }

                val commentId = it().data
                val comment = commentId?.let { it1 ->
                    requestBody.toPostComment(
                        it1,
                        userInfo,
                        ts
                    )
                }
                if(comment!=null){
                    val newItems = items.toMutableList()
                    val isFirstComment = items.isEmpty()
                    newItems.add(0, createComment(articleDetail, comment, false))

                    // 如果是第一条评论，增加一个刷新事件，用来去掉空内容遮罩
                    if(isFirstComment){
                        val newArticleDetail = articleDetail.copy(commentCount = 1)
                        copy(
                            items = newItems,
                            scrollToTop = true,
                            refresh = Success(
                                ArticleDetailWithCommentData(
                                    postDetail = newArticleDetail,
                                    commentData = listOf(comment),
                                    hasMoreDataToLoad = false
                                )
                            ),
                            articleDetail = newArticleDetail
                        )
                    }else{
                        copy(
                            items = newItems,
                            scrollToTop = true,
                            articleDetail = articleDetail.copy(commentCount = articleDetail.commentCount + 1)
                        )
                    }
                }else{
                    this
                }
            } else {
                this
            }
        }
    }


    fun setQueryType(queryType: Int) = withState { s ->
        if (s.loadMore is Loading || s.refresh is Loading) return@withState

        if (s.queryType != queryType) {
            setState { copy(queryType = queryType) }
        }

        refreshComment()
    }

    private fun createReply(
        comment: Comment,
        playerReply: PlayerReply
    ): Reply {
        return Reply(
            owner = comment,
            playerReply = playerReply,
            isSelf = accountInteractor.curUuid == playerReply.uid,
            isTextExpanded = false,
            isLastReply = false,
            isLiked = playerReply.isLike
        )
    }

    private fun createComment(
        articleDetail: PostDetail,
        playerComment: PostComment,
        isLiked: Boolean
    ): Comment {
        return Comment(
            articleDetail,
            playerComment,
            isLiked,
            accountInteractor.curUuid == playerComment.uid,
            false
        )
    }

    fun isMe(uid: String?) = accountInteractor.isMe(uid)

    private fun List<PostComment>.flatten(postDetail: PostDetail): List<CommentUIState> {
        val result = mutableListOf<CommentUIState>()

        this.forEach { playerComment ->
            val isLiked = playerComment.isLike
            val comment = createComment(postDetail, playerComment, isLiked)
            result.add(comment)

            playerComment.replyCommonPage?.dataList?.let { playerReply ->
                result.addAll(playerReply.map { createReply(comment, it) })
                result.setupReplyExpandCollapseBarStatus(comment)
            }
        }

        return result
    }

    private fun MutableList<CommentUIState>.setupReplyExpandCollapseBarStatus(comment: Comment) {
        //TODO  代码优化，比较复杂

        val playerComment = comment.playerComment
        val retrievedPlayerReplyCount = comment.playerComment.replyCommonPage?.dataList?.size ?: 0

        // 如果回复数量超过了默认获取的回复数量，则显示展开收起条
        if (playerComment.replyCount > REPLY_DEFAULT_COUNT) {

            var indexOfReplyExpandCollapseBar = this.indexOfLast {
                it is ReplyExpandCollapseBar &&
                        it.referencedComment.playerComment.commentId == playerComment.commentId
            }

            if (indexOfReplyExpandCollapseBar == -1) {
                indexOfReplyExpandCollapseBar = this.size
                this.add(
                    ReplyExpandCollapseBar(
                        status = ReplyExpandCollapseBarStatus.Default,
                        expandable = true,
                        collapsable = true,
                        referencedComment = comment,
                        remainReplyCountToExpand = 0
                    )
                )
            }

            // 如果取回的回复数量大于等于最大的回复数量，则不显示展开更多 否则显示展开更多
            val expandable = retrievedPlayerReplyCount < playerComment.replyCount
            val collapsable = retrievedPlayerReplyCount > REPLY_DEFAULT_COUNT

            val replyExpandCollapseBar = this[indexOfReplyExpandCollapseBar] as ReplyExpandCollapseBar

            this[indexOfReplyExpandCollapseBar] = replyExpandCollapseBar.copy(
                status = replyExpandCollapseBar.status,
                expandable = expandable || replyExpandCollapseBar.status == ReplyExpandCollapseBarStatus.Collapsed,
                collapsable = (collapsable && replyExpandCollapseBar.status != ReplyExpandCollapseBarStatus.Collapsed) ||
                        replyExpandCollapseBar.status == ReplyExpandCollapseBarStatus.Expanded,
                referencedComment = comment,
                remainReplyCountToExpand = (playerComment.replyCount - REPLY_DEFAULT_COUNT)
            )
        }

        // FixUp last reply status
        for ((index, uiState) in this.withIndex()) {
            if(uiState is Reply){
                val hasNext = index + 1 < this.size
                if(hasNext){
                    val next = this[index + 1]
                    val isLastReply = next !is Reply || next.owner.playerComment.commentId != uiState.owner.playerComment.commentId
                    this[index] = uiState.copy(isLastReply = isLastReply)
                }else{
                    this[index] = uiState.copy(isLastReply = true)
                }
            }
        }
    }

    private fun List<CommentUIState>.insertReplies(
        repliesOwnerCommentId: String,
        replies: List<PlayerReply>? = null,
        position: InsertPosition = InsertPosition.Tail
    ): List<CommentUIState> {
        if (replies.isNullOrEmpty()) return this

        val result = toMutableList()

        val indexOfComment =
            result.indexOfLast { it is Comment && it.playerComment.commentId == repliesOwnerCommentId }

        if (indexOfComment == -1) {
            return result
        }

        // 替换原来的评论为新的评论对象
        val comment = result[indexOfComment] as Comment
        val newComment = comment.copy(
            playerComment = comment.playerComment.copy(
                replyCommonPage = comment.playerComment.replyCommonPage?.copy(dataList = ArrayList(
                    if (position == InsertPosition.Head) {
                        replies + (comment.playerComment.replyCommonPage.dataList ?: emptyList())
                    } else {
                        (comment.playerComment.replyCommonPage.dataList ?: emptyList()) + replies
                    }
                ))
            )
        )

        result[indexOfComment] = newComment


        // 插入新的回复
        val replyInsertIndex = if(position == InsertPosition.Head){
            indexOfComment + 1
        }else{
            val indexOfCommentLastReply = result.indexOfLast { it is Reply && it.owner.playerComment.commentId == repliesOwnerCommentId }
            if (indexOfCommentLastReply != -1) indexOfCommentLastReply + 1 else indexOfComment + 1
        }

        val mappedReplies = replies.map { createReply(comment, it) }
        result.addAll(replyInsertIndex, mappedReplies)

        result.setupReplyExpandCollapseBarStatus(newComment)

        return result
    }

    private fun List<CommentUIState>.getComment(commentId: String): Pair<Int, Comment>? {

        val index = indexOfFirst { it is Comment && it.playerComment.commentId == commentId }
        if (index == -1) {
            return null
        }

        val comment = this[index]
        if (comment !is Comment) {
            return null
        }

        return index to comment
    }

    private fun List<CommentUIState>.getReply(commentId: String, replyId: String): Pair<Int, Reply>? {

        val indexOfReply = indexOfFirst {
            it is Reply &&
                    it.owner.playerComment.commentId == commentId &&
                    it.playerReply.replyId == replyId
        }

        if (indexOfReply == -1) {
            return null
        }

        val reply = this[indexOfReply] as Reply

        return indexOfReply to reply
    }

    private fun List<CommentUIState>.getReplyExpandCollapseBar(commentId: String): Pair<Int, ReplyExpandCollapseBar>? {

        val index = this.indexOfLast {
            it is ReplyExpandCollapseBar && it.referencedComment.playerComment.commentId == commentId
        }

        if (index == -1) {
            return null
        }

        val replyExpandCollapseBar = this[index]
        if (replyExpandCollapseBar !is ReplyExpandCollapseBar) {
            return null
        }

        return index to replyExpandCollapseBar
    }

    private fun MutableList<CommentUIState>.removeReplies(commentId: String, keepCnt: Int) {
        val beginIndex =
            this.indexOfFirst { it is Reply && it.owner.playerComment.commentId == commentId }
        val endIndex =
            this.indexOfLast { it is Reply && it.owner.playerComment.commentId == commentId }

        if (beginIndex == -1 || endIndex == -1) {
            Timber.w("RemoveReplies Illegal status [$beginIndex $endIndex]")
            return
        }

        val cnt = (endIndex - beginIndex) + 1
        if (cnt <= keepCnt) {
            return
        }

        this.removeRange(beginIndex + keepCnt, endIndex)
    }

    private fun MutableList<*>.removeRange(beginIndex: Int, endIndex: Int) {
        val cnt = endIndex - beginIndex
        for (i in 0..cnt) {
            this.removeAt(beginIndex)
        }
    }

    private fun List<PostComment>.distinct(state: CommentViewModelState): List<PostComment> {
        val commentList = state.items.filterIsInstance<Comment>().map { it.playerComment.commentId }
        return this.filter { !commentList.contains(it.commentId) }
    }


}
