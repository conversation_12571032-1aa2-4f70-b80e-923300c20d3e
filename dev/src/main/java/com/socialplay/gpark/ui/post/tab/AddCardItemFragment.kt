package com.socialplay.gpark.ui.post.tab

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.view.isVisible
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.RedeliverOnStart
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.databinding.FragmentAddCardItemBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.post.card.AddCardFrameModelState
import com.socialplay.gpark.ui.post.card.AddCardFrameViewModel
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Created by bo.li
 * Date: 2023/9/28
 * Desc:
 */
@SuppressLint("ClickableViewAccessibility")
class AddCardItemFragment :
    BaseRecyclerViewFragment<FragmentAddCardItemBinding>(R.layout.fragment_add_card_item) {
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val viewModel: AddCardItemViewModel by fragmentViewModel()
    private val parentViewModel: AddCardFrameViewModel by parentFragmentViewModel()

    private val etWatcher by lazy { getTextWatcher() }
    private val actionListener by lazy { getEditorAction() }
    private val keyListener by lazy { getEditKeyListener() }

    private val addCardItemListener = object : ICommunityAddCardListener {
        override fun onClickAdd(card: PostCardInfo) {
            Analytics.track(EventConstants.ADD_POST_GAME_LIST_ADD_GAME_CLICK) {
                put("game_source", card.resourceType)
                put("gameid", card.gameId)
            }
            parentViewModel.addCard(card)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAddCardItemBinding? {
        return FragmentAddCardItemBinding.inflate(inflater, container, false)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val hint = getString(R.string.post_parties_search_placeholder)
        initEvent()
        binding.eTSearch.hint = hint
        updateEtView(binding.eTSearch.text.toString().isBlank())
        setupRefreshLoading(hint)
    }


    private fun setupRefreshLoading(hint: String) {
        binding.lv.setVerticalBias(0.2F)
        binding.lv.setRetry {
            withState(viewModel) {
                if (it.currentPage == AddCardItemModelState.PAGE_SEARCH) {
                    search()
                } else {
                    viewModel.refresh()
                }
            }
        }
        viewModel.onEach(
            AddCardItemModelState::searchList,
            AddCardItemModelState::relatedBundle,
            AddCardItemModelState::currentPage
        ) { searchList, relatedBundle, currentPage ->
            if (currentPage == AddCardItemModelState.PAGE_RELATED) {
                if (relatedBundle is Fail) {
                    binding.lv.showError()
                } else if (relatedBundle is Loading) {
                    binding.lv.showLoading()
                } else if (relatedBundle is Success && relatedBundle().emptyList) {
                    binding.lv.showEmpty(
                        getString(R.string.footer_load_end),
                        R.drawable.icon_no_recent_activity
                    )
                } else {
                    binding.lv.hide()
                }
            } else {
                if (searchList is Success && searchList().isEmpty()) {
                    binding.lv.showEmpty(
                        getString(R.string.no_param_found, hint),
                        R.drawable.icon_no_results
                    )
                } else if (searchList is Fail) {
                    binding.lv.showError()
                } else if (searchList is Loading) {
                    binding.lv.showLoading()
                } else {
                    binding.lv.hide()
                }
            }
        }
    }

    private fun search() {
        Analytics.track(
            EventConstants.GAME_CARD_SEARCH_CLICK
        )
        viewModel.search()
        InputUtil.hideKeyboard(binding.eTSearch)
    }

    private fun updateEtView(isEmpty: Boolean) {
        binding.tvSearch.isEnabled = !isEmpty
        binding.ivClearSearch.isVisible = !isEmpty
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initEvent() {
        binding.tvSearch.setOnAntiViolenceClickListener {
            search()
        }
        binding.eTSearch.setOnFocusChangeListener { v, hasFocus ->
            binding.tvSearch.isVisible = hasFocus
        }
        binding.eTSearch.setOnEditorActionListener(actionListener)
        binding.eTSearch.setOnKeyListener(keyListener)
        binding.eTSearch.addTextChangedListener(etWatcher)
        binding.ivClearSearch.setOnAntiViolenceClickListener {
            binding.eTSearch.setText("")
            viewModel.clearSearch()
        }
        binding.eTSearch.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                binding.mlContentContainer.transitionToState(R.id.show_search_button)
            } else {
                binding.mlContentContainer.transitionToState(R.id.hide_search_button)
            }
        }
    }

    private fun MetaEpoxyController.buildModels(
        addedCardList: List<PostCardInfo>,
        searchList: Async<List<PostCardInfo>>,
        searchLoadMore: Async<LoadMoreState>,
        currentPage: String,
        relatedBundle: Async<RelatedCardListBundle>,
        relatedLoadMore: Async<LoadMoreState>,
    ) {
        if (currentPage == AddCardItemModelState.PAGE_SEARCH) {
            val list = searchList()
            addSearchCardList(addedCardList, list ?: emptyList(), addCardItemListener)
            if (!list.isNullOrEmpty()) {
                loadMoreFooter(searchLoadMore, showEnd = false) {
                    viewModel.loadMoreSearch()
                }
            }
        } else if (currentPage == AddCardItemModelState.PAGE_RELATED) {
            val bundle = relatedBundle() ?: return
            val list = arrayListOf<RelatedCardListWrap>()
            bundle.pgcDeveloped?.let { list.add(it) }
            bundle.pgcVisited?.let { list.add(it) }
            addRelatedCardList(addedCardList, list, addCardItemListener)
            if (list.isNotEmpty()) {
                loadMoreFooter(relatedLoadMore, showEnd = false) {
                    viewModel.loadMore()
                }
            }
        }
    }

    override fun epoxyController(): EpoxyController = MetaEpoxyController {
        if (view == null || isRemoving) return@MetaEpoxyController
        withState(parentViewModel, viewModel) { state1, state2 ->
            buildModels(
                state1.addedCardList,
                state2.searchList,
                state2.searchLoadMore,
                state2.currentPage,
                state2.relatedBundle,
                state2.relatedLoadMore
            )
        }
    }.also {
        parentViewModel.onEach(AddCardFrameModelState::addedCardList, RedeliverOnStart) { _ ->
            it.requestModelBuild()
        }
        viewModel.onEach(
            AddCardItemModelState::searchList,
            AddCardItemModelState::searchLoadMore,
            AddCardItemModelState::currentPage,
            AddCardItemModelState::relatedBundle,
            AddCardItemModelState::relatedLoadMore,
            RedeliverOnStart
        ) { _, _, _, _, _->
            it.requestModelBuild()
        }
    }

    private fun getTextWatcher() = object : TextWatcherAdapter() {
        override fun afterTextChanged(s: Editable?) {
            viewModel.changeKeyWord(s.toString())
            updateEtView(binding.eTSearch.text.toString().isBlank())
        }
    }

    private fun getEditorAction() = TextView.OnEditorActionListener { _, actionId, _ ->
        if (actionId == EditorInfo.IME_ACTION_SEARCH) { // 按下按钮，这里和xml文件中的EditText中属性imeOptions对应
            search()
        }
        true //返回true，保留软键盘;false，隐藏软键盘
    }

    private fun getEditKeyListener() = View.OnKeyListener { _, keyCode, _ ->
        if (keyCode == KeyEvent.KEYCODE_DEL) {
            viewModel.clearSearch()
        }
        false
    }

    override fun onDestroyView() {
        binding.eTSearch.removeTextChangedListener(etWatcher)
        binding.eTSearch.setOnEditorActionListener(null)
        binding.eTSearch.setOnKeyListener(null)
        super.onDestroyView()
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_ADD_CARD_RELATED
}