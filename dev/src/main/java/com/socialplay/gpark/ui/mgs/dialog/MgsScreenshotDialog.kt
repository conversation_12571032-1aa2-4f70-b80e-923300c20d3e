package com.socialplay.gpark.ui.mgs.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.biz.mgs.data.model.request.MgsShareScreenshot
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.databinding.DialogMgsScreenshotBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.util.*
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

class MgsScreenshotDialog(
    private val metaApp: Context,
    private val activity: Activity,
    private val shareScreenshot: MgsShareScreenshot,
    private val userNumber: String?,
    private val jumpUrl: String?
) : Dialog(activity, android.R.style.Theme_Dialog) {

    private lateinit var binding: DialogMgsScreenshotBinding

    companion object {
        private val qrCodeSize = 50.dp
    }

    init {
        initView()
    }

    private fun initDialogParams() {
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        binding = DialogMgsScreenshotBinding.inflate(LayoutInflater.from(metaApp))
        val height = ScreenUtil.getScreenHeight(activity)
        val width = ScreenUtil.getScreenWidth(activity)
        val w = if (height > width) width else height
        val params = binding.clShareScreenshot.layoutParams
        params.width = w
        binding.clShareScreenshot.layoutParams = params
        val p = binding.ivScreenshot.layoutParams
        p.width = w - ScreenUtil.dp2px(activity, 24f)
        binding.ivScreenshot.layoutParams= p
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            0.50F,
            gravity = Gravity.CENTER,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
        binding.clShareScreenshot.setOnAntiViolenceClickListener { }
        binding.root.setOnAntiViolenceClickListener {
            dismiss()
        }
        binding.btnClose.setOnAntiViolenceClickListener {
//            Analytics.track(EventConstants.EVENT_INVITE_FRIEND_QR_CLOSE_CLICK) {
//                putAll(analyticMap)
//            }
            dismiss()
        }
        binding.btnShare.setOnAntiViolenceClickListener {
            MainScope().launch(Dispatchers.Main) {
                GlobalContext.get().get<AccountInteractor>()
                if (false) {
                    ToastUtil.showShort(context, context.getString(R.string.u13_share_tip))
                    return@launch
                }
                Analytics.track(EventConstants.EVENT_SHARE_AVATAR_WAY) {
                    putAll(mapOf("way" to "2"))
                }
                binding.btnList.gone()
                val file = withContext(Dispatchers.IO) {
                    ViewUtils.view2File(binding.clShareScreenshot)
                }
                binding.btnList.visible()
                SystemShare.shareImageFileBySystem(activity, file)
                dismiss()
            }
        }
        binding.btnSaveGallery.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_SHARE_AVATAR_WAY) {
                putAll(mapOf("way" to "1"))
            }
            MainScope().launch {
                binding.btnList.gone()
                val result = ViewUtils.viewSaveGallery(binding.clShareScreenshot)
                binding.btnList.visible()
                withContext(Dispatchers.Main) {
                    val resultMsg = if (result)  context.getString(R.string.save_successfully_cap) else context.getString(R.string.save_failed)
                    ToastUtil.showShort(context, resultMsg)
                    dismiss()
                }
            }
        }
        val build = QRCode.newQRCodeUtil().content(jumpUrl).width(qrCodeSize).height(qrCodeSize).build()
        binding.ivQrcode.setImageBitmap(build)
        binding.tvUuid.visible(shareScreenshot.isShowUuid)
        val uuidFormat = if (userNumber.isNullOrEmpty()) "" else context.getString(R.string.uuid_prefix, userNumber)
        binding.tvUuid.text = uuidFormat
        binding.tvRoleId.visible(!shareScreenshot.roleId.isNullOrEmpty())
        val roleIdFormat = if (shareScreenshot.roleId.isNullOrEmpty()) "" else context.getString(R.string.preset_id_prefix, shareScreenshot.roleId)
        binding.tvRoleId.text = roleIdFormat
        Glide.with(context)
            .load(shareScreenshot.imagePath).transform(RoundedCorners(16.dp))
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(true)
            .into(binding.ivScreenshot)
    }

}