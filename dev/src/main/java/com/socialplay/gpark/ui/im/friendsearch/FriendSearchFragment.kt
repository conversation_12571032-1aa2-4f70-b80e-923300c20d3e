package com.socialplay.gpark.ui.im.friendsearch

import android.os.Bundle
import android.os.PowerManager
import android.os.PowerManager.WakeLock
import android.text.Editable
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.databinding.FragmentFriendSearchBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/23
 *  desc   :
 */
@FlowPreview
@ExperimentalCoroutinesApi
class FriendSearchFragment : BaseFragment<FragmentFriendSearchBinding>() {
    private val viewModel: FriendSearchViewModel by viewModel()
    private val adapter = FriendSearchAdapter(::glide) {
        UserLabelView.showDescDialog(this, it)
    }
    private val actionListener by lazy { getEditorAction() }

    override fun init() {
        initView()
        initData()
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentFriendSearchBinding? {
        return FragmentFriendSearchBinding.inflate(inflater, container, false)
    }

    private fun initData() {
        setFragmentResultListenerByActivity(
            QRCodeScanFragment.KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE,
            viewLifecycleOwner
        ) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val result = ScanResultData.from(bundle)

            if (result != null && request != null) {
                viewLifecycleOwner.lifecycleScope.launch {
                    GlobalContext.get().get<QrCodeInteractor>()
                        .dispatchQRCodeScanResult(this@FriendSearchFragment, request, result)
                }
            }
        }
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.friendSearch.collectLatest {
                adapter.submitData(it)
            }
        }
        binding.tvMyId.setOnAntiViolenceClickListener {
            copyId()
        }
        binding.ivQrCode.setOnAntiViolenceClickListener {
            viewModel.fetchQrCode()
        }
        viewModel.qrLiveData.observe(viewLifecycleOwner) {
            it.data?.url?.let {
                val qrCodeBitmap = QRCode.newQRCodeUtil()
                    .margin("0")
                    .content(it)
                    .size(dp(160))
                    .build()
                binding.ivQrCode.setImageBitmap(qrCodeBitmap)
            }
        }
        viewModel.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            it?.let {
                binding.tvMyName.text = it.nickname
                binding.tvMyId.setTextWithArgs(R.string.user_id, it.userNumber.orEmpty())
                glide?.run {
                    load(it.portrait).placeholder(R.drawable.placeholder_circle)
                        .circleCrop()
                        .into(binding.ivAvatar)
                }
            }
        }
    }

    override fun loadFirstData() {
        InputUtil.showSoftBoard(binding.eTSearch)
        viewModel.fetchQrCode()
    }

    private fun initView() {
        binding.titleBar.setOnBackClickedListener {
            navigateUp()
        }
        binding.ivClearSearch.setOnAntiViolenceClickListener {
            binding.eTSearch.setText("")
            clearSearResult()
        }
        binding.ivScanBtn.setOnAntiViolenceClickListener {
            MetaRouter.IM.goQRCodeScan(
                requireActivity(),
                this,
                QRCodeScanFragment.KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE,
                ScanEntry.Friend
            )
        }
        binding.tvSearch.setOnAntiViolenceClickListener {
            getSearchResult(binding.eTSearch.text.toString())
        }
        binding.eTSearch.setOnEditorActionListener(actionListener)
        binding.eTSearch.addTextChangedListener(etWatcher)
        updateEtView(binding.eTSearch.text?.toString().isNullOrBlank())
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        initAdapter()

        binding.eTSearch.setOnFocusChangeListener { v, hasFocus ->
            if (isBindingAvailable()) {
                if (hasFocus) {
                    binding.mlContentContainer.transitionToState(R.id.show_search_button)
                } else {
                    binding.mlContentContainer.transitionToState(R.id.hide_search_button)
                }
            }
        }
    }

    private fun updateEtView(isEmpty: Boolean) {
        binding.tvSearch.isEnabled = !isEmpty
        binding.ivClearSearch.visible(!isEmpty)
        binding.ivScanBtn.visible(isEmpty)
    }

    private fun initAdapter() {
        adapter.apply {
            withStatusAndRefresh(viewLifecycleOwner, binding.lv, null) {
                Timber.d("Empty::$it keywords:${viewModel.searchKeyFlow.value}")

                val isEmptyResult = it && viewModel.searchKeyFlow.value.isNotEmpty()
                if (isEmptyResult) {
                    if (!NetUtil.isNetworkAvailable()) {
                        binding.ivNoFriendTipImg.setImageResource(R.drawable.icon_no_network_connection)
                    } else {
                        binding.ivNoFriendTipImg.setImageResource(R.drawable.icon_no_results)
                    }
                }
                binding.clEmptyTip.visible(isEmptyResult)
                binding.clMyInfo.visible(viewModel.searchKeyFlow.value.isEmpty() && !isEmptyResult)
            }

            val loadMoreAdapter = HomeLoadMoreFooterAdapter {
                retry()
            }
            addLoadStateListener { loadStates ->
                loadMoreAdapter.loadState = loadStates.append
            }
            addChildClickViewIds(R.id.tv_add, R.id.v_click)
            setOnItemChildClickListener { view, position ->
                val item = adapter.peek(position) ?: return@setOnItemChildClickListener
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                    return@setOnItemChildClickListener
                }
                when (view.id) {
                    R.id.tv_add -> {
                        Analytics.track(EventConstants.EVENT_IM_ADD_FRIEND_CLICK) { put("from", 3) }
                        MetaRouter.IM.goFriendApply(
                            fragment = this@FriendSearchFragment,
                            uuid = item.uid,
                            userNumber = item.userNumber ?: "",
                            gamePackageName = "",
                            tagIds = item.tagIds,
                            labelInfo = item.labelInfo,
                            userName = item.nickname,
                            avatar = item.portrait
                        )
                    }

                    R.id.v_click -> {
                        if (!item.uid.isNullOrBlank()) {
                            Analytics.track(
                                EventConstants.EVENT_SEARCH_FRIEND_SEARCH_RESULT_ITEM_CLICK
                            )
                            MetaRouter.IM.goAddFriendDialog(
                                this@FriendSearchFragment,
                                item.uid,
                                from = "3"
                            )
                        }
                    }
                }
            }
            binding.recyclerView.adapter = adapter
        }
    }

    private fun clearSearResult() {
        binding.lv.visible(false)
        viewModel.clearSearchResult()
    }

    private fun getEditorAction() = TextView.OnEditorActionListener { _, actionId, _ ->
        if (actionId == EditorInfo.IME_ACTION_SEARCH) { // 按下按钮，这里和xml文件中的EditText中属性imeOptions对应
            getSearchResult(binding.eTSearch.text.toString())
        }
        true //返回true，保留软键盘;false，隐藏软键盘
    }

    private val etWatcher = object : TextWatcherAdapter() {
        override fun afterTextChanged(s: Editable?) {
            updateEtView(s?.toString().isNullOrBlank())
            if (!s.isNullOrEmpty()) {
                getSearchResult(s.toString(), false)
            }
        }
    }

    /**
     * 搜索点击
     */
    private fun getSearchResult(keyword: String?, hideKeyboard: Boolean = true) {
        Analytics.track(EventConstants.EVENT_SEARCH_FRIEND_SEARCH_CLICK)

        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        if (keyword.isNullOrBlank()) {
            toast(R.string.friend_search_key_empty_tip)
            return
        }
        if (keyword == viewModel.searchKeyFlow.value) {
            adapter.refresh()
        }
        if (hideKeyboard) {
            InputUtil.hideKeyboard(binding.eTSearch)
        }
        viewModel.searchNewKey(keyword)
    }

    private fun copyId() {
        viewModel.accountInteractor.accountLiveData.value?.userNumber?.let {
            lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(it, requireContext(), "ID")
                context?.let {
                    toast(R.string.id_copied_ok)
                }
            }
        }
    }

    override fun onDestroyView() {
        binding.eTSearch.removeTextChangedListener(etWatcher)
        binding.recyclerView.adapter = null
        adapter.setOnItemChildClickListener(null)
        binding.eTSearch.setOnEditorActionListener(null)
        binding.eTSearch.setOnKeyListener(null)
        super.onDestroyView()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_FRIEND_SEARCH
}