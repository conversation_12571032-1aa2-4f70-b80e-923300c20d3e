package com.socialplay.gpark.ui.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentAccountSettingBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.extension.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * @author: ning.wang
 * @date: 2021-09-28 11:52 上午
 * @desc:
 */
class AccountSettingFragment : BaseFragment<FragmentAccountSettingBinding>() {

    companion object {
        // 注销时未绑定邮箱先去绑定邮箱
        const val REQUEST_KEY_DITOUT_BIND = "request_key_ditout_bind"

        // 注销时已绑定邮箱先去验证邮箱
        const val REQUEST_KEY_DITOUT_VERIFY = "request_key_ditout_verify"

        // 注销时邮箱验证码
        const val KEY_DITOUT_VERIFY_CODE = "request_key_ditout_verify_code"
    }

    private val viewModel by viewModel<AccountSettingViewModel>()
    private val args by navArgs<AccountSettingFragmentArgs>()

    private val accountInteractor: AccountInteractor by inject()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAccountSettingBinding? {
        return FragmentAccountSettingBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initData() {
        viewModel.accountLivedata.observe(viewLifecycleOwner) {
            it?.let { fillData(it) }
        }
        viewModel.logoutStateCallback.observe(viewLifecycleOwner) {
            binding.vLoading.gone()
            if (it.succeeded) {
                findNavController().popBackStack()
                MetaRouter.Startup.guideLogin(this, LoginSource.Logout)
            }
        }
        viewModel.isU13LiveData.observe(viewLifecycleOwner) {
            binding.clParentEmail.isVisible = it
        }
        viewModel.checkU13()
    }

    private fun initView() {
        binding.apply {
            tblTitleBar.setOnBackClickedListener {
                findNavController().popBackStack()
            }

            clLogOut.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_LOGOUT_CLICK)
                showLogoutConfirmDialog()
            }

            clConnectedAccounts.setOnAntiViolenceClickListener {
                MetaRouter.Account.connectedAccounts(this@AccountSettingFragment, LoginPageSource.AccountSetting, args.gameId)
            }
            clAccount.setOnAntiViolenceClickListener {
                if (!isHasAccount()) {
                    MetaRouter.Account.bindAccountAndPassword(
                        this@AccountSettingFragment,
                        LoginPageSource.AccountSetting,
                        null
                    )
                }
            }
            clChangePassword.setOnAntiViolenceClickListener {
                if (isHasAccount()) {
                    MetaRouter.Account.passwordChange(this@AccountSettingFragment, LoginPageSource.AccountSetting, args.gameId)
                }
            }

            clEmail.setOnAntiViolenceClickListener {
                if (!isHasEmail()) {
                    MetaRouter.Account.emailBind(this@AccountSettingFragment, LoginPageSource.AccountSetting, args.gameId)
                } else {
                    MetaRouter.Account.emailBindChangeOld(this@AccountSettingFragment, LoginPageSource.AccountSetting, args.gameId)
                }
            }

            clParentEmail.setOnAntiViolenceClickListener {
                if (!hasParentEmail()) {
                    MetaRouter.Account.parentEmailBind(this@AccountSettingFragment, LoginPageSource.AccountSetting, args.gameId)
                } else {
                    MetaRouter.Account.emailBindChangeNew(this@AccountSettingFragment, "", true, LoginPageSource.AccountSetting, args.gameId)
                }
            }

            clAccountCancellation.setOnAntiViolenceClickListener {
                if (isHasEmail()) {
                    MetaRouter.Account.accountCancellationVerify(this@AccountSettingFragment, REQUEST_KEY_DITOUT_VERIFY, LoginPageSource.AccountSetting, args.gameId) {
                        it?.let { showAccountCancellationConfirmDialog(it) }
                    }
                } else {
                    showAccountCancellationBindEmailDialog()
                }
            }

            controlDiffFlavorShowItems()

        }
    }

    private fun controlDiffFlavorShowItems() {
        visibleList(binding.clEmail, binding.clConnectedAccounts, binding.clChangePassword, visible = !EnvConfig.isParty())
    }

    override fun loadFirstData() {
        fillData(accountInteractor.accountLiveData.value)
    }

    private fun fillData(userInfo: MetaUserInfo?) {
        if (userInfo == null) return
        userInfo.bindEmail?.let { binding.tvEmail.text = userInfo.getEncryptedEmail(false) }
        binding.tvParentEmail.text = if (!userInfo.getParentEmail().isNullOrEmpty()) userInfo.getEncryptedEmail(true) else getString(R.string.add_parent_email)
        binding.tvParentEmail.setTextColor(resources.getColor(if (userInfo.getParentEmail().isNullOrEmpty()) R.color.color_FF6B7A else R.color.textColorSecondary))
        if (isHasAccount()) {
            binding.tvAccount.text = userInfo.getEncryptedAccount()
        }
        binding.ivAccountMore.gone(!isHasAccount())
        binding.clChangePassword.visible(isHasAccount())

        controlDiffFlavorShowItems()
    }

    private fun showLogoutConfirmDialog() {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.logout_confirm_title))
            .cancelBtnTxt(getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.text_confirm), lightBackground = true)
            .cancelCallback {
                Analytics.track(EventConstants.EVENT_LOGOUT_CANCEL_CLICK)
            }
            .confirmCallback {
                Analytics.track(EventConstants.EVENT_LOGOUT_CONFIRM_CLICK)
                binding.vLoading.visible()
                viewModel.logout()
            }
            .navigate()
    }

    private fun showAccountCancellationBindEmailDialog() {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.account_cancellation_bind_email))
            .cancelBtnTxt(getString(R.string.dialog_cancel), lightBackground = false, isVisible = false)
            .confirmBtnTxt(getString(R.string.text_confirm), lightBackground = true)
            .cancelCallback {

            }
            .confirmCallback {
//                MetaRouter.Account.cancellationBindEmail(this, REQUEST_KEY_DITOUT_BIND, LoginPageSource.AccountSetting, args.gameId) {
//                    if (isHasEmail()) {
//                        MetaRouter.Account.accountCancellationVerify(this@AccountSettingFragment, REQUEST_KEY_DITOUT_VERIFY, LoginPageSource.AccountSetting, args.gameId) {
//                            it?.let { showAccountCancellationConfirmDialog(it) }
//                        }
//                    }
//                }
            }
            .navigate()
    }

    private fun showAccountCancellationConfirmDialog(code: String) {
        ConfirmDialog.Builder(this)
            .image(R.drawable.dialog_icon_cry)
            .content(getString(R.string.account_cancellation_confirm))
            .cancelBtnTxt(getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.text_confirm), lightBackground = true)
            .isRed(true)
            .cancelCallback {

            }
            .confirmCallback {
                MetaRouter.Account.accountCancellation(this, code, LoginPageSource.AccountSetting, args.gameId)
            }
            .navigate(requireActivity(), "showAccountCancellationConfirmDialog")
    }

    private fun isHasEmail(): Boolean {
        if (EnvConfig.isParty()) {
            return !accountInteractor.accountLiveData.value?.phoneNumber.isNullOrEmpty()
        }
        return !accountInteractor.accountLiveData.value?.bindEmail.isNullOrEmpty()
                && accountInteractor.accountLiveData.value?.bindEmail!!.isNotEmpty()
                && accountInteractor.accountLiveData.value?.bindEmail!! != "null"
    }

    private fun hasParentEmail(): Boolean {
        return !accountInteractor.accountLiveData.value?.thirdBindInfo?.parentEmail?.bindId.isNullOrEmpty()
    }

    private fun isHasAccount(): Boolean {
        return accountInteractor.accountLiveData.value?.hasAccount() == true
    }

    private fun showLoading(msg: String) {
        binding.vLoading.showLoading(msg = msg)
        binding.vLoading.visible()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_LOGIN_SHOW_PAGE) {
            put(EventConstants.KEY_LOGIN_PAGE_NAME, getFragmentName())
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginPageSource.Unknown.source)
            args.gameId?.let {
                put(EventConstants.KEY_LOGIN_GAME_CODE, it)
            }
        }
    }

    override fun getFragmentName(): String {
        return PageNameConstants.FRAGMENT_NAME_ACCOUNT
    }
}