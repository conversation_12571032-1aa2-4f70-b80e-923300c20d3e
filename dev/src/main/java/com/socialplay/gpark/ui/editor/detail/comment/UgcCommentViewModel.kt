package com.socialplay.gpark.ui.editor.detail.comment

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.airbnb.mvrx.withState
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.editor.detail.commentlist.BaseCommentListViewModel
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.delay
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/11
 *     desc   :
 * </pre>
 */
data class UgcCommentState(
    val ugcId: String,
    val detail: Async<UgcDetailInfo> = Uninitialized,
    val addReplyResult: Async<PostComment> = Uninitialized,
    val delay: Async<Int> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
) : MavericksState {
    constructor(args: UgcCommentArgs) : this(args.ugcId)
}

class UgcCommentViewModel(
    initialState: UgcCommentState,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor
) : BaseViewModel<UgcCommentState>(initialState) {

    val detail: UgcDetailInfo?
        get() = withState(this) { it.detail.invoke() }

    fun initData() = withState { s ->
        if (s.detail.shouldLoad) {
            getUgcDetailInfo()
        }
    }

    fun getUgcDetailInfo() = withState { s ->
        if (!s.detail.shouldLoad) return@withState
        repo.getUgcDetailPage(s.ugcId).execute { result ->
            copy(detail = result)
        }
    }

    fun addComment(commentContent: String?) = withState { s ->
        if (commentContent.isNullOrBlank() || s.addReplyResult is Loading) return@withState
        val ts = System.currentTimeMillis()
        val requestBody = PostCommentRequestBody(
            commentContent,
            BaseCommentListViewModel.MODULE_UGC,
            s.ugcId
        )
        repo.addPostComment(requestBody).map {
            Analytics.track(
                EventConstants.UGC_GAME_REVIEW_PUBLISH_SUCCESS,
                "gameid" to s.ugcId,
                "reviewtype" to 0
            )
            it.toastMsg?.takeIf { it.isNotEmpty() }?.let { toastMsg ->
                setState {
                    copy(toast = toast.toMsg(toastMsg))
                }
            }
            if (it.data.isNullOrEmpty()) {
                throw ApiDataException(String::class)
            }
            requestBody.toPostComment(
                it.data,
                accountInteractor.accountLiveData.value,
                ts
            )
        }.execute {
            copy(addReplyResult = it)
        }
    }

    fun delayTask(duration: Long, taskId: Int) {
        suspend {
            delay(duration)
        }.map {
            taskId
        }.execute {
            copy(delay = it)
        }
    }

    companion object : KoinViewModelFactory<UgcCommentViewModel, UgcCommentState>() {
        const val TASK_KEYBOARD = 1
        const val TASK_JUMP = 2

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcCommentState
        ): UgcCommentViewModel {
            return UgcCommentViewModel(state, get(), get())
        }
    }
}