package com.socialplay.gpark.ui.search

import android.os.Bundle
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.paging.PagingData
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.SearchGameItem
import com.socialplay.gpark.databinding.FragmentSearchBinding
import com.socialplay.gpark.databinding.HeaderSearchHistoryBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.search.SearchHelper
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.search.tab.SearchResultMutableTabFragment
import com.socialplay.gpark.ui.view.FlowLayoutManager
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.onTouch
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collectLatest
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/13
 * desc   :
 * </pre>
 */


@FlowPreview
@ExperimentalCoroutinesApi
class SearchFragment : BaseEditorFragment<FragmentSearchBinding>() {

    private val viewModel by viewModel<SearchViewModel>()
    private val adapter = SearchAdapter(::glide)
    private var textWatcher: TextWatcher? = null
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }
    private val metaKV:MetaKV by inject()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSearchBinding? {
        return FragmentSearchBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    private fun initData() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.searchResultFlow.collectLatest {
                adapter.submitData(it)
            }
        }
    }

    private fun initView() {
        if (PandoraToggle.isSearchResultMutableTab){
            childFragmentManager.beginTransaction().run {
                val cachedFragment = childFragmentManager.findFragmentByTag("SearchResultMutableTabFragment")
                if (cachedFragment != null) {
                    show(cachedFragment)
                    setMaxLifecycle(cachedFragment, Lifecycle.State.RESUMED)
                } else {
                    replace(
                        R.id.tabFragment,
                        SearchResultMutableTabFragment::class.java,
                        null,
                        "SearchResultMutableTabFragment"
                    )
                }
                commitAllowingStateLoss()
            }

        }
        //监听TS游戏拉起状态
        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchPrepare {
            }
            onLaunchGame {
                gameStartScenes.show()
            }
            onPauseDownload {
            }
            onLaunchGameEnd { _, e ->
                if (e != null) gameStartScenes.hide()
            }
        }
        initHistory()

        adapter.setOnItemClickListener { view, position ->
            val peek = safeGetItem(position)?: return@setOnItemClickListener
            if (peek.itemType == 0) {
                viewModel.postSearchReport(peek.contentId?:"", if (peek.isUgc) 2 else SearchHelper.PGC_TYPE)
                goGameDetail(peek)
                Analytics.track(
                    EventConstants.EVENT_ITEM_CLICK,
                    "gameid" to peek.id,
                    "packagename" to peek.packageName,
                    "game_type" to (if (peek.isUgc) "ugc" else "pgc"),
                    "show_categoryid" to CategoryId.SEARCH_RESULT,
                    "show_param1" to position
                )
            } else {
                if (PandoraToggle.isSearchResultMutableTab){
                    binding.tabFragment.visible()
                    binding.rv.gone()
                }
                InputUtil.hideKeyboard(binding.etSearch)
                binding.etSearch.setText(peek.displayName.toString())
                binding.etSearch.setSelection(peek.displayName.toString().length)
                search(peek.displayName.toString(), peek.id)
                Analytics.track(EventConstants.EVENT_SEARCH_TRIGGER, "keyword" to peek.displayName.toString(), "click" to "true")
            }
        }
        adapter.addChildClickViewIds(R.id.tvEnter)
        adapter.setOnItemChildClickListener { _, position ->
            val peek = safeGetItem(position)?:return@setOnItemChildClickListener
            val resIdBean = ResIdBean().setCategoryID(CategoryId.SEARCH_RESULT).setClickGameTime(System.currentTimeMillis())
            if (peek.itemType == 0) {
                viewModel.postSearchReport(peek.contentId?:"", if (peek.isUgc) 2 else SearchHelper.PGC_TYPE)
                if(peek.isUgc) {
                    val params = EditorUGCLaunchParams(peek.id,peek.packageName,peek.displayName.toString(),peek.iconUrl ?: "","")
                    editorGameLaunchHelper?.startUgcGame(this, params, resIdBean.setTsType(ResIdBean.TS_TYPE_UCG))
                }else{
                    if (tsLaunch.isLaunching(peek.id)) {
                        toast(R.string.launching)
                    } else {
                        metaKV.analytic.saveClickLaunchTime(peek.packageName, System.currentTimeMillis())
                        val params = TSLaunchParams(GameDetailInfo(peek.id, pkg = peek.packageName), resIdBean)
                        tsLaunch.launch(requireContext(), params)
                    }
                }
            }
        }
        adapter.setOnItemShowListener { view, position ->
            val peek = safeGetItem(position)?:return@setOnItemShowListener
            if (peek.itemType == 0) {
                Analytics.track(
                    EventConstants.EVENT_ITEM_SHOW,
                    "gameid" to peek.id,
                        "packagename" to peek.packageName,
                    "game_type" to (if (peek.isUgc) "ugc" else "pgc"),
                    "show_categoryid" to CategoryId.SEARCH_RESULT,
                    "show_param1" to position
                )
            }
        }

        var skipLenovo = viewModel.hasSearchResult
        textWatcher = binding.etSearch.addTextChangedListener(afterTextChanged = {
            val result = it?.toString() ?: ""
            Timber.i("mingbin_watcher result = $result keyword = ${viewModel.keyWordLiveData.value?.first}")
            binding.imgClear.isVisible = result.isNotEmpty()
            if (result.trim() == viewModel.keyWordLiveData.value?.first) {
                if (PandoraToggle.isSearchResultMutableTab){
                    binding.tabFragment.visible()
                    binding.rv.gone()
                    binding.rvHistory.gone()
                }
                return@addTextChangedListener
            }
            if (result.isEmpty()) {
                adapter.submitData(viewLifecycleOwner.lifecycle, PagingData.empty())
            }
            if (skipLenovo) {
                skipLenovo = false
            } else {
                viewModel.searchKey(result, true)
            }
            binding.rvHistory.visible(!viewModel.hasSearchResult && result.isEmpty() && !viewModel.historyLiveData.value.isNullOrEmpty())
            binding.loading.hide()
            if (PandoraToggle.isSearchResultMutableTab){
                binding.tabFragment.gone()
                binding.rv.visible()
            }
        })
        binding.etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val result = binding.etSearch.text?.toString() ?: ""
                if (result.trim().isNotEmpty()){
                    Analytics.track(EventConstants.EVENT_SEARCH_TRIGGER, "keyword" to result, "click" to "false")
                }
                search(result)
                InputUtil.hideKeyboard(binding.etSearch)
                true
            } else {
                false
            }
        }
        binding.rv.adapter = adapter
        if (!PandoraToggle.isSearchResultMutableTab){
            adapter.withStatusAndRefresh(
                viewLifecycleOwner,
                binding.loading,
                null,
                getString(R.string.search_empty),
                R.drawable.icon_no_results
            )
        }

        binding.tvCancel.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.SEARCH_BACK_CLICK)
            navigateUp()
        }
        binding.imgClear.setOnAntiViolenceClickListener {
            binding.etSearch.text?.clear()
        }

        binding.vTouchArea.onTouch(viewLifecycleOwner) { _, e ->
            if (e.actionMasked == MotionEvent.ACTION_DOWN) {
                InputUtil.hideKeyboard(binding.etSearch)
            }
            false
        }
    }

    private fun initHistory() {
        binding.rvHistory.layoutManager = FlowLayoutManager(12.dp)
        val searchHistoryAdapter = SearchHistoryAdapter()
        val headerBinding = HeaderSearchHistoryBinding.inflate(LayoutInflater.from(requireContext()), null, false)
        searchHistoryAdapter.addHeaderView(headerBinding.root)
        headerBinding.ivDelete.setOnAntiViolenceClickListener {
            viewModel.clearSearchHistory()
        }
        binding.rvHistory.adapter = searchHistoryAdapter
        searchHistoryAdapter.setOnAntiViolenceItemClickListener { _, _, position ->
            val result = searchHistoryAdapter.getItem(position)
            InputUtil.hideKeyboard(binding.etSearch)
            binding.etSearch.setText(result)
            binding.etSearch.setSelection(result.length)
            search(result)
        }
        viewModel.historyLiveData.observe(viewLifecycleOwner) {
            searchHistoryAdapter.setList(it)
            if (it.isNullOrEmpty() || viewModel.hasSearchResult) {
                binding.rvHistory.gone()
            }
        }
        if (PandoraToggle.isSearchResultMutableTab) {
            if (!viewModel.keyWordLiveData.value?.first.isNullOrEmpty()) {
                binding.tabFragment.visible()
                binding.rv.gone()
                binding.rvHistory.gone()
            }
        }
    }

    private fun search(result: String, id: String? = null) {
        viewModel.searchKey(result, false, id)
        if (PandoraToggle.isSearchResultMutableTab) {
            binding.tabFragment.visible()
            binding.rvHistory.gone()
            binding.rv.gone()
        }
    }

    private fun safeGetItem(position: Int): SearchGameItem? = kotlin.runCatching { adapter.peek(position) }.getOrNull()

    private fun goGameDetail(peek: SearchGameItem) {
        val resIdBean = ResIdBean().setCategoryID(CategoryId.SEARCH_RESULT)
        if (peek.isUgc) {
            MetaRouter.MobileEditor.ugcDetail(
                this,
                peek.id,
                null,
                resIdBean
            )
        } else {
            MetaRouter.GameDetail.navigate(this, peek.id, resIdBean, peek.packageName)
        }
    }

    override fun loadFirstData() {
        Analytics.track(EventConstants.EVENT_SEARCH_SHOW)
        viewModel.getSearchHistory()
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            InputUtil.showSoftBoard(binding.etSearch)
        }
    }

    override fun getFragmentName(): String {
        return PageNameConstants.FRAGMENT_NAME_SEARCH
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.etSearch)
        super.onPause()
    }

    override fun onDestroyView() {
        textWatcher?.let { binding.etSearch.removeTextChangedListener(it) }
        super.onDestroyView()
    }
}