package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.biz.mgs.data.model.MgsPlayerDressInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemPlayerDressBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp

class PlayerDressAdapter : BaseAdapter<MgsPlayerDressInfo, ItemPlayerDressBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemPlayerDressBinding {
        return ItemPlayerDressBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemPlayerDressBinding>,
        item: MgsPlayerDressInfo,
        position: Int
    ) {
        holder.binding.apply {
            if (!item.dressImage.isNullOrEmpty()) {
                Glide.with(ivDress)
                    .load(item.dressImage)
                    .placeholder(R.drawable.icon_placeholder_img_default)
                    .transform(CenterCrop(), RoundedCorners(13.dp))
                    .into(ivDress)
                ivDress.isVisible = true
                ivMark.isVisible = item.memberExclusive

            } else {
                ivDress.isVisible = false
                ivMark.isVisible = false
            }



        }
    }
}