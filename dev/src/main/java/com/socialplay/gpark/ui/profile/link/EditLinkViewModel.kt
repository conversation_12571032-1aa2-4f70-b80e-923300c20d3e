package com.socialplay.gpark.ui.profile.link

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.event.UserDataUpdateEvent
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.GsonUtil
import org.koin.android.ext.android.get

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/16
 *     desc   :
 *
 */

data class EditLinkState(
    val list: List<ProfileLinkInfo> = emptyList(),
    val args: EditLinkFragmentArgs,
    val isEditMode: Boolean = false
) : MavericksState {
    constructor(args: EditLinkFragmentArgs) : this(args = args, list = emptyList())
}

class EditLinkViewModel(val metaRepository: IMetaRepository, val initialState: EditLinkState) :
    BaseViewModel<EditLinkState>(initialState) {
    companion object :
        KoinViewModelFactory<EditLinkViewModel, EditLinkState>() {


        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: EditLinkState
        ): EditLinkViewModel {
            return EditLinkViewModel(get(), state)
        }
    }

    init {
        getLinkList()
    }

    private fun getLinkList() {
        withState {oldState->
            val list =  GsonUtil.gsonSafeParseCollection<List<ProfileLinkInfo>>(oldState.args.data)?: emptyList()
            setState { copy(list = list) }
        }
    }

    fun changeEditMode(isEditMode: Boolean) {
        if (oldState.isEditMode != isEditMode){
            setState { copy(isEditMode = isEditMode) }
        }
    }

    fun addLink(it: ProfileLinkInfo) = withState { oldState ->
        val list = oldState.list
        val newList = ArrayList<ProfileLinkInfo>()
        newList.addAll(list)
        setState { copy(list = (newList.toList() + it)) }
        UserDataUpdateEvent.notifyAddProfileLinkInfo(it)
    }

    fun deleteItem(it: ProfileLinkInfo) = withState { oldState ->
        val list = ArrayList<ProfileLinkInfo>()
        list.addAll(oldState.list)
        list.remove(it)
        setState { copy(list = list.toList()) }
        it.id?.let {
            metaRepository.deleteLink(it).execute {
                this
            }
        }
        if (list.isNullOrEmpty()){
            //列表清空以后。退出编辑模式
            setState { copy(isEditMode = false) }
        }
        UserDataUpdateEvent.notifyDeleteProfileLinkInfo(it)
    }
}