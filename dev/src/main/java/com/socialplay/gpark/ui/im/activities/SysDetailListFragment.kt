package com.socialplay.gpark.ui.im.activities

import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.SysHeaderInfo.TabItemEntity.Companion.LINK_TYPE_NATIVE
import com.socialplay.gpark.data.model.SysHeaderInfo.TabItemEntity.Companion.LINK_TYPE_WEB_INNER
import com.socialplay.gpark.data.model.SysHeaderInfo.TabItemEntity.Companion.LINK_TYPE_WEB_OUTER
import com.socialplay.gpark.databinding.FragmentSysActivitiesBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setBackgroundColorByRes
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.collectLatest
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

class SysDetailListFragment:BaseFragment<FragmentSysActivitiesBinding>() {

    private val viewModel:SysActivitiesViewModel by viewModel()

    private val args: SysDetailListFragmentArgs by navArgs()

    private val sysActivitiesAdapter = SysActivitiesAdapter(::glide)
    private val isGameStyle get() = PandoraToggle.isNewMessage && args.type == 1L
    private val sysMessageAdapter = SysMessageAdapter(::glide, isGameStyle)

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSysActivitiesBinding? {
        return FragmentSysActivitiesBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initView() {
        binding.titleBar.setTitle(args.title)
        binding.titleBar.setOnBackClickedListener {
            showAppNotificationDialog()

        }
        initAdapter()
        initTab()
    }
    private fun showAppNotificationDialog() {
        if (viewModel.showIMNotificationLiveData.value == true) {
            viewModel.updateNotificationTime()
            showNotificationDialog(false)
            Timber.d("showAppNotificationDialog")
            return
        }
        if (viewModel.showIMNotificationNumLiveData.value == true) {
            showNotificationDialog(true)
            Timber.d("showAppNotificationDialog_num")
            return
        }
        navigateUp()
    }
    private fun showNotificationDialog(isNumber: Boolean) {
        Analytics.track(EventConstants.EVENT_INTERACT_PUS_POST_SHOW)
        val map = hashMapOf<String, String>()
        if (isNumber) {
            map["num"] = PandoraToggle.postInteractPushNotificationFrequency.toString()
        }
        NotificationPermissionManager.showPermissionDialog(
            this,
            title = getString(R.string.notification_im_title),
            content = getString(R.string.notification_im_content),
            cancelText = getString(R.string.notification_app_cancel),
            confirmText = getString(R.string.notification_app_sure),
            dismissCallBack = {
                navigateUp()
            }, confirmCallBack = {resut->
                if (resut) {
                    map["result"] = "0"
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        map
                    )
                } else {
                    map["result"] = "1"
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        map
                    )
                }
            }
        )
    }

    private fun initAdapter() {
        if (args.groupContentType == 1) {
            binding.refresh.setBackgroundColorByRes(R.color.white)
            sysActivitiesAdapter.apply {
                withStatusAndRefresh(
                    viewLifecycleOwner,
                    binding.lv,
                    binding.refresh,
                    getString(R.string.no_message),
                    R.drawable.icon_no_contacts
                ) {
                    refresh()
                }
                addLoadStateListener { loadStates ->
                    HomeLoadMoreFooterAdapter {
                        retry()
                    }.loadState = loadStates.append
                }
                binding.rv.adapter = this
                setOnItemClickListener { _, position ->
                    val item = peek(position) ?: return@setOnItemClickListener
                    onItemClick(item)
                }
                setOnItemShowListener { view, position ->
                    val item = peek(position) ?: return@setOnItemShowListener
                    Analytics.track(
                        EventConstants.EVENT_NOTICE_SEND_MESSAGE_READ,
                        "group_id" to args.groupId,
                        "assignment_id" to item.source,
                        "message_id" to item.msgId,
                        "mould_id" to item.tempId,
                        "taskid" to item.sourceTaskId
                    )
                }
            }
        } else {
            binding.refresh.setBackgroundColorByRes(R.color.color_F6F6F6)
            sysMessageAdapter.apply {
                withStatusAndRefresh(
                    viewLifecycleOwner,
                    binding.lv,
                    binding.refresh,
                    getString(R.string.no_message),
                    R.drawable.icon_no_contacts
                ) {
                    refresh()
                }
                addLoadStateListener { loadStates ->
                    HomeLoadMoreFooterAdapter {
                        retry()
                    }.loadState = loadStates.append
                }
                binding.rv.adapter = this

                if (isGameStyle){
                    addChildClickViewIds(R.id.llContent)
                    setOnItemChildClickListener { view, position ->
                        val item = peek(position) ?: return@setOnItemChildClickListener
                        onItemClick(item)
                    }
                }else{
                    setOnItemClickListener { _, position ->
                        val item = peek(position) ?: return@setOnItemClickListener
                        onItemClick(item)
                    }
                }
                setOnItemShowListener { view, position ->
                    val item = kotlin.runCatching { peek(position) }.getOrNull() ?: return@setOnItemShowListener
                    Analytics.track(
                        EventConstants.EVENT_NOTICE_SEND_MESSAGE_READ,
                        "group_id" to args.groupId,
                        "assignment_id" to item.source,
                        "message_id" to item.msgId,
                        "mould_id" to item.tempId,
                        "taskid" to item.sourceTaskId
                    )
                }
            }
        }
    }

    private fun initTab() {
        if (!isGameStyle) return
        val tabList = GsonUtil.gsonSafeParseCollection<ArrayList<SysHeaderInfo.TabItemEntity>>(args.TabList)
        if (tabList.isNullOrEmpty()) return
        binding.rvTab.visible()
        binding.rvTab.layoutManager = GridLayoutManager(context, tabList.size, GridLayoutManager.VERTICAL,false)
        binding.rvTab.adapter = GameBottomTabAdapter(tabList).apply {
            setOnItemClickListener { view, position ->
                val item = tabList[position]
                val jumpResult = if (item.linkType.isNullOrEmpty() || item.linkValue.isNullOrEmpty()) {
                    "no_link"
                } else if (!item.isSupportLinkType()) {
                    "no_support_type"
                } else if (jumpByLink(item.linkType, item.linkValue)) {
                    "ok"
                } else {
                    "no_support_value"
                }
                Analytics.track(
                    EventConstants.EVENT_NOTICE_GAME_CLICK,
                    "group_id" to args.groupId,
                    "jumpid" to item.id.toString(),
                    "jumpurl" to item.linkValue.toString(),
                    "source" to args.source
                )
            }
        }
    }

    private fun jumpByLink(linkType: String, linkValue: String): Boolean {
        return when (linkType) {
            LINK_TYPE_NATIVE -> {
                val uri = runCatching {
                    Uri.parse(linkValue)
                }.getOrNull() ?: return false
                MetaDeepLink.handle(
                    requireActivity(),
                    this@SysDetailListFragment,
                    null,
                    uri,
                    LinkData.SOURCE_SYS_MESSAGE
                ).succeeded
            }
            LINK_TYPE_WEB_INNER -> {
                MetaRouter.Web.navigate(this, url = linkValue,  isWebOutside = false)
                true
            }
            LINK_TYPE_WEB_OUTER -> {
                InstallUtil.launchOutWeb(requireActivity(), linkValue)
            }
            else ->{
                false
            }
        }
    }

    private fun onItemClick(item: SysActivitiesInfo) {
        var isHandle = false
        val uri = Uri.parse(item.linkValue?:"")
        if (item.isNative() && !item.linkType.isNullOrEmpty()) {
            isHandle = MetaDeepLink.handle(
                requireActivity(),
                this@SysDetailListFragment,
                null,
                uri,
                LinkData.SOURCE_SYS_MESSAGE
            ).succeeded
        } else if (item.isWebInner() || item.isWebOut()){
            MetaRouter.Web.navigate(this, null, uri.toString(), isWebOutside = item.isWebOut())
            isHandle = true
        }

        val jumpResult = if (item.linkType.isNullOrEmpty() || item.linkValue.isNullOrEmpty()) {
            "no_link"
        } else if (!item.isSupportLinkType()) {
            "no_support_type"
        } else if (isHandle) {
            "ok"
        } else {
            "no_support_value"
        }
        Analytics.track(
            EventConstants.EVENT_NOTICE_SEND_MESSAGE_CLICK_JUMP,
            "group_id" to args.groupId,
            "assignment_id" to item.source,
            "message_id" to item.msgId,
            "mould_id" to item.tempId,
            "jump_result" to jumpResult,
            "taskid" to item.sourceTaskId
        )
    }

    private fun initData() {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            viewModel.getList(args.groupId).collectLatest {
                if (args.groupContentType == 1) {
                    sysActivitiesAdapter.submitData(it)
                } else {
                    sysMessageAdapter.submitData(it)
                }
            }
        }
        if (args.groupContentType == 1) {
            viewModel.checkNotificationPermission(args.unReadCount)
        }
    }

    override fun loadFirstData() {
    }

    override fun getFragmentName(): String {
        return "SysActivitiesFragment"
    }

}