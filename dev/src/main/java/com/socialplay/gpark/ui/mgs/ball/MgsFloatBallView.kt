package com.socialplay.gpark.ui.mgs.ball

import android.Manifest
import android.app.Application
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.constraintlayout.motion.widget.TransitionAdapter
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.databinding.MgsOrdinaryFloatBallLayoutBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatBallListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsSceneListener
import com.socialplay.gpark.ui.mgs.user.MgsUserPresenter
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.voice.WaveVoiceView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.StringUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2022/1/20
 * Desc:
 */
class MgsFloatBallView(val app: Application, val metaApp: Context, val openGameRecord: Boolean, val listener: OnMgsFloatBallListener) : LinearLayout(metaApp),
    OnMgsSceneListener {

    private var ordinaryHandler: Handler? = null

    lateinit var binding: MgsOrdinaryFloatBallLayoutBinding

    private val mgsUserPresenter by lazy { MgsUserPresenter(this) }
    private val mgsInteractor: MgsInteractor = GlobalContext.get().get()
    init {
        initView()
    }

    private fun initView() {
        binding = MgsOrdinaryFloatBallLayoutBinding.inflate(LayoutInflater.from(metaApp), this, true)
        ordinaryHandler = Handler(Looper.getMainLooper())
        binding.ivMgsExit.setImageResource(if (openGameRecord ||  mgsInteractor.hasSettingPanel()) R.drawable.icon_mgs_more_menu else R.drawable.icon_mgs_exit_ball)

        binding.vUser.rlLike.setOnAntiViolenceClickListener {
            mgsUserPresenter.clickLike()
        }
        binding.vUser.clUserInfo.setOnAntiViolenceClickListener {
            listener.showUserCard(mgsUserPresenter.getCurrentShowViewOpenId())
            Analytics.track(EventConstants.EVENT_CLICK_MGS_AVATAR) {
                putAll(listener.getGameAnalytics())
            }
        }
        initVoiceView()
        initMotionBallListener()
        initSwitch()
    }
    private fun initSwitch(){
        changeVoiceStatus(listener.isAllMute())
        binding.mscSwitch.setOnClickListener {
            listener.muteAllRemoteAudioStreams((!listener.isAllMute()))
        }
    }

    fun changeVoiceStatus(mute:Boolean) {
        binding.mscSwitch.setImageResource(if (mute) R.drawable.icon_all_close else R.drawable.icon_all_open)
    }
    fun updateSelfAudioState(open: Boolean){
        if (PandoraToggle.enableBulletChat) {
            return
        }
        binding.voiceOpenView.invisible(!open)
        binding.voiceShield.visible(!open)
    }

    private fun initVoiceView() {
        binding.voiceOpenView.setVolumeListener(object : WaveVoiceView.IClickVolumeListener {
            override fun onMute() {
                handleClickAudio(false)
            }

            override fun onUnmute() {
                handleClickAudio(true)
            }
        })
        binding.voiceShield.setOnAntiViolenceClickListener {
            ToastUtil.gameShowShort(metaApp.getString(R.string.voice_shield))
        }
    }

    fun handleClickAudio(isOpen: Boolean) {
        val event = if (isOpen) EventConstants.MGS_VOICE_OPEN_CLICK else EventConstants.MGS_VOICE_CLOSE_CLICK
        Analytics.track(event) {
            putAll(listener.getGameAnalytics())
            put("type", "mgs")
        }
        if (!PermissionRequest.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)) {
            listener.checkMicPermission()
            return
        }
        listener.changeVoiceMuteState(isOpen)
    }

    /**
     * 改变语音按钮状态
     */
    fun changeVoiceState(isOpen: Boolean, lastVolume: Int = 0, nowVolume: Int = 0) {
        if (mgsUserPresenter.canShowBallAudio()) {
            binding.voiceOpenView.updateVoiceState(isOpen, lastVolume, nowVolume)
        }
    }

    private fun initMotionBallListener() {
        binding.ordinaryFloatBall.addTransitionListener(object : TransitionAdapter() {
            override fun onTransitionCompleted(motionLayout: MotionLayout, currentId: Int) {
                changeLayoutSize(currentId == R.id.floating_ball_start)
                if (currentId == R.id.floating_ball_end) {
                    ordinaryHandler?.removeCallbacksAndMessages(null)
                    ordinaryHandler?.postDelayed({
                        Analytics.track(EventConstants.EVENT_CLICK_MGS_FLOAT) {
                            putAll(listener.getGameAnalytics())
                            put("isclose", "close")
                        }
                        binding.ordinaryFloatBall.transitionToState(R.id.floating_ball_start)
                    }, 3000)
                }
            }
        })
    }

    fun setBallOnTouchListener(onTouchListener: OnTouchListener) {
        binding.ordinaryFloatBall.setOnTouchListener(onTouchListener)
        binding.vMessageBall.setOnTouchListener(onTouchListener)
        binding.danmuSwitch.setOnTouchListener(onTouchListener)
        binding.vMgsExitBall.setOnTouchListener(onTouchListener)
        binding.vMgsMemberBall.setOnTouchListener(onTouchListener)
        binding.setting.setOnTouchListener(onTouchListener)
        binding.quiteGame.setOnTouchListener(onTouchListener)
    }

    fun updateUnreadMessageCount(unReadCount: Int) {
        binding.ivMgsUnread.isVisible = unReadCount > 0
    }
    fun updateMessageViewStatus(isOpen: Boolean) {
        binding.imgMessageSwitch.setImageResource(if (isOpen) R.drawable.icon_mgs_message_open else R.drawable.icon_mgs_message_close)
    }

    /**
     * 展示未加入房间状态的悬浮球
     */
    fun setOrdinary(ordinary: Boolean, isApplicationCreated:Boolean = false) {
        binding.apply {
            ordinaryFloatBall.isInvisible = !ordinary
            messageBall.isVisible = !ordinary
            vMgsMemberBall.isVisible = !ordinary
            vMgsExitBall.isVisible = !ordinary
            updateFloatBallVoice(!ordinary)
            binding.ordinaryFloatBall.getConstraintSet(R.id.floating_ball_end)
                .setVisibility(R.id.setting, if (mgsInteractor.hasSettingPanel()) View.VISIBLE else View.GONE)
        }
        updateMessageBallStatus()
        if (ordinary) {
            //默认悬浮球的状态
            if (binding.ordinaryFloatBall.currentState != R.id.floating_ball_end) {
                changeLayoutSize(true)
                binding.ordinaryFloatBall.transitionToState(R.id.floating_ball_start)
            }
        } else {
            //普通悬浮球的高度设置为mgs悬浮球高度
            binding.ordinaryFloatBall.setSize(40.dp, 40.dp)
        }
    }

    private fun updateFloatBallVoice(inRoom :Boolean) {
        binding.apply {
            if (inRoom && mgsInteractor.canAudio()) {
                clVoice.visible()
            } else {
                clVoice.gone()
            }
            if (PandoraToggle.enableBulletChat) {
                //弹幕功能开启时，有语音功能直接展示
                mscSwitch.isVisible = inRoom && mgsInteractor.canAudio()
                voiceOpenView.gone()
                voiceShield.gone()
            } else {
                //正常的悬浮球语音功能，是否展示
                clVoice.visible(inRoom && mgsUserPresenter.canShowBallAudio())
                if (inRoom && mgsUserPresenter.canShowBallAudio()) {
                    //悬浮球部分展示语音按钮
                    voiceOpenView.visible()
                    mscSwitch.visible()
                } else {
                    mscSwitch.gone()
                    voiceOpenView.gone()
                    voiceShield.gone()
                }
            }
        }
    }



    /**
     * 消息悬浮球展示样式：弹幕样式或者普通消息样式
     */
    fun updateMessageBallStatus() {
        if (PandoraToggle.enableBulletChat) {
            binding.danmuSwitch.isVisible = true
            binding.vMessageBall.isVisible = false
            if (mgsUserPresenter.getDanmuStatus()) {
                binding.danmuSwitch.setImageResource(R.drawable.icon_danmu_open)
            } else {
                binding.danmuSwitch.setImageResource(R.drawable.icon_danmu_close)
            }
        } else {
            binding.vMessageBall.isVisible = true
            binding.danmuSwitch.isVisible = false
        }
    }
    fun updateDanmuStatus() {
        mgsUserPresenter.setDanmuStatus()
    }


    /**
     * 普通悬浮球展示退出游戏
     */
    fun showOrdinaryQuit() {
        val analyticsMap = hashMapOf<String, String>().apply {
            putAll(listener.getGameAnalytics())
            put("float_type", "normal")
        }
        if (binding.ordinaryFloatBall.currentState != R.id.floating_ball_end) {
            Analytics.track(EventConstants.EVENT_CLICK_MGS_FLOAT) {
                putAll(analyticsMap)
                put("state", "default")
            }
            binding.ordinaryFloatBall.getConstraintSet(R.id.floating_ball_end)
                .setVisibility(R.id.setting, if (mgsInteractor.hasSettingPanel()) View.VISIBLE else View.GONE)
            binding.ordinaryFloatBall.transitionToState(R.id.floating_ball_end)
            changeLayoutSize(false)
        } else {
            Analytics.track(EventConstants.EVENT_CLICK_MGS_GAME_QUIT) {
                putAll(analyticsMap)
            }
            listener.ordinaryBallQuitGame()
        }
    }

    /**
     * 改变普通悬浮球宽度
     */
    private fun changeLayoutSize(isShrunk: Boolean) {
        binding.ordinaryFloatBall.setSize(
            if (isShrunk) 40.dp else 78.dp, when {
                isShrunk ->  40.dp
                mgsInteractor.hasSettingPanel() -> 91.dp
                else ->  40.dp
            }
        )
    }

    /**
     * 点击悬浮球外部收起
     */
    fun actionOutside() {
        if (binding.ordinaryFloatBall.currentState != R.id.floating_ball_start) {
            Analytics.track(EventConstants.EVENT_CLICK_MGS_FLOAT) {
                putAll(listener.getGameAnalytics())
                put("isclose", "close")
            }
            ordinaryHandler?.removeCallbacksAndMessages(null)
            binding.ordinaryFloatBall.transitionToState(R.id.floating_ball_start)
        }
    }

    private fun getMgsSceneConfig() {
        mgsUserPresenter.getMgsSceneConfig()
    }

    /**
     * 编辑用户头像是否展示
     */
    fun updateUserView(visible: Boolean) {
        binding.vUser.root.visible(visible)
        if (!visible) {
            binding.vUser.apply {
                clUserInfo.gone()
                rlLike.gone()
            }
            return
        }
        getMgsSceneConfig()
    }

    override fun setUserInfo(isShow: Boolean, nickName: String?, avatar: String?) {
        binding.vUser.clUserInfo.visible(isShow)
        if (isShow) {
            val isBeyondLimit = StringUtil.isBeyondCharacterLimit(nickName ?:"", 12)
            binding.vUser.tvNickName.text = if (isBeyondLimit.first) nickName?.substring(0, isBeyondLimit.second) + "..." else nickName
            Glide.with(context).load(avatar).transform(CircleCrop()).into(binding.vUser.ivAvatar)
            listener.updateBallViewSize()
            Analytics.track(EventConstants.EVENT_SHOW_MGS_AVATAR) {
                putAll(listener.getGameAnalytics())
            }
        }
    }

    override fun setLikeState(isShow: Boolean, isLike: Boolean?) {
        binding.vUser.rlLike.visible(isShow)
        if (isShow) {
            binding.vUser.cbLike.isChecked = isLike ?: false
            listener.updateBallViewSize()
        }
    }

    override fun setInputVisibility(isShow: Boolean) {
        listener.isShowMgsInput(isShow)
    }

    /**
     * 改变语音按钮是否可见
     */
    fun changeVoiceVisible(isShow: Boolean, isOpenMic: Boolean) {
        updateFloatBallVoice(mgsInteractor.getMgsRoomParams() != null)
        changeVoiceState(isOpenMic)
    }

    fun getDanmuStatus(): Boolean {
       return mgsUserPresenter.getDanmuStatus()
    }
}