package com.socialplay.gpark.ui.im.activities

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.account.PostNotificationRecord
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import timber.log.Timber

class SysActivitiesViewModel(val metaRepository: IMetaRepository, val content:Application) : ViewModel() {
    private val _showIMNotificationLiveData = SingleLiveData<Boolean>()
    val showIMNotificationLiveData = _showIMNotificationLiveData

    private val _showIMNotificationNumLiveData = SingleLiveData<Boolean>()
    val showIMNotificationNumLiveData = _showIMNotificationNumLiveData

    fun getList(id:Long) = metaRepository.getSysActivitiesInfo(id,10).cachedIn(viewModelScope)

    fun checkNotificationPermission(unReadCount: Long) = viewModelScope.launch {
        if (PandoraToggle.postInteractPushNotificationFrequency != -1) {
            val isNeed =  NotificationPermissionManager.postNeedPermission(content,true)
            Timber.d("needPermissionDialog $isNeed")
            if (isNeed) {
                // 大于一周
                _showIMNotificationLiveData.postValue(true)
            } else {
                if (PandoraToggle.postInteractPushNotificationFrequency == -1) {
                    _showIMNotificationNumLiveData.postValue(false)
                } else if (unReadCount > 0 && unReadCount >= PandoraToggle.postInteractPushNotificationFrequency) {
                    //未读次数大于频控次数
                    _showIMNotificationNumLiveData.postValue(true)
                }
                Timber.d("needPermissionDialog ${PandoraToggle.postInteractPushNotificationFrequency}  ${unReadCount}")
            }
        }
    }
    fun updateNotificationTime() = GlobalScope.launch {
        NotificationPermissionManager.updatePostPermissionTime(true)
    }

}