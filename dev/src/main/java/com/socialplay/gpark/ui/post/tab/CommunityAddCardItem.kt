package com.socialplay.gpark.ui.post.tab

import androidx.annotation.StringRes
import androidx.core.view.isVisible
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.databinding.ItemAddCardBinding
import com.socialplay.gpark.databinding.ItemRelatedCardTitleBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Created by bo.li
 * Date: 2023/10/7
 * Desc:
 */

interface ICommunityAddCardListener : IBaseEpoxyItemListener {
    fun onClickAdd(card: PostCardInfo)
}

fun MetaEpoxyController.addSearchCardList(
    addedCardList: List<PostCardInfo>,
    list: List<PostCardInfo>,
    listener: ICommunityAddCardListener
) {
    list.forEachIndexed { index, item ->
        add(
            CommunityAddCardItem(
                addedCardList.any { it.gameId == item.gameId && it.resourceType == item.resourceType},
                item,
                listener
            ).id("SearchCardItem-${item.gameId}")
        )
    }
}

fun MetaEpoxyController.addRelatedCardList(
    addedCardList: List<PostCardInfo>,
    list: List<RelatedCardListWrap>,
    listener: ICommunityAddCardListener
) {
    list.forEachIndexed { wrapIndex, item ->
        if (item.list.isNotEmpty()) {
            add(CommunityRelatedCardTitleItem(item.title).id("RelatedCardTitleItem-${item.title}"))
        }
        item.list.forEachIndexed { index, communityCardInfo ->
            add(
                CommunityAddCardItem(
                    addedCardList.any { it.gameId == communityCardInfo.gameId && it.resourceType == communityCardInfo.resourceType},
                    communityCardInfo,
                    listener
                ).id("RelatedCardItem-${item.title}-${communityCardInfo.gameId}")
            )
        }
        // 没end，先不展示下面的列表
        if (!item.end) return
    }
}


data class CommunityRelatedCardTitleItem(
    @StringRes val title: Int,
) : ViewBindingItemModel<ItemRelatedCardTitleBinding>(
    R.layout.item_related_card_title,
    ItemRelatedCardTitleBinding::bind
) {
    override fun ItemRelatedCardTitleBinding.onBind() {
        tvTitle.text = getItemView().context.getString(title)
    }
}

data class CommunityAddCardItem(
    val added: Boolean,
    val item: PostCardInfo,
    val listener: ICommunityAddCardListener
) : ViewBindingItemModel<ItemAddCardBinding>(
    R.layout.item_add_card,
    ItemAddCardBinding::bind
) {
    override fun ItemAddCardBinding.onBind() {
        root.setOnAntiViolenceClickListener {
            if (!added) {
                listener.onClickAdd(item)
            }
        }
        includeCard.apply {
            tvGameTitle.text = item.gameName
            tvGameLike.text = UnitUtil.formatKMCount(item.likeCount)
            tvGamePeople.text = UnitUtil.formatKMCount(item.player)

            tvGameLike.isVisible = item.isPgcType
            tvGameScore.isVisible = false
            tvGamePeople.isVisible = item.isUgcType
            listener.getGlideOrNull()?.run {
                load(item.gameIcon).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvGameAuthor.text =
                getItemView().context.getString(R.string.creator_cap_with_param, item.gameAuthor)
            tvAdd.enableWithAlpha(!added)
            tvAdd.text =
                getItemView().context.getString(if (added) R.string.added_cap else R.string.add)
            tvAdd.isVisible = true
        }
    }
}