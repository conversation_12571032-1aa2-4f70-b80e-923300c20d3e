package com.socialplay.gpark.ui.account.guide

import android.content.ComponentCallbacks
import android.content.Context
import androidx.lifecycle.asFlow
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.ViolateCheckException
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.VisitorInfoApiResult
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.extension.collectWithTimeout
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2024/1/3
 * Desc:
 */
data class GuideLoginModelState(
    // 上次登录账号
    val continueAccount: ContinueAccountInfo? = null,
    // 去新手引导页面
    val enterGuidePage: Async<Boolean> = Uninitialized,
    // 去主页
    val enterMainPage: Async<Boolean> = Uninitialized,
    // 违规信息
    val violateInfo: ViolateMessage? = null
) : MavericksState

class GuideLoginViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    private val updateAppInteractor: UpdateAppInteractor,
    private val metaKV: MetaKV,
    initialState: GuideLoginModelState
) : BaseViewModel<GuideLoginModelState>(initialState) {

    val lastLoginType get() = metaKV.account.lastLoginType

    init {
        getContinueAccount()
    }

    private fun getContinueAccount() {
        repository.getContinueAccount().map {
            it.data
        }.execute {
            if (it is Success) {
                copy(continueAccount = it())
            } else {
                this
            }
        }
    }

    fun getContinueAccountIfPossible() {
        if (!viewModelScope.isActive) return
        withState { s ->
            if (s.continueAccount != null) return@withState
            getContinueAccount()
        }
    }

    /**
     * @param [loginType] 登录来源 com.socialplay.gpark.data.model.LoginType
     */
    fun visitorLogin(loginType: LoginType) {
        withState { currentState ->
            if (currentState.enterGuidePage is Loading || currentState.enterMainPage is Loading) return@withState
            combine(
                accountInteractor.visitorLoginFLow(loginType),
                checkGuideAbTest()
            ) { loginResult, toggle ->
                if (loginResult is LoginState.Failed) {
                    loginResult.violateMessage?.let {
                        setViolateOperation(it)
                        throw ViolateCheckException()
                    } ?: throw ApiResultCodeException(
                        loginResult.code,
                        loginResult.message.ifNullOrEmpty {
                            GlobalContext.get().get<Context>()
                                .getString(R.string.create_new_player_failed)
                        },
                        VisitorInfoApiResult::class
                    )
                }
                toggle
            }.execute(Dispatchers.IO) { loginResult ->
                copy(
                    enterGuidePage = if (loginType == LoginType.CreateAccount) loginResult else enterGuidePage,
                    enterMainPage = if (loginType == LoginType.LastAccount) loginResult else enterMainPage
                )
            }
        }
    }

    /**
     * 新手引导开关结果
     */
    private fun checkGuideAbTest() = flow {
        // 产品定超时1s
        if (PandoraInit.abTestConfigFetchedLiveData.value != true) {
            PandoraInit.abTestConfigFetchedLiveData.asFlow().collectWithTimeout(1_000L)
        }
        emit(true)
    }

    private fun setViolateOperation(info: ViolateMessage?) {
        setState {
            copy(violateInfo = info)
        }
    }

    fun clearViolateOperation() {
        setViolateOperation(null)
    }

    companion object : KoinViewModelFactory<GuideLoginViewModel, GuideLoginModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GuideLoginModelState
        ): GuideLoginViewModel {
            return GuideLoginViewModel(get(), get(), get(), get(), state)
        }
    }
}