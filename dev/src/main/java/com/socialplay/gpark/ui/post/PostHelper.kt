package com.socialplay.gpark.ui.post

import android.widget.TextView
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.util.extension.backgroundTintListByRes
import com.socialplay.gpark.util.extension.clearCompoundDrawables
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/26
 *     desc   :
 * </pre>
 */
object PostHelper {

    fun updateReviewStatus(tv: TextView, status: Int) {
        when {
            PostDetail.isInProgress(status) -> {
                tv.visible()
                tv.setText(R.string.under_review)
                tv.setTextColorByRes(R.color.color_4AB4FF)
                tv.backgroundTintListByRes(R.color.color_EBF6FF)
                tv.compoundDrawables(left = R.drawable.ic_label_under_review)
            }

            PostDetail.isFail(status) -> {
                tv.visible()
                tv.setText(R.string.review_not_pass)
                tv.setTextColorByRes(R.color.color_FF4C45)
                tv.backgroundTintListByRes(R.color.color_FFF1F1)
                tv.compoundDrawables(left = R.drawable.ic_label_review_not_passed)
            }

            else -> {
                tv.gone()
            }
        }
    }

    fun updateReviewStatusForIcon(tv: TextView, status: Int) {
        when {
            PostDetail.isInProgress(status) -> {
                tv.compoundDrawables(right = R.drawable.ic_label_under_review)
            }

            PostDetail.isFail(status) -> {
                tv.compoundDrawables(right = R.drawable.ic_label_review_not_passed)
            }

            else -> {
                tv.clearCompoundDrawables()
            }
        }
    }
}