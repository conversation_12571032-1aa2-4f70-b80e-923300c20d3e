package com.socialplay.gpark.ui.editor.backups

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.cloud.UgcBackupInfo
import com.socialplay.gpark.databinding.FragmentUgcBackupBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.editor.BaseEditorFragmentMaverick
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

@Parcelize
data class UgcBackupFragmentArgs(
    val fileId: String,
    val gameIdentity: String,
    val path: String,
    val type: Int,
    val gid: String? = null,
    val pkg: String? = null,
    val needCallback: Boolean = false
) : Parcelable

class UgcBackupFragment :
    BaseEditorFragmentMaverick<FragmentUgcBackupBinding>(R.layout.fragment_ugc_backup) {

    companion object {
        const val KEY = "UgcBackupFragment"
        const val KEY_RESULT = "result"
    }

    private val viewModel: UgcBackupViewModel by fragmentViewModel()
    private val args by args<UgcBackupFragmentArgs>()

    private val backupController by lazy { backupController() }

    private var result = false

    private val itemListener = object : IUgcBackupListener {
        override fun click(item: UgcBackupInfo) {
            Analytics.track(
                EventConstants.BACKUP_PROFILE_CLICK,
                "page" to item.archiveName,
                "fileid" to args.fileId,
                "cloudid" to item.archiveId,
                "gameid" to args.gameIdentity
            )
            ConfirmDialog.Builder(this@UgcBackupFragment)
                .content(getString(R.string.restore_confirmation))
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.dialog_confirm))
                .dismissCallback { action ->
                    when (action) {
                        ConfirmDialog.CONFIRM -> {
                            openBackup(item)
                        }

                        ConfirmDialog.CANCEL -> {
                            Analytics.track(
                                EventConstants.SECONDARY_BACKUP_PROFILE,
                                "fileid" to args.fileId,
                                "cloudid" to item.archiveId,
                                "button" to "2"
                            )
                        }
                    }
                }
                .show()
        }

        override fun show(item: UgcBackupInfo) {
            Analytics.track(
                EventConstants.BACKUP_PROFILE_EXPOSURE,
                "page" to item.archiveName,
                "fileid" to args.fileId,
                "cloudid" to item.archiveId,
                "gameid" to args.gameIdentity
            )
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcBackupBinding? {
        return FragmentUgcBackupBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        backupController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.titleBar.setOnBackClickedListener { navigateUp() }

        binding.recyclerView.setController(backupController)

        val epoxyVisibilityTracker = EpoxyVisibilityTracker()
        epoxyVisibilityTracker.attach(viewLifecycleOwner, binding.recyclerView)

        viewModel.setupRefreshLoading(
            UgcBackupState::refresh,
            binding.loadingView,
            binding.refreshLayout
        ) {
            viewModel.refresh()
        }

        if (args.gid == null) {
            viewLifecycleOwner.lifecycleScope.launch {
                viewModel.loadGid(args.gameIdentity)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        backupController.onSaveInstanceState(outState)
    }

    private fun backupController() = simpleController(
        viewModel,
        UgcBackupState::list
    ) {
        it.forEach { item ->
            ugcBackupItem(item, itemListener)
        }
    }

    private fun openBackup(it: UgcBackupInfo) {
        val gid = args.gid ?: viewModel.gidPkg?.gameId
        val pkg = args.pkg ?: viewModel.gidPkg?.platformPackageName
        Analytics.track(
            EventConstants.SECONDARY_BACKUP_PROFILE,
            "fileid" to args.fileId,
            "cloudid" to it.archiveId,
            "button" to "1"
        )
        if (gid == null || pkg == null) {
            viewLifecycleOwner.lifecycleScope.launch {
                val gidPkg = viewModel.loadGid(args.gameIdentity)
                if (gidPkg != null) {
                    editorGameLaunchHelper?.startLocalGame(
                        this@UgcBackupFragment,
                        gidPkg.gameId,
                        args.path,
                        gidPkg.platformPackageName,
                        args.fileId,
                        ResIdBean().setCategoryID(CategoryId.UGC_BACKUP),
                        ugcSlot = it.slot,
                        archiveId = it.archiveId.toString()
                    )
                    result = true
                } else {
                    toast(R.string.failed_restore_backup)
                }
            }
        } else {
            editorGameLaunchHelper?.startLocalGame(
                this,
                gid,
                args.path,
                pkg,
                args.fileId,
                ResIdBean().setCategoryID(CategoryId.UGC_BACKUP),
                ugcSlot = it.slot,
                archiveId = it.archiveId.toString()
            )
            result
        }
    }

    override fun invalidate() {}

    override fun getPageName(): String {
        return "UgcBackupFragment"
    }

    override fun onDestroy() {
        if (args.needCallback && result) {
            setFragmentResult(KEY, bundleOf(KEY_RESULT to true))
        }
        super.onDestroy()
    }
}