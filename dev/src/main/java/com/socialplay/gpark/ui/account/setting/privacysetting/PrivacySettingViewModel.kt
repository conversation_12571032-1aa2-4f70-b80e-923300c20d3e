package com.socialplay.gpark.ui.account.setting.privacysetting

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.PrivacySetting
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 * </pre>
 */
data class PrivacySettingState(
    val settings: List<PrivacySetting> = emptyList()
) : MavericksState

class PrivacySettingViewModel(
    initialState: PrivacySettingState,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    val metaKV: MetaKV
) : BaseViewModel<PrivacySettingState>(initialState) {

    init {
        initSwitches()
    }

    fun initSwitches() {
        val ootd = accountInteractor.accountLiveData.value?.ootdPrivateSwitch
        if (ootd == null) {
            repo.getPrivacySwitch().map {
                accountInteractor.updatePrivacySwitch(
                    ootdPrivateSwitch = it.ootdPrivateSwitch,
                    updateCache = true
                )
                PrivacySetting(
                    PrivacySetting.SWITCH_TRY_ON,
                    init = it.ootdPrivateSwitch ?: false,
                    titleRes = R.string.allow_other_try_on
                )
            }.execute { result ->
                when (result) {
                    is Success -> {
                        copy(settings = settings.insertAt(0, result()).orEmpty())
                    }

                    else -> {
                        this
                    }
                }
            }
        } else {
            val setting = PrivacySetting(
                PrivacySetting.SWITCH_TRY_ON,
                init = ootd,
                titleRes = R.string.allow_other_try_on
            )
            setState {
                copy(settings = settings.insertAt(0, setting).orEmpty())
            }
        }
    }

    fun switchSettingStatus(
        setting: PrivacySetting,
        position: Int,
        enable: Boolean = !setting.enable
    ) {
        val newSetting = setting.copy(
            enable = enable,
            showLabelRedDot = if (setting.isScreenshot && setting.showLabelRedDot) {
                metaKV.appKV.iShowScreenshotPrivacyRedHot
            } else {
                setting.showLabelRedDot
            }
        )
        val newSettings = oldState.settings.replaceAt(position, newSetting) ?: return
        setState { copy(settings = newSettings) }
    }

    fun clearRedDot(
        setting: PrivacySetting,
        position: Int
    ) {
        val newSetting = setting.copy(
            showLabelRedDot = if (setting.isScreenshot && setting.showLabelRedDot) {
                metaKV.appKV.iShowScreenshotPrivacyRedHot
            } else {
                setting.showLabelRedDot
            }
        )
        val newSettings = oldState.settings.replaceAt(position, newSetting) ?: return
        setState { copy(settings = newSettings) }
    }

    override fun onCleared() {
        super.onCleared()
        if (oldState.settings.any { it.isChanged }) {
            var switch = PrivacySwitch()
            var changed = 0
            var ootdPrivateSwitchChanged = false
            oldState.settings.forEach {
                when (it.id) {
                    PrivacySetting.SWITCH_TRY_ON -> {
                        switch = switch.copy(ootdPrivateSwitch = it.enable)
                        if (it.isChanged) {
                            ootdPrivateSwitchChanged = true
                            changed++
                        }
                    }

                    PrivacySetting.SWITCH_SCREENSHOT_SHARE -> {
                        if (it.isChanged) {
                            metaKV.appKV.enableScreenshotShare = it.enable
                            changed++
                        }
                    }

                    PrivacySetting.SWITCH_MESSAGE_ON -> {
                        switch = switch.copy(chatMessageSwitch = it.enable)
                        if (it.isChanged) {
                            changed++
                        }
                    }

                    PrivacySetting.SWITCH_RELATION_LIST -> {
                        switch = switch.copy(followerShowSwitch = it.enable)
                        if (it.isChanged) {
                            changed++
                        }
                    }

                    PrivacySetting.SWITCH_FRIEND_REQUESTS_NOTIFY -> {
                        if (it.isChanged) {
                            accountInteractor.setFriendRequestsNoticeEnabled(it.enable)
                        }
                    }
                }
            }
            if (changed > 0) {
                GlobalScope.launch {
                    val result = repo.setPrivacySwitch(switch)
                    if (result.succeeded && result.data == true) {
                        accountInteractor.updatePrivacySwitch(switch, updateCache = true)
                        if (ootdPrivateSwitchChanged) {
                            Analytics.track(
                                EventConstants.PRIVACY_SETTINGS_TRY_ON_CLICK,
                                "set_result" to (if (switch.ootdPrivateSwitch == true) "0" else "1")
                            )
                        }
                    }
                }
            }
        }
    }

    companion object : KoinViewModelFactory<PrivacySettingViewModel, PrivacySettingState>() {
        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): PrivacySettingState {
            val accountInteractor: AccountInteractor = get()
            val metaKV: MetaKV = get()
            val userInfo = accountInteractor.accountLiveData.value
            return PrivacySettingState(
                settings = buildList {
                    if (PandoraToggle.enableShareScreenshot) {
                        add(
                            PrivacySetting(
                                PrivacySetting.SWITCH_SCREENSHOT_SHARE,
                                init = metaKV.appKV.enableScreenshotShare,
                                titleRes = R.string.enable_screenshot_sharing,
                                showLabelRedDot = metaKV.appKV.iShowScreenshotPrivacyRedHot
                            )
                        )
                    }

                    if (PandoraToggle.isIMTipsClose) {
                        add(
                            PrivacySetting(
                                PrivacySetting.SWITCH_MESSAGE_ON,
                                init = userInfo?.chatMessageSwitch ?: true,
                                titleRes = R.string.im_tip_close_title
                            )
                        )
                    }
                    add(
                        PrivacySetting(
                            PrivacySetting.SWITCH_RELATION_LIST,
                            init = userInfo?.canShowFollower() ?: true,
                            titleRes = R.string.allow_relation_show
                        )
                    )
                    if (PandoraToggle.enableFriendRequestPopUp) {
                        add(
                            PrivacySetting(
                                PrivacySetting.SWITCH_FRIEND_REQUESTS_NOTIFY,
                                init = accountInteractor.enableFriendRequestsNotice,
                                titleRes = R.string.setting_allow_notify_friend_requests
                            )
                        )
                    }
                }
            )
        }

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: PrivacySettingState
        ): PrivacySettingViewModel {
            return PrivacySettingViewModel(state, get(), get(), get())
        }
    }
}