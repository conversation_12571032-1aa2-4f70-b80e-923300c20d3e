package com.socialplay.gpark.ui.editor.home.datalist

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.editor.GroupedData
import com.socialplay.gpark.data.model.editor.GroupedData.Companion.CHOICE_CARD_TYPE_AI_BOT
import com.socialplay.gpark.data.model.plot.PlotTemplate
import com.socialplay.gpark.databinding.FragmentEditorHomeDataListBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.carouselNoSnapBuilder
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel.Companion.GENDER_ALL
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel.Companion.GENDER_FEMALE
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel.Companion.GENDER_MALE
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel.Companion.GENDER_NON
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.PopWindowUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import org.koin.androidx.viewmodel.ext.android.sharedViewModel


class EditorHomeDataListFragment :
    BaseRecyclerViewFragment<FragmentEditorHomeDataListBinding>(R.layout.fragment_editor_home_data_list) {

    private val mainViewModel: MainViewModel by sharedViewModel()

    companion object {
        private const val GRID_LIST_COL_COUNT = 6
    }
    private var _popup: PopWindowUtil? = null
    override fun isEnableTrackPageExposure(): Boolean = false

    override fun getPageName(): String = ""

    private val viewModel: EditorHomeDataViewModel by fragmentViewModel()

    override val recyclerView: EpoxyRecyclerView get() = (binding.rvList as EpoxyRecyclerView)

    private var tsGameLaunchHelper: TSGameLaunchHelper? = null

    private var needUpdateDailyTaskStatus = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorHomeDataListBinding? {
        return FragmentEditorHomeDataListBinding.inflate(inflater, container, false)
    }

    override fun invalidate() {

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }
    private fun initView(){
        tsGameLaunchHelper = TSGameLaunchHelper(this)
        binding.rvList.layoutManager =
            GridLayoutManager(requireContext(), GRID_LIST_COL_COUNT)

        binding.lvLoadingView.setVerticalBias(0.2F)

        viewModel.setupRefreshLoading(
            asyncProp = EditorHomeDataViewModelState::mergedRefreshStatus,
            loadingView = binding.lvLoadingView,
            refreshLayout = binding.refreshLayout,
        ) {
            if(PandoraToggle.isOpenAiBot){
                viewModel.refresh(true, viewModel.oldState.tagId, viewModel.oldState.gender)
            }else{
                viewModel.refresh(true)
            }

        }

    }


    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        EditorHomeDataViewModelState::list,
        EditorHomeDataViewModelState::flyWheelData,
        EditorHomeDataViewModelState::bannerListData,
        EditorHomeDataViewModelState::loadMore
    ) { list, flyWheelData, bannerList,loadMore ->

        // 是否显示第一个分组的顶部间距
        var showFirstGroupTopMargin = false

        if ((flyWheelData is Success)) {
            val noticeList = flyWheelData.invoke()
            if(noticeList.isNotEmpty()){
                showFirstGroupTopMargin = true
                carouselNoSnapBuilder {
                    id("flywheel")
                    spanSizeOverride { _, _, _ -> GRID_LIST_COL_COUNT }

                    noticeList.forEachIndexed { index, data ->
                        editorHomeFlyWheelItem(index, data) { _, uniJumpConfig ->
                            Analytics.track(EventConstants.EVENT_EVENT_OPERATION_POSITION_CLICK) {
                                put("card_id_string", uniJumpConfig.uniqueCode ?: "")
                                put("card_name", uniJumpConfig.title ?: "")
                            }
                            UniJumpUtil.jump(this@EditorHomeDataListFragment, uniJumpConfig, LinkData.SOURCE_HALF_HOME, CategoryId.CATEGORY_ID_ROLE_FLY_WHEEL, mainViewModel, null)
                            if (uniJumpConfig.jumpType == UniJumpConfig.POS_DAILY_TASK_REWARD) {
                                needUpdateDailyTaskStatus = true
                            }
                        }
                    }
                }
            }
        }

        if (bannerList is Success) {
            val bannerListData = bannerList.invoke()
            if(bannerListData.isNotEmpty()){
                spacer(height = 26.dp, spanCount = GRID_LIST_COL_COUNT)
                add(
                    EditorHomeDataListItemBanner(bannerList = bannerListData)
                        .onClick { _, data ->
                            Analytics.track(
                                EventConstants.AVATAR_TAB_EVENT_CLICK,
                                "name" to data.title.toString(),
                                "operation_id" to data.id.toString(),
                                "categoryId" to CategoryId.OPERATION_POSITION_EDITOR_HOME_BANNER,
                            )
                            UniJumpUtil.jump(
                                this@EditorHomeDataListFragment,
                                data,
                                LinkData.SOURCE_HALF_HOME,
                                CategoryId.OPERATION_POSITION_EDITOR_HOME_BANNER,
                                null
                            )
                        }
                        .onShow { _, data ->
                            Analytics.track(
                                EventConstants.AVATAR_TAB_EVENT_SHOW,
                                "name" to data.title.toString(),
                                "operation_id" to data.id.toString(),
                                "categoryId" to CategoryId.OPERATION_POSITION_EDITOR_HOME_BANNER,
                            )
                        }
                        .id("editorHomePopularItem-Banner")
                        .spanSizeOverride { _, _, _ -> GRID_LIST_COL_COUNT }
                )
                spacer(height = 4.dp, spanCount = GRID_LIST_COL_COUNT)
            }
        }
        var oldIndex = 0
        list.forEachIndexed { groupIndex, groupedData ->
            if (groupedData.items != null && groupedData.items.isNotEmpty()) {
                if ((groupIndex == 0 && showFirstGroupTopMargin) || groupIndex != 0) {
                    spacer(height = 18.dp, spanCount = GRID_LIST_COL_COUNT)
                }
                groupCardTitle(this, groupIndex, groupedData)
                spacer(height = 6.dp, spanCount = GRID_LIST_COL_COUNT)

                when (groupedData.type) {
                    GroupedData.CHOICE_CARD_TYPE_TTAI_MOMENT  -> {
                        carouselNoSnapBuilder {
                            id("group_carousel_${groupIndex}")
                            spanSizeOverride { _, _, _ -> GRID_LIST_COL_COUNT }

                            padding(Carousel.Padding.dp(0, 0, 0, 0, 10))

                            groupedData.items.forEachIndexed { index, item ->
                                when (groupedData.type) {
                                    GroupedData.CHOICE_CARD_TYPE_TTAI_MOMENT -> editorHomeMomentItem(
                                        index,
                                        item as PlotTemplate
                                    ) { _, _ ->
                                        val resIdBean = ResIdBean()
                                            .setCategoryID(CategoryId.AVATAR_TAB_LAUNCH_GAME)
                                        handleMomentItemClicked(item, resIdBean)
                                    }
                                }
                            }
                        }
                    }


                    GroupedData.CHOICE_CARD_TYPE_TTAI_POPULAR -> {
                        groupedData.items.forEachIndexed { index, item ->

                            val colCnt = 3
                            val spanCnt = GRID_LIST_COL_COUNT / colCnt

                            val column = index % colCnt

                            val columnSpacing = 10.dp.toFloat()
                            val rowSpacing = 10.dp.toFloat()

                            val startMargin = (column * columnSpacing / colCnt)
                            val endMargin = (columnSpacing - (column + 1) * columnSpacing / colCnt)

                            val isLastRow = index >= groupedData.items.size - colCnt

                            editorHomePopularItem(
                                index, item as GameSuggestionInfo,
                                startMargin.toInt(),
                                endMargin.toInt(),
                                (if (isLastRow) 0 else rowSpacing).toInt(),
                                spanCnt
                            ) { _, _ ->
                                handlePopularItemClicked(
                                    item,
                                    ResIdBean().setCategoryID(CategoryId.AVATAR_TAB_LAUNCH_GAME)
                                )
                            }
                        }
                    }

                    GroupedData.CHOICE_CARD_TYPE_AI_BOT       -> {
                        groupedData.items.forEachIndexed { index, item ->
                            val colCnt = 2
                            val spanCnt = GRID_LIST_COL_COUNT / colCnt
                            val column = index % colCnt
                            val columnSpacing = 10.dp
                            val rowSpacing = 12.dp
                            val startMargin =
                                (column * columnSpacing / colCnt)
                            val endMargin =
                                (columnSpacing - (column + 1) * columnSpacing / colCnt)
                            val isLastRow = index >= groupedData.items.size - colCnt
                            oldIndex++
                            editorAiBotItem(
                                width = (ScreenUtil.screenWidth - 42.dp) / 2,
                                oldIndex,
                                item as BotInfo,
                                startMargin,
                                endMargin,
                                if (isLastRow) 0 else rowSpacing,
                                spanCnt = spanCnt
                            ) { position, data ->
                                MetaRouter.AiBot.gotoAiBotDetail(
                                    this@EditorHomeDataListFragment,
                                    data.botId,
                                    data.id,
                                    "1"
                                )
                            }
                        }
                    }

                    ChoiceCardType.UGC_CREATE                 -> {
                        groupedData.items.forEachIndexed { index, item ->

                            val colCnt = 2
                            val spanCnt = GRID_LIST_COL_COUNT / colCnt

                            val column = index % colCnt

                            val columnSpacing = 16.dp
                            val rowSpacing = 12.dp

                            val startMargin =
                                (column * columnSpacing / colCnt)
                            val endMargin =
                                (columnSpacing - (column + 1) * columnSpacing / colCnt)
                            val isLastRow = index >= groupedData.items.size - colCnt

                            editorHomeUgcItem(
                                index,
                                item as ChoiceGameInfo,
                                startMargin,
                                endMargin,
                                if (isLastRow) 0 else rowSpacing,
                                spanCnt = spanCnt
                            ) { position, data ->
                                val resIdBean =
                                    ResIdBean()
                                        .setGameId(item.code ?: "")
                                        .setCategoryID(CategoryId.AVATAR_TAB_LAUNCH_GAME)
                                        .setParam1(groupIndex)
                                        .setParam2(position)
                                        .setParamExtra(groupedData.title)

                                handleUgcItemClicked(data, resIdBean)
                            }
                        }
                    }
                }
            } else {
                if (groupedData.type == CHOICE_CARD_TYPE_AI_BOT) {
                    //AiBot 数据为空，但是要展示标题
                    if ((groupIndex == 0 && showFirstGroupTopMargin) || groupIndex != 0) {
                        spacer(height = 18.dp, spanCount = GRID_LIST_COL_COUNT)
                    }
                    groupCardTitle(this, groupIndex, groupedData)
                }
            }
        }
        if(PandoraToggle.isOpenAiBot){
            loadMoreFooter(loadMore, spanSize = GRID_LIST_COL_COUNT, showEnd = false) {
                viewModel.loadMore()
            }
        }
        spacer(height = 24.dp, spanCount = GRID_LIST_COL_COUNT)
    }
    private fun groupCardTitle(
        metaEpoxyController: MetaEpoxyController,
        groupIndex: Int,
        groupedData: GroupedData<*>
    ){
        when(groupedData.type  ){
            GroupedData.CHOICE_CARD_TYPE_AI_BOT -> {
                if (groupedData.id != null) {
                    metaEpoxyController.add(EditorHomeDataListAiBotItemTitle(
                        position = groupIndex,
                        item = groupedData,
                        tagList = groupedData.tag as List<BotLabelInfo>,
                        showSeeAll = ChoiceCardType.isUgcCreate(groupedData.type ?: 0),
                        viewModel.oldState.gender,
                        seeAllListener = {
                            val id = viewModel.oldState.tagId
                            val list = if (id != null) listOf(id) else emptyList()
                            MetaRouter.AiBot.showAiTagDialog(this, list, false, 1) { list ->
                                if (!list.isNullOrEmpty()) {
                                    val botLabelInfo = list.first()
                                    viewModel.refresh(false, botLabelInfo.tagId)
                                    Analytics.track(
                                        EventConstants.COMMUNITY_APP_BOT_FILTER,
                                        "tag_list" to (botLabelInfo.tagName ?: "")
                                    )
                                }
                            }

                        },
                        labelListener =  { p, lable ->
                            viewModel.refresh(false, lable.tagId)
                            Analytics.track(EventConstants.COMMUNITY_APP_BOT_FILTER,"tag_list" to lable.tagName)
                        }
                        ,{
                            //gender
                            show(it)
                        }
                    ).id("editorAIBotListItemTitle-${groupIndex}")
                        .spanSizeOverride { _, _, _ -> GRID_LIST_COL_COUNT })
                }
            }
            else ->{
                metaEpoxyController.add(
                    EditorHomeDataListItemTitle(
                        position = groupIndex,
                        item = groupedData,
                        showSeeAll = ChoiceCardType.isUgcCreate(groupedData.type ?: 0),
                        seeAllListener = { _, data ->
                            if(data.type == ChoiceCardType.UGC_CREATE){
                                MetaRouter.MobileEditor.ugcAll(
                                    this@EditorHomeDataListFragment,
                                    data.id ?: "",
                                    data.title ?: ""
                                )
                            }
                        }
                    ).id("editorHomeDataListItemTitle-${groupIndex}")
                        .spanSizeOverride { _, _, _ -> GRID_LIST_COL_COUNT }
                )
            }
        }
    }
    private fun getGenderName(): String {
        return if (viewModel.oldState.gender?.equals(GENDER_NON) == true) {
            "non_binart"
        } else if (viewModel.oldState.gender?.equals(GENDER_MALE) == true) {
            "Male"
        } else if (viewModel.oldState.gender?.equals(GENDER_FEMALE) == true) {
            "Female"
        } else {
            "All"
        }
    }

    fun show(anchor:View) {
        dismissInternal()
        val popup =   PopWindowUtil.PopupWindowBuilder(requireContext())
            .setView(R.layout.view_ai_bot_pop)
            .size(LinearLayout.LayoutParams.WRAP_CONTENT.toFloat(), LinearLayout.LayoutParams.WRAP_CONTENT.toFloat())
            .setFocusable(true)
            .setTouchable(true)
            .setOutsideTouchable(true)
            .create().apply {
                showAsDropDown(anchor)
               getView<ConstraintLayout>(R.id.cl_gender_all)?.setOnAntiViolenceClickListener {
                    setGender(GENDER_ALL,"all")
                }
                getView<ConstraintLayout>(R.id.cl_gender_female)?.setOnAntiViolenceClickListener {
                    setGender(GENDER_FEMALE,"Female")
                }
                getView<ConstraintLayout>(R.id.cl_gender_male)?.setOnAntiViolenceClickListener {
                    setGender(GENDER_MALE,"Male")
                }
                getView<ConstraintLayout>(R.id.cl_gender_non)?.setOnAntiViolenceClickListener {
                    setGender(GENDER_NON,"non_binart")
                }
            }
        this._popup = popup
    }
    private fun setGender(type: Int, gender:String) {
        Analytics.track(EventConstants.COMMUNITY_APP_BOT_FILTER,"gender" to gender)
        viewModel.setGender(type)
        dismissInternal()
    }
    private fun dismissInternal() {
        this._popup?.dissmiss()
        this._popup = null
    }
    private fun handleMomentItemClicked(item: PlotTemplate,resIdBean: ResIdBean) {
        Analytics.track(EventConstants.HALFSCREEN_MOMENTS_CLICK) {
            put("templateId", item.templateId.toString())
        }

        viewModel.recordTemplateClickedEvent(item.templateId.toString())

        tsGameLaunchHelper?.startPlotGame(
            gid = item.gameId,
            gameName = item.templateName,
            templateId = "${item.templateId}",
            source = "3",
            expand = item.extraConfig,
            resIdBean = resIdBean
        )
    }

    private fun handlePopularItemClicked(item: GameSuggestionInfo, resIdBean: ResIdBean){

        Analytics.track(EventConstants.HALFSCREEN_GAMELIST_CLICK) {
            put("gameId", item.getMatchGameId().orEmpty())
        }

        if (item.gameType == GameSuggestionInfo.GAME_TYPE_UGC) {
            if (PandoraToggle.enableUgcDetail) {
                MetaRouter.MobileEditor.ugcDetail(
                    this,
                    item.getMatchGameId().orEmpty(),
                    null,
                    resIdBean
                )
            } else {
                resIdBean.setTsType(ResIdBean.TS_TYPE_UCG)
                val params = EditorUGCLaunchParams(item.getMatchGameId().orEmpty(),item.packageName,item.name?:"",item.icon ?: "","")
                tsGameLaunchHelper?.startUgcGame(params, resIdBean)
            }
        } else {
            resIdBean.setTsType(ResIdBean.TS_TYPE_NORMAL)
            MetaRouter.GameDetail.navigate(
                this,
                item.getMatchGameId().orEmpty(),
                resIdBean,
                item.packageName ?: "",
                type = "ts"
            )
        }
    }

    private fun handleUgcItemClicked(item: ChoiceGameInfo, resIdBean: ResIdBean) {

        Analytics.track(EventConstants.HALFSCREEN_UGCLIST_CLICK) {
            put("ugcId", item.code ?: "")
        }

        if (PandoraToggle.enableUgcDetail) {
            MetaRouter.MobileEditor.ugcDetail(
                this@EditorHomeDataListFragment,
                item.code.orEmpty(),
                null,
                resIdBean
            )
        } else {
            resIdBean.setTsType(ResIdBean.TS_TYPE_UCG)
            val params = EditorUGCLaunchParams(item.code?:"",item.packageName,item.displayName?:"",item.iconUrl ?: "","")
            tsGameLaunchHelper?.startUgcGame(params,resIdBean)
        }
    }

    override fun onResume() {
        super.onResume()
        if (needUpdateDailyTaskStatus){
            needUpdateDailyTaskStatus = false
            viewModel.refreshDailyTaskTipsStatus()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dismissInternal()
    }
}