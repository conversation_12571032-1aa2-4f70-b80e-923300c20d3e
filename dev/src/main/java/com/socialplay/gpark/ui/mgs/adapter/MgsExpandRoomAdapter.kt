package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.meta.biz.mgs.data.model.Member
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.meta.biz.mgs.data.model.MgsRoomUser.Companion.RELATION_APPLY
import com.meta.biz.mgs.data.model.MgsRoomUser.Companion.RELATION_BOTH
import com.meta.biz.mgs.data.model.MgsRoomUser.Companion.RELATION_UN_RELATED
import com.socialplay.gpark.databinding.ItemMgsExpandRoomBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setEndMargin
import com.socialplay.gpark.util.extension.setPaddingEnd
import org.koin.core.context.GlobalContext

class MgsExpandRoomAdapter : BaseAdapter<Member, ItemMgsExpandRoomBinding>() {
    private val accountInteractor by GlobalContext.get().inject<AccountInteractor>()
    private val mgsInteractor by GlobalContext.get().inject<MgsInteractor>()

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): ItemMgsExpandRoomBinding {
        return ItemMgsExpandRoomBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(holder: BindingViewHolder<ItemMgsExpandRoomBinding>, item: Member, position: Int) {
        Glide.with(holder.itemView.context).load(item.avatar).error(R.drawable.icon_default_avatar).transform(CircleCrop()).into(holder.binding.ivMgsRoomAvatar)
        holder.binding.tvMgsRoomUserName.text = item.nickname

        val isMe = item.uuid == accountInteractor.accountLiveData.value?.uuid
        val canAddFriend = item.relation == RELATION_UN_RELATED && !isMe
        val showAddFriendBtn = !isMe && (item.relation == RELATION_UN_RELATED || item.relation == RELATION_APPLY || item.relation == RELATION_BOTH)
        holder.binding.tvMgsRoomAddFriend.isEnabled = canAddFriend
        holder.binding.tvMgsRoomAddFriend.isInvisible = !showAddFriendBtn

        when (item.relation) {
            RELATION_UN_RELATED -> {
                //没有关系
                holder.binding.tvMgsRoomAddFriend.text = context.getString(R.string.meta_mgs_add_friend)
            }
            RELATION_APPLY  -> {
                //已经申请
                holder.binding.tvMgsRoomAddFriend.text = context.getString(R.string.meta_mgs_apply)
            }
            RELATION_BOTH   -> {
                //已经是好友
                holder.binding.tvMgsRoomAddFriend.text = context.getString(R.string.added_cap)
            }
        }
        val showVoice = !isMe && mgsInteractor.canAudio()
        val showOfficial = item.isOfficial()
        holder.binding.ivVoiceState.isVisible = showVoice
        holder.binding.ivVoiceState.setImageDrawable(ContextCompat.getDrawable(context, if (item.isOpenAudio) R.drawable.icon_mgs_open_other else R.drawable.icon_mgs_close_other))
        holder.binding.tvMgsRoomUserName.setPaddingEnd(if (showOfficial) 0 else 8.dp)
        holder.binding.ivCertification.isVisible = showOfficial
        holder.binding.ivCertification.setEndMargin(if (showVoice) 15.dp else 8.dp)
    }
}