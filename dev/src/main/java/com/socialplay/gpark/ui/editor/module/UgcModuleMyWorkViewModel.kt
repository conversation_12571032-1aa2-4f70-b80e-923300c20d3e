package com.socialplay.gpark.ui.editor.module

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.model.asset.UgcModule
import com.socialplay.gpark.data.model.outfit.UgcDesignFeedRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.extension.dropAt
import com.socialplay.gpark.util.extension.getWithIndex
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import org.koin.android.ext.android.get

data class UgcModuleMyWorkState(
    val modules: Async<List<UgcModule>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val orderType: Int = UgcDesignFeedRequest.ORDER_DEFAULT,
    val page: Int = 1,
    val uniqueTag: Int = 0,
    val deleteUgcModuleResult: Async<UgcModule> = Uninitialized,
    val publishUgcModuleResult: Async<UgcModule> = Uninitialized,
    val operatingModule: Pair<UgcModule, Int>? = null,
    val tags: Async<List<UgcDesignProfileTag>> = Uninitialized,
    val filterVisibility: Boolean? = null,
    val filterTags: List<Int> = emptyList()
) : MavericksState

class UgcModuleMyWorkViewModel(
    initState: UgcModuleMyWorkState,
    private val ugcRepo: UgcRepository,
) : BaseViewModel<UgcModuleMyWorkState>(initState) {

    private val moduleSet = HashSet<String>()

    init {
        load(true)
    }

    fun load(isRefresh: Boolean, force: Boolean = false) = withState { s ->
        if (!force && s.loadMore is Loading) return@withState
        val newPage = if (isRefresh) 1 else s.page + 1
        ugcRepo.getMyModules(
            s.orderType,
            PAGE_SIZE,
            newPage,
            s.filterTags,
            s.filterVisibility
        ).map {
            if (isRefresh) {
                moduleSet.clear()
            }
            val newList = it.data?.filter { module ->
                val result = module.isSupported && !moduleSet.contains(module.feedId)
                if (result) {
                    moduleSet.add(module.feedId)
                }
                result
            }
            it.copy(
                data = if (isRefresh) {
                    newList
                } else {
                    oldState.modules().insertAt(-1, newList)
                },
                isLastPage = it.isLastPage || newList.isNullOrEmpty()
            )
        }.execute { result ->
            when (result) {
                is Success -> {
                    val wrapper = result()
                    copy(
                        modules = Success(wrapper.data.orEmpty()),
                        loadMore = Success(LoadMoreState(isEnd = wrapper.isLastPage)),
                        page = newPage,
                        uniqueTag = if (isRefresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        modules = if (isRefresh) Fail(result.error, modules()) else modules,
                        loadMore = Fail(
                            result.error,
                            value = if (isRefresh) LoadMoreState(needRefresh = true) else null
                        )
                    )
                }

                else -> {
                    copy(
                        modules = if (isRefresh) Loading(modules()) else modules,
                        loadMore = Loading()
                    )
                }
            }
        }
    }

    fun public(module: UgcModule, position: Int) = withState { s ->
        val toPublic = !module.published
        if (toPublic) {
            ugcRepo.publicUgcAsset(module.feedId)
        } else {
            ugcRepo.privateUgcAsset(module.feedId)
        }.map {
            val oldList = oldState.modules()
            val (oldPosition, oldModule) = oldList.getWithIndex(position) { e ->
                e.feedId == module.feedId
            } ?: return@map null
            if (oldModule.published == toPublic) return@map null
            val newModule = oldModule.copy(published = toPublic)
            oldList.replaceAt(oldPosition, newModule)
        }.execute {
            when (it) {
                is Success -> {
                    val newList = it()
                    copy(
                        modules = if (newList != null) {
                            modules.copyEx(newList)
                        } else {
                            modules
                        },
                        publishUgcModuleResult = Success(module)
                    )
                }

                else -> {
                    copy(publishUgcModuleResult = it.map { module })
                }
            }
        }
    }

    fun delete(module: UgcModule, position: Int) = withState { s ->
        ugcRepo.deleteUgcAsset(module.feedId).map {
            val oldList = oldState.modules()
            oldList.dropAt(position, module)
        }.execute {
            when (it) {
                is Success -> {
                    val newList = it()
                    copy(
                        modules = if (newList != null) {
                            modules.copyEx(newList)
                        } else {
                            modules
                        },
                        deleteUgcModuleResult = Success(module)
                    )
                }

                else -> {
                    copy(deleteUgcModuleResult = it.map { module })
                }
            }
        }
    }

    fun updateFilter(orderType: Int, visibility: Boolean?, tags: List<Int>) = withState {
        setState { copy(orderType = orderType, filterVisibility = visibility, filterTags = tags) }
        load(true)
    }

    fun fetchTagsIfNeed() = withState { s ->
        if (s.tags.shouldLoad) {
            ugcRepo.getUgcAssetTagTree().map {
                it.find { it.code == 5 }?.children?.map { it.toUgcDesignProfileTag() }.orEmpty()
            }.execute { copy(tags = it) }
        }
    }

    fun operateModule(ugcModule: UgcModule, position: Int) = withState { s ->
        setState { copy(operatingModule = ugcModule to position) }
    }

    fun edit(itemId: String, title: String?, desc: String?) = withState { s ->
        val oldList = s.modules()
        val (oldModuleIdx, oldModule) = oldList.getWithIndex { it.feedId == itemId }
            ?: return@withState
        val newList = oldList.replaceAt(oldModuleIdx, oldModule.copy(title = title, comment = desc))
        setState { copy(modules = modules.copyEx(newList)) }
    }

    override fun onCleared() {
        moduleSet.clear()
        super.onCleared()
    }

    companion object :
        KoinViewModelFactory<UgcModuleMyWorkViewModel, UgcModuleMyWorkState>() {

        const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcModuleMyWorkState
        ): UgcModuleMyWorkViewModel {
            return UgcModuleMyWorkViewModel(state, get())
        }
    }
}