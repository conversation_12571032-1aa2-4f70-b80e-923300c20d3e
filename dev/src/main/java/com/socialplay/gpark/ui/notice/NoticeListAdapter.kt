package com.socialplay.gpark.ui.notice

import android.graphics.Color
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import com.socialplay.gpark.databinding.AdapterEditorNoticeBinding
import com.socialplay.gpark.databinding.AdapterSystemNoticeItemBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.toLongOrZero
import java.util.concurrent.TimeUnit

class NoticeListAdapter(private val glide: RequestManager, private val onEditorJumpClick: (EditorNotice.Notice) -> Unit) : BasePagingDataAdapter<NoticeWrapper, ViewBinding>(DIFF_ITEM_CALLBACK) {

    companion object {
        private val TIMESTAMP_DISPLAY_INTERVAL = TimeUnit.MINUTES.toMillis(30)

        private val DIFF_ITEM_CALLBACK = object : DiffUtil.ItemCallback<NoticeWrapper>() {
            override fun areItemsTheSame(oldItem: NoticeWrapper, newItem: NoticeWrapper): Boolean {
                return oldItem.type == newItem.type
                        && oldItem.editorNotice?.archiveId == newItem.editorNotice?.archiveId
                        && oldItem.systemNotice?.id == newItem.systemNotice?.id
            }

            override fun areContentsTheSame(oldItem: NoticeWrapper, newItem: NoticeWrapper): Boolean {
                return oldItem.type == newItem.type
                        && oldItem.editorNotice == newItem.editorNotice
                        && oldItem.systemNotice == newItem.systemNotice
            }
        }
    }

    override fun convert(holder: BindingViewHolder<ViewBinding>, item: NoticeWrapper, position: Int) {
        if (item.isEditorNotice()) {
            item.editorNotice?.let {
                convertEditorView(holder.binding as AdapterEditorNoticeBinding, it, position)
            }
        } else if (item.isSystemNotice()) {
            item.systemNotice?.let {
                convertSystemView(holder.binding as AdapterSystemNoticeItemBinding, it, position)
            }
        }
    }

    /**
     * 系统通知
     */
    private fun convertSystemView(binding: AdapterSystemNoticeItemBinding, item: UniJumpConfig, position: Int) {
        binding.clMessageContent.clipToOutline = true
        binding.tvNoticeTitle.text = item.title.toString()
        // TODO 更换IM运营位接口后, 需要测试一下此字段是否显示正常
        binding.tvNoticeContent.text = item.param1.toString()

        val nextItem = if (position >= itemCount - 1) null else getItem(position + 1)
        val nextMessageTimestamp = if (nextItem == null) {
            0L
        } else if (nextItem.systemNotice != null) {
            nextItem.systemNotice.effectiveTimeBegin.toLongOrZero
        } else if (nextItem.editorNotice != null) {
            nextItem.editorNotice.sendTimeLong
        } else {
            0L
        }

        //两个item的时间间隔小于30分钟不显示时间戳
        val duration = item.effectiveTimeBegin.toLongOrZero - nextMessageTimestamp
        binding.tvSendTimeNotice.visible(duration >= TIMESTAMP_DISPLAY_INTERVAL)

        binding.tvSendTimeNotice.text = DateUtil.getFormatTimeByTimeStamp(item.effectiveTimeBegin.toLongOrZero)

        glide.load(item.iconUrl).placeholder(R.drawable.placeholder_notice_img).into(binding.ivCover)
    }

    /**
     * 移动编辑器通知
     */
    private fun convertEditorView(binding: AdapterEditorNoticeBinding, item: EditorNotice.Notice, position: Int) {
        binding.tvSendTimeNotice.text = DateUtil.getFormatTimeByTimeStamp(item.sendTimeLong)
        binding.tvNoticeContent.movementMethod = LinkMovementMethod()
        binding.tvNoticeContent.text = SpannableHelper.Builder()
            .text(item.content)
            .text(context.getString(R.string.notice_my_build_prefix))
            .text(context.getString(R.string.notice_my_build))
            .click(NoticeSpanClick {
                onEditorJumpClick(item)
            }).build()
    }

    class NoticeSpanClick(val call: () -> Unit) : ClickableSpan() {
        override fun onClick(widget: View) {
            call()
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.isUnderlineText = false
            ds.color = Color.parseColor("#2194FE")
        }
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): ViewBinding {
        return if (viewType == NoticeWrapper.TYPE_EDITOR) {
            AdapterEditorNoticeBinding.inflate(layoutInflater, parent, false)
        } else {
            AdapterSystemNoticeItemBinding.inflate(layoutInflater, parent, false)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return getItem(position)?.type ?: 1
    }
}