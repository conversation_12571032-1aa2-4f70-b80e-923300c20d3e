package com.socialplay.gpark.ui.web.jsinterfaces.ext

import android.webkit.JavascriptInterface
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.runBlocking
import org.json.JSONArray

/**
 * 游戏相关js接口
 * <AUTHOR>
 * @date 2021/05/24
 */
@JavascriptInterface
fun JsBridgeApi.isGameInstall(paramArray: JSONArray): String {

    val gamePackage = paramArray.optString(0)

    if (gamePackage.isNullOrBlank()) {
        return createErrorResult(msg = "function isGameInstall() params gamePackage isBlank")
    }
    return createSuccessResult(data = "false")
}


/**
 * TS游戏相关js接口
 */
@JavascriptInterface
fun JsBridgeApi.isTsGameInstall(paramArray: JSONArray): String {

    val gamePackage = paramArray.optString(0)

    if (gamePackage.isNullOrBlank()) {
        return createErrorResult(msg = "function isGameInstall() params gamePackage isBlank")
    }
    val isInstall = runBlocking {
        val isInstall = MWBiz.isAvailable()
        isInstall
    }
    return createSuccessResult(data = "$isInstall")
}

/**
 * 第一个参数是下载进度，这里已经忽略掉了，旧版本存在这个bug ，会导致点击下载突然0 跑到30% 40%这样
 */
fun JsBridgeApi.downloadGame(paramArray: JSONArray): String {
    return createErrorResult(msg = "download game failed")
}

fun JsBridgeApi.downloadStop(paramArray: JSONArray): String {
    return createErrorResult(msg = "download stop failed")
}

fun JsBridgeApi.playGame(paramArray: JSONArray): String {
    val gameJson: String = paramArray.optString(0)
    val webDownload = GsonUtil.gsonSafeParse<GameDetailInfo>(gameJson)
    val result = webDownload?.let { appInfo ->
        val launchSuccess = runBlocking {
            val launchSuccess = if (!appInfo.isTSGame()) {
               false
            } else {
                val channel = Channel<Boolean>(Channel.CONFLATED)
                tsLaunch.onLaunchListener {
                    onLaunchGameEnd { params, e ->
                        channel.trySend(e == null)
                    }
                }
                val gameInfo = tsLaunch.createTSGameDetailInfo( appInfo.id, appInfo.packageName, appInfo.name, appInfo.startupExtension)
                val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.WEB_GAME)
                val params = TSLaunchParams(gameInfo, resIdBean)
                tsLaunch.launch(helper.contract.requireContext(), params)
                runCatching { channel.receive() }.getOrElse { false }
            }
            launchSuccess
        }
        launchSuccess
    } ?: false
    if (!result) return createErrorResult(msg = "play game failed")
    return createSuccessResult(data = "")
}
