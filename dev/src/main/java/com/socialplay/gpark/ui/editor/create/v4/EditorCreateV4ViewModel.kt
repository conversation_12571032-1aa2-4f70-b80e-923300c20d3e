package com.socialplay.gpark.ui.editor.create.v4

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.FormWorkV4Info
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.TagInfo
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.extension.addAllImmutable
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/06
 *     desc   :
 * </pre>
 */
data class EditorCreateV4State(
    val banners: Async<List<UniJumpConfig>> = Uninitialized,
    val tags: List<TagInfo>? = null,
    val templates: Async<List<FormWorkV4Info>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val tagId: Int? = null,
    val archive: Async<UgcFormWorkArchiveData?> = Uninitialized,
    val isFirstLoadComplete: Boolean = false,
) : MavericksState

class EditorCreateV4ViewModel(
    initialState: EditorCreateV4State,
    val repo: IMetaRepository
) : BaseViewModel<EditorCreateV4State>(initialState) {

    fun fetchBanners() {
        repo.getUgcBannerList().map {
            check(it.succeeded)
            it.data?.take(10).orEmpty()
        }.execute {
            copy(banners = it)
        }
    }

    fun fetchTemplates(isRefresh: Boolean, tagInfo: TagInfo? = null) = withState { s ->
        if (s.loadMore is Loading) return@withState
        val targetPage = if (isRefresh) {
            1
        } else {
            s.page + 1
        }
        repo.getFormWorkV4ListMvrk(ReqFormWorkV4Body(targetPage, if (tagInfo != null) tagInfo.tagId else s.tagId)).execute { result ->
            when (result) {
                is Success -> {
                    copy(
                        tags = tags ?: result().tagList,
                        templates = if (isRefresh) {
                            Success(result().formworkGameList.orEmpty())
                        } else {
                            templates.map {
                                it.addAllImmutable(result().formworkGameList).orEmpty()
                            }
                        },
                        loadMore = Success(LoadMoreState(isEnd = result().end || result().formworkGameList.isNullOrEmpty())),
                        page = targetPage
                    )
                }

                is Fail -> {
                    copy(
                        templates = if (isRefresh) Fail(result.error, templates()) else templates,
                        loadMore = Fail(result.error)
                    )
                }

                else -> {
                    copy(
                        templates = if (isRefresh) Loading(templates()) else templates,
                        loadMore = Loading(),
                        tagId = if (tagInfo != null) tagInfo.tagId else tagId
                    )
                }
            }
        }
    }

    fun checkTemplate(gameCode: String, archiveId: String) = withState { s ->
        if (s.archive is Loading) return@withState
        repo.checkFormWorkArchiveMvrk(ReqFormWorkArchiveBody(gameCode, archiveId)).map {
            it?.copy(gameCode = gameCode, archiveId = archiveId)
        }.execute {
            copy(archive = it)
        }
    }

    // 添加状态更新方法
    fun markFirstLoadComplete() = setState { copy(isFirstLoadComplete = true) }

    companion object : KoinViewModelFactory<EditorCreateV4ViewModel, EditorCreateV4State>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: EditorCreateV4State
        ): EditorCreateV4ViewModel {
            return EditorCreateV4ViewModel(state, get())
        }
    }
}