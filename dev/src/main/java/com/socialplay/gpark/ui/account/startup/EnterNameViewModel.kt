package com.socialplay.gpark.ui.account.startup

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/11/09
 *     desc   :
 * </pre>
 */
data class EnterNameState(
    val validateResult: Async<String> = Uninitialized,
    val updateProfileResult: Async<Boolean> = Uninitialized,
    val chooseRoleResult: Async<Boolean> = Uninitialized
) : MavericksState

class EnterNameViewModel(
    private val context: Application,
    private val metaKV: MetaKV,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    private val editorGameLoadInteractor: EditorGameLoadInteractor,
    initialState: EnterNameState
) : BaseViewModel<EnterNameState>(initialState) {

    fun validateName(name: String) {
        repo.checkNickname(name, accountInteractor.curUuid).map {
            assert(it) { context.getString(R.string.common_failed) }
            name
        }.execute { copy(validateResult = it) }
    }

    fun updateProfile(name: String, avatar: DefaultRoleInfo) = withState { s ->
        if (s.updateProfileResult.shouldLoad) {
            accountInteractor.updateUser(
                mNickname = name,
                reviewBirth = false
            ).map {
                assert(it.succeeded && it.data == true) {
                    it.message.ifNullOrBlank {
                        context.getString(
                            R.string.common_failed
                        )
                    }
                }
                true
            }.execute { copy(updateProfileResult = it) }
        }
        if (s.chooseRoleResult.shouldLoad) {
            editorGameLoadInteractor.chooseRole(avatar).map {
                assert(it.succeeded && it.data == true) {
                    it.message.ifNullOrBlank {
                        context.getString(
                            R.string.common_failed
                        )
                    }
                }
                true
            }.execute { copy(chooseRoleResult = it) }
        }
    }

    fun updateProfileWithAllInfo(name: String, avatar: DefaultRoleInfo, age: Int? = null, gender: Int? = null) = withState { s ->
        if (s.updateProfileResult.shouldLoad) {
            // 如果有年龄信息，计算生日
            val birthday = if (age != null) {
                val calendar = java.util.Calendar.getInstance()
                calendar.set(java.util.Calendar.MONTH, 0)
                calendar.set(java.util.Calendar.DATE, 1)
                val birthYear = calendar.get(java.util.Calendar.YEAR) - age
                calendar.set(java.util.Calendar.YEAR, birthYear)
                
                // 如果有年龄信息，添加分析跟踪
                com.socialplay.gpark.function.analytics.Analytics.track(
                    com.socialplay.gpark.function.analytics.EventConstants.BIRTHDAY_PAGE_PLAY_CLICK,
                    "age" to age
                )
                
                if (age in 9..18) {
                    com.socialplay.gpark.function.analytics.Analytics.track(
                        com.socialplay.gpark.function.analytics.EventConstants.EVENT_AF_AGE_9_18
                    )
                }
                
                calendar.time.time
            } else null

            accountInteractor.updateUser(
                mNickname = name,
                mBirthday = birthday,
                mGender = gender ?: -1,
                reviewBirth = age != null // 如果有年龄信息才进行生日审核
            )
                .map {
                    assert(it.succeeded && it.data == true) {
                        it.message.ifNullOrBlank {
                            context.getString(
                                R.string.common_failed
                            )
                        }
                    }
                    true
                }
                .execute { copy(updateProfileResult = it) }
        }
        if (s.chooseRoleResult.shouldLoad) {
            editorGameLoadInteractor.chooseRole(avatar).map {
                assert(it.succeeded && it.data == true) {
                    it.message.ifNullOrBlank {
                        context.getString(
                            R.string.common_failed
                        )
                    }
                }
                true
            }.execute { copy(chooseRoleResult = it) }
        }
    }

    fun backupChooseRole(avatar: DefaultRoleInfo) {
        metaKV.account.setBackupChooseRole(avatar)
    }

    companion object : KoinViewModelFactory<EnterNameViewModel, EnterNameState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: EnterNameState
        ): EnterNameViewModel {
            return EnterNameViewModel(get(), get(), get(), get(), get(), state)
        }
    }

}