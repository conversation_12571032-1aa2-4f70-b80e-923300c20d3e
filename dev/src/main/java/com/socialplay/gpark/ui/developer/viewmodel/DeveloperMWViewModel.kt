package com.socialplay.gpark.ui.developer.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.kv.MetaKV
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * xingxiu.hou
 * 2022/2/10
 */
class DeveloperMWViewModel(private val metaApi: MetaApi, private val metaKv: MetaKV) : ViewModel() {

    private val _engineVersions = MutableLiveData<List<String>>()
    val engineVersions = _engineVersions

    fun getEngineVersionList() = viewModelScope.launch {
        runCatching {
            val host = metaKv.mw.coreHotfixUrl.let {
                Timber.d("META#VERSE:: coreHotfixUrl $it")
                it.ifEmpty { BuildConfig.MW_CORE_URL }
            }
            val postFix = if (EnvConfig.isParty()) {
                "/resource/Android/Versions"
            } else {
                "/Android/Versions"
            }
            metaApi.getMWEngineVersions("$host$postFix").versions
        }.getOrElse { listOf() }.also {
            _engineVersions.postValue(it)
        }
    }

}