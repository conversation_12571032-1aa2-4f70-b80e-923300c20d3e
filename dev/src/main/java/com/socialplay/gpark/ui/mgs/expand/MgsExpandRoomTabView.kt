package com.socialplay.gpark.ui.mgs.expand

import android.app.Application
import android.view.LayoutInflater
import android.widget.RelativeLayout
import com.meta.biz.mgs.data.model.Member
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewMgsExpandRoomBinding
import com.socialplay.gpark.ui.mgs.adapter.MgsExpandRoomAdapter
import com.socialplay.gpark.ui.mgs.listener.OnMgsExpandRoomTabListener
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.visible

class MgsExpandRoomTabView(val app: Application, val metaApp: Application, val listener: OnMgsExpandRoomTabListener) :
    RelativeLayout(metaApp) {

    private lateinit var binding: ViewMgsExpandRoomBinding
    private lateinit var mgsRoomAdapter: MgsExpandRoomAdapter

    init {
        initView()
        initData()
    }

    private fun initData() {
        updateRoomPlayerNum()
    }

    private fun initView() {
        binding = ViewMgsExpandRoomBinding.inflate(LayoutInflater.from(metaApp), this, true)
        binding.vMgsRoomLine.visible(ScreenUtil.isHorizontalScreen(context))
        mgsRoomAdapter = MgsExpandRoomAdapter()
        binding.rvMgsRoomUser.itemAnimator = null
        binding.rvMgsRoomUser.adapter = mgsRoomAdapter
        binding.tvMgsRoomPlayerNum.text = metaApp.getString(R.string.mgs_room_member_num, 0)
        mgsRoomAdapter.addChildClickViewIds(R.id.tvMgsRoomAddFriend, R.id.rlMgsRoomItemRoot, R.id.ivVoiceState)
        mgsRoomAdapter.setOnItemChildClickListener { view, position ->
            when (view.id) {
                R.id.tvMgsRoomAddFriend -> {
                    mgsRoomAdapter.data[position].let { listener.addFriendByUuid(it.uuid) }
                }
                R.id.rlMgsRoomItemRoot  -> {
                    mgsRoomAdapter.data[position].openId.let { listener.showUserCardByOpenId(it) }
                }
                R.id.ivVoiceState -> {
                    mgsRoomAdapter.data[position].let { listener.changeMuteState(!it.isOpenAudio, it.openId, "2") }
                }
            }
        }
    }

    /**
     * 更新房间成员列表
     */
    fun updateRoomMemberList(memberList: MutableList<Member>) {
        mgsRoomAdapter.clearList()
        mgsRoomAdapter.addData(memberList)
        updateRoomPlayerNum()
    }

    private fun updateRoomPlayerNum() {
        binding.tvMgsRoomPlayerNum.text = metaApp.getString(R.string.mgs_room_member_num, mgsRoomAdapter.data.size)
    }

    /**
     * 增加房间成员
     */
    fun addRoomUser(user: Member) {
        mgsRoomAdapter.addData(user)
        updateRoomPlayerNum()
    }

    /**
     * 移除房间成员
     */
    fun removeRoomUser(user: Member) {
        val position = getMemberPosition(user)
        mgsRoomAdapter.removeAt(position)
        updateRoomPlayerNum()
    }

    /**
     * 更新房间成员
     */
    fun updateRoomUser(user: Member) {
        val position = getMemberPosition(user)
        mgsRoomAdapter.setData(position, user)
        updateRoomPlayerNum()
    }

    fun updateOtherVoiceState(user: Member) {
        val position = getMemberPosition(user)
        if (position in 0 until mgsRoomAdapter.data.size) {
            mgsRoomAdapter.setData(position, user)
        }
    }

    /**
     * 获取房间成员位置
     */
    private fun getMemberPosition(user: Member): Int {
        mgsRoomAdapter.data.forEachIndexed { index, member ->
            if (user.openId == member.openId) {
                return index
            }
        }
        return -1
    }

}