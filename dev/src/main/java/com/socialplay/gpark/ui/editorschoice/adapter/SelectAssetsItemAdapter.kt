package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemSelectAssetsBinding
import com.socialplay.gpark.ui.editorschoice.adapter.BaseEditorsChoiceItemAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import android.view.View
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.dp

/**
 * Select Assets item adapter
 */
class SelectAssetsItemAdapter(
    data: MutableList<ChoiceGameInfo>?,
    private val glide: RequestManager
) : BaseEditorsChoiceItemAdapter<AdapterChoiceCardItemSelectAssetsBinding>(data) {

    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterChoiceCardItemSelectAssetsBinding {
        return AdapterChoiceCardItemSelectAssetsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterChoiceCardItemSelectAssetsBinding>,
        item: ChoiceGameInfo
    ) {
        holder.binding.apply {
            // Load asset image
            glide.load(item.imageUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivAssetIcon)
            
            // Set asset title
            tvAssetTitle.text = item.displayName
            
            // Set price (using description field for price info)
            if (!item.description.isNullOrEmpty()) {
                tvAssetPrice.text = item.description
                tvAssetPrice.visibility = View.VISIBLE
            } else {
                tvAssetPrice.visibility = View.GONE
            }
            
            // Set creator info if available
            if (!item.nickname.isNullOrEmpty()) {
                tvCreatorName.text = item.nickname
                tvCreatorName.visibility = View.VISIBLE
            } else {
                tvCreatorName.visibility = View.GONE
            }
            
            // Set like count or other stats
            item.likeCount?.let { count ->
                if (count > 0) {
                    tvStats.text = "${count} likes"
                    tvStats.visibility = View.VISIBLE
                } else {
                    tvStats.visibility = View.GONE
                }
            } ?: run {
                tvStats.visibility = View.GONE
            }
            
            // Set tags if available
            if (!item.tagList.isNullOrEmpty()) {
                val firstTag = item.tagList?.firstOrNull()
                if (!firstTag.isNullOrEmpty()) {
                    tvAssetTag.text = firstTag
                    tvAssetTag.visibility = View.VISIBLE
                } else {
                    tvAssetTag.visibility = View.GONE
                }
            } else {
                tvAssetTag.visibility = View.GONE
            }
        }
    }
}
