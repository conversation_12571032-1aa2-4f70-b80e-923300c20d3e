package com.socialplay.gpark.ui.dialog

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.databinding.AdapterSimpleButtonBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * create by: bin on 2023/3/8
 */
class SimpleButtonAdapter: BaseAdapter<SimpleListData, AdapterSimpleButtonBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int,
    ): AdapterSimpleButtonBinding {
        return AdapterSimpleButtonBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<AdapterSimpleButtonBinding>,
        item: SimpleListData,
        position: Int,
    ) {
        holder.binding.btnCancel.text = item.text
        holder.binding.btnCancel.setBackgroundResource(item.bgResource)
        holder.binding.btnCancel.setTextColor(ContextCompat.getColor(context,item.textColor))
    }
}