package com.socialplay.gpark.ui.im.conversation

import android.os.Bundle
import android.os.SystemClock
import android.text.TextWatcher
import android.view.View
import android.view.ViewTreeObserver
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.databinding.DialogEditGroupChatNameBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.LengthCallbackFilter
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ime.KeyboardListener
import com.socialplay.gpark.util.ime.WindowInsetsHelper
import com.socialplay.gpark.util.property.viewBinding

class EditGroupChatNameDialog : BaseDialogFragment() {
    private val vm: EditGroupChatInfoViewModel by fragmentViewModel()
    private val args by navArgs<EditGroupChatNameDialogArgs>()

    companion object {
        const val MAX_LENGTH = 32
        const val REQUEST_KEY_GROUP_EDIT_NAME = "request_key_group_edit_name"
        const val KEY_GROUP_EDIT_NAME_RESULT = "key_group_edit_name_result"
    }

    override fun getStyle() = R.style.DialogStyleV2_Input_NoAnimation

    override val binding: DialogEditGroupChatNameBinding by viewBinding(
        DialogEditGroupChatNameBinding::inflate
    )

    override fun maxWidth(): Int {
        val ctx = context
        ctx ?: return 0
        return (ScreenUtil.getScreenWidth(ctx) * 2 / 3).coerceAtLeast(dp(343))
            .coerceAtMost(ScreenUtil.getScreenWidth(ctx) - dp(30))
    }

    private var textWatcher: TextWatcher? = null

    private var toastTs = 0L

    private var windowInsetsHelper: WindowInsetsHelper = WindowInsetsHelper()

    private val keyboardListener = object : KeyboardListener() {
        override fun onProgress(keyboardHeight: Int) {
            if (isBindingAvailable()) {
                binding.dialogLayout.translationY = (-keyboardHeight).toFloat()
            }
        }

        override fun onKeyboardHideEnd() {
            if (isBindingAvailable()) {
                binding.dialogLayout.translationY = 0f
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = dialog?.window
        if (window != null) {
            windowInsetsHelper.apply(
                window,
                binding.root,
                keyboardListener
            )
        }
    }

    private var showKeyboardFirstTime = true
    override fun onResume() {
        super.onResume()
        if (showKeyboardFirstTime) {
            showKeyboardFirstTime = false
            binding.etInput.requestFocusFromTouch()
            // DialogFragment 在 onResume 的时候可能还没准备好接收输入法, 所以需要等到 WindowFocusChange 的时候做一次兜底
            dialog?.window?.decorView?.viewTreeObserver?.addOnWindowFocusChangeListener(
                object : ViewTreeObserver.OnWindowFocusChangeListener {
                    override fun onWindowFocusChanged(hasFocus: Boolean) {
                        if (hasFocus) {
                            InputUtil.showSoftBoard(binding.etInput)
                            dialog?.window?.decorView?.viewTreeObserver?.removeOnWindowFocusChangeListener(
                                this
                            )
                        }
                    }
                })
            InputUtil.showSoftBoard(binding.etInput)
        }
    }

    override fun init() {
        binding.etInput.filters = arrayOf(LengthCallbackFilter(MAX_LENGTH) {
            context?.let {
                val curTs = SystemClock.elapsedRealtime()
                if (curTs - toastTs > 2000) {
                    toastTs = curTs
                    toast(getString(R.string.up_to_x_chars, MAX_LENGTH))
                }
            }
        })
        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.dialogLayout.isClickable = true

        textWatcher = binding.etInput.doAfterTextChanged { text ->
            val hasContent = !text.isNullOrBlank()
            if (hasContent) {
                binding.tvSave.isEnabled = true
                binding.tvSave.setBackgroundResource(R.drawable.bg_ffef30_round_40)
                binding.tvSave.setTextColorByRes(R.color.color_1A1A1A)
            } else {
                binding.tvSave.isEnabled = false
                binding.tvSave.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
                binding.tvSave.setTextColorByRes(R.color.color_999999)
            }
            binding.ivClearText.visible(hasContent)
            binding.tvWordCount.text = SpannableHelper.Builder()
                .text("(${text?.length ?: 0}/")
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsSemiBold600)
                .text("$MAX_LENGTH)")
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsRegular400)
                .build()
        }
        binding.ivClearText.setOnClickListener {
            binding.etInput.text?.clear()
        }
        binding.tvSave.setOnAntiViolenceClickListener {
            val text = binding.etInput.text?.toString()?.trim()
            if (!text.isNullOrEmpty()) {
                vm.editGroupChatName(args.groupId, text)
            }
        }
        binding.tvCancel.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        binding.etInput.setText(args.currentGroupName)
        vm.onAsync(
            EditGroupChatInfoModelState::editResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                ToastUtil.showShort(R.string.toast_edit_group_name_failed)
            },
            onLoading = {

            },
            onSuccess = { groupDetailResult ->
                if (groupDetailResult.succeeded) {
                    dismissWithResult(groupDetailResult.data)
                } else {
                    ToastUtil.showShort(
                        groupDetailResult.message
                            ?: getString(R.string.toast_edit_group_name_failed)
                    )
                }
            })
    }

    private fun dismissWithResult(groupDetail: GroupChatDetailInfo?) {
        if (groupDetail != null) {
            setFragmentResult(REQUEST_KEY_GROUP_EDIT_NAME, Bundle().apply {
                putParcelable(
                    KEY_GROUP_EDIT_NAME_RESULT, groupDetail.simplifiedMembers()
                )
            })
        }
        dismissAllowingStateLoss()
    }

    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInput.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        windowInsetsHelper.unApply()
        super.onDestroyView()
    }
}