package com.socialplay.gpark.ui.developer.migrate

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.databinding.FragmentEditorMigrateBinding
import com.socialplay.gpark.function.editor.Migrate233Util
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import timber.log.Timber
import java.io.File


/**
 * Created by bo.li
 * Date: 2023/4/6
 * Desc: 迁移国内工程到海外
 */
class MigrateLocalDraftFragment: BaseFragment<FragmentEditorMigrateBinding>() {
    val metaKV by inject<MetaKV>()

    private var activityResultLauncher: ActivityResultLauncher<Intent>? = null
    private val overseaDraftRootFile by lazy { File("${Environment.getExternalStorageDirectory()}/MigrateDraft/Oversea") }

    companion object {
        private const val TAG = "checkcheck_migrate"
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorMigrateBinding? {
        return FragmentEditorMigrateBinding.inflate(inflater, container, false)
    }

    override fun init() {
        activityResultLauncher = registerForActivityResult(ManageResultContract()) {}
        binding.tvEnv.text = "${getString(R.string.debug_current_env)}：${metaKV.developer.envType}, ${if (BuildConfig.DEBUG) "debug" else "release"}"
        binding.refresh.setOnRefreshListener {
            refresh()
        }
        binding.title.setOnBackClickedListener {
            findNavController().navigateUp()
        }
        binding.tvBtn.setOnAntiViolenceClickListener {
            binding.loading.showLoading()
            getPermission {
                if (it) {
                    viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                        val result = Migrate233Util.handle233Migrate(overseaDraftRootFile)
                        binding.et.setText(result)
                        ToastUtil.showLong(requireContext(), R.string.debug_migrate_finish)
                        binding.loading.hide()
                        refresh()
                    }
                } else {
                    binding.loading.hide()
                    ToastUtil.showLong(requireContext(), R.string.debug_migrate_out_storage)
                }
            }
        }
    }

    private fun refresh() {
        getPermission {
            if (it) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    binding.tvSize.text = "${countSize()}"
                }
            } else {
                ToastUtil.showLong(requireContext(), R.string.debug_migrate_out_storage2)
            }
            binding.refresh.isRefreshing = false
        }
    }

    class ManageResultContract : ActivityResultContract<Intent, Intent?>() {
        override fun createIntent(context: Context, input: Intent): Intent {
            return input
        }

        override fun parseResult(resultCode: Int, data: Intent?): Intent? {
            return data
        }

    }

    private fun getPermission(callback: (Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 先判断有没有权限
            if (Environment.isExternalStorageManager()) {
                callback(true)
            } else {
                goGetManagePermission()
                callback(false)
            }
        } else {
            PermissionRequest.with(requireActivity()).permissions(Permission.EXTERNAL_STORAGE).des(getString(R.string.debug_need_out_storage_permission2)).enableGoSettingDialog().granted {
                callback(true)
            }.denied {
                callback(false)
            }.request()
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun goGetManagePermission() {
        val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
        intent.data = Uri.parse("package:" + requireContext().packageName)
        activityResultLauncher?.launch(intent)
    }

    private suspend fun countSize(): Int {
        return withContext(Dispatchers.IO) {
            return@withContext kotlin.runCatching {
                val root = overseaDraftRootFile
                if (!root.exists()){
                    Timber.tag(TAG).d("root == null || !root.exists()")
                    return@withContext 0
                }
                root.listFiles()?.filter { it.extension == "zip"}?.size ?:0
            }.getOrElse {
                Timber.tag(TAG).e("countSize: ${it}")
                withContext(Dispatchers.Main) {
                    toast("$it")
                }
                0
            }
        }
    }

    override fun loadFirstData() {
        refresh()
    }

    override fun getFragmentName(): String = "MigrateLocalDraftFragment"
}