package com.socialplay.gpark.ui.view.voice

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemMyVoiceBinding
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/12/9
 * Desc:
 */
class WaveVoiceView: RelativeLayout{
    private lateinit var bind: ItemMyVoiceBinding

    private var volumeListener: IClickVolumeListener? = null
    internal var isOpenVoice = false

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        bind = ItemMyVoiceBinding.inflate(LayoutInflater.from(context), this, true)

        updateVoiceState(false)
    }

    fun setVolumeListener(listener: IClickVolumeListener) {
        bind.ivVoiceState.setOnAntiViolenceClickListener {
            if (isOpenVoice) {
                volumeListener?.onMute()
            } else {
                volumeListener?.onUnmute()
            }
        }
        volumeListener = listener
    }

    fun updateVoiceState(isOpen: Boolean, lastVolume: Int = 0, nowVolume: Int = 0) {
        isOpenVoice = isOpen
        val isSpeaking = isOpen && nowVolume > 0
        bind.waveVoice.isVisible = isSpeaking
        if (isSpeaking) {
            // 播放动画
            bind.ivVoiceState.setImageDrawable(context.getDrawable(R.drawable.icon_voice_open_speak))
            updateVolume(lastVolume, nowVolume)
        } else if (isOpen) {
            bind.ivVoiceState.setImageDrawable(context.getDrawable(R.drawable.icon_voice_me_open))
            closeVolumeAnim()
        } else {
            bind.ivVoiceState.setImageDrawable(context.getDrawable(R.drawable.icon_voice_close))
            closeVolumeAnim()
        }
    }

    private fun updateVolume(lastVolume: Int, nowVolume: Int) {
        bind.waveVoice.startAnimation(getWaveDuration(lastVolume, nowVolume)) {
            bind.ivVoiceState.setImageDrawable(context.getDrawable(R.drawable.icon_voice_me_open))
        }
    }

    private fun closeVolumeAnim() {
        bind.waveVoice.cancelAnimation()
    }

    fun resetVoiceView() {
        updateVoiceState(false)
    }

    /**
     * 动画时长
     */
    private fun getWaveDuration(last: Int? = 0, nowVolume: Int? = 125): Long {
        val maxV = 255
        val maxD = 1000
        val duration = (nowVolume?.toFloat() ?: 255f) / maxV * maxD
        var space = maxD - duration
        if (space < 500) {
            space = 500f
        }
        return space.toLong()
    }

    interface IClickVolumeListener {
        fun onMute()
        fun onUnmute()
    }
}