package com.socialplay.gpark.ui.editorschoice.header.friends

import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfoDiff
import com.socialplay.gpark.databinding.AdapterItemFriendsBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.dp

/**
 * 2023/8/11
 */
class ChoiceHomeHeaderFriendsAdapter(
    private val glide: RequestManager,
    private val itemWidth: Int
) :
    BaseDifferAdapter<ChoiceFriendInfo, AdapterItemFriendsBinding>(ChoiceFriendInfoDiff) {

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterItemFriendsBinding {
        return AdapterItemFriendsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterItemFriendsBinding>,
        item: ChoiceFriendInfo,
    ) {
        if (item.isAdd()) {
            showAddItem(holder.binding, item)
        } else {
            showUserItem(holder.binding, item)
        }
    }

    private fun showAddItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        binding.iv.updateLayoutParams {
            width = itemWidth
        }
        binding.root.updateLayoutParams<MarginLayoutParams> {
            marginStart = 5.dp
            marginEnd = 12.dp
        }
        binding.iv.setImageResource(item.placeholderIcon)
        binding.ivOnline.isVisible = false
        binding.tvUserName.text = item.userName
        binding.tvGameName.isVisible = false
        binding.tvEmptyHint.isVisible = item.showHint
    }

    private fun showUserItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        binding.iv.updateLayoutParams {
            width = itemWidth
        }
        binding.root.updateLayoutParams<MarginLayoutParams> {
            marginEnd = 12.dp
        }
        binding.tvEmptyHint.isVisible = false
        glide.load(item.userAvatar)
            .placeholder(item.placeholderIcon)
            .transform(CircleCrop())
            .into(binding.iv)
        binding.tvUserName.text = item.userName
        binding.tvGameName.isVisible = false
        binding.ivOnline.isVisible = false
        if (item.isInGame()) {
            binding.ivOnline.isVisible = true
            binding.tvGameName.apply {
                isVisible = true
                text = item.gameName
            }
        } else {

            binding.ivOnline.isVisible = item.isOnline()
        }
    }
}