package com.socialplay.gpark.ui.room

import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants

/**
 * @des:
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/7/14 18:43
 */
object HomeRoomAnalytics {


    fun trackRoomShow(chatRoomInfo: ChatRoomInfo, categoryId: Int) {
        Analytics.track(
            EventConstants.EVENT_SHOW_CHATROOM_ROOM_CARD,
            "show_categoryid" to categoryId,
            "roomname" to chatRoomInfo.roomName,
            "gameId" to chatRoomInfo.platformGameId,
            "theme" to (chatRoomInfo.tag ?: ""),
            "roomid" to chatRoomInfo.roomId,
            "online" to chatRoomInfo.number,
            "alivetime" to chatRoomInfo.aliveTime,
            "style" to chatRoomInfo.getRoomStyle()
        )
    }

    fun trackRoomClick(chatRoomInfo: ChatRoomInfo, categoryId: Int) {
        Analytics.track(
            EventConstants.EVENT_CLICK_CHATROOM_ROOM_CARD,
            "show_categoryid" to categoryId,
            "roomname" to chatRoomInfo.roomName,
            "gameId" to chatRoomInfo.platformGameId,
            "theme" to  (chatRoomInfo.tag ?: ""),
            "roomid" to chatRoomInfo.roomId,
            "alivetime" to chatRoomInfo.aliveTime,
            "online" to chatRoomInfo.number,
            "style" to chatRoomInfo.getRoomStyle()
        )
    }

    fun trackRoomDetailShow(chatRoomInfo: ChatRoomInfo, categoryId: Int) {
        Analytics.track(
            EventConstants.EVENT_SHOW_CHATROOM_ROOM_CARD_DETAIL,
            "roomname" to chatRoomInfo.roomName,
            "gameId" to chatRoomInfo.platformGameId,
            "theme" to  (chatRoomInfo.tag ?: ""),
            "roomid" to chatRoomInfo.roomId,
            "show_categoryid" to categoryId,
            "style" to chatRoomInfo.getRoomStyle()
        )
    }

    fun trackRoomDetailEnterCLick(chatRoomInfo: ChatRoomInfo, result: String,categoryId:Int) {
        Analytics.track(
            EventConstants.EVENT_CLICK_CHATROOM_ROOM_CARD_DETAIL_ENTER,
            "roomname" to chatRoomInfo.roomName,
            "gameId" to chatRoomInfo.platformGameId,
            "theme" to  (chatRoomInfo.tag ?: ""),
            "roomid" to chatRoomInfo.roomId,
            "show_categoryid" to categoryId,
            "result" to result,
            "style" to chatRoomInfo.getRoomStyle()
        )
    }

    fun trackHomeRoomCardShow(roomNumber: Int) {
        Analytics.track(
            EventConstants.EVENT_SHOW_CHATROOM,
            "roomnumber" to roomNumber
        )
    }

    fun trackHomeRoomCardSeeAllCLick() {
        Analytics.track(EventConstants.EVENT_CLICK_CHATROOM_BUTTON_ALL_ROOM)
    }

    fun trackCreateRoomPageShow() {
        Analytics.track(EventConstants.EVENT_SHOW_CREATE_ROOM)
    }

    fun trackCreateRoomPagePublishClick(
        roomName: String,
        roomTag: String,
        result: String,
        style: String
    ) {
        Analytics.track(
            EventConstants.EVENT_CLICK_CREATE_ROOM_BUTTON_PUBLISH,
            "result" to result,
            "roomname" to roomName,
            "theme" to roomTag,
            "style" to style

        )
    }

    fun trackAllRoomPageShow() {
        Analytics.track(EventConstants.EVENT_SHOW_ALL_ROOM)
    }

    fun trackAllRoomPageCreateRoomClick() {
        Analytics.track(EventConstants.EVENT_CLICK_ALL_ROOM_BUTTON_CREATE_ROOM)
    }
}