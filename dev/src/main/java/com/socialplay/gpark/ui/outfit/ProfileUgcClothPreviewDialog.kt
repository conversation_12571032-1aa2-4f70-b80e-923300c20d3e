package com.socialplay.gpark.ui.outfit

import android.os.Parcelable
import android.view.WindowManager
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.databinding.DialogProfileUgcClothPreviewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.extension.backgroundTintListByColorStr
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByStr
import com.socialplay.gpark.util.extension.setWidthEx
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import kotlin.math.min

@Parcelize
data class ProfileUgcClothPreviewArgs(
    val uuid: String,
    val isMe: Boolean,
    val cloth: ProfileCurrentCloth
) : Parcelable

class ProfileUgcClothPreviewDialog : BaseDialogFragment() {

    companion object {
        fun show(
            fragment: Fragment,
            uuid: String,
            isMe: Boolean,
            cloth: ProfileCurrentCloth
        ): ProfileUgcClothPreviewDialog {
            Analytics.track(
                EventConstants.CURRENTLY_PGC_CLOTHING_BAR_DESIGN_CLICK
            )
            val dialog = ProfileUgcClothPreviewDialog()
            dialog.arguments = ProfileUgcClothPreviewArgs(uuid, isMe, cloth).asMavericksArgs()
            dialog.show(fragment.childFragmentManager, "ProfileUgcClothPreviewDialog")
            return dialog
        }
    }

    override val binding by viewBinding(DialogProfileUgcClothPreviewBinding::inflate)
    private val mainViewModel: MainViewModel by sharedViewModel()

    private val args by args<ProfileUgcClothPreviewArgs>()

    override fun init() {
        glide?.run {
            load(args.cloth.iconUrl).into(binding.ivThumb)
        }
        val dialogWidth = min(screenWidth - dp(32), dp(344))
        binding.clContainer.setWidthEx(dialogWidth)
        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.ivCloseBtn.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvTryOn.setOnAntiViolenceClickListener {
            context?.let {
                MetaRouter.MobileEditor.fullScreenRole(
                    it,
                    FullScreenEditorActivityArgs(
                        categoryId = CategoryId.UGC_DESIGN_PROFILE_CLOTHES,
                        tryOn = RoleGameTryOn.create(
                            tryOnUserId = args.uuid,
                            from = RoleGameTryOn.FROM_PROFILE_CURRENT_OUTFIT,
                            pgcClothItemId = args.cloth.itemId
                        )
                    )
                )
            }
            dismissAllowingStateLoss()
        }

        when (args.cloth.acquiredType) {
            ProfileCurrentCloth.TYPE_FREE -> {
                binding.tvPrice.gone()
                binding.icFreeLabel.visible()
            }

            ProfileCurrentCloth.TYPE_STORE -> {
                val price = args.cloth.price
                if (price == null || price == 0L) {
                    binding.tvPrice.gone()
                    binding.icFreeLabel.visible()
                } else {
                    binding.tvPrice.text = price.toString()
                    binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_coin)
                    binding.tvPrice.backgroundTintListByColorStr("#FFF8E5")
                }
            }

            ProfileCurrentCloth.TYPE_CREDIT -> {
                val price = args.cloth.price
                if (price == null || price == 0L) {
                    binding.tvPrice.gone()
                    binding.icFreeLabel.visible()
                } else {
                    binding.tvPrice.text = price.toString()
                    binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_credit)
                    binding.tvPrice.backgroundTintListByColorStr("#EAFFC7")
                }
            }

            ProfileCurrentCloth.TYPE_VIP -> {
                binding.tvPrice.setText(R.string.cloth_price_member)
                binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_sub)
                binding.tvPrice.setTextColorByStr("#8061AC")
                binding.tvPrice.backgroundTintListByColorStr("#EDE5FF")
            }

            ProfileCurrentCloth.TYPE_DRAW -> {
                binding.tvPrice.setText(R.string.cloth_price_box)
                binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_blind)
                binding.tvPrice.setTextColorByStr("#7E5F0D")
                binding.tvPrice.backgroundTintListByColorStr("#FFFBE3")
            }

            ProfileCurrentCloth.TYPE_EVENT -> {
                binding.tvPrice.setText(R.string.cloth_price_event)
                binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_event)
                binding.tvPrice.setTextColorByStr("#84323A")
                binding.tvPrice.backgroundTintListByColorStr("#FFEEF0")
            }

            ProfileCurrentCloth.TYPE_GIFT_PACK -> {
                binding.tvPrice.setText(R.string.cloth_price_gift)
                binding.tvPrice.compoundDrawables(left = R.drawable.ic_cloth_price_gift_pack)
                binding.tvPrice.setTextColorByStr("#437699")
                binding.tvPrice.backgroundTintListByColorStr("#D7EEFF")
            }

            else -> {
                binding.tvPrice.gone()
            }
        }

        val label1 = args.cloth.tags?.getOrNull(0)
        val label2 =  args.cloth.tags?.getOrNull(1)
        binding.tvLabel1.goneIfValueEmpty(label1)
        binding.tvLabel2.goneIfValueEmpty(label2)
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
}