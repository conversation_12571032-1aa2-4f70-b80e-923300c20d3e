package com.socialplay.gpark.ui.im.friendadd

import android.view.Gravity
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.databinding.FragmentMyQrCodeBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject


class MyQRCodeFragment : BaseDialogFragment() {

    private val metaKV: MetaKV by inject()
    private val accountInteractor by inject<AccountInteractor>()
    override val binding by viewBinding(FragmentMyQrCodeBinding::inflate)

    private val QR_CDOE_SIZE = 200

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun init() {
        accountInteractor.accountLiveData.value?.apply {
            binding.tvNickName.text = nickname
        }

        binding.ivClose.setOnClickListener { navigateUp() }
    }

    override fun loadFirstData() {
        metaKV.account.getMyQrCode().apply {
            val qrCodeBmp = QRCode.newQRCodeUtil().margin("0").content(this).width(QR_CDOE_SIZE).height(QR_CDOE_SIZE).build()
            binding.ivQrCode.setImageBitmap(qrCodeBmp)
        }
    }

}