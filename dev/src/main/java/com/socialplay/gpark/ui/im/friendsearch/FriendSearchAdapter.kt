package com.socialplay.gpark.ui.im.friendsearch

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.friend.FriendSearchInfo
import com.socialplay.gpark.databinding.AdapterFriendSearchBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.view.UserLabelListener
import com.socialplay.gpark.util.extension.visible

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/04
 *     desc   : 好友列表适配器
 */
class FriendSearchAdapter(
    private val glide: GlideGetter,
    private val listener: UserLabelListener
) : BasePagingDataAdapter<FriendSearchInfo, AdapterFriendSearchBinding>(DIFF_CALLBACK) {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<FriendSearchInfo>() {

            override fun areItemsTheSame(oldItem: FriendSearchInfo, newItem: FriendSearchInfo) = oldItem.uid == newItem.uid

            override fun areContentsTheSame(oldItem: FriendSearchInfo, newItem: FriendSearchInfo): Boolean {
                return oldItem.uid == newItem.uid
            }
        }
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterFriendSearchBinding {
        return AdapterFriendSearchBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterFriendSearchBinding>, item: FriendSearchInfo, position: Int) {
        val binding = holder.binding
        glide()?.run {
            load(item.portrait).placeholder(R.drawable.icon_default_avatar)
                .into(binding.ivAvatar)
        }
        binding.tvFriendName.text = item.nickname
        context.apply {
            binding.tv233Count.text = getString(R.string.friend_id_formatted).format(item.userNumber)
            binding.tvRecentlyPlayed.text = getString(R.string.recently_played_fromatted).format(item.lastPlayGameName)
        }


        binding.tvAdd.visible(!item.isSelf && !item.isMyFriend())

        binding.tvRecentlyPlayed.visible(!item.lastPlayGameName.isNullOrBlank())
        binding.labelGroup.show(item.tagIds, item.labelInfo, glide = glide())
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<AdapterFriendSearchBinding>) {
        super.onViewAttachedToWindow(holder)
        holder.binding.labelGroup.setListener(listener)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<AdapterFriendSearchBinding>) {
        super.onViewDetachedFromWindow(holder)
        holder.binding.labelGroup.setListener(null)
    }
}