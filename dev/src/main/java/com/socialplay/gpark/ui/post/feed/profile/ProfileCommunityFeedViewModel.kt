package com.socialplay.gpark.ui.post.feed.profile

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.util.ToastData
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class ProfileCommunityFeedModelState(
    override val refresh: Async<List<CommunityFeedInfo>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized
) : ICommunityFeedModelState {

    override fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelState {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState {
        return copy(notifyCheckVideo = checkVideo)
    }

    override fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        val newRefresh = result.map {
            it.dataList.distinctBy { it.postId }
        }
        return copy(
            refresh = newRefresh,
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end == true) }
        )
    }

    override fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke()
                result.map { wrapper ->
                    if (oldList.isNullOrEmpty()) {
                        wrapper.dataList
                    } else {
                        oldList + wrapper.dataList
                    }.distinctBy { it.postId }
                }
            } else {
                refresh
            },
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }
}

class ProfileCommunityFeedViewModel(
    private val repository: IMetaRepository,
    val accountInteractor: AccountInteractor,
    initialState: ProfileCommunityFeedModelState
) : BaseCommunityFeedViewModel<ProfileCommunityFeedModelState>(repository, accountInteractor, initialState) {

    fun refresh(otherUid: String, force: Boolean = false) {
        withState { oldState ->
            if (!force && oldState.loadMore is Loading) return@withState
            repository.getCommunityProfileFeed(otherUid, PAGE_SIZE, 1).map {
                notifyCheckVideo()
                it
            }.execute { result ->
                feedRefresh(result) as ProfileCommunityFeedModelState
            }
        }
    }

    fun loadMore(otherUid: String) {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            val nextPage = oldState.nextPage
            repository.getCommunityProfileFeed(otherUid, PAGE_SIZE, nextPage).execute { result ->
                feedLoadMore(result) as ProfileCommunityFeedModelState
            }
        }
    }

    companion object :
        KoinViewModelFactory<ProfileCommunityFeedViewModel, ProfileCommunityFeedModelState>() {

        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ProfileCommunityFeedModelState
        ): ProfileCommunityFeedViewModel {
            return ProfileCommunityFeedViewModel(get(), get(), state)
        }
    }
}