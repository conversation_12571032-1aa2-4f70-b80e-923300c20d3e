package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.UgcDesignCardMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.ifNullOrEmpty

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/29
 *     desc   :
 * </pre>
 */
@ProviderTag(
    messageContent = UgcDesignCardMessage::class,
    showReadState = false,
    showWarning = true,
    showSenderPortrait = true
)
class UgcDesignCardMessageItemProvider :
    IContainerItemProvider.MessageProvider<UgcDesignCardMessage>() {

    override fun newView(context: Context?, parent: ViewGroup?): View {
        val view =
            LayoutInflater.from(context)
                .inflate(R.layout.item_im_share_ugc_design, null as ViewGroup?)
        view.tag = ViewHolder(view)
        return view
    }

    override fun bindView(
        view: View,
        position: Int,
        content: UgcDesignCardMessage?,
        message: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
        val holder: ViewHolder = view.tag as ViewHolder
        val data = content?.getUgcDesignInfo()
        holder.tvTitle.text = data?.title.ifNullOrEmpty { view.getString(R.string.fashion_design) }
        ThreadHelper.runOnUiThreadCatching {
            Glide.with(view)
                .load(data?.icon)
                .placeholder(R.drawable.placeholder)
                .into(holder.ivIcon)
        }
        if (message?.messageDirection == Message.MessageDirection.SEND) {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_send)
            holder.root.setPaddingEx(left = view.dp(12), right = view.dp(18))
            holder.tvTitle.setTextColorByRes(R.color.textColorPrimary)
        } else {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_received)
            holder.root.setPaddingEx(left = view.dp(18), right = view.dp(12))
            holder.tvTitle.setTextColorByRes(R.color.textColorPrimary)
        }
        view.setOnClickListener {
            data?.let {
                messageClickListener?.clickUgcDesignCard(message?.message, it)
            }
        }
    }

    override fun getContentSummary(context: Context?, data: UgcDesignCardMessage?): Spannable {
        return SpannableString(context?.getString(R.string.share_fashion_design_content))
    }

    override fun onItemClick(
        p0: View?,
        p1: Int,
        p2: UgcDesignCardMessage?,
        p3: UIMessage?,
        p4: MessageListAdapter.OnMessageClickListener?
    ) {
    }

    private class ViewHolder(view: View) {
        var ivIcon: ImageView = view.findViewById(R.id.iv_icon)
        var tvTitle: TextView = view.findViewById(R.id.tv_title)
        var root: ConstraintLayout = view.findViewById(R.id.cl_root)
    }
}