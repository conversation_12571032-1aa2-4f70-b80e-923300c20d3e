package com.socialplay.gpark.ui.mgs.expand

import android.app.Activity
import android.app.Application
import android.view.View
import android.widget.*
import androidx.viewpager.widget.ViewPager
import com.meta.biz.mgs.data.model.MGSMessage
import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsRoomInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.im.MgsGameInviteEventInfo
import com.socialplay.gpark.data.model.mgs.MgsTabEnum
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.intermodal.base.BaseGamePage
import com.socialplay.gpark.ui.mgs.adapter.MgsGameTabAdapter
import com.socialplay.gpark.ui.mgs.listener.*
import com.socialplay.gpark.ui.mgs.view.MgsTabLayout
import com.socialplay.gpark.ui.view.CommonViewPager
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/06
 *     desc   : 展开悬浮窗
 *
 */
class MgsMessageExpandView(val app: Application, val metaApp: Application,
                           private val selectTabEnum: MgsTabEnum = MgsTabEnum.ROOM_PLAYER_TAB) : BaseGamePage(), MgsExpandViewCall {
    var tabLayout: MgsTabLayout? = null
    var viewPager: CommonViewPager? = null
    private val presenter = MgsExpandPresenter(this)

    private val roomView: MgsExpandRoomTabView by lazy { MgsExpandRoomTabView(app, metaApp, roomTabListener) }
//    private val chatView: MgsExpandChatTabView by lazy { MgsExpandChatTabView(app, metaApp, chatTabListener) }
    private val friendView: MgsExpandFriendTabView by lazy { MgsExpandFriendTabView(app, metaApp, friendTabListener) }

    var chatShowTime = System.currentTimeMillis()
    private val gameInfo: GameDetailInfo? by lazy { presenter.getMgsGameInfo() }
    private val analyticInfo by lazy {
        mapOf("gameid" to gameInfo?.id.toString(), "gamepkg" to (gameInfo?.packageName ?: ""))
    }

    override fun layoutLandId(): Int = R.layout.view_mgs_expand_land

    override fun layoutId(): Int = R.layout.view_mgs_expand


    var onMgsExpandListener: OnMgsExpandListener? = null

    private val onPageChangeAdapter = object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}
        override fun onPageSelected(position: Int) {
//            if (position == MgsTabEnum.CHAT_TAB.position) {
//                chatShowTime = System.currentTimeMillis()
//            } else {
                sendChatShowTimeAnalytic()
//            }
            sendTabShowAnalytic(position)
        }
        override fun onPageScrollStateChanged(state: Int) {
        }
    }

    private val friendTabListener = object : OnMgsExpandFriendTabListener {
        override fun fetchActivity(): Activity? {
            return currentActivity
        }

        override fun getMgsRoomInfo(): MgsRoomInfo? {
            return presenter.getMgsRoomInfo()
        }

        override fun showUserCardByUuid(uuid: String) {
            // 房间消息列表用户
            presenter.showUserCardByUuid(uuid)
            Analytics.track(EventConstants.EVENT_CLICK_MGS_USER) {
                putAll(analyticInfo)
            }
        }

        override fun getGameInfo(): GameDetailInfo? {
            return gameInfo
        }

        override fun sendGameInviteMessage(mgsGameInviteEventInfo: MgsGameInviteEventInfo) {
            presenter.sendGameInviteMessage(mgsGameInviteEventInfo)
        }
    }

    private val chatTabListener = object : OnMgsExpandChatTabListener {

        override fun getGameInfo(): GameDetailInfo? {
            return gameInfo
        }

        override fun showUserCardByUuid(uuid: String) {
            presenter.showUserCardByUuid(uuid)
            Analytics.track(EventConstants.EVENT_CLICK_MGS_USER) {
                putAll(analyticInfo)
            }
        }
    }

    private val roomTabListener = object : OnMgsExpandRoomTabListener {

        override fun showUserCardByOpenId(openId: String) {
            //房间成员
            presenter.showUserCardByOpenId(openId)
            Analytics.track(EventConstants.EVENT_CLICK_MGS_USER) {
                putAll(analyticInfo)
            }
        }

        override fun addFriendByUuid(uuid: String) {
            presenter.addFriendByUuid(uuid)
            Analytics.track(EventConstants.EVENT_MGS_CLICK_ADD_FRIEND) {
                putAll(analyticInfo)
                put("location", "chatroom_user_picture")
            }
        }

        override fun getGameInfo(): GameDetailInfo? {
            return gameInfo
        }

        override fun changeMuteState(isOpen: Boolean, openId: String, from: String) {
            presenter.changeOtherVoiceState(isOpen, openId, from)
        }
    }

    override fun initView(view: View) {
        initViewPager(view)
        initEvent(view)
        fetchTop(view)
    }

    private fun fetchTop(view: View) {
//        val height = NotchScreenUtil.fitNotchProScreen(metaApp)
//        if (height != 0) {
//            val spaceView = view.findViewById<View>(R.id.vMgsExpandSpace)
//            view.post {
//                if (!ScreenUtil.isHorizontalScreen(metaApp)) {
//                    spaceView.setHeight(height)
//                } else {
//                    spaceView.setWidth(height)
//                }
//            }
//        }
    }

    private fun initViewPager(view: View) {
        val viewList = arrayListOf<View>(roomView, friendView)
        viewPager = view.findViewById(R.id.vp_mgs)
        viewPager?.apply {
            canScroll = false
            isSmoothScroll = false
            this.adapter = MgsGameTabAdapter(viewList)
            addOnPageChangeListener(onPageChangeAdapter)
            currentItem = MgsTabEnum.ROOM_PLAYER_TAB.position
        }
        tabLayout = view.findViewById(R.id.tlMgsExpand)
        tabLayout?.apply {
            addTabSelectListener(object : OnMgsTabSelectListener {
                override fun onSelect(mgsTabEnum: MgsTabEnum) {
                    viewPager?.currentItem = mgsTabEnum.position
                }
            })
            selectTab(MgsTabEnum.ROOM_PLAYER_TAB)
        }
        tabLayout?.selectTab(selectTabEnum)
    }

    private fun initEvent(view: View) {
        view.findViewById<LinearLayout>(R.id.meta_mgs_rl_room).setOnClickListener { close() }
        view.findViewById<RelativeLayout>(R.id.rlMgsExpandLayer).setOnClickListener { }
    }

    fun onActivityResumed(activity: Activity) {
        chatShowTime = System.currentTimeMillis()
    }

    fun onActivityPaused() {
        sendChatShowTimeAnalytic()
    }

    private fun sendTabShowAnalytic(position: Int) {
        val kind = when (position) {
            MgsTabEnum.ROOM_PLAYER_TAB.position -> {
                EventConstants.EVENT_SHOW_MGS_MEMBER
            }
//            MgsTabEnum.CHAT_TAB.position        -> {
//                EventConstants.EVENT_SHOW_MGS_ROOM
//            }
            MgsTabEnum.MY_FRIEND_TAB.position   -> {
                EventConstants.EVENT_SHOW_MGS_INVITE
            }
            else                                -> {
                return
            }
        }
        Analytics.track(kind) {
            putAll(analyticInfo)
            // 代表聊天界面的来源
            put("source", "ball")
        }
    }

    private fun sendChatShowTimeAnalytic() {
        val closeTime = System.currentTimeMillis() - chatShowTime
        Analytics.track(EventConstants.EVENT_SHOW_MGS_ROOM_TIME) {
            putAll(analyticInfo)
            put("time", closeTime)
        }
    }

    override fun initData() {
        updateRoomUserList(presenter.getMemberList())
        updateRoomStatus(presenter.getMgsRoomInfo())
        updateMessageList(presenter.getHistoryMessageList())
        presenter.iniData()
    }

    /**
     * 更新数据
     */
    override fun updateMessageList(value: MutableList<MGSMessage>?) {
        val newData = value ?: ArrayList()
//        chatView.updateMessageList(newData)
    }

    /**
     * 添加数据
     */
    override fun addMessageList(value: MGSMessage) {
        Timber.d("mgs_message_addMessageList")
//        chatView.addMessageList(value)
    }

    override fun updateRoomUserList(userList: MutableList<Member>?) {
        roomView.updateRoomMemberList(userList ?: ArrayList())
    }

    override fun addRoomUser(user: Member) {
        roomView.addRoomUser(user)
    }

    override fun removeRoomUser(user: Member) {
        roomView.removeRoomUser(user)
    }

    override fun updateRoomUser(user: Member) {
        roomView.updateRoomUser(user)
    }

    override fun updateRoomStatus(mgsRoomInfo: MgsRoomInfo?) {
        if (mgsRoomInfo == null) {
            close()
        }
    }

    override fun updateOtherVoiceState(member: Member) {
        roomView.updateOtherVoiceState(member)
    }

    override fun removeAllFloatView() {
        close()
    }



    override fun close() {
        onMgsExpandListener?.closeExpandView()
        onMgsExpandListener = null
        viewPager?.adapter = null
        viewPager?.removeOnPageChangeListener(onPageChangeAdapter)
        presenter.remove()
        sendChatShowTimeAnalytic()
        super.close()
    }
}