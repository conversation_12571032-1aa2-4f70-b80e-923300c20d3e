package com.socialplay.gpark.ui.gamereview.dialog

import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import com.socialplay.gpark.databinding.DialogDeleteMyReviewBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

/**
 * Created by bo.li
 * Date: 2022/7/5
 * Desc:
 */
class DeleteMyReviewDialog: BaseDialogFragment() {
    override val binding by viewBinding(DialogDeleteMyReviewBinding::inflate)

    private var clickedConfirm = false

    companion object {
        const val REQUEST_DELETE_MY_REVIEW = "request_delete_my_review"
        const val KEY_CONFIRM = "confirm"
    }

    override fun init() {
        binding.tvDeleteReviewConfirm.setOnAntiViolenceClickListener {
            clickedConfirm = true
            dismiss()
        }
        binding.tvDeleteReviewCancel.setOnAntiViolenceClickListener {
            dismiss()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        setFragmentResultByActivity(REQUEST_DELETE_MY_REVIEW, bundleOf(KEY_CONFIRM to clickedConfirm))
        super.onDismiss(dialog)
    }

    override fun loadFirstData() {

    }

    override fun gravity(): Int = Gravity.BOTTOM

    override fun marginHorizontal(context: Context): Int = 16.dp

    override fun isCancelable(): Boolean = true

    override fun isClickOutsideDismiss(): Boolean = true

    override fun isBackPressedDismiss(): Boolean = true
}