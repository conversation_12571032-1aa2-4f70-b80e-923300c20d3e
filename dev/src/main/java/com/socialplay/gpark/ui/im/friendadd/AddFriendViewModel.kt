package com.socialplay.gpark.ui.im.friendadd

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent

/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/22
 *  desc   :
 */
class AddFriendViewModel(private val metaRepository: IMetaRepository, private val metaKV: MetaKV) : ViewModel(), KoinComponent {

    val qrCodeCallback: LifecycleCallback<(Boolean) -> Unit> = LifecycleCallback()

    init {
        getQrCodeUrl(false)
    }

    fun getQrCodeUrl(isNeedDispatch: Boolean) = viewModelScope.launch {
        val localQrCode = metaKV.account.getMyQrCode()
        // 做contains判断原因：防止旧用户已经将错误qrcode存进当前uuid的mmkv中的情况，不展示错误的qrcode
        if (!localQrCode.isNullOrBlank() && localQrCode.contains(metaKV.account.uuid)) {
            dispatchCallback(true, isNeedDispatch)
            return@launch
        }
        metaRepository.getQrCode().collect {
            val qrCode = it.data
            if (!qrCode.isNullOrBlank()) {
                metaKV.account.setMyQrCode(qrCode)
            }
            dispatchCallback(!qrCode.isNullOrBlank(), isNeedDispatch)
        }
    }

    private fun dispatchCallback(isSuccess: Boolean, isNeedDispatch: Boolean) {
        if (!isNeedDispatch) return
        qrCodeCallback.dispatchOnMainThread {
            invoke(isSuccess)
        }
    }
}