package com.socialplay.gpark.ui.outfit

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.google.android.material.tabs.TabLayout
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetProfile
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.databinding.FragmentUgcAssetListBinding
import com.socialplay.gpark.databinding.PopUpUgcAssetListBinding
import com.socialplay.gpark.databinding.TabIndicatorUgcAssetListBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize

@Parcelize
data class UgcAssetListArgs(
    val entrance: Int,
    val uuid: String,
    val isMe: Boolean,
    val title: String?
) : Parcelable

class UgcAssetListFragment :
    BaseRecyclerViewFragment<FragmentUgcAssetListBinding>(R.layout.fragment_ugc_asset_list) {

    private val args by args<UgcAssetListArgs>()
    private val vm: UgcAssetListViewModel by fragmentViewModel()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvItem

    private var curTabPos = 0
    private var spanSize = 4

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpUgcAssetListBinding.inflate(layoutInflater) }

    private var loadingDialog: DialogFragment? = null

    private val tabListener by lazy {
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabPos = tab.position
                val tagInfo = tab.tag as? UgcDesignProfileTag ?: return
                updateTabView(tab, true)
                if (curTabPos != tabPos) {
                    curTabPos = tabPos
                    vm.updateTabSelection(tabPos, tagInfo.code)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabView(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        }
    }

    private val tabVisibilityListener = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            val tagInfo = tab.tag as? UgcDesignProfileTag ?: return
            if (vm.oldState.mode in UgcAssetListState.MODE_VISIBILITY_PRIVATE..UgcAssetListState.MODE_VISIBILITY_PUBLIC) {
                vm.changeMode(tagInfo.code)
            }
            updateTabView(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            updateTabView(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val itemListenerV2 = object : IProfileUgcAssetListener {
        override fun clickAsset(item: UgcAssetProfile, position: Int) {
            if (item.published) {
                MetaRouter.UgcDesign.detail(
                    this@UgcAssetListFragment,
                    item.itemId,
                    CategoryId.UGC_ASSET_LIST
                )
            } else {
                toast(R.string.ugc_asset_click_hidden_tips)
            }
        }

        override fun canSelect(item: UgcAssetProfile, mode: Int, position: Int): Boolean {
            return vm.canSelect(item, mode, position)
        }

        override fun isSelected(item: UgcAssetProfile, mode: Int, position: Int): Boolean {
            return vm.isSelected(item, mode, position)
        }

        override fun select(item: UgcAssetProfile, mode: Int, position: Int) {
            vm.select(item, mode, position)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcAssetListBinding? {
        return FragmentUgcAssetListBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        spanSize = if (isPad) {
            ((screenWidth - dp(24)) / dp(88)).coerceAtLeast(4)
        } else {
            4
        }

        binding.tbl.setTitle(args.title)

        binding.tbl.setOnBackAntiViolenceClickedListener {
            back()
        }
        binding.ivMoreBtn.setOnAntiViolenceClickListener {
            popupBinding.cv.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val x = -popupBinding.cv.measuredWidth + binding.ivMoreBtn.width.coerceAtLeast(binding.ivMoreBtn.measuredWidth) - dp(22)
            val y = -dp(10)
            popupWindow.showAsDropDownByLocation(it, x, y)
        }
        binding.tvSubmitBtn.setOnAntiViolenceClickListener {
            vm.finishOp()
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            back()
        }
        binding.rvItem.layoutManager = GridLayoutManager(requireContext(), spanSize)
        initVisibilityTab()
        initPopup()

        vm.setupRefreshLoading(
            UgcAssetListState::assets,
            binding.lv,
            binding.refresh,
            emptyMsg = if (args.isMe) getString(R.string.profile_assets_tab_empty) else getString(R.string.profile_assets_tab_empty_other)
        ) {
            vm.load(true)
        }
        vm.onAsync(UgcAssetListState::tags, onLoading = {
            binding.lv.showLoading()
        }, onFail = { _, _ ->
            binding.lv.showError()
        }) {
            initTabLayout(it)
        }
        vm.onEach(UgcAssetListState::assets, UgcAssetListState::mode) { assets, mode ->
            binding.ivMoreBtn.visible(args.isMe && !assets().isNullOrEmpty() && mode == UgcAssetListState.MODE_NORMAL)
        }
        vm.onEach(UgcAssetListState::mode) {
            when (it) {
                UgcAssetListState.MODE_NORMAL -> {
                    binding.tl.visible()
                    binding.tlVisibility.gone()
                    binding.tvSubmitBtn.gone()
                    binding.tbl.setTitle(args.title)
                    binding.refresh.isEnabled = true
                }

                UgcAssetListState.MODE_PIN -> {
                    binding.tl.gone()
                    binding.tlVisibility.gone()
                    binding.tvSubmitBtn.visible()
                    binding.tbl.setTitle(getString(R.string.pin_cap))
                    binding.refresh.isEnabled = false
                }

                UgcAssetListState.MODE_VISIBILITY_PRIVATE -> {
                    binding.tl.gone()
                    binding.tlVisibility.visible()
                    binding.tvSubmitBtn.visible()
                    binding.tbl.setTitle(getString(R.string.ugc_asset_op_hide))
                    binding.refresh.isEnabled = false
                }

                UgcAssetListState.MODE_VISIBILITY_PUBLIC -> {
                    binding.tl.gone()
                    binding.tlVisibility.visible()
                    binding.tvSubmitBtn.visible()
                    binding.tbl.setTitle(getString(R.string.ugc_asset_op_unhide))
                    binding.refresh.isEnabled = false
                }
            }
        }
        vm.onAsync(UgcAssetListState::opResult, deliveryMode = uniqueOnly(), onFail = { _ ->
            toast(R.string.common_failed)
        }) {
            if (!it.second) {
                toast(R.string.common_failed)
            }
        }
        vm.onEach(UgcAssetListState::opResult) {
            if (it is Loading) {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
            } else {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = null
            }
        }
        vm.registerAsyncErrorToast(UgcAssetListState::loadMore)
    }

    private fun initVisibilityTab() {
        kotlin.runCatching {
            binding.tlVisibility.addOnTabSelectedListener(viewLifecycleOwner, tabVisibilityListener)
            binding.tlVisibility.removeAllTabs()
            binding.tlVisibility.addTab(
                createTab(
                    binding.tlVisibility,
                    UgcDesignProfileTag(
                        UgcAssetListState.MODE_VISIBILITY_PRIVATE,
                        getString(R.string.ugc_asset_op_hide)
                    )
                ),
                true
            )
            binding.tlVisibility.addTab(
                createTab(
                    binding.tlVisibility,
                    UgcDesignProfileTag(
                        UgcAssetListState.MODE_VISIBILITY_PUBLIC,
                        getString(R.string.ugc_asset_op_unhide)
                    )
                )
            )
        }
    }

    private fun initPopup() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationFromRight
        }
        popupBinding.root.setOnClickListener {
            vm.changeMode(UgcAssetListState.MODE_PIN)
            popupWindow.dismiss()
        }
        popupBinding.vHideClick.setOnAntiViolenceClickListener {
            if (binding.tlVisibility.selectedTabPosition == 1) {
                binding.tlVisibility.getTabAt(0)?.let {
                    binding.tlVisibility.selectTab(it)
                }
            }
            vm.changeMode(UgcAssetListState.MODE_VISIBILITY_PRIVATE)
            popupWindow.dismiss()
        }
    }

    private fun initTabLayout(tags: List<UgcDesignProfileTag>?) {
        tags ?: return
        kotlin.runCatching {
            binding.tl.addOnTabSelectedListener(viewLifecycleOwner, tabListener)
            binding.tl.removeAllTabs()
            tags.forEachIndexed { index, data ->
                binding.tl.addTab(createTab(binding.tl, data), curTabPos == index)
            }
//            binding.tl.addTabGapFirstLast(dp(8), dp(8))
            binding.tl.visible()
        }
    }

    private fun updateTabView(tab: TabLayout.Tab, isSelected: Boolean) {
        val cv = tab.customView ?: return
        TabIndicatorUgcAssetListBinding.bind(cv).apply {
            tvNormal.invisible(isSelected)
            tvSelected.invisible(!isSelected)
        }
    }

    private fun createTab(tl: TabLayout, item: UgcDesignProfileTag): TabLayout.Tab {
        val tabViewBinding = TabIndicatorUgcAssetListBinding.inflate(layoutInflater)
        val tab = tl.newTab()
        tab.id = item.code
        tabViewBinding.tvNormal.text = item.msg
        tabViewBinding.tvSelected.text = item.msg
        tab.customView = tabViewBinding.root
        tab.tag = item
        return tab
    }

    private fun back() {
        if (vm.oldState.mode != UgcAssetListState.MODE_NORMAL) {
            vm.changeMode(UgcAssetListState.MODE_NORMAL)
        } else {
            navigateUp()
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcAssetListState::assets,
        UgcAssetListState::loadMore,
        UgcAssetListState::mode,
        UgcAssetListState::flag,
        UgcAssetListState::uniqueTag,
    ) { assets, loadMore, mode, flag, uniqueTag ->
        when (mode) {
            UgcAssetListState.MODE_NORMAL -> {
                val items = assets()
                if (!items.isNullOrEmpty()) {
                    items.forEachIndexed { index, item ->
                        profileUgcAsset(
                            item,
                            args.entrance,
                            args.isMe,
                            index,
                            UgcAssetListState.MODE_NORMAL,
                            flag,
                            uniqueTag,
                            itemListenerV2
                        )
                    }

                    loadMoreFooter(
                        loadMore,
                        idStr = "UgcAssetListFooter-${uniqueTag}",
                        spanSize = spanSize,
                        showEnd = false
                    ) {
                        vm.load(loadMore()?.needRefresh == true)
                    }
                }
            }

            UgcAssetListState.MODE_PIN -> {
                assets()?.forEachIndexed { index, item ->
                    profileUgcAsset(
                        item,
                        args.entrance,
                        args.isMe,
                        index,
                        mode,
                        flag,
                        uniqueTag,
                        itemListenerV2
                    )
                }
            }

            UgcAssetListState.MODE_VISIBILITY_PRIVATE -> {
                val items = assets()?.filter { it.published }
                if (items.isNullOrEmpty()) {
                    empty(
                        descRes = R.string.ugc_asset_op_hide_empty,
                        top = dp(60),
                        idStr = "UgcAssetListOpEmpty",
                        spanSize = spanSize
                    )
                } else {
                    items.forEachIndexed { index, item ->
                        profileUgcAsset(
                            item,
                            args.entrance,
                            args.isMe,
                            index,
                            mode,
                            flag,
                            uniqueTag,
                            itemListenerV2
                        )
                    }
                }
            }

            UgcAssetListState.MODE_VISIBILITY_PUBLIC -> {
                val items = assets()?.filter { !it.published }
                if (items.isNullOrEmpty()) {
                    empty(
                        descRes = R.string.ugc_asset_op_unhide_empty,
                        top = dp(60),
                        idStr = "UgcAssetListOpEmpty",
                        spanSize = spanSize
                    )
                } else {
                    items.forEachIndexed { index, item ->
                        profileUgcAsset(
                            item,
                            args.entrance,
                            args.isMe,
                            index,
                            mode,
                            flag,
                            uniqueTag,
                            itemListenerV2
                        )
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        loadingDialog = null
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        super.onDestroyView()
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_ASSET_LIST
}