package com.socialplay.gpark.ui.editorschoice.adapter

import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.IChoiceItem
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeCardAdapter
import com.socialplay.gpark.ui.view.WrapRecyclerView
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import java.util.concurrent.atomic.AtomicBoolean

/**
 *
 * <AUTHOR>
 * @date 2021/07/05
 */
class SmallCardProvider(
    private val glide: RequestManager,
    private val itemCallback: ChoiceHomeCardAdapter.HomeItemCallback? = null
) : BaseItemProvider<ChoiceCardInfo>() {

    private val lazyInit = AtomicBoolean(false)
    private lateinit var itemDecoration: DividerItemDecoration

    override val itemViewType: Int = ChoiceCardType.SMALL

    override val layoutId: Int
        get() = R.layout.adapter_choice_card_provider_small


    override fun convert(helper: BaseViewHolder, card: ChoiceCardInfo) {
        helper.getView<TextView>(R.id.tv_card_title).text = card.cardName

        if (!lazyInit.getAndSet(true)) {
            itemDecoration = DividerItemDecoration(context, LinearLayoutManager.HORIZONTAL)
            ResourcesCompat.getDrawable(context.resources, R.drawable.divider_transparent_10, null)
                ?.let { itemDecoration.setDrawable(it) }
        }

        val recyclerView = helper.getView<WrapRecyclerView>(R.id.rv_choice_item_list)
        recyclerView.disallowParentInterceptTouchEvent = true
        recyclerView.apply {
            setHasFixedSize(true)
            val linearLayoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            linearLayoutManager.initialPrefetchItemCount = 3
            layoutManager = linearLayoutManager

            removeItemDecoration(itemDecoration)
            addItemDecoration(itemDecoration)
            val smallCardGameItemAdapter = SmallItemAdapter(card.gameList, glide).apply {
                setOnAntiViolenceItemClickListener { adapter, _, itemPosition ->
                    adapter.getItemOrNull(itemPosition)?.let {
                        itemCallback?.onItemClick(
                            helper.absoluteAdapterPosition,
                            card,
                            itemPosition,
                            it,
                            false
                        )
                    }
                }
                setOnItemShowListener { item, itemPosition ->
                    itemCallback?.onItemShow(
                        helper.absoluteAdapterPosition,
                        card,
                        itemPosition,
                        item,
                        false
                    )
                }
            }
            adapter = smallCardGameItemAdapter
        }
    }
}