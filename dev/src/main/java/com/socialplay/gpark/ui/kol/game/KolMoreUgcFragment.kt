package com.socialplay.gpark.ui.kol.game

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentKolMoreUgcBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.extension.navigateUp
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc: kol-更多ugc游戏
 */

/**
 * @param type 页面类型 [com.socialplay.gpark.ui.kol.game.KolMoreUgcFragment.PAGE_TYPE_RECOMMEND]
 */
@Parcelize
data class KolMoreUgcFragmentArgs(val type: String) : Parcelable

class KolMoreUgcFragment :
    BaseRecyclerViewFragment<FragmentKolMoreUgcBinding>(R.layout.fragment_kol_more_ugc) {

    private val args by args<KolMoreUgcFragmentArgs>()

    private val mVmRecommend: FollowedKolMoreUgcViewModel by fragmentViewModel()
    private val mVmFollowed: RecommendKolMoreUgcViewModel by fragmentViewModel()
    private val viewModel: BaseKolMoreUgcViewModel
        get() = when (args.type) {
            PAGE_TYPE_FOLLOW -> {
                mVmRecommend
            }

            PAGE_TYPE_RECOMMEND -> {
                mVmFollowed
            }

            else -> {
                throw IllegalArgumentException("page type not support: ${args.type}")
            }
        }

    private var listListener: IKolMoreUgcGameAction? = null
    private lateinit var epoxyVisibilityTracker: EpoxyVisibilityTracker

    companion object {
        const val PAGE_TYPE_RECOMMEND = "RECOMMEND"
        const val PAGE_TYPE_FOLLOW = "FOLLOWED"
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvMoreUgc

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentKolMoreUgcBinding? {
        return FragmentKolMoreUgcBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        listListener = getInitListener()
        epoxyVisibilityTracker = EpoxyVisibilityTracker().apply {
            attach(recyclerView)
        }
        binding.title.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.title.setTitle(getTitle())
        initData()
    }

    private fun initData() {
        viewModel.registerAsyncErrorToast(KolMoreUgcModelState::asyncList)
        viewModel.setupRefreshLoading(
            KolMoreUgcModelState::asyncList,
            binding.loadingMoreUgc,
            binding.refresh
        ) {
            viewModel.refresh()
        }
    }

    private fun getInitListener() = object : IKolMoreUgcGameAction {
        override fun goUgcDetail(ugId: String, gameCode: String, packageName: String) {
            Analytics.track(
                getClickEvent(),
                EventParamConstants.KEY_GAMEID to ugId,
                EventParamConstants.KEY_SHOW_CATEGORYID to getCategoryId(),
                EventParamConstants.KEY_PACKAGENAME to packageName,
            )
            MetaRouter.MobileEditor.ugcDetail(
                this@KolMoreUgcFragment,
                ugId,
                gameCode,
                ResIdBean().setCategoryID(getCategoryId())
                    .setGameId(ugId)
            )
        }

        override fun onItemShow(ugId: String, packageName: String) {
            Analytics.track(
                getShowEvent(),
                EventParamConstants.KEY_GAMEID to ugId,
                EventParamConstants.KEY_SHOW_CATEGORYID to getCategoryId(),
                EventParamConstants.KEY_PACKAGENAME to packageName,
            )
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    private fun getShowEvent() = when (args.type) {
        PAGE_TYPE_FOLLOW -> {
            EventConstants.EVENT_FOLLOW_UGC_SHOW
        }

        PAGE_TYPE_RECOMMEND -> {
            EventConstants.UGC_RECOMMEND_FEED_ITEM_SHOW
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    private fun getClickEvent() = when (args.type) {
        PAGE_TYPE_FOLLOW -> {
            EventConstants.UGC_FOLLOW_FEED_ITEM_CLICK
        }

        PAGE_TYPE_RECOMMEND -> {
            EventConstants.UGC_RECOMMEND_FEED_ITEM_CLICK
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    private fun getCategoryId() = when (args.type) {
        PAGE_TYPE_FOLLOW -> {
            CategoryId.CATEGORY_ID_KOL_FOLLOWED_UGC_DETAIL
        }

        PAGE_TYPE_RECOMMEND -> {
            CategoryId.CATEGORY_ID_KOL_RECOMMEND_UGC_DETAIL
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    private fun getTitle() = when (args.type) {
        PAGE_TYPE_FOLLOW -> {
            getString(R.string.kol_follow_work)
        }

        PAGE_TYPE_RECOMMEND -> {
            getString(R.string.recommend_for_you)
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        KolMoreUgcModelState::list,
        KolMoreUgcModelState::loadMore
    ) { list, loadMore ->
        // 列表
        list.forEachIndexed { index, game ->
            kolMoreUgcGame(game, index, listListener)
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, spanSize = 2) {
                viewModel.loadMore()
            }
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        listListener = null
        epoxyVisibilityTracker.detach(recyclerView)
        super.onDestroyView()
    }

    override fun getPageName(): String =
        PageNameConstants.FRAGMENT_NAME_KOL_MORE_UGC_PREFIX
}
