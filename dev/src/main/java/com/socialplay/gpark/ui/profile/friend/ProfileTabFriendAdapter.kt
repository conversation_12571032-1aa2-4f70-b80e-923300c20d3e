package com.socialplay.gpark.ui.profile.friend

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.profile.friend.FriendEntity
import com.socialplay.gpark.databinding.ItemProfileTabFriendBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.addIf
import com.socialplay.gpark.util.extension.ifEmptyNull

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/26 1:43 下午
 * @describe:
 */
class ProfileTabFriendAdapter(private val glide: RequestManager) : BasePagingDataAdapter<FriendEntity, ItemProfileTabFriendBinding>(CALLBACK) {

    companion object {


        private const val CHANGED_DISPLAY_NICKNAME = "CHANGED_DISPLAY_NICKNAME"
        private const val CHANGED_DISPLAY_PORTRAIT = "CHANGED_DISPLAY_PORTRAIT"
        private const val CHANGED_DISPLAY_RELATION = "CHANGED_DISPLAY_RELATION"


        private val CALLBACK = object : DiffUtil.ItemCallback<FriendEntity>() {
            override fun areItemsTheSame(oldItem: FriendEntity, newItem: FriendEntity): Boolean {
                return oldItem.uid == newItem.uid
            }

            override fun areContentsTheSame(oldItem: FriendEntity, newItem: FriendEntity): Boolean {
                return oldItem == newItem
            }

            override fun getChangePayload(oldItem: FriendEntity, newItem: FriendEntity): Any? {
                return arrayListOf<String>()
                    .addIf(CHANGED_DISPLAY_NICKNAME) { oldItem.nickname != newItem.nickname }
                    .addIf(CHANGED_DISPLAY_PORTRAIT) { oldItem.portrait != newItem.portrait }
                    .addIf(CHANGED_DISPLAY_RELATION) { oldItem.relation != newItem.relation }
                    .ifEmptyNull()
            }
        }
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): ItemProfileTabFriendBinding {
        return ItemProfileTabFriendBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<ItemProfileTabFriendBinding>, item: FriendEntity, position: Int) {
        val binding = holder.binding

        binding.tvUserName.text = item.nickname
        glide.load(item.portrait).placeholder(R.drawable.placeholder_white_round).into(binding.ivAvator)
        binding.tvOpts.apply {
            if (item.isSelf()) {
                isVisible = false
            } else {
                isVisible = true
                text = if (!item.isMyFriend()) context.getString(R.string.add_friend) else context.getString(R.string.chat_cap)
                background = if (!item.isMyFriend()) context.getDrawable(R.drawable.bg_button_pressed) else context.getDrawable(R.drawable.bg_button_cancel_f2f6fe)
            }
        }
    }
}