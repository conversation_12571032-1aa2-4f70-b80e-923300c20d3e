package com.socialplay.gpark.ui.gamedetail.sendflower

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class SendFlowerState(
    val coinsBalance: Long? = null,
    val giftFlowers: Async<List<GiftFlower>> = Uninitialized,
    val selectedGift: GiftFlower? = null,
    val giftCount: Int = 1,
    val customizeFlowerGiftProductId: Async<Long> = Uninitialized,
    val requiresCoins: Long = 0,
    val sendFlowerUserList: Async<List<SendGiftData>> = Uninitialized,
) : MavericksState

class SendFlowerViewModel(
    initialState: SendFlowerState,
    private val api: IMetaRepository,
    private val payInteractor: IPayInteractor,
    private val tTaiInteractor: TTaiInteractor,
    val accountInteractor: AccountInteractor,
) : BaseViewModel<SendFlowerState>(initialState) {
    fun loadBalance() {
        viewModelScope.launch {
            payInteractor.getBalance { userBalance ->
                setState {
                    copy(coinsBalance = userBalance?.leCoinNum)
                }
            }
        }
    }

    fun updateCoinsBalance(coinsBalance: Long) {
        setState {
            copy(coinsBalance = coinsBalance)
        }
    }

    fun loadCustomizeFlowerGiftProductId() = withState { s ->
        tTaiInteractor.getTTaiWithTypeV3<Long>(TTaiKV.ID_SEND_FLOWER_CUSTOMIZE_PRODUCT_ID).map { productId->
            productId?: BuildConfig.CUSTOMIZE_FLOWER_GIFT_PRODUCT_ID
        }.execute {
            copy(customizeFlowerGiftProductId = it)
        }
    }

    fun loadFlowerGifts() {
        viewModelScope.launch {
            api.getFlowerGifts().collect { result ->
                if (result.succeeded && result.data != null) {
                    setState {
                        copy(giftFlowers = Success(result.data!!))
                    }
                } else {
                    setState {
                        copy(
                            giftFlowers = Fail(
                                result.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    fun loadSendFlowerUserList(gameId: String, count: Int = 15) {
        viewModelScope.launch {
            val userList = api.getSendGiftUserList(gameId, count)
            if (userList.succeeded && userList.data != null) {
                setState {
                    copy(sendFlowerUserList = Success(userList.data!!))
                }
            } else {
                setState {
                    copy(
                        sendFlowerUserList = Fail(
                            userList.exception ?: Exception("unknown exception")
                        )
                    )
                }
            }
        }
    }

    fun updateSelectedGift(gift: GiftFlower, customizeGiftCount: Int? = null) {
        setState {
            copy(
                selectedGift = gift,
                giftCount = customizeGiftCount ?: 1,
                requiresCoins = if (customizeGiftCount == null) {
                    gift.getSingleGiftPrice()
                } else {
                    // 自定义送花数量
                    getGiftPrice(gift,customizeGiftCount)
                }
            )
        }
    }

    fun getGiftPrice(gift: GiftFlower, count: Int): Long {
        return gift.getSingleGiftPrice() * count
    }

    companion object : KoinViewModelFactory<SendFlowerViewModel, SendFlowerState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SendFlowerState
        ): SendFlowerViewModel {
            return SendFlowerViewModel(state, get(), get(), get(), get())
        }
    }
}