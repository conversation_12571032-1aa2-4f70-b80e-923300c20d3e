package com.socialplay.gpark.ui.im.groupchat

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo
import com.socialplay.gpark.databinding.FragmentGroupsRequestListBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize

@Parcelize
data class GroupsRequestListFragmentArgs(
    val targetUid: String
) : Parcelable

class GroupsRequestListFragment :
    BaseRecyclerViewFragment<FragmentGroupsRequestListBinding>(R.layout.fragment_groups_request_list) {
    companion object {
        fun newInstance(args: GroupsRequestListFragmentArgs): GroupsRequestListFragment {
            return GroupsRequestListFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    private val args: GroupsRequestListFragmentArgs by args()
    private val viewModel: GroupsRequestListViewModel by fragmentViewModel()
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val itemClickedListener = object : IItemGroupApplyClickedListener {
        override fun onAvatarClicked(info: GroupChatApplyInfo) {
            val uuid = info.askUuid ?: return
            // TODO from 参数需要确定
            MetaRouter.Profile.other(
                this@GroupsRequestListFragment,
                uuid,
                "12"
            )
        }

        override fun onCenterClicked(info: GroupChatApplyInfo) {
            if (info.status == GroupChatApplyInfo.STATUS_APPLYING) {
                val parentFragment = <EMAIL> ?: return
                MetaRouter.IM.goGroupJoinRequestApprovalDialog(
                    parentFragment,
                    info
                ) { result ->
                    if (info.id != null) {
                        if (result == GroupChatApplyInfo.STATUS_AGREE) {
                            viewModel.processApplyJoinGroupChat(
                                info.id,
                                true
                            )
                        } else if (result == GroupChatApplyInfo.STATUS_REJECT) {
                            viewModel.processApplyJoinGroupChat(
                                info.id,
                                false
                            )
                        }
                    }
                }
            }
        }

        override fun onMoreClicked(info: GroupChatApplyInfo) {
            if (info.id == null) {
                return
            }
            viewModel.processApplyJoinGroupChat(
                info.id,
                true
            )
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentGroupsRequestListBinding? {
        return FragmentGroupsRequestListBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        glide = Glide.with(this)
        binding.loadingView.setRetry {
            viewModel.getRequestList(args.targetUid, true)
        }

        viewModel.setupRefreshLoading(
            GroupsRequestListModelState::requestList,
            binding.loadingView,
            binding.refresh,
            emptyMsg = getString(R.string.request_page_no_groups_request)
        ) {
            viewModel.getRequestList(args.targetUid, true)
        }

        viewModel.onAsync(GroupsRequestListModelState::processApplyResult, onFail = { _, _ ->
            if (!NetUtil.isNetworkAvailable()) {
                toast(R.string.net_unavailable)
            }
        }, onLoading = {
        }, onSuccess = { processApplyResult ->
            val result = processApplyResult.result
            if (result.succeeded && result.data == true) {
                viewModel.updateRequestInfo(processApplyResult.askId, processApplyResult.accept)
            } else {
                toast(result.message ?: getString(R.string.toast_handle_request_join_group_failed))
            }
        })
        viewModel.getLastWatchRequestPageTime()
        viewModel.getRequestList(args.targetUid, true)
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        GroupsRequestListModelState::refreshFlag,
        GroupsRequestListModelState::requestList,
        GroupsRequestListModelState::requestListGetTimestamp,
        GroupsRequestListModelState::loadMore,
        GroupsRequestListModelState::lastWatchRequestPageTime
    ) { refreshFlag, requestListAsync, requestListGetTimestamp, loadMore, lastWatchRequestPageTime ->
        if (requestListAsync is Success) {
            viewModel.updateLastWatchRequestPageTime(requestListGetTimestamp)
            val requestList = requestListAsync.invoke()
            requestList.forEach { requestInfo ->
                add {
                    GroupJoinRequestItem(
                        requestInfo,
                        lastWatchRequestPageTime <= (requestInfo.createTime ?: 0),
                        itemClickedListener
                    ).id("GroupChatRequestItem-$refreshFlag-${requestInfo.id}")
                }
            }
        }
        if (loadMore !is Uninitialized) {
            if (requestListAsync is Success && requestListAsync.invoke().isNotEmpty()) {
                loadMoreFooter(loadMore, idStr = "LoadMoreFooter-$refreshFlag", endText = "") {
                    viewModel.getRequestList(args.targetUid, false)
                }
            }
        }
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_GROUPS_REQUEST
}