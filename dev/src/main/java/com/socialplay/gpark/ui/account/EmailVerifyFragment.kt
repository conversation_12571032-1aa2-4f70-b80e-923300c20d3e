package com.socialplay.gpark.ui.account

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.account.EmailScene
import com.socialplay.gpark.data.model.account.EmailSendResult
import com.socialplay.gpark.databinding.FragmentAccountEmailVerifyBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.StringUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.replaceChinese
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * created by liyanfeng on 2022/6/22 9:28 上午
 * @describe: 忘记密码
 */
class EmailVerifyFragment : BaseFragment<FragmentAccountEmailVerifyBinding>() {

    private val viewModel by viewModel<EmailVerifyViewModel>()
    private val countDownTimer: CountDownTimer by lazy { getCountDownTimer() }
    private val args by navArgs<EmailVerifyFragmentArgs>()
    private val accountInteractor: AccountInteractor by inject()
    private var emailTextContent: String = ""

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAccountEmailVerifyBinding? {
        return FragmentAccountEmailVerifyBinding.inflate(inflater, container, false)
    }

    override fun init() {
        Timber.tag("Account-EmailVerify").d("${args.verifyScene.key} requestKey=${args.requestResultKey}")
        initObserve()
        initView()
        initData()
    }

    private fun initView() {
        binding.apply {
            when (args.verifyScene) {
                EmailScene.RetrievePassword -> {
                    tbl.setTitle(getString(R.string.text_forgot))
                    tvTips.text = getString(R.string.account_find_password_by_bind_email)
                    etEmail.hint = getString(R.string.enter_email)
                }

                EmailScene.BindEmail -> {
                    tbl.setTitle(getString(R.string.text_email_address))
                    tvTips.text = getString(R.string.account_email_address_bind_tips)
                    etEmail.hint = getString(R.string.enter_email_address)
                }

                EmailScene.ChangeEmailOld -> {
                    tbl.setTitle(getString(R.string.account_email_address_change))
                    tvTips.text = getString(R.string.verify_your_email)
                    etEmail.hint = getString(R.string.enter_email_address)
                    accountInteractor.accountLiveData.value?.getEncryptedEmail(false)?.let {
                        etEmail.setText(it)
                        etEmail.isEnabled = false
                        var email = accountInteractor.accountLiveData.value?.bindEmail
                        if (EnvConfig.isParty()) {
                            email = accountInteractor.accountLiveData.value?.phoneNumber
                        }
                        viewModel.postEmailValueChanged(email)
                    }
                }

                EmailScene.ChangeEmailNew -> {
                    tbl.setTitle(getString(R.string.account_email_address_change))
                    tvTips.text = getString(R.string.account_email_address_change_new_tips)
                    etEmail.hint = getString(R.string.enter_email_address)
                }

                EmailScene.ChangeParentEmailNew -> {
                    tbl.setTitle(getString(R.string.account_parent_email_address))
                    tvTips.text = getString(R.string.account_change_parent_email_address_tips) + getString(R.string.account_parent_email_address_tips)
                    etEmail.hint = getString(R.string.enter_parent_email_address)
                }

                EmailScene.BindParentEmail -> {
                    tbl.setTitle(getString(R.string.account_parent_email_address))
                    tvTips.text = getString(R.string.account_parent_email_address_tips)
                    etEmail.hint = getString(R.string.enter_parent_email_address)
                }

                EmailScene.DieOut -> {
                    tbl.setTitle(getString(R.string.account_setting_account_cancellation))
                    tvTips.text = getString(R.string.verify_your_email)
                    etEmail.hint = getString(R.string.enter_email_address)
                    if (EnvConfig.isParty()) {
                        val starPhoneNumber = StringUtil.getStarPhoneNumber(accountInteractor.accountLiveData.value?.phoneNumber)
                        etEmail.setText(starPhoneNumber)
                        etEmail.isEnabled = false
                        viewModel.postEmailValueChanged(accountInteractor.accountLiveData.value?.phoneNumber)
                    } else {
                        accountInteractor.accountLiveData.value?.getEncryptedEmail(false)?.let {
                            etEmail.setText(it)
                            etEmail.isEnabled = false
                            viewModel.postEmailValueChanged(accountInteractor.accountLiveData.value?.bindEmail)
                        }
                    }

                }
            }

            tbl.setOnBackClickedListener {
                navigateUp()
            }
            binding.tvSend.setOnAntiViolenceClickListener {
                val email = if (args.verifyScene == EmailScene.ChangeEmailOld
                    || args.verifyScene == EmailScene.DieOut
                ) {
                    if (EnvConfig.isParty()) {
                        accountInteractor.accountLiveData.value?.phoneNumber
                    } else {
                        accountInteractor.accountLiveData.value?.bindEmail
                    }
                } else {
                    binding.etEmail.text.toString()
                }
                viewModel.sendEmail(email, args.verifyScene)
            }
            tvSure.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_SHOW_PAGE_CONFIRM_CLICK) {
                    putAll(getCommonAnalyticParams())
                }
                when (args.verifyScene) {
                    EmailScene.BindEmail -> {
                        // 绑定邮箱不需要用/auth/v1/email/check接口来验证
                        viewModel.bindEmail(binding.etEmail.text.toString(), binding.etVerifyCode.text.toString(), args.verifyScene)
                        return@setOnAntiViolenceClickListener
                    }

                    EmailScene.BindParentEmail -> {
                        // 绑定家长邮箱不需要用/auth/v1/email/check接口来验证
                        viewModel.bindParentEmail(binding.etEmail.text.toString(), binding.etVerifyCode.text.toString(), args.verifyScene)
                        return@setOnAntiViolenceClickListener
                    }

                    EmailScene.ChangeEmailNew -> {
                        // 邮箱换绑不需要用/auth/v1/email/check接口来验证
                        args.oldEmailCode?.let {
                            viewModel.bindEmailChange(binding.etEmail.text.toString(), binding.etVerifyCode.text.toString(), args.oldEmailCode!!, args.verifyScene)
                        }
                        return@setOnAntiViolenceClickListener
                    }

                    EmailScene.ChangeParentEmailNew -> {
                        // 家长邮箱换绑不需要用/auth/v1/email/check接口来验证
                        args.oldEmailCode?.let {
                            viewModel.bindParentEmailChange(binding.etEmail.text.toString(), binding.etVerifyCode.text.toString(), args.oldEmailCode!!, args.verifyScene)
                        }
                        return@setOnAntiViolenceClickListener
                    }

                    else -> {
                        viewModel.checkEmail(emailTextContent, binding.etVerifyCode.text.toString(), args.verifyScene)
                    }
                }
            }
            etEmail.addTextChangedListener { viewModel.postEmailValueChanged(it?.toString()) }
            etVerifyCode.addTextChangedListener { viewModel.postPasswordValueChanged(it?.toString()) }
        }

    }

    private fun initData() {

    }

    private fun initObserve() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.emailFlow.collect {
                binding.tvSend.isEnabled = viewModel.isEmailValid(it)
                emailTextContent = it ?: ""
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.emailAndVerifyCodeValidFlow.collect {
                binding.tvSure.isEnabled = it
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.emailAndVerifyCodeCheckResultFlow.collect {
                it?.let { toast(it) }
            }
        }

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.emailSendFlow.collectLatest {
                Timber.tag(EmailVerifyViewModel.TAG).d("emailSendFlow scene:[${it.scene.key}] $it")
                if (it is EmailSendResult.Success) {
                    ToastUtil.showLong(requireContext(), getString(R.string.verify_code_send_success))
                    countDownTimer.start()
                } else if (it is EmailSendResult.Failed) {
                    ToastUtil.showLong(requireContext(), it.message.replaceChinese(getString(R.string.verify_code_send_failed)))
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.emailCheckFlow.collectLatest {
                Timber.tag(EmailVerifyViewModel.TAG).d("emailCheckFlow ${it?.scene?.key} ${if (it is EmailSendResult.Failed) it.message else ""}")
                it?.let {
                    if (it is EmailSendResult.Success) {
                        handleEmailCheckSuccess(it.scene)
                    } else if (it is EmailSendResult.Failed) {
                        handleEmailCheckFailed(it.scene, it.message)
                    }
                }
            }
        }
    }

    private fun handleEmailCheckSuccess(scene: EmailScene) {
        when (scene) {
            EmailScene.RetrievePassword -> {
                MetaRouter.Account.passwordSet(this, binding.etEmail.text.toString(), binding.etVerifyCode.text.toString(), LoginPageSource.EmailVerify(scene), args.gameId)
            }

            EmailScene.BindEmail, EmailScene.BindParentEmail -> {
                Analytics.track(EventConstants.EVENT_SECURITY_BINDEMAIL_RESULT) {
                    put(EventConstants.KEY_LOGIN_RESULT, "1")
                }
                toast(R.string.account_email_address_bind_success)
                args.requestResultKey?.let {
                    setFragmentResult(args.requestResultKey!!, Bundle())
                }
                navigateUp()
            }

            EmailScene.ChangeEmailOld -> {
                findNavController().popBackStack()
                MetaRouter.Account.emailBindChangeNew(this, binding.etVerifyCode.text.toString(), false, LoginPageSource.EmailVerify(scene), args.gameId)
            }

            EmailScene.ChangeEmailNew, EmailScene.ChangeParentEmailNew -> {
                Analytics.track(EventConstants.EVENT_SECURITY_CHANGEEMAIL_RESULT) {
                    put(EventConstants.KEY_LOGIN_RESULT, "1")
                }
                toast(R.string.account_email_address_bind_change_success)
                navigateUp()
            }

            EmailScene.DieOut -> {
                args.requestResultKey?.let {
                    setFragmentResult(args.requestResultKey!!, Bundle().apply { putString(AccountSettingFragment.KEY_DITOUT_VERIFY_CODE, binding.etVerifyCode.text.toString()) })
                }
                navigateUp()
            }
        }
    }

    private fun handleEmailCheckFailed(scene: EmailScene, message: String?) {
        when (scene) {
            EmailScene.RetrievePassword -> {
                toast(message.replaceChinese(getString(R.string.verify_code_invalid)))
            }

            EmailScene.BindEmail -> {
                Analytics.track(EventConstants.EVENT_SECURITY_BINDEMAIL_RESULT) {
                    put(EventConstants.KEY_LOGIN_RESULT, "2")
                }
                toast(message.replaceChinese(getString(R.string.account_email_address_bind_failed)))
            }

            EmailScene.ChangeEmailOld -> {
                toast(message.replaceChinese(getString(R.string.verify_code_invalid)))
            }

            EmailScene.ChangeEmailNew -> {
                Analytics.track(EventConstants.EVENT_SECURITY_CHANGEEMAIL_RESULT) {
                    put(EventConstants.KEY_LOGIN_RESULT, "2")
                }
                toast(message.replaceChinese(getString(R.string.account_email_address_bind_change_failed)))
            }

            EmailScene.ChangeParentEmailNew, EmailScene.BindParentEmail -> {
                toast(message.replaceChinese(getString(R.string.account_email_address_bind_change_failed)))
            }

            EmailScene.DieOut -> {
                toast(message.replaceChinese(getString(R.string.verify_code_invalid)))
            }
        }
    }

    private fun getCountDownTimer() = object : CountDownTimer(60_000, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            val restTime = millisUntilFinished / 1000
            binding.tvSend.apply {
                isEnabled = false
                text = if (restTime > 0) getString(R.string.x_second, restTime.toString()) else getString(R.string.friend_apply_send)
            }
        }

        override fun onFinish() {
            binding.tvSend.apply {
                isEnabled = true
                //isEnabled = viewModel.isEmailValid(binding.etEmail.text.toString()) && viewModel.isVerifyCodeValid(binding.etVerifyCode.text.toString())
                text = getString(R.string.friend_apply_send)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_LOGIN_SHOW_PAGE) {
            putAll(getCommonAnalyticParams())
        }
    }

    private fun getCommonAnalyticParams(): Map<String, Any> {
        return hashMapOf<String, Any>().apply {
            put(EventConstants.KEY_LOGIN_PAGE_NAME, "${getFragmentName()}_${args.verifyScene.key}")
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginPageSource.Unknown.source)
            args.gameId?.let {
                put(EventConstants.KEY_LOGIN_GAME_CODE, it)
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        countDownTimer.cancel()
    }

    override fun loadFirstData() {

    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_ACCOUNT_EMAIL_VERIFY

}