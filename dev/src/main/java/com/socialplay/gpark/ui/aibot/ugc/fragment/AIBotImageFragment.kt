package com.socialplay.gpark.ui.aibot.ugc.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.databinding.FragmentAiBotImageBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.property.viewBinding

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/09/02
 *     desc   :
 *
 */
class AIBotImageFragment :BaseFragment<FragmentAiBotImageBinding>() {
    val viewModel: AIBotUGCCreateViewModel by sharedViewModelFromParentFragment<AIBotUGCCreateViewModel>()
    companion object {
        const val maxSize = 50 * 1024 * 1024L
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotImageBinding? {
        return FragmentAiBotImageBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.clUpload.setOnAntiViolenceClickListener {
            choosePictures(1)
        }
        binding.imgReload.setOnAntiViolenceClickListener {
            choosePictures(1)
        }
        binding.imgPhoto.setOnAntiViolenceClickListener {
            choosePictures(1)
        }
        binding.loadingProgress.setOnAntiViolenceClickListener {

        }
        viewModel.uploadImage.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.succeeded) {
                    updateImageStatus(!it.data.isNullOrEmpty())
                    if (!it.data.isNullOrEmpty()) {
                        Glide.with(requireContext()).load(it.data)
                            .transform(CenterCrop(), RoundedCorners(12.dp))
                            .into(binding.imgPhoto)
                    } else {
                        binding.imgPhoto.setImageResource(0)
                    }
                } else {
                    binding.imgPhoto.setImageResource(0)
                    ToastUtil.showShort(it.message)
                    updateImageStatus(false)
                }
            }
            binding.loadingProgress.gone()
        }
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_BOTTOM_BG).transform(CenterCrop(), RoundedCorners(10.dp)).into(binding.bgLineAiBotUgcLoading)
    }
    private fun updateImageStatus(isSelect:Boolean){
        binding.clUpload.visible(!isSelect)
        binding.imgReload.visible(isSelect)
        binding.bgLineAiBotUgcLoading.visible(!isSelect)
    }
    private fun choosePictures(count: Int) {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(count)
            .setFilterMaxFileSize(maxSize)
            .setImageEngine(GlideEngine)
            .setCompressEngine(LubanCompressEngine())
            .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(requireContext()))
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    if (isBindingAvailable()){
                        binding.loadingProgress.visible()
                    }
                    result?.let { viewModel.uploadImage(it[0].compressPath, requireContext()) }
                }

                override fun onCancel() {
                    if (isBindingAvailable()){
                        binding.loadingProgress.gone()
                    }
                }
            })
    }
    override fun loadFirstData() {

    }

    override fun getFragmentName(): String =PageNameConstants.FRAGMENT_AI_BOT_IMAGE

}