package com.socialplay.gpark.ui.developer.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import com.meta.pandora.Pandora
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.util.DeviceUtil
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2021/6/7
 */
class DevAppParamsViewModel : ViewModel() {

    private val deviceInteractor by lazy { GlobalContext.get().get<DeviceInteractor>() }
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    private val currentUser by lazy { accountInteractor.accountLiveData.value }

    fun getAppParams(context: Context) = liveData<List<Pair<String, Any>>> {
        val displayMetrics = context.resources.displayMetrics
        val result = mutableListOf(
            "PKG" to context.packageName,
            "Version Code" to BuildConfig.VERSION_CODE,
            "Version Name" to BuildConfig.VERSION_NAME,
            "pandoraVersion" to Pandora.version(),
            "isDebug" to BuildConfig.DEBUG,
            "Build Time" to BuildConfig.BUILD_TIME,
            "Build Type" to BuildConfig.BUILD_TYPE,
            "channel" to deviceInteractor.channelId,
            "smid" to deviceInteractor.smid,
            "oaid" to deviceInteractor.oaid,
            "deviceId" to deviceInteractor.deviceId,
            "androidId" to deviceInteractor.androidId,
            "installationId" to deviceInteractor.installationId,
            "uniquePsuedoId" to deviceInteractor.uniquePsuedoId,
            "imei" to deviceInteractor.imei,
            "currentUser" to getUser(),
            "Base Url New" to BuildConfig.BASE_URL,
            "rom" to getRom(),
            "Resolution(w*h)" to "${displayMetrics.widthPixels}*${displayMetrics.heightPixels}",
        )
        val allParam = DevAppParamsWrapper.getAppParams(context)
        allParam.addAll(result)
        emit(allParam)
    }

    private fun getUser(): String {
        return currentUser.toString()
    }

    private fun getRom(): String {
        val romType = DeviceUtil.getRomType()
        return if (romType == DeviceUtil.ROM_TYPE.MIUI) {
            "xiaomi"
        } else if (romType == DeviceUtil.ROM_TYPE.EMUI) {
            "huawei"
        } else if (romType == DeviceUtil.ROM_TYPE.Flyme) {
            "meizu"
        } else {
            "other"
        }
    }
}