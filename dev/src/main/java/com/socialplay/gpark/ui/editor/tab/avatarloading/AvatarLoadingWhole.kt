package com.socialplay.gpark.ui.editor.tab.avatarloading

import android.animation.ValueAnimator
import android.view.animation.AccelerateInterpolator
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.IncludeAvatarLoadingBinding
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask

/**
 * Created by bo.li
 * Date: 2022/8/17
 * Desc: 角色展示全程遮罩
 */
class AvatarLoadingWhole : IAvatarLoadingListener {

    private lateinit var loadingBinding: IncludeAvatarLoadingBinding
    private val avatarAlphaAnimator by lazy {
        ValueAnimator.ofFloat(1F, 1F, 1F, 0.3F, 1F).setDuration(2200).apply {
            repeatCount = -1
            repeatMode = ValueAnimator.RESTART
            interpolator = AccelerateInterpolator()
            addUpdateListener {
                loadingBinding.ivMaskNew.alpha = it.animatedValue as Float
            }
        }
    }

    override fun setLoadingView(binding: IncludeAvatarLoadingBinding) {
        loadingBinding = binding
        Glide.with(loadingBinding.root).load(CacheCdnImageTask.DEFAULT_AVATAR)
            .into(binding.ivMaskNew)
        Glide.with(loadingBinding.root).load(CacheCdnImageTask.BG_AVATAR).placeholder(R.color.white)
            .into(binding.vLoadingBgNew)
    }

    override fun showLoading() {
        if (!loadingBinding.root.isVisible) {
            loadingBinding.root.isVisible = true
            loadingBinding.vLoadingBgNew.isVisible = true
            loadingBinding.ivMaskNew.isVisible = true
            loadingBinding.animLoadingViewNew.isVisible = true
            loadingBinding.animLoadingViewNew.playAnimation()
            if (!avatarAlphaAnimator.isStarted) {
                avatarAlphaAnimator.start()
            }
        }
    }

    override fun hideLoading() {
        if (loadingBinding.root.isVisible) {
            loadingBinding.root.isVisible = false
            loadingBinding.vLoadingBgNew.isVisible = false
            loadingBinding.ivMaskNew.isVisible = false
            loadingBinding.animLoadingViewNew.isVisible = false
            loadingBinding.animLoadingViewNew.cancelAnimation()
            if (avatarAlphaAnimator.isRunning) {
                avatarAlphaAnimator.cancel()
            }
        }
    }

    override fun onClearBinding() {
        loadingBinding.animLoadingViewNew.cancelAnimation()
        avatarAlphaAnimator.cancel()
    }
}