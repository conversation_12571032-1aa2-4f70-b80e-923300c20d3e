package com.socialplay.gpark.ui.account.setting

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.socialplay.gpark.data.model.MineActionItem
import com.socialplay.gpark.data.model.MineActionJump
import com.socialplay.gpark.databinding.AdapterMineItemBinding
import com.socialplay.gpark.databinding.ViewSettingLogoutBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.view.preference.PreferenceItemStyle
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/14
 * desc   :
 * </pre>
 */


class MineItemAdapter : BasePagingDataAdapter<MineActionItem, AdapterMineItemBinding>(CALLBACK) {

    companion object {
        private const val ICON = "ICON"
        private const val NAME = "NAME"
        private val CALLBACK = object : DiffUtil.ItemCallback<MineActionItem>() {
            override fun areItemsTheSame(oldItem: MineActionItem, newItem: MineActionItem): Boolean {
                return oldItem.displayNameResId == newItem.displayNameResId
            }

            override fun areContentsTheSame(oldItem: MineActionItem, newItem: MineActionItem): Boolean {
                return oldItem == newItem
            }
        }
    }

    private var isSet = false

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterMineItemBinding {
        return AdapterMineItemBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterMineItemBinding>, item: MineActionItem, position: Int) {
        if (item.jump is MineActionJump.LogoutActionItem) {
            holder.binding.root.setStyle(PreferenceItemStyle.Custom(ViewSettingLogoutBinding.inflate(LayoutInflater.from(context)).root))
//        } else if(item.jump is MineActionJump.Space) {
//            // 待定，看UX需不需要，做UI的分隔，有需要再改这里
        } else {
            holder.binding.root.setStyle(
                PreferenceItemStyle.Text(
                    context.getString(item.displayNameResId),
                    showLabelRedDot = item.showLabelRedDot
                )
            )
        }
    }

    fun setIsSet(){
        isSet = true
    }
}