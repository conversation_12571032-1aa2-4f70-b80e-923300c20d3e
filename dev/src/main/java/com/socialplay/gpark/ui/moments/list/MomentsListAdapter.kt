package com.socialplay.gpark.ui.moments.list

import android.content.Context
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.moments.PlotTemplate
import com.socialplay.gpark.databinding.AdapterPlotItemBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context

/**
 * 2024/10/10
 */
class MomentsListAdapter : BasicQuickAdapter<PlotTemplate, AdapterPlotItemBinding>(), LoadMoreModule {

    override fun convert(holder: BaseVBViewHolder<AdapterPlotItemBinding>, item: PlotTemplate) {
        Glide.with(holder.itemView)
            .load(item.materialUrl)
            .placeholder(R.drawable.placeholder_corner)
            .into(holder.binding.ivImg)
        holder.binding.tvShortNum.text = formatShort(holder.binding.context, item.useCount)
        holder.binding.tvName.text = item.templateName
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int) =
        parent.createViewBinding(AdapterPlotItemBinding::inflate)

    private fun formatShort(context: Context, short: Int): String {
        return context.getString(
            R.string.moment_user_format,
            UnitUtil.formatKMCount(short.toLong())
        )
    }
}