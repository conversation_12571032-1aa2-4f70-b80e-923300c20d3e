package com.socialplay.gpark.ui.gamedetail.room

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameRoomDetail
import com.socialplay.gpark.data.model.GameRoomMembers
import com.socialplay.gpark.databinding.ItemDetailRoomListBinding
import com.socialplay.gpark.databinding.ItemDetailRoomUserBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter

/**
 * xingxiu.hou
 * 2022/4/15
 */
class GameRoomListAdapter : BasicQuickAdapter<GameRoomDetail, ItemDetailRoomListBinding>(),
    LoadMoreModule {

    private fun getItemBg(pos: Int): Int {
        return when (pos % 3) {
            0 -> R.drawable.shape_room_list_item_bg_1
            1 -> R.drawable.shape_room_list_item_bg_2
            2 -> R.drawable.shape_room_list_item_bg_3
            else -> R.drawable.shape_room_list_item_bg_1
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<ItemDetailRoomListBinding>,
        item: GameRoomDetail,
    ) {
        val bg = getItemBg(holder.layoutPosition)
        holder.binding.ivRoomItemBg.setBackgroundResource(bg)
        holder.binding.tvRoomMember.text = "${item.number}/${item.limitNumber}"
        holder.binding.tvRoomJoin.isEnabled = item.number < item.limitNumber
        holder.binding.rvRoomUser.layoutManager = GridLayoutManager(context, 8)
        val userAdapter = GameRoomListUserAdapter()
        holder.binding.rvRoomUser.adapter = userAdapter
        userAdapter.setList(item.members)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemDetailRoomListBinding {
        return ItemDetailRoomListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }
}

class GameRoomListUserAdapter : BasicQuickAdapter<GameRoomMembers, ItemDetailRoomUserBinding>() {

    override fun convert(
        holder: BaseVBViewHolder<ItemDetailRoomUserBinding>,
        item: GameRoomMembers,
    ) {
        Glide.with(context)
            .load(item.avatar)
            .placeholder(R.drawable.placeholder_circle)
            .transform(CircleCrop())
            .into(holder.binding.ivRoomUserAvatar)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemDetailRoomUserBinding {
        return ItemDetailRoomUserBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}