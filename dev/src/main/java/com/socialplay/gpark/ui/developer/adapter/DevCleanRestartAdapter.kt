package com.socialplay.gpark.ui.developer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterDeveloperClearRestartBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * xingxiu.hou
 * 2021/7/9
 */
class DevCleanRestartAdapter :
    BaseAdapter<Pair<String, Pair<String, String>>, AdapterDeveloperClearRestartBinding>() {

    override fun convert(holder: BindingViewHolder<AdapterDeveloperClearRestartBinding>, item: Pair<String, Pair<String, String>>, position: Int) {
        holder.binding.tvName.text = item.first
        val text = if (item.second.first.isEmpty()) {
            context.getString(R.string.debug_no_config)
        } else {
            "${item.second.first}\n${item.second.second}"
        }
        holder.binding.tvValue.text = text
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterDeveloperClearRestartBinding {
        return AdapterDeveloperClearRestartBinding.inflate(
            layoutInflater,
            parent,
            false
        )
    }


}