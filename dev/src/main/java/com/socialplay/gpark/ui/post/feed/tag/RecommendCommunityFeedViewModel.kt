package com.socialplay.gpark.ui.post.feed.tag

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class RecommendCommunityFeedModelState(
    override val refresh: Async<List<CommunityFeedInfo>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val scrollToTop: Async<Boolean> = Uninitialized,
    val hotTopics: List<PostTag>? = null,
    val hotTopicPage: Int = 1,
) : ICommunityFeedModelState {

    override fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelState {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState {
        return copy(notifyCheckVideo = checkVideo)
    }

    override fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        val newRefresh = result.map { wrapper ->
            wrapper.dataList.distinctBy { it.postId }
        }
        return copy(
            refresh = newRefresh,
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }

    override fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke()
                result.map { wrapper ->
                    if (oldList.isNullOrEmpty()) {
                        wrapper.dataList
                    } else {
                        oldList + wrapper.dataList
                    }.distinctBy { it.postId }
                }
            } else {
                refresh
            },
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }
}

class RecommendCommunityFeedViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: RecommendCommunityFeedModelState
) : BaseCommunityFeedViewModel<RecommendCommunityFeedModelState>(
    repository,
    accountInteractor,
    initialState
) {


    @Throws
    private fun refreshFeedFlow(orderType: Int, tagId: Long?, type: String) = flow {
        emit(repository.getCommunityFeed(
            orderType,
            PostTagFeedRequest.getTagType(type),
            null,
            null,
            FEED_PAGE_SIZE,
            1
        ).map {
            notifyCheckVideo()
            it
        }.invoke()
        )
    }

    fun refreshAll(orderType: Int, tagId: Long?, type: String) {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            // 处理type的string和int转换
            combine(
                refreshHotTopicsFlow(),
                refreshFeedFlow(orderType, tagId, type)
            ) { topic, feed ->
                topic to feed
            }.execute { result ->
                copy(
                    hotTopics = result.map { it.first }(),
                    hotTopicPage = if (result is Success) hotTopicPage + 1 else hotTopicPage
                ).feedRefresh(result.map { it.second }) as RecommendCommunityFeedModelState
            }
        }
    }

    fun loadMoreTagFeed(orderType: Int, tagId: Long?, type: String) {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            val nextPage = oldState.nextPage
            repository.getCommunityFeed(
                orderType,
                PostTagFeedRequest.getTagType(type),
                null,
                null,
                FEED_PAGE_SIZE,
                nextPage
            ).execute { result ->
                feedLoadMore(result) as RecommendCommunityFeedModelState
            }
        }
    }

    fun removePublishingItem(ts: Long?) {
        withState { oldState ->
            if (oldState.list.isEmpty()) return@withState
            val newList = ArrayList(oldState.list).apply {
                if (ts != null) {
                    removeAll { it.localId == ts }
                } else {
                    removeAll { it.localPublishing }
                }
            }
            Timber.d("checkcheck_publish, removePublishingItem ts: ${ts}")
            setState {
                copy(refresh = refresh.copyEx(newList))
            }
        }
    }

    fun addPublishingItem(info: CommunityFeedInfo, publishing: Boolean) = viewModelScope.launch {
        val state = awaitState()
        val skip = state.list.any { it.localId == info.localId } && publishing
        if (skip) {
            return@launch
        }
        val newList = ArrayList(state.list)
        val oldIndex = newList.indexOfFirst { it.localId == info.localId || it.postId == info.postId }
        if (oldIndex in 0..newList.lastIndex) {
            newList[oldIndex] = newList[oldIndex].copy(
                localPublishing = publishing,
                postId = info.postId,
                mediaList = info.mediaList,
                tagList = info.tagList,
                status = if (!publishing) {
                    PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                } else {
                    PostDetail.STATUS_OK
                },
                plotCardList = info.plotCardList
            )
            if (!publishing) {
                scrollToTop()
            }
        } else {
            scrollToTop()
            newList.add(
                0,
                info.copy(
                    localPublishing = publishing, status = if (!publishing) {
                        PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                    } else {
                        PostDetail.STATUS_OK
                    }
                )
            )
        }
        Timber.d("checkcheck_publish, addPublishingItem localId: ${info.localId}, postId: ${info.postId}, publishing: ${publishing}")
        setState {
            copy(refresh = refresh.copyEx(newList))
        }
    }

    fun removeScrollToTop() {
        suspend {
            false
        }.execute {
            copy(scrollToTop = it)
        }
    }

    private fun scrollToTop() {
        withState {
            if (it.scrollToTop is Loading) return@withState
            suspend {
                delay(500)
                true
            }.execute {
                copy(scrollToTop = it)
            }
        }
    }

    @Throws
    private fun refreshHotTopicsFlow() = flow {
        val result = runCatching {
            repository.fetchHotTopics(TOPIC_PAGE_SIZE, oldState.hotTopicPage).invoke()
                ?.distinctBy { it.tagId }
        }.getOrNull()
        emit(result)
    }

    companion object :
        KoinViewModelFactory<RecommendCommunityFeedViewModel, RecommendCommunityFeedModelState>() {

        private const val FEED_PAGE_SIZE = 20
        private const val TOPIC_PAGE_SIZE = 6

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: RecommendCommunityFeedModelState
        ): RecommendCommunityFeedViewModel {
            return RecommendCommunityFeedViewModel(get(), get(), state)
        }
    }
}