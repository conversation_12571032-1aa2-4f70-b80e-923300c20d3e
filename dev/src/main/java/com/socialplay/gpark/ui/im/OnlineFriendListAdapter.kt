package com.socialplay.gpark.ui.im

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.databinding.ViewOnlineFriendHeaderBinding
import com.socialplay.gpark.databinding.ViewOnlineFriendListBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.displayName

class OnlineFriendListAdapter(val glide: RequestManager) : BaseAdapter<FriendInfo, ViewOnlineFriendListBinding>() {

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ViewOnlineFriendListBinding {
        return ViewOnlineFriendListBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<ViewOnlineFriendListBinding>, item: <PERSON>In<PERSON>, position: Int) {
        holder.binding.tvName.text = item.displayName
        glide.load(item.avatar).into(holder.binding.ivAvatar)
    }

}