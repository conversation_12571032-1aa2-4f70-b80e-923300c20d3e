package com.socialplay.gpark.ui.editor.module

import android.view.View
import androidx.core.view.isVisible
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.BannerWrapper
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.databinding.ItemModuleHomeBannerBinding
import com.socialplay.gpark.databinding.ItemModuleHomeNewTemplateBinding
import com.socialplay.gpark.databinding.ItemModuleHomeProjectBinding
import com.socialplay.gpark.databinding.ItemModuleHomeProjectTitleBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.editorschoice.adapter.BannerItemAdapter
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.youth.banner.Banner
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle

interface IUgcModuleHomeListener : IBaseEpoxyItemListener {
    fun clickTips(view: View)
    fun clickTemplate()
    fun clickMore(view: View, item: EditorCreationShowInfo, position: Int)
    fun clickContinueBtn(item: EditorCreationShowInfo, position: Int)
    fun showTemplate(view: View)
    fun clickBanner(item: UniJumpConfig, position: Int)
}

data class UgcModuleHomeBannerItem(
    val item: BannerWrapper,
    val listener: IUgcModuleHomeListener
) : ViewBindingItemModel<ItemModuleHomeBannerBinding>(
    R.layout.item_module_home_banner,
    ItemModuleHomeBannerBinding::bind
) {

    override fun ItemModuleHomeBannerBinding.onBind() {
        banner.setHeight(((screenWidth - dp(32)) / 343.0f * 171).toInt())
        val size = item.banners.size
        if (item.position !in 1..size) {
            item.position = 1
        } else {
            banner.startPosition = item.position
        }
        val singleBanner = size <= 1
        // 指示器
        indicator.isVisible = !singleBanner
        indicator.apply {
            val dp6 = dp(6).toFloat()
            val dp8 = dp(8).toFloat()
            setIndicatorStyle(IndicatorStyle.ROUND_RECT)
            setSliderWidth(dp6, dp6)
            setSliderHeight(dp6)
            setSlideMode(IndicatorSlideMode.NORMAL)
            setSliderGap(dp8)
            setPageSize(size)
            notifyDataChanged()
        }
        indicator.setCurrentPosition(item.position - 1)
        val adapter = BannerItemAdapter(
            item.banners.map { it.iconUrl.orEmpty() },
            true,
            glide = listener.getGlideOrNull()
        )
        banner.registerOnPageCallback()
            .setAdapter(adapter)
            .isAutoLoop(size > 1)
            .setLoopTime(5_000L)
            .setBannerGalleryEffect(8, 8, 1.0f)
            .setOnBannerListener { _, position ->
                item.banners.getOrNull(position)?.let {
                    listener.clickBanner(
                        it,
                        position,
                    )
                }
            }
            .addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    indicator.onPageScrolled(position, positionOffset, positionOffsetPixels)
                }

                override fun onPageSelected(position: Int) {
                    item.position = position + 1
                    indicator.onPageSelected(position)
                }

                override fun onPageScrollStateChanged(state: Int) {
                    indicator.onPageScrollStateChanged(state)
                }
            })
    }

    override fun ItemModuleHomeBannerBinding.onUnbind() {
        banner.unregisterOnPageCallback()
            .setOnBannerListener(null)
            .addOnPageChangeListener(null)
            .stop()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        when (visibilityState) {
            VisibilityState.VISIBLE -> {
                view.findViewById<Banner<*, *>>(R.id.banner).start()
            }

            VisibilityState.INVISIBLE -> {
                view.findViewById<Banner<*, *>>(R.id.banner).stop()
            }

            else -> {}
        }
    }
}

data class UgcModuleHomeNewTemplate(
    val listener: IUgcModuleHomeListener
) : ViewBindingItemModel<ItemModuleHomeNewTemplateBinding>(
    R.layout.item_module_home_new_template,
    ItemModuleHomeNewTemplateBinding::bind
) {
    override fun ItemModuleHomeNewTemplateBinding.onBind() {
        tvNewTemplate.setOnAntiViolenceClickListener {
            listener.clickTemplate()
        }
    }

    override fun ItemModuleHomeNewTemplateBinding.onUnbind() {
        tvNewTemplate.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.showTemplate(view)
        }
    }
}

data class UgcModuleHomeProjectTitle(
    val count: Int,
    val max: Int,
    val listener: IUgcModuleHomeListener
) : ViewBindingItemModel<ItemModuleHomeProjectTitleBinding>(
    R.layout.item_module_home_project_title,
    ItemModuleHomeProjectTitleBinding::bind
) {

    override fun ItemModuleHomeProjectTitleBinding.onBind() {
        tvDraftCount.setTextWithArgs(
            R.string.ugc_module_project_count,
            count.coerceAtLeast(0),
            max
        )
        ivTips.setOnAntiViolenceClickListener {
            listener.clickTips(it)
        }
    }

    override fun ItemModuleHomeProjectTitleBinding.onUnbind() {
        ivTips.unsetOnClick()
    }
}

data class UgcModuleHomeProject(
    val item: EditorCreationShowInfo,
    val position: Int,
    val listener: IUgcModuleHomeListener
) : ViewBindingItemModel<ItemModuleHomeProjectBinding>(
    R.layout.item_module_home_project,
    ItemModuleHomeProjectBinding::bind
) {
    override fun ItemModuleHomeProjectBinding.onBind() {
        tvTitle.text = item.getGameName()
        val showTime = item.getShowTime()
        tvTime.text = showTime.formatWholeDate()
        tvTime.visible(showTime > 0)

        val banner = item.getGameBanner()
        if (banner?.startsWith("http") == true) {
            listener.getGlideOrNull()?.run {
                load(banner).placeholder(R.drawable.placeholder)
                    .into(ivGameCover)
            }
        } else {
            listener.getGlideOrNull()?.run {
                load(banner).diskCacheStrategy(DiskCacheStrategy.NONE)
                    .skipMemoryCache(true)
                    .placeholder(R.drawable.placeholder)
                    .into(ivGameCover)
            }
        }
        visibleList(
            vCloudGradient,
            ivHasCloud,
            visible = item.isClouded()
        )

        if (item.isOnlyCloud()) {
            tvContinueBtn.setText(R.string.select_backup_all_caps)
        } else {
            tvContinueBtn.setText(R.string.ugc_module_project_continue_btn)
        }

        tvContinueBtn.setOnAntiViolenceClickListener {
            listener.clickContinueBtn(item, position)
        }
        ivMore.setOnAntiViolenceClickListener {
            listener.clickMore(it, item, position)
        }
    }
}