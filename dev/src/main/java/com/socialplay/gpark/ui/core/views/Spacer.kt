package com.socialplay.gpark.ui.core.views

import android.content.Context
import android.view.ViewGroup
import android.widget.Space
import androidx.annotation.Px
import com.socialplay.gpark.ui.core.ViewItemModel
import com.socialplay.gpark.util.extension.setSize


fun MetaModelCollector.spacer(
    @Px width: Int = ViewGroup.LayoutParams.MATCH_PARENT,
    @Px height: Int = 0,
    id: Long? = null,
    idStr: String? = null,
    spanCount: Int = 1
) {
    add {
        Spacer(
            width, height, spanCount
        ).apply {
            id?.let { id(id) } ?: idStr?.let { id(idStr) } ?: id("Spacer-$it")
            spanSizeOverride { totalSpanCount, _, _ ->
                spanCount.coerceAtMost(totalSpanCount)
            }
        }
    }
}

data class Spacer(
    @Px private val width: Int,
    @Px private val height: Int,
    val spanCount: Int
) : ViewItemModel<Space>() {

    override fun Space.onBind() {
        setSize(<EMAIL>, <EMAIL>)
    }

    override fun createView(parent: ViewGroup, context: Context): Space {
        return Space(context).apply {
            layoutParams = ViewGroup.LayoutParams(<EMAIL>, <EMAIL>)
        }
    }

}