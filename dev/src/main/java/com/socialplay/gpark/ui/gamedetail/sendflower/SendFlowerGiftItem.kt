package com.socialplay.gpark.ui.gamedetail.sendflower

import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.databinding.ItemSendFlowerDialogBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible


interface IFlowerGiftItemListener {
    fun onClicked(gift: GiftFlower, isCustomize: Boolean, isSelected: Boolean)
}

data class SendFlowerGiftItem(
    val glide: RequestManager?,
    val giftFlower: GiftFlower,
    val isCustomize: <PERSON><PERSON><PERSON>,
    val isSelected: <PERSON><PERSON><PERSON>,
    val listener: IFlowerGiftItemListener,
) : ViewBindingItemModel<ItemSendFlowerDialogBinding>(
    R.layout.item_send_flower_dialog,
    ItemSendFlowerDialogBinding::bind
) {
    override fun ItemSendFlowerDialogBinding.onBind() {
        root.setOnClickListener {
            listener.onClicked(giftFlower, isCustomize, isSelected)
        }
        if (isSelected) {
            itemRoot.setBackgroundResource(R.drawable.bg_round_16_9242ff_stroke_2)
        } else {
            itemRoot.background = null
        }
        if (isCustomize) {
            tvCoins.gone()
            tvMinimumDesc.visible()
            tvFlowerCount.setText(R.string.dialog_send_flowers_item_customize_title)
        } else {
            tvCoins.visible()
            tvMinimumDesc.gone()
            tvFlowerCount.text = giftFlower.name
            tvCoins.text = UnitUtilWrapper.formatCoinCont(giftFlower.getSingleGiftPrice())
        }
        glide?.load(giftFlower.icon)
            ?.placeholder(R.drawable.ic_single_flower)
            ?.into(ivGift)
    }
}