package com.socialplay.gpark.ui.account.startup

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentStartupSelectModeBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.sharedViewModel

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   :
 * </pre>
 */
@Parcelize
data class SelectModeFragmentArgs(val mode: Int) : Parcelable {
    companion object {
        const val MODE_0 = 0
        const val MODE_1 = 1
        const val MODE_2 = 2
    }
}

class SelectModeFragment :
    BaseFragment<FragmentStartupSelectModeBinding>(R.layout.fragment_startup_select_mode) {

    override var navColorRes = R.color.color_0E0922

    private val vm: SelectModeViewModel by fragmentViewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private val args by args<SelectModeFragmentArgs>()
    private val onBackPressed = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {}
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStartupSelectModeBinding? {
        return FragmentStartupSelectModeBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        statusBarViewModel.update(false)
        mainViewModel.fetchModeGameInfo()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, onBackPressed)

        Analytics.track(
            EventConstants.MODE_PAGE_SHOW,
            "page" to args.mode
        )

        if (args.mode == SelectModeFragmentArgs.MODE_2) {
            binding.ivTop.setPaddingEx(top = 71.dp, bottom = 1.dp)
            binding.ivTop.setImageResource(R.drawable.ic_select_mode_party)
            binding.tvTopTitle.setText(R.string.select_party)
            binding.tvTopDesc.setText(R.string.select_party_desc)
            binding.ivBottom.setPaddingEx(top = 75.dp, bottom = 19.dp)
            binding.ivBottom.setImageResource(R.drawable.ic_select_mode_loading)
            binding.tvBottomTitle.text = ""
            binding.tvBottomDesc.text = ""
        }

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, onBackPressed)

        binding.tvNextBtn.enableWithAlpha(false)

        binding.ivTop.setOnClickListener {
            vm.updateMode(
                if (args.mode == SelectModeFragmentArgs.MODE_1) {
                    SelectModeState.MODE_WORLD_MAP
                } else {
                    SelectModeState.MODE_PARTY_FEED
                }
            )
        }
        binding.ivBottom.setOnClickListener {
            vm.updateMode(
                if (args.mode == SelectModeFragmentArgs.MODE_1) {
                    SelectModeState.MODE_PARTY_FEED
                } else {
                    SelectModeState.MODE_WORLD_MAP
                }
            )
        }
        binding.tvNextBtn.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.MODE_SELECT_CLICK,
                "page" to PandoraToggle.selectMode,
                "result" to vm.oldState.mode.toString()
            )
            mainViewModel.mode = vm.oldState.mode
            MetaRouter.Main.tabHome(requireContext())
        }

        vm.onEach(SelectModeState::mode) {
            when (it) {
                SelectModeState.MODE_WORLD_MAP -> {
                    if (args.mode == SelectModeFragmentArgs.MODE_1) {
                        binding.ivTop.isSelected = true
                        binding.ivBottom.isSelected = false
                    } else {
                        binding.ivTop.isSelected = false
                        binding.ivBottom.isSelected = true
                    }
                    binding.tvNextBtn.enableWithAlpha(true)
                }

                SelectModeState.MODE_PARTY_FEED -> {
                    if (args.mode == SelectModeFragmentArgs.MODE_1) {
                        binding.ivTop.isSelected = false
                        binding.ivBottom.isSelected = true
                    } else {
                        binding.ivTop.isSelected = true
                        binding.ivBottom.isSelected = false
                    }
                    binding.tvNextBtn.enableWithAlpha(true)
                }

                else -> {
                    binding.ivTop.isSelected = false
                    binding.ivBottom.isSelected = false
                    binding.tvNextBtn.enableWithAlpha(false)
                }
            }
        }
        mainViewModel.selectModeLiveData.observe(viewLifecycleOwner) {
            val iv: ImageView
            val tvTitle: TextView
            val tvDesc: TextView
            if (args.mode == SelectModeFragmentArgs.MODE_1) {
                iv = binding.ivTop
                tvTitle = binding.tvTopTitle
                tvDesc = binding.tvTopDesc
            } else {
                iv = binding.ivBottom
                tvTitle = binding.tvBottomTitle
                tvDesc = binding.tvBottomDesc
            }
            if (it.isNet) {
                Glide.with(iv)
                    .load(it.icon)
                    .placeholder(R.drawable.ic_select_mode_loading)
                    .into(iv)
                tvTitle.text = it.title
                tvDesc.text = it.description
            } else {
                iv.setImageResource(R.drawable.ic_select_mode_map)
                tvTitle.setText(R.string.select_world)
                tvDesc.setText(R.string.select_world_desc)
            }
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_SELECT_MODE
}