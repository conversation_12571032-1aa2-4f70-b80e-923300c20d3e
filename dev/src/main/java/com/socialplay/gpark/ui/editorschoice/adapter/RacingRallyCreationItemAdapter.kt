package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemRacingRallyBinding
import com.socialplay.gpark.ui.editorschoice.adapter.BaseEditorsChoiceItemAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import android.view.View
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.dp

/**
 * Racing Rally item adapter for creation items (subsequent items)
 */
class RacingRallyCreationItemAdapter(
    data: MutableList<ChoiceGameInfo>?,
    private val glide: RequestManager
) : BaseEditorsChoiceItemAdapter<AdapterChoiceCardItemRacingRallyBinding>(data) {

    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterChoiceCardItemRacingRallyBinding {
        return AdapterChoiceCardItemRacingRallyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterChoiceCardItemRacingRallyBinding>,
        item: ChoiceGameInfo
    ) {
        holder.binding.apply {
            // Load image
            glide.load(item.imageUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivGameIcon)
            
            // Set title
            tvGameTitle.text = item.displayName
            
            // Set creator info
            if (!item.nickname.isNullOrEmpty()) {
                tvCreatorNickname.text = item.nickname
                tvCreatorNickname.visibility = View.VISIBLE
                
                // Load creator avatar
                glide.load(item.avatar)
                    .placeholder(R.drawable.icon_default_avatar)
                    .transform(CenterCrop(), RoundedCorners(12.dp))
                    .into(sivCreatorAvatar)
                sivCreatorAvatar.visibility = View.VISIBLE
            } else {
                tvCreatorNickname.visibility = View.GONE
                sivCreatorAvatar.visibility = View.GONE
            }
            
            // Set like count
            item.likeCount?.let { count ->
                if (count > 0) {
                    tvZan.text = count.toString()
                    tvZan.visibility = View.VISIBLE
                } else {
                    tvZan.visibility = View.GONE
                }
            } ?: run {
                tvZan.visibility = View.GONE
            }
        }
    }
}
