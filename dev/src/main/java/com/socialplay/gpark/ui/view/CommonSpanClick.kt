package com.socialplay.gpark.ui.view

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.annotation.ColorInt

class CommonSpanClick(@ColorInt val color: Int, private val showUnderline: Boolean, val call: () -> Unit) : ClickableSpan() {
    override fun onClick(widget: View) {
        call.invoke()
    }

    override fun updateDrawState(ds: TextPaint) {
        ds.isUnderlineText = showUnderline
        ds.color = color
    }
}