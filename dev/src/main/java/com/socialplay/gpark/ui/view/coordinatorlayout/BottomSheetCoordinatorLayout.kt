package com.socialplay.gpark.ui.view.coordinatorlayout

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout.AttachedBehavior
import androidx.core.view.ViewCompat
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
import com.socialplay.gpark.util.extension.runWhenDestroyed


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/11/15
 *     desc   : https://github.com/natario1/BottomSheetCoordinatorLayout
 * </pre>
 */
@CoordinatorLayout.DefaultBehavior(BottomSheetCoordinatorBehavior::class)
class BottomSheetCoordinatorLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : VerticalCoordinatorLayout(context, attrs, defStyleAttr), AttachedBehavior,
    AppBarLayout.OnOffsetChangedListener {

    private var bottomSheetBehavior: BottomSheetCoordinatorBehavior? = null
    private var appBarBehavior: AppBarLayout.Behavior? = null
    private var appBarOffset = 0
    private var hasAppBar = false

    var lifecycleOwner: LifecycleOwner? = null

    init {
        val dummyView = View(context)
        val dummyBehavior = DummyBehavior<View>()
        ViewCompat.setElevation(dummyView, ViewCompat.getElevation(this))
        dummyView.fitsSystemWindows = false
        val lp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        lp.behavior = dummyBehavior
        addView(dummyView, lp)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        if (bottomSheetBehavior != null) return

        bottomSheetBehavior = BottomSheetCoordinatorBehavior.from(this@BottomSheetCoordinatorLayout)

        val abl = findAppBar()
        if (abl != null) {
            abl.addOnOffsetChangedListener(this)
            appBarBehavior = (abl.layoutParams as LayoutParams).behavior as AppBarLayout.Behavior
            appBarBehavior?.setDragCallback(object : AppBarLayout.Behavior.DragCallback() {
                override fun canDrag(appBarLayout: AppBarLayout): Boolean {
                    return true
                }
            })
            hasAppBar = true
        } else {
            hasAppBar = false
        }

        lifecycleOwner?.runWhenDestroyed {
            lifecycleOwner = null
            bottomSheetBehavior = null
            appBarBehavior?.setDragCallback(null)
            appBarBehavior = null
            abl?.removeOnOffsetChangedListener(this)
        }
    }

    private fun findAppBar(): AppBarLayout? {
        for (i in 0 until childCount) {
            val v = getChildAt(i)
            if (v is AppBarLayout) {
                return v
            }
        }
        return null
    }

    fun getState(): Int {
        return bottomSheetBehavior?.state ?: -1
    }

    override fun onOffsetChanged(appBarLayout: AppBarLayout?, verticalOffset: Int) {
        if (verticalOffset == appBarOffset) {
            // Nothing
        } else if (bottomSheetBehavior?.state != BottomSheetBehavior.STATE_EXPANDED) {
            appBarBehavior?.topAndBottomOffset = appBarOffset
        } else {
            appBarOffset = verticalOffset
        }
    }

    fun canScrollUp(): Boolean {
        return if (hasAppBar) {
            appBarOffset != 0
        } else {
            true
        }
    }

    fun hasAppBar(): Boolean {
        return hasAppBar
    }

    override fun getBehavior(): BottomSheetCoordinatorBehavior {
        return BottomSheetCoordinatorBehavior()
    }

    private inner class DummyBehavior<DummyView : View> : Behavior<DummyView> {
        constructor() : super()
        constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

        private fun shouldForwardEvent(
            sheet: BottomSheetCoordinatorLayout,
            fingerGoingUp: Boolean
        ): Boolean {
            return when (sheet.getState()) {
                BottomSheetBehavior.STATE_EXPANDED -> {
                    !sheet.canScrollUp() && !fingerGoingUp
                }

                BottomSheetBehavior.STATE_COLLAPSED,
                BottomSheetBehavior.STATE_DRAGGING,
                BottomSheetBehavior.STATE_SETTLING -> {
                    true
                }

                else -> {
                    false
                }
            }
        }

        override fun onStartNestedScroll(
            coordinatorLayout: CoordinatorLayout,
            child: DummyView,
            directTargetChild: View,
            target: View,
            axes: Int,
            type: Int
        ): Boolean {
            val sheet = coordinatorLayout as BottomSheetCoordinatorLayout
            return if (shouldForwardEvent(sheet, false)) {
                sheet.behavior.onStartNestedScroll(
                    coordinatorLayout,
                    sheet,
                    directTargetChild,
                    target,
                    axes,
                    type
                )
            } else {
                false
            }
        }

        override fun onNestedPreScroll(
            coordinatorLayout: CoordinatorLayout,
            child: DummyView,
            target: View,
            dx: Int,
            dy: Int,
            consumed: IntArray,
            type: Int
        ) {
            val sheet = coordinatorLayout as BottomSheetCoordinatorLayout
            if (shouldForwardEvent(sheet, dy > 0)) {
                sheet.behavior.onNestedPreScroll(
                    coordinatorLayout,
                    sheet,
                    target,
                    dx,
                    dy,
                    consumed,
                    type
                )
            }
        }

        override fun onStopNestedScroll(
            coordinatorLayout: CoordinatorLayout,
            child: DummyView,
            target: View,
            type: Int
        ) {
            val sheet = coordinatorLayout as BottomSheetCoordinatorLayout
            if (shouldForwardEvent(sheet, false)) {
                sheet.behavior.onStopNestedScroll(coordinatorLayout, sheet, target, type)
            }
        }

        override fun onNestedPreFling(
            coordinatorLayout: CoordinatorLayout,
            child: DummyView,
            target: View,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            val sheet = coordinatorLayout as BottomSheetCoordinatorLayout
            return if (shouldForwardEvent(sheet, velocityY > 0)) {
                sheet.behavior.onNestedPreFling(
                    coordinatorLayout,
                    sheet,
                    target,
                    velocityX,
                    velocityY
                )
            } else {
                false
            }
        }
    }

}