package com.socialplay.gpark.ui.profile.fans

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.flow.combine
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/20
 * Desc:
 */

data class UserFansTabModelState(
    val count: RelationCountResult,
    val uuid: String = "",
    val type: Int = UserFansTabFragmentDialog.TYPE_LOCATION_FANS,
    val tabItems: List<CommonTabItem>
) : MavericksState {
    constructor(args: UserFansTabFragmentDialogArgs) : this(
        RelationCountResult(
            args.followCount,
            args.fansCount,
            args.friendCount?:0,
        ),
        args.uuid,
        args.type,
        getTitle(args.uuid)
    )
}

private fun getTitle(uuid: String): List<CommonTabItem> {
    val list = arrayListOf(
        CommonTabItem(R.string.user_fans_title, UserFansItemFragment.TYPE_FANS) {
            UserFansItemFragment.newInstance(
                UserFansItemFragment.TYPE_FANS,
                uuid
            )
        },
        CommonTabItem(R.string.user_follow_title, UserFansItemFragment.TYPE_FOLLOWING) {
            UserFansItemFragment.newInstance(
                UserFansItemFragment.TYPE_FOLLOWING,
                uuid
            )
        },
    )
    if (PandoraToggle.isIMEntrance) {
        list.add(0, CommonTabItem(R.string.user_friends_title, UserFansItemFragment.TYPE_FRIEND) {
            UserFansItemFragment.newInstance(
                UserFansItemFragment.TYPE_FRIEND,
                uuid
            )
        })

    }
    return list.toList()
}

class UserFansTabViewModel(
    private val repository: IMetaRepository,
    initialState: UserFansTabModelState
) : BaseViewModel<UserFansTabModelState>(initialState) {

    init {
        updateCount()
    }

    fun cancelJump2Fans() = withState { oldState->
        setState {
            copy(type = 0)
        }
    }

    private fun updateCount() {
        withState {
            if (PandoraToggle.isOpenAiBot) {
                repository.getRelationCount(
                    RelationCountRequest(
                        it.uuid,
                        RelationType.Follow.value
                    )
                ).combine(
                    repository.getRelationCount(
                        RelationCountRequest(
                            it.uuid,
                            RelationType.AiBot.value
                        )
                    )
                ) { a, b ->
                    RelationCountResult(a.forwardCount + b.forwardCount, a.reverseCount, oldState.count.friendCount)
                }.execute {
                    val data = it.invoke()
                    val info = RelationCountResult(
                        data?.forwardCount ?: 0,
                        data?.reverseCount ?: 0,
                        oldState.count.friendCount
                    )
                    copy(count = if (it is Success) info else count)
                }
            } else {
                repository.getRelationCount(
                    RelationCountRequest(
                        it.uuid,
                        RelationType.Follow.value
                    )
                )
                    .execute {
                        val data = it.invoke()
                        val info = RelationCountResult(
                            data?.forwardCount ?: 0,
                            data?.reverseCount ?: 0,
                            oldState.count.friendCount
                        )
                        copy(count = if (it is Success) info else count)
                    }
            }

        }
    }

    companion object : KoinViewModelFactory<UserFansTabViewModel, UserFansTabModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UserFansTabModelState
        ): UserFansTabViewModel {
            return UserFansTabViewModel(get(), state)
        }
    }
}