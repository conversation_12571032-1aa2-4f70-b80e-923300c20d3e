package com.socialplay.gpark.ui.account.startup

import androidx.annotation.ColorRes
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.AgeOption
import com.socialplay.gpark.databinding.ItemStartupSelectBirthdayBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.backgroundTintListByRes
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.unsetOnClick

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/11/17
 *     desc   :
 * </pre>
 */
interface ISelectBirthdayListener {
    fun pickAge(age: Int)
}

fun MetaModelCollector.createSelectBirthdayItem(
    item: AgeOption,
    selected: <PERSON><PERSON><PERSON>,
    listener: ISelectBirthdayListener
) {
    add {
        SelectBirthdayItem(
            item,
            selected,
            R.color.white_6,
            R.color.color_F6F6F6,
            R.color.white,
            R.color.textColorPrimary,
            listener
        ).id(item.age)
    }
}

fun MetaModelCollector.createSelectBirthdayItemV2(
    item: AgeOption,
    selected: Boolean,
    listener: ISelectBirthdayListener
) {
    add {
        SelectBirthdayItem(
            item,
            selected,
            R.color.color_F6F6F6,
            R.color.color_FFEF30,
            R.color.textColorPrimary,
            R.color.textColorPrimary,
            listener
        ).id(item.age)
    }
}

data class SelectBirthdayItem(
    val item: AgeOption,
    val selected: Boolean,
    @ColorRes val unselectedBgTint: Int,
    @ColorRes val selectedBgTint: Int,
    @ColorRes val unselectedTvColor: Int,
    @ColorRes val selectedTvColor: Int,
    val listener: ISelectBirthdayListener
) : ViewBindingItemModel<ItemStartupSelectBirthdayBinding>(
    R.layout.item_startup_select_birthday,
    ItemStartupSelectBirthdayBinding::bind
) {
    override fun ItemStartupSelectBirthdayBinding.onBind() {
        root.text = item.ageLabel
        if (selected) {
            root.setTextColorByRes(selectedTvColor)
            root.backgroundTintListByRes(selectedBgTint)
        } else {
            root.setTextColorByRes(unselectedTvColor)
            root.backgroundTintListByRes(unselectedBgTint)
        }
        root.setOnClickListener { listener.pickAge(item.age) }
    }

    override fun ItemStartupSelectBirthdayBinding.onUnbind() {
        root.unsetOnClick()
    }
}