package com.socialplay.gpark.ui.view

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.max


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/25
 *     desc   :
 * </pre>
 */
class FlowLayoutManager(private val gap: Int) : RecyclerView.LayoutManager() {
    override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
        detachAndScrapAttachedViews(recycler)

        var left = paddingLeft
        var top = paddingTop
        val right = width - paddingRight
        var maxChildHeight = 0

        for (i in 0.until(itemCount)) {
            val child = recycler.getViewForPosition(i)
            addView(child)
            measureChildWithMargins(child, 0, 0)

            val childWidth = getDecoratedMeasuredWidth(child)
            val childHeight = getDecoratedMeasuredHeight(child)

            if (right - left < childWidth) {
                left = paddingLeft
                top += maxChildHeight
                maxChildHeight = childHeight
            }

            layoutDecorated(child, left, top, left + childWidth, top + childHeight)
            left += childWidth + gap

            maxChildHeight = max(maxChildHeight, childHeight)
        }
    }
}