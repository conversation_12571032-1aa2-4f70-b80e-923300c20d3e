package com.socialplay.gpark.ui.profile.home

import android.app.Activity
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.databinding.ItemProfileEntranceBinding
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.ui.view.ExposureLinearLayout
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.youth.banner.adapter.BannerAdapter

class ProfileEntranceAdapter(
    data: List<List<ProfileEntrance>>,
    val itemListener: IProfileEntranceListener
) : BannerAdapter<List<ProfileEntrance>, ProfileEntranceAdapter.ViewHolder>(data) {

    private var itemVisibilityChangeListener: ((position: Int, isVisible: Boolean) -> Unit)? = null

    private val analyticsMap = mutableMapOf<Int, Boolean>()

    fun updateData(data: List<List<ProfileEntrance>>) {
        //这里的代码自己发挥，比如如下的写法等等
        mDatas.clear()
        mDatas.addAll(data)
        notifyDataSetChanged()
    }

    fun setItemVisibilityChangeListener(listener: ((position: Int, isVisible: Boolean) -> Unit)) {
        itemVisibilityChangeListener = listener
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(parent.createViewBinding(ItemProfileEntranceBinding::inflate))
    }

    override fun onBindView(
        holder: ViewHolder,
        data: List<ProfileEntrance>,
        position: Int,
        size: Int
    ) {
        holder.bind(data, position)

        holder.binding.root.setVisibilityListener(object : ExposureLinearLayout.VisibilityListener {
            override fun onVisibilityChanged(isVisible: Boolean) {
                val pos = getRealPosition(holder.bindingAdapterPosition)

                val isVisibleBefore = analyticsMap[pos] ?: false
                if (isVisibleBefore != isVisible) {
                    analyticsMap[pos] = isVisible
                    itemVisibilityChangeListener?.invoke(pos, isVisible)
                }
            }
        })
    }

    inner class ViewHolder(val binding: ItemProfileEntranceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private var curPosition: Int = 0

        fun bind(entrances: List<ProfileEntrance>, position: Int) {
            curPosition = position
            val context = itemView.context
            if (context is Activity && context.isDestroyed) return
            when (entrances.size) {
                4 -> {
                    binding.tvEntrance1.bindEntrance(entrances[0])
                    binding.tvEntrance2.bindEntrance(entrances[1])
                    binding.tvEntrance3.bindEntrance(entrances[2])
                    binding.tvEntrance4.bindEntrance(entrances[3])
                }

                3 -> {
                    binding.tvEntrance1.bindEntrance(entrances[0])
                    binding.tvEntrance2.bindEntrance(entrances[1])
                    binding.tvEntrance3.bindEntrance(entrances[2])
                    binding.tvEntrance4.invisible()
                }

                2 -> {
                    binding.tvEntrance1.bindEntrance(entrances[0])
                    binding.tvEntrance2.bindEntrance(entrances[1])
                    binding.tvEntrance3.invisible()
                    binding.tvEntrance4.invisible()
                }

                1 -> {
                    binding.tvEntrance1.bindEntrance(entrances[0])
                    binding.tvEntrance2.invisible()
                    binding.tvEntrance3.invisible()
                    binding.tvEntrance4.invisible()
                }

                0 -> {
                    binding.tvEntrance1.invisible()
                    binding.tvEntrance2.invisible()
                    binding.tvEntrance3.invisible()
                    binding.tvEntrance4.invisible()
                }
            }
        }

        private fun MetaTextView.bindEntrance(item: ProfileEntrance) {
            visible()
            setText(item.titleResId)
            compoundDrawables(top = item.iconResId)
            setOnAntiViolenceClickListener {
                itemListener.clickProfileEntrance(item)
            }
        }
    }
}