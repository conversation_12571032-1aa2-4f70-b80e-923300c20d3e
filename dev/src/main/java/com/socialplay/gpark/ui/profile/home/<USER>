package com.socialplay.gpark.ui.profile.home

import android.content.ComponentCallbacks
import android.os.Parcelable
import android.os.SystemClock
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.interactor.UserChangedCallback
import com.socialplay.gpark.data.interactor.UserMemberInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.event.UserDataUpdateEvent
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.SubscribeProductInfo
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.user.BadgeInfo
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.data.model.user.UserRelation
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.outfit.ProfileUgcDesignFragment
import com.socialplay.gpark.ui.post.feed.profile.ProfileCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.profile.ProfileCommunityFeedFragmentArgs
import com.socialplay.gpark.ui.profile.HomepageTab
import com.socialplay.gpark.ui.profile.outfit.ProfileTabOutfitFragment
import com.socialplay.gpark.ui.profile.recent.ProfileTabRecentFragment
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcFragment
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcFragmentArgs
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.LruMap
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

@Parcelize
data class ProfileArgs(
    val uuid: String,
    val from: String,
    val checkFollow: Boolean,
    val isMe: Boolean,
    val isFromBottom: Boolean,
    val entry: Int
) : Parcelable

data class ProfileState(
    val isMe: Boolean,
    val isMineTab: Boolean,
    val uuid: String,
    val tabs: List<Pair<HomepageTab, () -> Fragment>>? = null,
    val selectedTab: Int = 0,
    val userProfile: Async<UserProfileInfo> = Uninitialized,
    val followCallback: Async<Pair<Boolean, Long>> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
    val memberInfo: MemberInfo? = null,
    val subscribeProduct: Async<SubscribeProductInfo> = Uninitialized,
    val shareProfileData: Async<ShareRawData.UserExtra> = Uninitialized,
    val currentClothes: Async<List<ProfileCurrentCloth>> = Uninitialized,
    val isFollow: Boolean = false,
    val fansCount: Long = 0,
    val blockCallback: Async<Boolean> = Uninitialized,
    val goBuildBtnVisible: Boolean = false,
    val banner: Async<UniJumpConfig?> = Uninitialized,
    val drawerItems: List<HomeDrawerEntrance> = buildDrawerEntrance(isMineTab, memberInfo),
    val entrances: List<List<ProfileEntrance>> = buildEntrance(isMe, memberInfo),
    val groupCount: GroupChatCount? = null,
    val nickname: String? = null,
    val signature: String? = null,
    val portrait: String? = null,
    val wholeBodyImage: String? = null,
    val mediaLinks: List<ProfileLinkInfo>? = null,
    val avatarPopupAsync: Async<UniJumpConfig> = Uninitialized
) : MavericksState {

    companion object {
        fun buildDrawerEntrance(
            isMineTab: Boolean,
            memberInfo: MemberInfo?
        ): List<HomeDrawerEntrance> {
            if (!isMineTab) return emptyList()
            return buildList {
                add(HomeDrawerEntrance.EditProfile())
                add(HomeDrawerEntrance.AddFriend())
                add(HomeDrawerEntrance.AccountSetting())
                if (PandoraToggle.walletEntrance.split(",").any { it == "1" || it == "2" }) {
                    add(HomeDrawerEntrance.MyBalance())
                }
                if (PandoraToggle.isVipPlusOpen() || (PandoraToggle.isVipStatusOpen() && memberInfo?.isActive() == true)) {
                    add(HomeDrawerEntrance.Membership())
                }
                add(HomeDrawerEntrance.PrivacySetting(false))
                add(HomeDrawerEntrance.AboutUs())
            }
        }

        fun buildEntrance(isMe: Boolean, memberInfo: MemberInfo?): List<List<ProfileEntrance>> {
            if (!isMe) return emptyList()
            val list = ArrayList<ArrayList<ProfileEntrance>>()
            fun addEntrance(entrance: ProfileEntrance) {
                if (list.isEmpty()) {
                    list.add(arrayListOf(entrance))
                } else if (list.last().size < 4) {
                    list.last().add(entrance)
                } else {
                    list.add(arrayListOf(entrance))
                }
            }
            if (PandoraToggle.isVipPlusOpen() || (PandoraToggle.isVipStatusOpen() && memberInfo?.isActive() == true)) {
                addEntrance(ProfileEntrance.Premium())
            }
            if (PandoraToggle.walletEntrance.split(",").any { it == "1" || it == "2" }) {
                addEntrance(ProfileEntrance.Wallet())
            }
            if (PandoraToggle.showProfileGroupChatLayout) {
                addEntrance(ProfileEntrance.MyGroup())
            }
            addEntrance(ProfileEntrance.Build())
            addEntrance(ProfileEntrance.TryOn())
            return list
        }
    }

    val avatarPopup = avatarPopupAsync()
}

class ProfileViewModel(
    initState: ProfileState,
    private val repo: com.socialplay.gpark.data.IMetaRepository,
    val payInteractor: IPayInteractor,
    private val memberInteractor: UserMemberInteractor,
    val tTaiInteractor: TTaiInteractor,
    val accountInteractor: AccountInteractor,
    private val ugcRepository: UgcRepository,
    private val h5PageConfigInteractor: H5PageConfigInteractor,
    private val metaKV: MetaKV
) : BaseViewModel<ProfileState>(initState) {

    private val bannerClose = LruMap<String, String>(20)

    val nickname
        get() = oldState.let {
            if (it.isMe) {
                accountInteractor.accountLiveData.value?.nickname ?: it.userProfile()?.nickname
            } else {
                it.userProfile()?.nickname
            }
        }

    private var showTryOnTipsInit = true
    var showTryOnTips: Boolean = true
        get() {
            if (showTryOnTipsInit) {
                showTryOnTipsInit = false
                field = metaKV.account.showProfileTryOnTips
            }
            return field
        }
        set(value) {
            if (value != field) {
                field = value
                metaKV.account.showProfileTryOnTips = value
            }
        }

    private val memberChangeListener = Observer<MemberInfo?> {
        if (PandoraToggle.isVipStatusOpen() && it?.isActive() == true) {
            val s = oldState
            val newDrawerEntrances = ProfileState.buildDrawerEntrance(s.isMineTab, it)
            val newEntrances = ProfileState.buildEntrance(s.isMe, it)
            setState {
                copy(
                    drawerItems = newDrawerEntrances,
                    entrances = newEntrances,
                    memberInfo = it
                )
            }
        }
    }

    private val userChangedCallback: UserChangedCallback = { _, new ->
        withState { s ->
            new ?: return@withState
            val newUuid = new.uuid
            if (!newUuid.isNullOrBlank() && newUuid != s.uuid) {
                setState {
                    copy(
                        uuid = newUuid,
                        tabs = null,
                        selectedTab = 0,
                        userProfile = Uninitialized,
                        shareProfileData = Uninitialized,
                    )
                }
                getProfile(force = true)
                return@withState
            }
            if (s.portrait != new.portrait || s.wholeBodyImage != new.wholeBodyImage) {
                setState {
                    copy(
                        portrait = new.portrait,
                        wholeBodyImage = new.wholeBodyImage
                    )
                }
            }
        }
    }

    val isBlocked get() = oldState.userProfile()?.let { isBlocked(it.blockRelation) } ?: false

    private var skipFollowEvent = false

    init {
        if (initState.isMe) {
            accountInteractor.addUserChangedCallback(userChangedCallback)
            memberInteractor.vipPlusLiveData.observeForever(memberChangeListener)
        }
        registerHermes()
        onEach(ProfileState::userProfile) {
            if (it !is Success) return@onEach
            val userProfile = it()
            withState { s ->
                if (s.isMe || !isBlocked(userProfile.blockRelation)) {
                    if (s.tabs.isNullOrEmpty()) {
                        val tabs = buildList {
                            if (PandoraToggle.openProfileUgc) {
                                add(HomepageTab.UGC to {
                                    ProfilePublishedUgcFragment.newInstance(
                                        ProfilePublishedUgcFragmentArgs(
                                            s.isMe,
                                            s.uuid
                                        )
                                    )
                                })
                            }
                            add(HomepageTab.RECENT to {
                                ProfileTabRecentFragment.newInstance(
                                    s.isMe,
                                    s.uuid
                                )
                            })
                            add(HomepageTab.POST to {
                                ProfileCommunityFeedFragment.newInstance(
                                    ProfileCommunityFeedFragmentArgs(s.uuid, s.isMe)
                                )
                            })
                            if ((s.isMe || s.isMineTab || userProfile.canTryOn()) && PandoraToggle.enableStyleCommunity) {
                                add(HomepageTab.CLOTHES to {
                                    ProfileTabOutfitFragment.newInstance(
                                        s.isMe || s.isMineTab,
                                        s.uuid
                                    )
                                })
                            }
                            if (PandoraToggle.enableProfileLibraryTab) {
                                add(HomepageTab.ASSETS to {
                                    ProfileUgcDesignFragment.newInstance(
                                        s.isMe,
                                        s.uuid
                                    )
                                })
                            }
                        }
                        setState { copy(tabs = tabs) }
                    }
                } else {
                    setState { copy(tabs = null, selectedTab = 0) }
                }
                if (s.isMe) {
                    if (s.isMineTab) {
                        accountInteractor.updatePrivacySwitch(
                            ootdPrivateSwitch = userProfile.ootdPrivateSwitch,
                            updateCache = true
                        )
                    } else {
                        accountInteractor.updatePrivacySwitch(
                            ootdPrivateSwitch = userProfile.ootdPrivateSwitch,
                            updateCache = false
                        )
                    }
                    accountInteractor.updateSparkBalance(userProfile.userRemainLightUpQuantity)
                }
            }
        }
        onEach(ProfileState::blockCallback) {
            if (it is Success && it()) {
                getProfile(force = true)
            }
        }
        getProfile()
        if (initState.isMe) {
            getBanners()
        } else {
            getGroupChatCount()
        }
    }

    fun changeSelectTab(position: Int) = withState { s ->
        if (s.selectedTab != position) {
            setState { copy(selectedTab = position) }
        }
    }

    fun getProfile(force: Boolean = false) = withState { s ->
        if (s.userProfile is Loading && !force) return@withState
        combine(
            repo.queryUserProfile(s.uuid),
            tTaiInteractor.getTTaiWithTypeV3<List<ProfileLinkInfo>>(TTaiKV.ID_LINK_PROFILE)
        ) { userProfileResult, linkConfigRaw ->
            val userProfile = userProfileResult.data
            check(userProfile != null)
            userProfile.also {
                val linkConfig = linkConfigRaw.orEmpty().associateBy { it.type }
                it.externalLinks = it.externalLinks.map { item ->
                    val linkInfo = linkConfig[item.type]
                    item.copy(icon = linkInfo?.icon.orEmpty())
                }
            }
        }.execute(retainValue = ProfileState::userProfile) { result ->
            when (result) {
                is Success -> {
                    copy(
                        userProfile = result,
                        fansCount = result().fansCount,
                        isFollow = if (!isMe) result().isFollow else isFollow,
                        nickname = result().nickname,
                        signature = result().signature,
                        portrait = result().portrait,
                        wholeBodyImage = result().wholeBodyImage,
                        mediaLinks = result().externalLinks
                    )
                }

                else -> {
                    copy(userProfile = result)
                }
            }
        }
    }

    fun changeFollow() = withState { s ->
        val toFollow = !s.isFollow
        val ts = SystemClock.elapsedRealtime()
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, s.uuid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_FOLLOW_PROFILE)
            put(
                EventParamConstants.KEY_TYPE,
                if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (toFollow) {
            repo.relationAddV2(s.uuid, RelationType.Follow.value)
        } else {
            repo.relationDelV2(s.uuid, RelationType.Follow.value)
        }.map {
            check(it)
            skipFollowEvent = true
            EventBus.getDefault()
                .post(UserFollowEvent(s.uuid, toFollow, UserFollowEvent.FROM_PROFILE))
            true
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(
                        isFollow = toFollow,
                        fansCount = if (toFollow) {
                            fansCount + 1
                        } else {
                            (fansCount - 1).coerceAtLeast(0)
                        },
                        followCallback = Success(toFollow to ts)
                    )
                }

                is Fail -> {
                    copy(followCallback = Fail(result.error))
                }

                else -> copy(followCallback = Loading())
            }
        }
    }

    fun getSubsProduct() = viewModelScope.launch {
        if (PayProvider.ENABLE_PREMIUM) {
            repo.getSubsProduct(IAPConstants.IAP_PRODUCT_TYPE_MEMBER_AWARD).map {
                val first = it.data?.firstOrNull()
                check(first != null)
                first
            }.execute {
                copy(subscribeProduct = it)
            }
        }
    }

    fun getUserExtra4Share() = withState { s ->
        repo.getRecentPlayGameListV4(s.uuid, 1, 20).combine(
            repo.qrCodeCreateFlow(
                QrCodeCreateRequest.homePage()
            )
        ) { recentGames, qrCode ->
            val url = qrCode.data?.url
            if (recentGames.code != 200 || !qrCode.succeeded || url.isNullOrBlank()) {
                null
            } else {
                ShareRawData.UserExtra(
                    recentGames.data?.validList.orEmpty(),
                    url
                )
            }
        }.map {
            check(it != null)
            it
        }.execute {
            copy(shareProfileData = it)
        }
    }

    fun clearNewFollowerRecord() = viewModelScope.launch {
        accountInteractor.clearRedBadge(BaseAccountInteractor.TYPE_NEW_FOLLOWER) {
            it?.copy(newFollower = BadgeInfo(false, 0, null))
        }
    }

    fun blockUser() = withState { s ->
        repo.relationAddV2(s.uuid, RelationType.Block.value).map {
            check(it)
            it
        }.execute {
            copy(blockCallback = it)
        }
    }

    fun unblockUser() = withState { s ->
        repo.relationDelV2(s.uuid, RelationType.Block.value).map {
            check(it)
            it
        }.execute {
            copy(blockCallback = it)
        }
    }

    fun getH5PageUrl(code: Long) = h5PageConfigInteractor.getH5PageUrl(code)

    fun checkPayConnect() {
        payInteractor.checkConnect()
    }

    fun updateBuildBtnVisible(visible: Boolean) = withState { s ->
        if (s.goBuildBtnVisible != visible) {
            setState { copy(goBuildBtnVisible = visible) }
        }
    }

    fun getBanners() = withState {
        val today = DateUtil.getTodayString()
        metaKV.miscKV.getProfileBannerClose()?.forEach {
            if (it.value == today) {
                bannerClose[it.key] = it.value
            }
        }
        repo.getProfileBannerList().map {
            it.data?.firstOrNull { !bannerClose.containsKey(it.id ?: it.uniqueCode) }
        }.execute {
            copy(banner = it)
        }
    }

    fun closeBanner() = withState { s ->
        val banner = s.banner() ?: return@withState
        val today = DateUtil.getTodayString()
        bannerClose[banner.id ?: banner.uniqueCode] = today
        metaKV.miscKV.updateProfileBannerClose(bannerClose.filter { it.value == today })
        setState { copy(banner = Success(null)) }
    }

    fun isBlocked(blockRelation: Int) =
        blockRelation == UserRelation.Me2Other.value || blockRelation == UserRelation.Both.value

    fun getGroupChatCount() = withState { s ->
        if (!PandoraToggle.showProfileGroupChatLayout) return@withState
        repo.getGroupChatCountByUuid(s.uuid).execute { result ->
            when (result) {
                is Success -> {
                    copy(groupCount = result())
                }

                else -> {
                    this
                }
            }
        }
    }

    fun trackClick(tab: String?, me: String?, other: String?) = withState { s ->
        if (s.isMineTab) {
            tab ?: return@withState
            Analytics.track(
                EventConstants.EVENT_HOME_CLICK,
                "click" to tab
            )
        } else if (s.isMe) {
            me ?: return@withState
            Analytics.track(
                EventConstants.EVENT_PROFILE_OWNER_CLICK,
                "click" to me
            )
        } else {
            other ?: return@withState
            Analytics.track(
                EventConstants.EVENT_PROFILE_VISITOR_CLICK,
                "click" to other
            )
        }
    }

    /**
     * 检查是否需要展示角色运营弹窗
     */
    fun checkShowAvatarPopupDialog() {
        if (!PandoraToggleWrapper.enableAvatarPopup) return
        repo.getAvatarPopupOperationConfig().map {
            check(it.succeeded && it.data != null) {
                it.message.orEmpty()
            }
            it.data!!
        }.execute {
            copy(avatarPopupAsync = it)
        }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) = withState { s ->
        if (skipFollowEvent) {
            skipFollowEvent = false
            return@withState
        }
        if (s.isMe) return@withState
        if (s.uuid == event.uuid && s.isFollow != event.followStatus) {
            val ts = SystemClock.elapsedRealtime()
            setState {
                copy(
                    isFollow = event.followStatus,
                    fansCount = if (event.followStatus) {
                        fansCount + 1
                    } else {
                        (fansCount - 1).coerceAtLeast(0)
                    },
                    followCallback = Success(event.followStatus to ts)
                )
            }
        }
    }

    @Subscribe
    fun onUserDataUpdateEvent(event: UserDataUpdateEvent) = withState { s ->
        if (!s.isMe) return@withState
        when (event.eventType) {
            UserDataUpdateEvent.TYPE_NICKNAME -> {
                setState {
                    copy(nickname = event.nickname)
                }
            }

            UserDataUpdateEvent.TYPE_SIGNATURE -> {
                setState {
                    copy(signature = event.signature)
                }
            }

            UserDataUpdateEvent.TYPE_EXTERNAL_LINK_ADD -> {
                event.profileLinkInfo ?: return@withState
                val newMediaLinks =
                    s.mediaLinks.insertAt(-1, event.profileLinkInfo)?.distinctBy { it.id }
                setState {
                    copy(mediaLinks = newMediaLinks)
                }
            }

            UserDataUpdateEvent.TYPE_EXTERNAL_LINK_DEL -> {
                event.profileLinkInfo ?: return@withState
                val newMediaLinks = s.mediaLinks?.filter { it.id != event.profileLinkInfo.id }
                setState {
                    copy(mediaLinks = newMediaLinks)
                }
            }
        }
    }

    override fun onCleared() {
        val oldState = oldState
        if (oldState.isMe) {
            accountInteractor.removeUserChangedCallback(userChangedCallback)
            memberInteractor.vipPlusLiveData.removeObserver(memberChangeListener)
        }
        unregisterHermes()
        bannerClose.clear()

        super.onCleared()
    }

    companion object :
        KoinViewModelFactory<ProfileViewModel, ProfileState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ProfileState
        ): ProfileViewModel {
            return ProfileViewModel(state, get(), get(), get(), get(), get(), get(), get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): ProfileState {
            val args = viewModelContext.args as ProfileArgs
            val accountInteractor: AccountInteractor = get()
            return ProfileState(
                args.isMe,
                args.isFromBottom,
                if (args.isFromBottom) {
                    accountInteractor.curUuidOrNull ?: args.uuid
                } else {
                    args.uuid
                }
            )
        }
    }
}