package com.socialplay.gpark.ui.post.tab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.CommunityBlockType
import com.socialplay.gpark.databinding.FragmentCommunityFeedTabBinding
import com.socialplay.gpark.databinding.TabIndicatorHomeCommunityBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.kol.KolCreatorFragment
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.main.OnBottomNavItemReselected
import com.socialplay.gpark.ui.post.feed.tag.RecommendCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragment
import com.socialplay.gpark.ui.videofeed.VideoFeedViewModel
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTouchSlop
import com.socialplay.gpark.util.extension.visible
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/22
 * Desc:
 */
class CommunityFeedTabFragment :
    BaseFragment<FragmentCommunityFeedTabBinding>(R.layout.fragment_community_feed_tab),
    OnBottomNavItemReselected {

    private val viewModel: CommunityFeedTabViewModel by fragmentViewModel()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private lateinit var pagerAdapter: CommonTabStateAdapter

    private val publishAnimView: PublishSceneAnimView = PublishSceneAnimView()
    private val mainViewModel by sharedViewModel<MainViewModel>()

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            viewModel.changeSelectedTag(position)
        }
    }

    companion object {
        private const val MAX_PROGRESS = 100
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentCommunityFeedTabBinding? {
        return FragmentCommunityFeedTabBinding.inflate(inflater, container, false)
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        val tabBinding = TabIndicatorHomeCommunityBinding.bind(cv)
        tabBinding.tvNormal.isInvisible = select
        tabBinding.tvSelected.isInvisible = !select

        updateTabStyle()
    }

    private fun updateTabStyle(){
        val selectedTabPosition = binding.tlFeed.selectedTabPosition

        val selectedTab = binding.tlFeed.getTabAt(selectedTabPosition)
        val selectedTagType = selectedTab?.tag as? CommunityBlockType

        for (i in 0 until binding.tlFeed.tabCount) {
            val tab = binding.tlFeed.getTabAt(i)
            val tagType = tab?.tag as? CommunityBlockType
            val customView = tab?.customView

            if (customView != null && tagType != null) {
                val binding = TabIndicatorHomeCommunityBinding.bind(customView)

                if(i != selectedTabPosition && selectedTagType?.otherTagTextColorWhenSelected != null){
                    binding.tvNormal.setTextColor(selectedTagType.otherTagTextColorWhenSelected)
                }else{
                    binding.tvNormal.setTextColor(tagType.normalTextColor)
                }

                val textShadow = selectedTagType?.textShadowWhenSelected
                if (textShadow != null) {
                    binding.tvNormal.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                    binding.tvSelected.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                } else {
                    binding.tvNormal.setShadowLayer(0F, 0F, 0F, 0)
                    binding.tvSelected.setShadowLayer(0F, 0F, 0F, 0)
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        publishAnimView.setDismissCallback {
            binding.includeChoosePublishScene.root.isVisible = true
        }
        initViewPager()
        initEvent()
        initData()
    }

    private fun initData() {
        viewModel.onEach(CommunityFeedTabModelState::tagList, deliveryMode = uniqueOnly()) {
            val tabs = it.map { type ->
                { createTabFragment(type) }
            }
            pagerAdapter.fragmentCreators = tabs
            pagerAdapter.notifyDataSetChanged()
        }
        viewModel.onEach(CommunityFeedTabModelState::jumpTabInfo) { bundle ->
            Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG).d("feedTab receive jumpType:$bundle")
            bundle ?: return@onEach
            viewModel.changeSelectedTag(bundle.tab)
        }
        viewModel.onEach(
            CommunityFeedTabModelState::publishingState
        ) { publishingState ->
            binding.pbPublish.isVisible = publishingState != null && !publishingState.publishOver()
            publishingState ?: return@onEach
            binding.pbPublish.progress =
                ((publishingState.uploadStatus?.curTotalPercent ?: 0.0) * MAX_PROGRESS).toInt()
                    .coerceAtMost(MAX_PROGRESS)
            if (publishingState.publishOver()) {
                viewModel.clearPublishing()
            }
        }
        viewModel.registerToast(CommunityFeedTabModelState::toastData)
        viewModel.onEach(CommunityFeedTabModelState::selectedTag) {
            viewModel.oldState.tagList.getOrNull(it)?.let { type ->
                binding.includeChoosePublishScene.root.visible(type.isPostPublishVisible)
                binding.vpFeed.isUserInputEnabled = type.isNestedScrollEnabled
            }

            if (binding.vpFeed.currentItem != it && it in 0 until pagerAdapter.itemCount) {
                binding.vpFeed.setCurrentItem(it, false)
            }
        }
        viewModel.collectRelay(mainViewModel.tabPendingConsumeDataFlow, mainViewModel)

        viewModel.onEach(CommunityFeedTabModelState::jumpTabInfo) { bundle ->
            Timber.tag(VideoFeedViewModel.DATA_RELAY_TAG).d("parent receive bundle: ${bundle}")
            if (bundle != null && bundle.tab !is CommunityBlockType.VideoFeed) {
                // 视频流有自己的操作，让他自己clear
                viewModel.clearRelay()
            }
        }

        mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.vMsgRedDot.visible(it > 0)
        }
    }

    private fun createTabFragment(type: CommunityBlockType): Fragment {
        return when (type) {
            is CommunityBlockType.Discover -> {
                KolCreatorFragment.newInstance(true, type.toTagCommunityFeedFragmentArgs().blockName)
            }

            is CommunityBlockType.Recommend -> {
                RecommendCommunityFeedFragment.newInstance(type.toTagCommunityFeedFragmentArgs())
            }

            else -> {
                TagCommunityFeedFragment.newInstance(type.toTagCommunityFeedFragmentArgs())
            }
        }
    }

    private fun initEvent() {
        binding.includeChoosePublishScene.root.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_COMMUNITY_ADD_POST_CLICK) {
                put(EventParamConstants.KEY_SOURCE, EventParamConstants.CLICK_PUBLISH_SOURCE_FEED)
            }
            publishAnimView.show(this, binding.includeChoosePublishScene.root)
            binding.includeChoosePublishScene.root.isVisible = false
        }

        binding.ivMsgEntrance.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatTabFragment(
                this,
                source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_MAPS
            )
        }
    }

    private fun initViewPager() {
        withState(viewModel) {

            val vc = ViewConfiguration.get(requireContext())
            binding.vpFeed.setTouchSlop(vc.scaledPagingTouchSlop * 3)

            binding.vpFeed.offscreenPageLimit = 1
            binding.tlFeed.addOnTabSelectedListener(tabCallback)
            binding.vpFeed.registerOnPageChangeCallback(vpCallback)
            pagerAdapter = CommonTabStateAdapter(
                it.tagList.map { type ->
                    { createTabFragment(type) }
                },
                childFragmentManager,
                viewLifecycleOwner.lifecycle
            )
            binding.vpFeed.adapterAllowStateLoss = pagerAdapter
            tabLayoutMediator = TabLayoutMediator(
                binding.tlFeed,
                binding.vpFeed
            ) { tab: TabLayout.Tab, position: Int ->
                withState(viewModel) {
                    val tabBinding = TabIndicatorHomeCommunityBinding.inflate(layoutInflater)
                    val name = kotlin.runCatching { it.blockNameList[position] }.getOrNull()
                    tabBinding.tvNormal.text = name
                    tabBinding.tvSelected.text = name

                    val type = it.tagList[position]

                    tabBinding.tvSelected.setTextColor(type.selectedTextColor)
                    tabBinding.tvNormal.setTextColor(type.normalTextColor)

                    tab.customView = tabBinding.root
                    tab.tag = type
                }
            }
            tabLayoutMediator?.attach()
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        publishAnimView.setDismissCallback(null)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.tlFeed.clearOnTabSelectedListeners()
        binding.vpFeed.adapterAllowStateLoss = null
        binding.vpFeed.unregisterOnPageChangeCallback(vpCallback)
        FeedVideoHelper.releaseByScene(FeedVideoHelper.SCENE_COMMUNITY_TAB_FEED)
        super.onDestroyView()
    }

    override fun isEnableTrackPageExposure(): Boolean {
        return false
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_FEED_TAB
    override fun onBottomNavReselected(item: MainBottomNavigationItem) {
        viewModel.scrollCurrentFeed()
    }
}