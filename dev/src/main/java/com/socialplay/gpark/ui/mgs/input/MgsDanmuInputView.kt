package com.socialplay.gpark.ui.mgs.input


import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.RelativeLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.meta.biz.mgs.data.model.Member
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewMgsDanmuInputBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getChatBg
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getCountColor
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getCountStrokeColor
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getInputBg
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getTextColor
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil.getUserStroke
import com.socialplay.gpark.ui.mgs.dialog.MgsInputDialog
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatInputListener
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.voice.WaveVoiceView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible
import java.util.Random


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/02/22
 *     desc   :
 *
 */
class MgsDanmuInputView : RelativeLayout {

    lateinit var binding: ViewMgsDanmuInputBinding
    private var listener: OnMgsFloatInputListener? = null



    constructor(
        context: Context
    ) : super(context) {
        initView(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(attrs)
    }


    fun setInputListener(listener: OnMgsFloatInputListener) {
        this.listener = listener
    }


    /**
     * 展示输入框（加入房间）
     * 隐藏输入框（离开房间）
     */
    fun setInputViewVisible(visible: Boolean) {
        binding.root.isVisible = visible
    }

    private fun initView(attrs: AttributeSet?) {
        binding = ViewMgsDanmuInputBinding.inflate(LayoutInflater.from(context), this, true)
        initVoiceView()

    }
    fun updateSelfAudioState(open: Boolean){
        binding.voiceOpenView.invisible(!open)
        binding.voiceShield.visible(!open)
    }

    private fun initVoiceView() {
        changeVoiceVisible(false)
        changeVoiceState(false)
    }

    /**
     * 改变语音按钮是否可见
     */
    fun changeVoiceVisible(isShow: Boolean) {
        binding.clVoice.isInvisible = !isShow
    }

    /**
     * 改变语音按钮状态
     */
    fun changeVoiceState(isOpen: Boolean, lastVolume: Int = 0, nowVolume: Int = 0) {
        binding.voiceOpenView.updateVoiceState(isOpen, lastVolume, nowVolume)
    }

    fun handleClickAudio(isOpen: Boolean) {
        val event =
            if (isOpen) EventConstants.MGS_VOICE_OPEN_CLICK else EventConstants.MGS_VOICE_CLOSE_CLICK
        Analytics.track(event) {
            listener?.getGameAnalytics()?.let { putAll(it) }
            put("type", "bullet_chat")
        }
        if (!PermissionRequest.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)) {
            listener?.checkMicPermission()
            return
        }
        listener?.changeVoiceMuteState(isOpen)
    }

    private fun showInputDialog(metaApp: Application, activity: Activity) {
        MgsDialogManager.showInputDialog(
            metaApp,
            activity,
            metaApp.getString(R.string.mgs_input_hint_message),
            "",
            150,
            object : MgsInputDialog.OnInputDialogListener {
                override fun onEditeChange(text: String?) {
                }

                override fun onSubmit(text: String?) {
                    listener?.getGameAnalytics()?.let {
                        Analytics.track(EventConstants.EVENT_CLICK_MGS_MESSAGE_INPUT) {
                            putAll(it)
                            put("location", "button2")
                        }
                    }
                    text?.let { sendMessage(it) }
                }

                override fun cancel(text: String?) {

                }

            }
        )
    }


    /**
     * 发送消息
     */
    private fun sendMessage(text: String): Boolean {
        listener?.getGameAnalytics()?.let {
            Analytics.track(EventConstants.EVENT_CLICK_MGS_MESSAGE_SEND) {
                putAll(it)
                put("location", "msg_button")
            }
        }
        return if (text.isBlank()) {
            false
        } else {
            listener?.sendMessage(text)

            true
        }
    }


    fun actionOutside() {

    }

    fun setBallOnTouchListener(onDanmuTouchListener: OnTouchListener) {
        binding.voiceOpenView.setOnTouchListener(onDanmuTouchListener)
        binding.voiceShield.setOnTouchListener(onDanmuTouchListener)
        binding.etInput.setOnTouchListener(onDanmuTouchListener)
        binding.clUser.setOnTouchListener(onDanmuTouchListener)
    }

    fun onClickFloatingBall(v: View, metaApp: Application, activity: Activity) {
        when (v.id) {
            R.id.etInput -> {
                showInputDialog(metaApp, activity)
            }
            R.id.cl_user -> {
                val time = listener?.getSelUserInfoByUuid()
                    ?.let { DanMuDataUtil.getDanMuLevelTime(it) }?:0
                listener?.getCurrentActivity()?.let {
                    MgsDialogManager.showDanmuDialog(
                        context,
                        it,
                        time
                    )
                }
            }
            R.id.voiceOpenView -> {
                if (binding.voiceOpenView.isOpenVoice) {
                    handleClickAudio(false)
                } else {
                    handleClickAudio(true)
                }
            }
            R.id.voiceShield -> {
                ToastUtil.gameShowShort(context.getString(R.string.voice_shield))
            }
        }
    }

    fun updateUserInfo(member: Member?) {
        val time = member?.let { DanMuDataUtil.getDanMuLevelTime(it) } ?: 0
        binding.clInput.background = context.getDrawable(getInputBg(member))
        if (time < 1) {
            Glide.with(context).load(member?.avatar).error(R.drawable.icon_default_avatar)
                .into(binding.userNormal)
            binding.clUserNormal.visible()
            binding.clUserIcon.gone()
            return
        }
        Glide.with(context).load(member?.avatar).error(R.drawable.icon_default_avatar)
            .into(binding.imgUserIcon)
        binding.clUserNormal.gone()
        binding.clUserIcon.visible()
        binding.textCount.text = time.toString()
        getUserStroke(member)?.let { binding.rlUserIcon.setImageResource(it) }
        binding.etInput.setTextColorByRes(getTextColor(member))
        if (time >= 15) {
            val layoutParams = binding.clInput.layoutParams
            layoutParams.height = ScreenUtil.dp2px(context, 34f)
            binding.clInput.layoutParams = layoutParams
        } else {
            val layoutParams = binding.clInput.layoutParams
            layoutParams.height = ScreenUtil.dp2px(context, 30f)
            binding.clInput.layoutParams = layoutParams
        }
        binding.textCount.setTextColorByRes(getCountColor(member))
        binding.textCount.setStrokeColor(
            ContextCompat.getColor(
                context,
                getCountStrokeColor(member)
            )
        )
    }

    fun updateInputViewVisible(visible: Boolean) {
       binding.rlInput.visible(visible)
    }


}