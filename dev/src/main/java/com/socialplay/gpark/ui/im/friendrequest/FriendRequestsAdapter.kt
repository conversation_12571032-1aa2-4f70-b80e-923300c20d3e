package com.socialplay.gpark.ui.im.friendrequest

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.friend.FriendRequestInfoWrapper
import com.socialplay.gpark.databinding.AdapterFriendRequestListBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.view.FolderTextView
import com.socialplay.gpark.ui.view.UserLabelListener
import com.socialplay.gpark.util.extension.addIf
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.tagIds

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */
class FriendRequestsAdapter(
    private val glide: GlideGetter,
    private val listener: UserLabelListener
) : BasePagingDataAdapter<FriendRequestInfoWrapper, AdapterFriendRequestListBinding>(CALLBACK) {

    companion object {
        private val CALLBACK = object : DiffUtil.ItemCallback<FriendRequestInfoWrapper>() {
            override fun areItemsTheSame(oldItem: FriendRequestInfoWrapper, newItem: FriendRequestInfoWrapper): Boolean {
                return oldItem == newItem
            }

            override fun areContentsTheSame(oldItem: FriendRequestInfoWrapper, newItem: FriendRequestInfoWrapper): Boolean {
                return oldItem == newItem && oldItem.expandState == newItem.expandState
            }

            override fun getChangePayload(oldItem: FriendRequestInfoWrapper, newItem: FriendRequestInfoWrapper): Any? {
                return arrayListOf<String>()
                    .addIf(CHANGED_STATE) { oldItem.info.status != newItem.info.status }
                    .addIf(CHANGED_EXPAND) { oldItem.expandState != newItem.expandState }
                    .ifEmptyNull()
            }
        }

        private const val CHANGED_STATE = "CHANGED_STATE"
        private const val CHANGED_EXPAND = "CHANGED_EXPAND"
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterFriendRequestListBinding {
        return AdapterFriendRequestListBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterFriendRequestListBinding>, item: FriendRequestInfoWrapper, position: Int) {
        val binding = holder.binding
        glide()?.run {
            load(item.info.avatar).placeholder(R.drawable.icon_default_avatar)
                .into(binding.ivAvatar)
        }
        binding.tvUserName.text = item.info.name
        binding.tvReson.text = item.info.reason
        binding.labelGroup.show(
            item.info.tagIds,
            item.info.labelInfo,
            glide = glide()
        )
        setRequestState(holder, item)
        setReason(holder, item)
    }

    override fun convert(holder: BindingViewHolder<AdapterFriendRequestListBinding>, item: FriendRequestInfoWrapper, position: Int, payloads: MutableList<Any>) {
        super.convert(holder, item, position, payloads)
        if (payloads.isEmpty()) {
            return
        }
        val payload = payloads[0] as? ArrayList<Any>
        if (payload.isNullOrEmpty()) {
            return
        }
        payload.forEach {
            when (it) {
                CHANGED_STATE -> {
                    setRequestState(holder, item)
                }

                CHANGED_EXPAND -> {
                    setReason(holder, item)
                }
            }
        }
    }

    private fun setRequestState(holder: BindingViewHolder<AdapterFriendRequestListBinding>, item: FriendRequestInfoWrapper) {
        val binding = holder.binding
        val hasProcessed = item.info.status != 0
        binding.ivDisAgree.isVisible = !hasProcessed
        binding.tvAgree.isVisible = !hasProcessed
        binding.tvApplyState.isVisible = hasProcessed
        binding.tvApplyState.setText(if (item.info.status == 1) R.string.friend_agreed else R.string.friend_disagreed)
    }

    private fun setReason(holder: BindingViewHolder<AdapterFriendRequestListBinding>, item: FriendRequestInfoWrapper) {
        val binding = holder.binding
        binding.tvReson.text = item.info.reason?.trim()
        binding.tvReson.setOnFoldTextStateChangeListener(object : FolderTextView.OnFoldTextStateChangeListener {
            override fun onFoldChange(isFold: Boolean) {
                item.expandState = !isFold
            }
        })
        binding.tvReson.isFold = item.expandState
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<AdapterFriendRequestListBinding>) {
        super.onViewAttachedToWindow(holder)
        holder.binding.labelGroup.setListener(listener)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<AdapterFriendRequestListBinding>) {
        holder.binding.tvReson.setOnFoldTextStateChangeListener(null)
        holder.binding.labelGroup.setListener(null)
        super.onViewDetachedFromWindow(holder)
    }
}