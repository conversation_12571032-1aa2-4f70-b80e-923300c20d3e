package com.socialplay.gpark.ui.party

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.FragmentPartyBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeFragment
import com.socialplay.gpark.ui.home.HomeViewModel
import com.socialplay.gpark.ui.home.adapter.GameItemPlayedAdapter
import com.socialplay.gpark.ui.post.PostMoreDialog
import com.socialplay.gpark.util.extension.collectWithLifecycle
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.collectLatest
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * create by: bin on 2023/3/1
 */
class PartyFragment : BaseEditorFragment<FragmentPartyBinding>() {
    private val viewModel by viewModel<PartyViewModel>()
    private val homeViewModel by viewModel<HomeViewModel>()

    private val adapter by lazy { PartyAdapter(Glide.with(this)) }

    private val playedAdapter = GameItemPlayedAdapter(::glide)

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPartyBinding? {
        return FragmentPartyBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    private fun initView() {
        initRv()
        initRecentPlayedView()
        binding.loading.setRetry {
            viewModel.loadData()
        }
    }

    private fun initRecentPlayedView() {
        binding.rvRecentlyPlayed.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        playedAdapter.setOnItemClickListener { _, position ->
            playedAdapter.peek(position)?.let {
                Timber.tag(ChoiceHomeFragment.TAG).d("playedAdapter.ItemClick $it")
                val resid = ResIdBean().setGameId(it.id).setCategoryID(CategoryId.PARTY).setParam1(position)
                MetaRouter.GameDetail.navigate(this@PartyFragment, it.id, resid, it.packageName?:"", type = "ts")
            }
        }
        binding.rvRecentlyPlayed.adapter = playedAdapter
        binding.rvRecentlyPlayed.setHasFixedSize(true)

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            playedAdapter.loadStateFlow.collectLatest {
                binding.rvRecentlyPlayed.isVisible = playedAdapter.itemCount > 0
                binding.tvPlayedTitle.isVisible = playedAdapter.itemCount > 0
                Timber.tag(ChoiceHomeFragment.TAG).d("collect playedAdapter.loadStateFlow ${it.refresh} isVisible:${playedAdapter.itemCount > 0} ${playedAdapter.itemCount}")
            }
        }

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            homeViewModel.playedGames.collectLatest {
                Timber.tag(ChoiceHomeFragment.TAG).d("collect playedGames $it")
                playedAdapter.submitData(it)
            }
        }
    }

    private fun jump(subInfo: ChoiceGameInfo, resIdBean: ResIdBean) {
        when (subInfo.type) {
            ChoiceContentType.GAME -> {
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", subInfo.code.toString())
                    put("game_type", subInfo.gcMode.toString())
                    putAll(
                        ResIdUtils.getAnalyticsMap(
                            resIdBean
                        )
                    )
                }
                if (subInfo.gcMode == "UGC") {
                    editorGameLaunchHelper?.startUgcGame(this,subInfo.code?:"",subInfo.packageName,subInfo.displayName?:"","",resIdBean)
                } else {
                    playGame(
                        subInfo.code ?: "",
                        subInfo.packageName ?: "",
                        subInfo.iconUrl ?: "",
                        subInfo.displayName ?: "",
                        subInfo.startupExtension,
                        subInfo.isTsGame(),
                        subInfo.isTsGame(),
                        resIdBean
                    )
                }
            }
            ChoiceContentType.LINK -> {
                subInfo.router?.let { router ->
                    jumpToWeb(this, subInfo.title, router)
                }
            }
            else                   -> {

            }
        }
    }

    private fun jumpToWeb(fragment: Fragment, title: String? = null, url: String) {
        if (url.startsWith("http://", ignoreCase = true) or url.startsWith("https", true)) {
            MetaRouter.Web.navigate(fragment, title, url)
        }
    }

    private fun playGame(id: String, packageName: String, icon: String, name: String, startupExtension: String?, isTsGame: Boolean, isMgsGame: Boolean, resIdBean: ResIdBean) {
        MetaRouter.GameDetail.navigate(this@PartyFragment, id, resIdBean, packageName, type = "ts")
    }

    private fun refreshMyGames() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            homeViewModel.playedGames.collectLatest {
                playedAdapter.submitData(it)
            }
        }
    }

    private fun initRv() {
        binding.refresh.setOnRefreshListener {
            viewModel.loadData()
            refreshMyGames()
        }
        binding.rv.adapter = adapter
        adapter.setOnItemClickListener { _, position ->
            val item = adapter.getItem(position)
            val resIdBean = ResIdBean().setGameId(item.code ?: "").setCategoryID(CategoryId.PARTY).setParam1(position)
            jump(item, resIdBean)
        }
        adapter.addChildClickViewIds(R.id.ivMore)
        adapter.setOnItemChildClickListener { view, position ->
            val postMoreDialog = PostMoreDialog()
            postMoreDialog.needCopy = false
            postMoreDialog.callback = {
                when (it) {
                    PostMoreDialog.CALLBACK_KEY_REPORT -> {
                        toast(R.string.report_review_suc)
                    }
                }
            }
            postMoreDialog.show(childFragmentManager, "more")
        }
        adapter.setOnItemShowListener { view, position ->
            val item = adapter.getItem(position)
            Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                put("gameid", item.code ?: "")
                put("packagename", item.packageName ?: "")
                put("game_type", item.typeToString())
                putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setGameId(item.code ?: "").setCategoryID(CategoryId.PARTY).setParam1(position)))
            }
        }
    }

    private fun initData() {
        viewModel.gameListLiveData.observe(viewLifecycleOwner) {
            binding.refresh.isRefreshing = false
            binding.loading.hide()
            val gameList = it.second

            if (gameList != null) {
                adapter.setList(gameList)
            } else {
                binding.loading.showError()
            }
        }
        viewModel.errMsgFlow.collectWithLifecycle(viewLifecycleOwner.lifecycle) {
            it?.apply {
                toast(this)
            }
        }
    }

    override fun loadFirstData() {
        viewModel.loadData()
        refreshMyGames()
    }

    override fun getFragmentName() = "PartyFragment"
}