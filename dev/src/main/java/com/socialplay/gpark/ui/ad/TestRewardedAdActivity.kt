package com.socialplay.gpark.ui.ad

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ActivityTestRewardAdBinding
import com.socialplay.gpark.function.ad.AdProxy
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import fromJson
import timber.log.Timber
import toJson
import java.lang.ref.WeakReference

class TestRewardedAdActivity : BaseActivity() {

    override val binding by viewBinding(ActivityTestRewardAdBinding::inflate)
    private var skipTime = 30
    private var startAutoPlay = true
    private var startWindow = 0
    private var startPosition: Long = 0
    private var processName = StartupProcessType.H.desc
    private val MSG_TIME = 1

    private val handler = Handler(Looper.getMainLooper()) { msg ->
        if (msg.what == MSG_TIME) {
            skipTime--
            if (skipTime > 0) {
                binding.tvSkip.text = getString(R.string.test_reward_ad_tips_time, skipTime)
                msg.target.sendEmptyMessageDelayed(MSG_TIME, 1000)
            } else {
                msg.target.removeMessages(MSG_TIME)
                binding.tvSkip.text = getString(R.string.test_reward_ad_tips_skip)
            }
        }
        false
    }

    companion object {
        private const val KEY_WINDOW = "window"
        private const val KEY_POSITION = "position"
        private const val KEY_AUTO_PLAY = "auto_play"

        private var videoAdCallback: IAdSdkBridge.VideoAdCallback? = null

        fun createIntent(
            context: Context,
            processName: String,
            adType: Int,
            gameId: String?,
            gamePkg: String?,
            data: Map<String, Any?>?,
            callback: IAdSdkBridge.VideoAdCallback? = null
        ): Intent {
            videoAdCallback = callback
            val intent = Intent(context, TestRewardedAdActivity::class.java)
            intent.putExtra(AdActivity.KEY_AD_TYPE, adType)
            intent.putExtra(AdActivity.KEY_PROCESS_NAME, processName)
            intent.putExtra(AdActivity.KEY_AD_GAME_PKG, gamePkg)
            intent.putExtra(AdActivity.KEY_AD_GAME_ID, gameId)
            intent.putExtra(AdActivity.KEY_AD_GAME_DATA, toJson(data))
            return intent
        }

        private class TestVideoAdCallbackImpl(
            private val processName: String,
            private val gamePkg: String,
            private val callbackAction: String?,
            private val rewardedAction: String?,
            private val ayRef: WeakReference<TestRewardedAdActivity>? = null
        ) : IAdSdkBridge.VideoAdCallback {
            override fun onShowError(error: String?) {
                Timber.d("onShowError $error")
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(
                        AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_SHOW_ERROR.value,
                        AdActivity.KEY_AD_SHOW_RESULT to false
                    )
                )
                backToTsGame(processName, gamePkg)
                ayRef?.get()?.finish()
            }

            override fun onShow() {
                Timber.d("onShow")
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(
                        AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_SHOW.value,
                        AdActivity.KEY_AD_SHOW_RESULT to true
                    )
                )
            }

            override fun onShowSkip() {
                Timber.d("onShowSkip")
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_SKIPPED.value)
                )
            }

            override fun onShowReward() {
                Timber.d("onShowReward")
                execTsActionFunc(processName, rewardedAction)
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_REWARDED.value)
                )
            }

            override fun onShowClose() {
                Timber.d("onShowClose")
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_CLOSED.value)
                )
                backToTsGame(processName, gamePkg)
                ayRef?.get()?.finish()
            }

            override fun onShowClick() {
                Timber.d("onShowClick")
                execTsActionFunc(
                    processName,
                    callbackAction,
                    linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_CLICKED.value)
                )
            }
        }


        private fun execTsActionFunc(
            processName: String, action: String?,
            data: Map<String, Any>? = null
        ) {
            AdProxy.callAction(processName, action, data)
        }

        private fun backToTsGame(processName: String, gamePkg: String?) {
            AdProxy.backToTsGame(processName, gamePkg)
        }

    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onInit()

    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        onInit()
    }

    private fun onInit() {
        val adType = intent?.getIntExtra(AdActivity.KEY_AD_TYPE, 0)
        processName =
            intent?.getStringExtra(AdActivity.KEY_PROCESS_NAME) ?: StartupProcessType.H.desc

        val gamePkg = intent?.getStringExtra(AdActivity.KEY_AD_GAME_PKG) ?: ""
        val gameId = intent?.getStringExtra(AdActivity.KEY_AD_GAME_ID)
        val dataStr = intent?.getStringExtra(AdActivity.KEY_AD_GAME_DATA)
        val dataMap = dataStr?.fromJson<Map<String, Any?>?>()
        val callbackAction = dataMap?.get("action") as? String
        val rewardedAction = dataMap?.get("action1") as? String

        if (videoAdCallback == null) {
            videoAdCallback = TestVideoAdCallbackImpl(
                processName, gamePkg, callbackAction, rewardedAction,
                WeakReference(this)
            )
        }
        binding.tvSkip.text = getString(R.string.test_reward_ad_tips_time, skipTime)

        binding.tvSkip.setOnAntiViolenceClickListener {
            videoAdCallback?.onShowSkip()
            videoAdCallback?.onShowClose()
            finish()
        }
        binding.ivVideoMask.setOnAntiViolenceClickListener {
            videoAdCallback?.onShowClick()
        }

        handler.removeMessages(MSG_TIME)
        handler.sendEmptyMessage(MSG_TIME)

        videoAdCallback?.onShow()
        videoAdCallback?.onShowReward()
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(KEY_AUTO_PLAY, startAutoPlay)
        outState.putInt(KEY_WINDOW, startWindow)
        outState.putLong(KEY_POSITION, startPosition)
    }


    override fun onResume() {
        super.onResume()
        if (skipTime >= 0) {
            handler.removeMessages(MSG_TIME)
            handler.sendEmptyMessageDelayed(MSG_TIME, 1000)
        }
    }

    override fun onPause() {
        super.onPause()
        handler.removeMessages(MSG_TIME)
    }

    override fun onBackPressed() {

    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
        videoAdCallback = null
    }

}