package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import com.bumptech.glide.Glide
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.VideoFeedCardMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.ifNullOrBlank
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/29
 *     desc   :
 * </pre>
 */
@ProviderTag(
    messageContent = VideoFeedCardMessage::class,
    showReadState = false,
    showWarning = true,
    showSenderPortrait = true
)
class VideoFeedCardMessageItemProvider :
    IContainerItemProvider.MessageProvider<VideoFeedCardMessage>() {

    override fun newView(context: Context?, parent: ViewGroup?): View {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.item_im_share_video_feed, null as ViewGroup?)
        view.tag = ViewHolder(view)
        return view
    }

    override fun bindView(
        view: View,
        position: Int,
        content: VideoFeedCardMessage?,
        message: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
        val holder: ViewHolder = view.tag as ViewHolder
        val data = content?.getVideoFeedCardInfo()
        if (data?.videoContent.isNullOrBlank()) {
            holder.tvTitle.gone()
        } else {
            holder.tvTitle.visible()
            holder.tvTitle.text = data?.videoContent
        }
        holder.tvUname.text = data?.videoAuthorName
        ThreadHelper.runOnUiThreadCatching {
            val glide = Glide.with(view)
            glide.load(data?.videoCover.ifNullOrBlank { data?.videoUrl.orEmpty() })
                .placeholder(R.drawable.placeholder)
                .centerCrop()
                .into(holder.ivCover)
            glide.load(data?.videoAuthorAvatar)
                .placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(holder.ivAvatar)
        }
        if (message?.messageDirection == Message.MessageDirection.SEND) {
            holder.root.setCardBackgroundColor(view.getColorByRes(R.color.color_4AB4FF))
            holder.root.setPaddingEx(left = view.dp(12), right = view.dp(18))
            holder.tvTitle.setTextColorByRes(R.color.white)
            holder.tvUname.setTextColorByRes(R.color.white)
        } else {
            holder.root.setCardBackgroundColor(view.getColorByRes(R.color.textColorSecondaryLight))
            holder.root.setPaddingEx(left = view.dp(18), right = view.dp(12))
            holder.tvTitle.setTextColorByRes(R.color.textColorPrimary)
            holder.tvUname.setTextColorByRes(R.color.textColorPrimary)
        }
        view.setOnClickListener {
            data?.let {
                messageClickListener?.clickVideoFeedCard(it)
            }
        }
    }

    override fun getContentSummary(context: Context?, data: VideoFeedCardMessage?): Spannable {
        return SpannableString(context?.getString(R.string.share_video_content))
    }

    override fun onItemClick(
        p0: View?,
        p1: Int,
        p2: VideoFeedCardMessage?,
        p3: UIMessage?,
        p4: MessageListAdapter.OnMessageClickListener?
    ) {
    }

    private class ViewHolder(view: View) {
        var ivCover: ImageView = view.findViewById(R.id.iv_cover)
        var tvTitle: TextView = view.findViewById(R.id.tv_title)
        var ivAvatar: ImageView = view.findViewById(R.id.iv_avatar)
        var tvUname: TextView = view.findViewById(R.id.tv_uname)
        var root: CardView = view.findViewById(R.id.cv_root)
    }
}