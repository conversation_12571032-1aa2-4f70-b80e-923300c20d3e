package com.socialplay.gpark.ui.im.groupchat

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo
import com.socialplay.gpark.data.model.groupchat.MgsGroupApplyPageRequest
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.views.LoadMoreState
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class ProcessApplyResult(
    val askId: Long,
    val accept: Boolean,
    val result: DataResult<Boolean>,
)

data class GroupsRequestListModelState(
    val refreshFlag: Int = 0,
    val pageNum: Int = 0,
    val requestList: Async<List<GroupChatApplyInfo>> = Uninitialized,
    val requestListGetTimestamp:Long = -1L,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val processApplyResult: Async<ProcessApplyResult> = Uninitialized,
    val lastWatchRequestPageTime: Long = 0L,
) : MavericksState

class GroupsRequestListViewModel(
    val initialState: GroupsRequestListModelState,
    val metaRepository: IMetaRepository,
    val metaKV: MetaKV,
) : BaseViewModel<GroupsRequestListModelState>(initialState) {
    fun getLastWatchRequestPageTime() {
        setState {
            copy(lastWatchRequestPageTime = metaKV.account.openGroupJoinRequestPageTime)
        }
    }

    fun updateLastWatchRequestPageTime(timestamp: Long) {
        // 这里不用更新 State.lastWatchRequestPageTime
        metaKV.account.openGroupJoinRequestPageTime = timestamp
    }

    fun getRequestList(targetUid: String, refresh: Boolean) = withState { state ->
        if (state.requestList is Loading || state.loadMore is Loading) {
            return@withState
        }
        val requestListGetTimestamp = System.currentTimeMillis()
        var pageNum = 1
        if (refresh) {
            setState {
                copy(
                    refreshFlag = state.refreshFlag + 1,
                    requestListGetTimestamp = requestListGetTimestamp,
                    requestList = Loading(),
                    pageNum = 1,
                )
            }
        } else {
            pageNum = state.pageNum + 1
            setState {
                copy(loadMore = Loading())
            }
        }
        val oldRequestList = state.requestList.invoke() ?: emptyList()
        viewModelScope.launch {
            val dataResult = metaRepository.getGroupChatPendingRequestList(
                MgsGroupApplyPageRequest(
                    pageNum = pageNum,
                    pageSize = 20,
                )
            )
            if (dataResult.succeeded && dataResult.data != null) {
                val requestList = dataResult.data!!.asks ?: emptyList()
                val hasNext = dataResult.data?.hasNext ?: false
                setState {
                    copy(
                        requestList = Success(
                            if (refresh || oldRequestList.isEmpty()) {
                                requestList
                            } else {
                                oldRequestList + requestList
                            }
                        ),
                        pageNum = pageNum,
                        loadMore = Success(LoadMoreState(isEnd = hasNext || requestList.isEmpty()))
                    )
                }
            } else {
                if (refresh) {
                    setState {
                        copy(
                            requestList = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                } else {
                    setState {
                        copy(
                            loadMore = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    fun processApplyJoinGroupChat(askId: Long, accept: Boolean) {
        if (oldState.processApplyResult is Loading) {
            return
        }
        setState {
            copy(
                processApplyResult = Loading()
            )
        }
        viewModelScope.launch {
            val dataResult = metaRepository.processApplyJoinGroupChat(
                askId,
                accept
            )
            setState {
                copy(
                    processApplyResult = Success(
                        ProcessApplyResult(
                            askId = askId,
                            accept = accept,
                            dataResult
                        )
                    )
                )
            }
        }
    }

    fun updateRequestInfo(askId: Long, accept: Boolean) {
        val requestList = oldState.requestList.invoke() ?: return
        val index = requestList.indexOfFirst { askId == it.id }
        if (index >= 0) {
            val newList = requestList.toMutableList()
            newList[index] = requestList[index].copy(
                status = if (accept) {
                    GroupChatApplyInfo.STATUS_AGREE
                } else {
                    GroupChatApplyInfo.STATUS_REJECT
                }
            )
            setState {
                copy(
                    requestList = Success(newList)
                )
            }
        }
    }

    companion object :
        KoinViewModelFactory<GroupsRequestListViewModel, GroupsRequestListModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GroupsRequestListModelState
        ): GroupsRequestListViewModel {
            return GroupsRequestListViewModel(state, get(), get())
        }
    }
}