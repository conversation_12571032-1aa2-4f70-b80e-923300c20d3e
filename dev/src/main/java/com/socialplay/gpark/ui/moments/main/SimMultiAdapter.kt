package com.socialplay.gpark.ui.moments.main

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding

/**
 * 2023/9/19
 */
interface SimMultiItem {
    override fun equals(other: Any?): Boolean
    override fun hashCode(): Int
    fun viewType(): Int
}

interface OnSimMultiItemBindViewHolder<T : SimMultiItem, VB : ViewBinding> {
    fun onBindViewHolder(binding: ViewBinding, position: Int, item: SimMultiItem) {
        onBindView(binding as VB, position, item as T)
    }

    fun onBindView(binding: VB, position: Int, item: T)

    fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup,
        attachToParent: Boolean,
    ): BaseSimMultiViewHolder
}

abstract class BaseSimMultiViewHolder(val binding: ViewBinding) :
    RecyclerView.ViewHolder(binding.root)

private val simMultiItemDiff = object : DiffUtil.ItemCallback<SimMultiItem>() {
    override fun areItemsTheSame(oldItem: SimMultiItem, newItem: SimMultiItem): Boolean {
        return newItem == oldItem
    }

    override fun areContentsTheSame(oldItem: SimMultiItem, newItem: SimMultiItem): Boolean {
        return newItem == oldItem
    }
}

abstract class SimMultiAdapter :
    ListAdapter<SimMultiItem, BaseSimMultiViewHolder>(simMultiItemDiff) {

    private val typeMapper = mutableMapOf<Int, OnSimMultiItemBindViewHolder<*, *>>()

    protected var mOnItemClickListener: ((index: Int, item: SimMultiItem) -> Unit)? = null

    fun <T : SimMultiItem, VB : ViewBinding> addType(
        type: Int,
        holder: OnSimMultiItemBindViewHolder<T, VB>,
    ) {
        typeMapper[type] = holder
    }

    fun setOnItemClickListener(listener: (index: Int, item: SimMultiItem) -> Unit) {
        this.mOnItemClickListener = listener
    }

    override fun onBindViewHolder(holder: BaseSimMultiViewHolder, position: Int) {
        val viewType = getItemViewType(position)
        typeMapper[viewType]?.also {
            val item = getItem(position)
            holder.binding.root.setOnClickListener {
                mOnItemClickListener?.invoke(position, item)
            }
            it.onBindViewHolder(holder.binding, position, getItem(position))
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseSimMultiViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return typeMapper[viewType]?.let {
            it.onCreateViewHolder(inflater, parent, false)
        } ?: throw RuntimeException("not fount plot view type")
    }

    override fun getItemViewType(position: Int): Int {
        return getItem(position).viewType()
    }
}