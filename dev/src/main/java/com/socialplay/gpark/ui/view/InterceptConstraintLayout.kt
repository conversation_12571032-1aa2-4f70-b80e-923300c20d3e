package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.EditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed
import com.socialplay.gpark.util.extension.wrappedActivity

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/22
 *     desc   :
 * </pre>
 */
class InterceptConstraintLayout @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defaultStyleAttr: Int = 0
) : ConstraintLayout(context, attributeSet, defaultStyleAttr) {

    var callback: (ConstraintLayout.(MotionEvent) -> Unit)? = null

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        callback?.invoke(this, ev)
        return super.dispatchTouchEvent(ev)
    }

    fun autoHideKeyboard(
        owner: LifecycleOwner,
        needClearFocus: Boolean = false,
        conditional: ((Rect, Int, Int) -> Boolean)? = null
    ) {
        val rect = Rect()
        registerCallback(owner) { ev ->
            kotlin.runCatching {
                if (ev.action == MotionEvent.ACTION_DOWN) {
                    (context.wrappedActivity()?.currentFocus as? EditText)?.let { et ->
                        et.getGlobalVisibleRect(rect)
                        val x = ev.rawX.toInt()
                        val y = ev.rawY.toInt()
                        val outsideEditText = !rect.contains(x, y)
                        if (outsideEditText && conditional?.invoke(rect, x, y) != false) {
                            if (needClearFocus) {
                                et.clearFocus()
                            }
                            InputUtil.hideKeyboard(et)
                        }
                    }
                }
            }
        }
    }

    fun interceptBottomSheet(
        owner: LifecycleOwner,
        behaviorGetter: () -> BottomSheetBehavior<*>?
    ) {
        isClickable = true // 不然无法响应除ACTION_DOWN以外的事件
        registerCallback(owner) { ev ->
            when (ev.action) {
                MotionEvent.ACTION_DOWN -> {
                    behaviorGetter.invoke()?.isDraggable = false
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    behaviorGetter.invoke()?.isDraggable = true
                }
            }
        }
    }

    private fun registerCallback(
        owner: LifecycleOwner,
        callback: (ConstraintLayout.(MotionEvent) -> Unit)
    ) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                this.callback = callback
            },
            unregister = {
                this.callback = null
            }
        )
    }
}