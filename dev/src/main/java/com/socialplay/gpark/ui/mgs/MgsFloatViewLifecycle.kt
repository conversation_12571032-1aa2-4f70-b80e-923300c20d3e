package com.socialplay.gpark.ui.mgs

import android.app.Activity
import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.graphics.Rect
import android.os.Process
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import androidx.core.view.isVisible
import com.bytedance.danmaku.render.engine.DanmakuView
import com.bytedance.danmaku.render.engine.control.DanmakuController
import com.bytedance.danmaku.render.engine.data.DanmakuData
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.mgs.data.model.MGSMessage
import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.request.MgsShareScreenshot
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.CloseRechargeDialogEvent
import com.socialplay.gpark.data.model.mgs.MgsTabEnum
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.intermodal.base.GamePageStarter
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.function.mw.OnMWGameLifecycleInterceptor
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.floatingball.BaseFloatingBallAdapter
import com.socialplay.gpark.ui.floatingball.BaseFloatingBallViewLifecycle
import com.socialplay.gpark.ui.mgs.ball.MgsFloatBallView
import com.socialplay.gpark.ui.mgs.ball.MgsFloatBallViewCall
import com.socialplay.gpark.ui.mgs.danmu.DanMuDataUtil
import com.socialplay.gpark.ui.mgs.danmu.advanced.AdvancedDanmakuFactory
import com.socialplay.gpark.ui.mgs.danmu.advanced.GradientDanmakuFactory
import com.socialplay.gpark.ui.mgs.danmu.layer.ScrollLayer
import com.socialplay.gpark.ui.mgs.dialog.BanBlockDialog
import com.socialplay.gpark.ui.mgs.expand.MgsMessageExpandView
import com.socialplay.gpark.ui.mgs.input.MgsDanmuInputView
import com.socialplay.gpark.ui.mgs.input.MgsInputView
import com.socialplay.gpark.ui.mgs.listener.OnMgsExpandListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatBallListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatInputListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatMessageListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsPlayerInfoDialogListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsQuitGameDialogListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsRecordListener
import com.socialplay.gpark.ui.mgs.menu.MgsMoreMenuView
import com.socialplay.gpark.ui.mgs.message.MgsFloatMessageView
import com.socialplay.gpark.ui.mgs.record.MgsRecordPlayerIdView
import com.socialplay.gpark.ui.mgs.record.MgsRecordView
import com.socialplay.gpark.ui.mgs.record.ScreenRecordDelegate
import com.socialplay.gpark.ui.mgs.view.MgsExpandLinearLayout
import com.socialplay.gpark.ui.permission.GamePermissionActivity
import com.socialplay.gpark.util.PackageUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.math.abs


/**
 * Created by bo.li
 * Date: 2022/1/18
 * Desc: 管理悬浮球、悬挂消息、聊天输入框、悬浮层等游戏内MGS业务的UI
 */
class MgsFloatViewLifecycle(
    private val app: Application,
    private val metaApp: Application,
    private val metaKV: MetaKV,
    private val isMwGame: Boolean = false
) : BaseFloatingBallViewLifecycle(),
    OnMgsExpandListener,
    MgsFloatBallViewCall {

    // 悬浮层
    private var mgsMessageExpandView: MgsMessageExpandView? = null

    // 悬浮球
    private lateinit var floatBallView: MgsFloatBallView

    // 悬浮消息
    private lateinit var floatMessageView: MgsFloatMessageView
    private var floatInputView: MgsInputView? = null
    private var floatRecordView: MgsRecordView? = null

    private lateinit var floatRecordPlayerIdView: MgsRecordPlayerIdView
    private var floatMgsDanmuInputView: MgsDanmuInputView? = null
    private var danmuView: DanmakuView? = null
    private val scope = CoroutineScope(Dispatchers.IO)


    private var mController: DanmakuController? = null
    private val contentSize: Float = ScreenUtil.dp2px(metaApp, 10f).toFloat()
    private val avatarHeight: Float = ScreenUtil.dp2px(metaApp, 24f).toFloat()
    private val stokeHeight: Float = ScreenUtil.dp2px(metaApp, 34f).toFloat()
    private val countSize: Float = ScreenUtil.dp2px(metaApp, 8f).toFloat()

    // 屏幕高度
    private val screenHeight: Int
        get() {
            return getScreenSize().second
        }

    private val screenWidth: Int
        get() {
            return getScreenSize().first
        }

    // 悬浮消息锚点高度
    private val messageAnchorY by lazy { ScreenUtil.dp2px(metaApp, 38F) }

    private var minTopY  = if(ScreenUtil.isHorizontalScreen(context = app))  ScreenUtil.dp2px(metaApp, 10F) else ScreenUtil.dp2px(metaApp, 35f)
    private var maxTopY= screenHeight - minTopY
    private var ballY = 0

    private var messageMinTopY = minTopY + messageAnchorY
    private var messageY = 0

    private var inputX =  (screenWidth- if (PandoraToggle.enableBulletChat) 160.dp else 105.dp)/2

    private var inputY= screenHeight - 58.dp

    private var recordY = minTopY + messageAnchorY



    // MetaVerse游戏退出方式不能直接用233形式的，需要单独处理
    var onMWGameLifecycleInterceptor: OnMWGameLifecycleInterceptor? = null

    private val mgsFloatBallPresenter by lazy { MgsFloatViewPresenter(metaApp, this) }

    private var screenRecordDelegate: ScreenRecordDelegate? = null

    private val isOpenGameRecord = PandoraToggle.openGameRecord

    //是否进入后台
    private var isBackground = false
    private var countActivity = 0
    private val mgsInteractor: MgsInteractor by lazy { GlobalContext.get().get() }

    /**
     * 初始化高度
     */
    private fun initTop(activity: Activity){
        minTopY  = if(ScreenUtil.isHorizontalScreen(context = activity))  ScreenUtil.dp2px(metaApp, 10F) else ScreenUtil.dp2px(metaApp, 35f)
        maxTopY= ScreenUtil.getScreenHeight(activity) - minTopY
        ballY = minTopY

        messageMinTopY = minTopY + messageAnchorY
        messageY = messageMinTopY

        recordY = minTopY + messageAnchorY
        inputX = (ScreenUtil.getScreenWidth(activity) - if (PandoraToggle.enableBulletChat) 160.dp else 105.dp) / 2
        inputY = ScreenUtil.getScreenHeight(activity) - 58.dp

    }
    override fun blackView(blackViewClass: Class<View>): Boolean {
        return blackViewClass.simpleName == MgsExpandLinearLayout::class.simpleName || blackViewClass.simpleName == DanmakuView::class.simpleName || super.blackView(
            blackViewClass
        )
    }

    override fun blackDecorView(): Boolean {
        // 防止遮挡输入法与弹窗
        return  !MgsDialogManager.anyDialogShowing()
    }

    override fun needCheckViewVisible(): Boolean {
        return true
    }

    private fun getScreenSize(): Pair<Int, Int> {
        val screenSize = curResumedActivity?.let { ScreenUtil.getScreenSize(it) }
        val screenWidth = screenSize?.getOrNull(0) ?: ScreenUtil.getScreenWidth(metaApp)
        val screenHeight = screenSize?.getOrNull(1) ?: ScreenUtil.getScreenHeight(metaApp)
        return Pair(screenWidth, screenHeight)
    }

    override val floatingBallAdapter: BaseFloatingBallAdapter = object : BaseFloatingBallAdapter() {
        override fun getViewCount(): Int {
            return MgsFloatPosition.values().size
        }

        override fun createView(position: Int): View {
            return when (position) {
                MgsFloatPosition.BALL.position             -> {
                    createBallView()
                }

                MgsFloatPosition.MESSAGE.position          -> {
                    createMessageView()
                }

                MgsFloatPosition.INPUT.position            -> {
                    createInputView()
                }

                MgsFloatPosition.RECORD.position           -> {
                    createRecordView()
                }

                MgsFloatPosition.RECORD_PLAYER_ID.position -> {
                    createRecordPlayerIdView()
                }

                else                                       -> {
                    createBallView()
                }
            }
        }

        override fun updateAllViewPosition(activity: Activity) {
            initTop(activity)
            Timber.d("updateAllViewPosition")
        }

        override fun afterCreateAllView() {
            viewCallUpdateMessageList(mgsFloatBallPresenter.getRoomMessage())
            updateMgsRoomStatus((mgsFloatBallPresenter.getMgsRoomInfo() != null), true)
        }

        override fun getY(position: Int): Int {
            return when (position) {
                MgsFloatPosition.BALL.position             -> {
                    ballY
                }

                MgsFloatPosition.MESSAGE.position          -> {
                    messageY
                }

                MgsFloatPosition.INPUT.position            -> {
                    inputY
                }

                MgsFloatPosition.RECORD.position           -> {
                    recordY
                }

                MgsFloatPosition.RECORD_PLAYER_ID.position -> {
                    screenHeight - 8.dp - floatRecordPlayerIdView.heightView
                }

                else                                       -> {
                    super.getY(position)
                }
            }
        }

        override fun getX(position: Int): Int {
            return when (position) {
                MgsFloatPosition.INPUT.position            -> {
                    inputX
                }

                MgsFloatPosition.RECORD_PLAYER_ID.position -> {
                    screenWidth - 20.dp - floatRecordPlayerIdView.widthView
                }

                else                                       -> {
                    super.getX(position)
                }
            }
        }

        override fun getWidth(position: Int): Int {
            return when (position) {
                MgsFloatPosition.BALL.position                                               -> {
                    super.getWidth(position)
                }

                MgsFloatPosition.RECORD.position, MgsFloatPosition.RECORD_PLAYER_ID.position -> {
                    screenRecordDelegate?.widthRecord ?: super.getWidth(position)
                }

                else                                                                         -> super.getWidth(
                    position
                )
            }
        }

        override fun getHeight(position: Int): Int {
            return when (position) {
                MgsFloatPosition.RECORD.position, MgsFloatPosition.RECORD_PLAYER_ID.position -> {
                    screenRecordDelegate?.heightRecord ?: super.getHeight(position)
                }

                else                                                                         -> super.getHeight(
                    position
                )
            }
        }
    }

    /**
     * 录屏
     */
    private val recordViewListener = object : OnMgsRecordListener {
        override fun onShowRecordView() {
            val isBelow = ballY < screenHeight / 2
            recordY = if (isBelow) {
                ballY + floatBallView.height
            } else {
                ballY - 46.dp
            }
            floatRecordView?.let { addOrRemoveView(it, true) }
            floatRecordPlayerIdView?.let { addOrRemoveView(it, true) }
            updateLayoutParamsSizeAndLocation()
        }

        override fun onHideRecordView() {
            updateLayoutParamsSize()
            floatRecordView?.let { addOrRemoveView(it, false) }
            floatRecordPlayerIdView?.let { addOrRemoveView(it, false) }
        }

    }

    /**
     * 悬浮输入框listener
     */
    private val floatInputListener = object : OnMgsFloatInputListener {
        override fun sendMessage(message: String) {
            mgsFloatBallPresenter.sendMessage(message) { canSend ->
                if (!canSend) {
                    ToastUtil.gameShowShort(
                        metaApp.getString(R.string.risk_review_not_pass)
                    )
                }
            }
        }

        override fun getGameAnalytics(): Map<String, String> = getGameCommonAnalytic()

        override fun changeVoiceMuteState(isOpen: Boolean) {
            mgsFloatBallPresenter.changeMyVoiceState(isOpen)
        }

        override fun canShowInputAudio(): Boolean {
            return mgsFloatBallPresenter.canShowInputAudio()
        }

        override fun checkMicPermission() {
            mgsFloatBallPresenter.checkMicPermission()
        }

        override fun isAllMute(): Boolean {
            return mgsFloatBallPresenter.isAllMute()
        }

        override fun muteAllRemoteAudioStreams(muted: Boolean) {
            mgsFloatBallPresenter.muteAllRemoteAudioStreams(muted)
        }

        override fun danmuActionMove(x: Int, y: Int) {
            inputX = x
            inputY = y
            updateView()
        }

        override fun getCurrentActivity(): Activity? {
            return  curResumedActivity
        }

        override fun getSelUserInfoByUuid(): Member? {
            return mgsFloatBallPresenter.getSelUserInfoByUuid()
        }
    }

      private fun addDanMuData(member: Member, content: String) {
         scope.launch {
             val bgHeight = if (DanMuDataUtil.getDanMuLevelTime(member) >= 15) { 24.dp.toFloat() } else { 20.dp.toFloat() }
             DanMuDataUtil.getDanMuData(
                 metaApp,
                 member,
                 bgHeight,
                 contentSize,
                 avatarHeight,
                 stokeHeight,
                 countSize,
                 content
             ) {
                 mController?.addFakeData(it)
             }
         }
    }

    /**
     * 悬浮球listener
     */
    private val floatBallListener = object : OnMgsFloatBallListener {
        override fun ordinaryBallQuitGame() {
            onMWGameLifecycleInterceptor?.quitGame(metaApp) ?: MetaRouter.Main.floatBallGameBack(
                metaApp,
                mgsFloatBallPresenter.getGameInfo()?.id
            )
            mgsFloatBallPresenter.remindCpWhenQuitGame(reallyQuit = true, normalBall = true)
        }

        override fun getGameAnalytics(): Map<String, String> = getGameCommonAnalytic()

        override fun showUserCard(openId: String) {
            mgsFloatBallPresenter.showUserCardByOpenId(openId)
        }

        override fun isShowMgsInput(isShow: Boolean) {
            if (!PandoraToggle.enableBulletChat) {
                floatInputView?.visible(isShow)
            }
        }

        override fun updateBallViewSize() {
            updateLayoutParamsSize()
        }

        override fun showQuitGame() {
            <EMAIL>()
        }

        override fun changeVoiceMuteState(isOpen: Boolean) {
            mgsFloatBallPresenter.changeMyVoiceState(isOpen)
        }

        override fun checkMicPermission() {
            mgsFloatBallPresenter.checkMicPermission()
        }

        override fun isAllMute(): Boolean {
            return mgsFloatBallPresenter.isAllMute()
        }

        override fun muteAllRemoteAudioStreams(muted: Boolean) {
            if (PandoraToggle.enableBulletChat && muted) {
                //弹幕开启时，全员静音时，关闭本地麦克风声音
                floatMgsDanmuInputView?.changeVoiceState(false)
                mgsFloatBallPresenter.changeMyVoiceState(false)
            }
            mgsFloatBallPresenter.muteAllRemoteAudioStreams(muted)
        }
    }

    /**
     * 悬浮消息listener
     */
    private val floatMessageListener = object : OnMgsFloatMessageListener {
        override fun notifyParams(y: Int) {
            messageY = y
            if (mgsFloatBallPresenter.floatMessageOpen()) {
                setFloatMessageShow(true)
            }
            updateView()
        }

        override fun getMessageY(): Int {
            return messageY
        }

        override fun getRoomMessageList(): MutableList<MGSMessage>? {
            return mgsFloatBallPresenter.getRoomMessage()
        }

        override fun collapseFloatMessage() {
            changeFloatMessageVisible(false)
        }


        override fun getGameAnalytics(): Map<String, String> = getGameCommonAnalytic()

    }

    private val onRecordTouchListener = object : View.OnTouchListener {

        // 上次的y坐标
        private var lastY = 0.0F

        // 正在拖拽
        private var isStartDragging = false

        // click范围
        private var touchScope = ViewConfiguration.get(metaApp).scaledTouchSlop

        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_OUTSIDE -> {

                }

                MotionEvent.ACTION_DOWN    -> {
                    lastY = event.rawY
                }

                MotionEvent.ACTION_MOVE    -> {
                    var dy = event.rawY - lastY
                    if (!isStartDragging && abs(dy) > touchScope) {
                        // 开始拖动
                        isStartDragging = true
                        if (dy > 0) {
                            dy -= touchScope
                        } else {
                            dy += touchScope
                        }
                    }
                    if (isStartDragging) {
                        // 已经在拖动
                        recordY += dy.toInt()
                        // 防止太靠上
                        recordY = recordY.coerceAtLeast(minTopY)
                        // 防止太靠下
                        floatRecordView?.let {
                            recordY = recordY.coerceAtMost(maxTopY - it.height)
                        }
                        updateView()
                        lastY = event.rawY
                    }
                }

                MotionEvent.ACTION_UP      -> {
                    if (isStartDragging) {
                        // 拖动结束
                        isStartDragging = false
                    } else {
                        // 点击事件
                        onClickRecordView(v)
                    }
                }

                MotionEvent.ACTION_CANCEL  -> {
                    if (isStartDragging) {
                        isStartDragging = false
                    }
                }
            }
            return true
        }
    }

    /**
     * 录屏中的点击事件
     */
    private fun onClickRecordView(view: View) {
        when (view.id) {
            R.id.iv_recording, R.id.chronometer_free_record -> {
                //停止录屏
                screenRecordDelegate?.stopRecord()
            }

            R.id.iv_voice                                   -> {
                //切换是否开启录制声音
                screenRecordDelegate?.checkSwitchAudioRecord()

            }
        }
    }

    private val onBallTouchListener = object : View.OnTouchListener {

        // 上次的y坐标
        private var lastY = 0.0F

        // 正在拖拽
        private var isStartDragging = false

        // click范围
        private var touchScope = ViewConfiguration.get(metaApp).scaledTouchSlop

        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_OUTSIDE -> {
                    floatBallView.actionOutside()
                }

                MotionEvent.ACTION_DOWN    -> {
                    lastY = event.rawY
                }

                MotionEvent.ACTION_MOVE    -> {
                    var dy = event.rawY - lastY
                    if (!isStartDragging && abs(dy) > touchScope) {
                        // 开始拖动
                        isStartDragging = true
                        if (dy > 0) {
                            dy -= touchScope
                        } else {
                            dy += touchScope
                        }
                    }
                    if (isStartDragging) {
                        // 已经在拖动
                        ballY += dy.toInt()
                        // 防止太靠上
                        ballY = ballY.coerceAtLeast(minTopY)
                        // 防止太靠下
                        ballY = ballY.coerceAtMost(maxTopY - floatBallView.height)
                        actionMove()
                        lastY = event.rawY
                    }
                }

                MotionEvent.ACTION_UP      -> {
                    if (isStartDragging) {
                        // 拖动结束
                        isStartDragging = false
                        updateMessageLocation(ballY)
                    } else {
                        // 点击事件
                        onClickFloatingBall(v)
                    }
                }

                MotionEvent.ACTION_CANCEL  -> {
                    if (isStartDragging) {
                        isStartDragging = false
                    }
                }
            }
            return true
        }
    }
    private val onDanmuTouchListener = object : View.OnTouchListener {

        // 上次的y坐标
        private var lastY = 0.0F
        private var lastX = 0.0f

        // 正在拖拽
        private var isStartDragging = false

        // click范围
        private var touchScope = ViewConfiguration.get(metaApp).scaledTouchSlop

        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_OUTSIDE -> {
                    floatMgsDanmuInputView?.actionOutside()
                }

                MotionEvent.ACTION_DOWN    -> {
                    lastY = event.rawY
                    lastX = event.rawX
                }

                MotionEvent.ACTION_MOVE    -> {
                    var dy = event.rawY - lastY
                    var dx = event.rawX - lastX
                    if (!isStartDragging && ((abs(dy) > touchScope) || abs(dx) > touchScope)
                        && (abs(dy) <= ((floatMgsDanmuInputView?.measuredHeight ?: 0)) / 2)
                        && abs(dx) <= ((floatMgsDanmuInputView?.measuredWidth ?: 0) / 2)
                    ) {
                        // 开始拖动
                        isStartDragging = true
                        if (dy > 0) {
                            dy -= touchScope
                        } else {
                            dy += touchScope
                        }
                        if (dx > 0) {
                            dx -= touchScope
                        } else {
                            dx += touchScope
                        }
                    }
                    if (isStartDragging) {
                        // 已经在拖动
                        inputY += dy.toInt()
                        inputX += dx.toInt()
                        // 防止太靠上
                        inputY = inputY.coerceAtLeast(minTopY)
                        inputX = inputX.coerceAtLeast(minTopY)
                        // 防止太靠下
                        inputY = inputY.coerceAtMost(maxTopY -( floatMgsDanmuInputView?.measuredHeight?:0))
                        inputX = inputX.coerceAtMost(screenWidth-minTopY)
                        updateView()
                        lastY = event.rawY
                        lastX = event.rawX
                    }
                }

                MotionEvent.ACTION_UP      -> {
                    if (isStartDragging) {
                        // 拖动结束
                        isStartDragging = false
                    } else {
                        // 点击事件
                        curResumedActivity?.let {
                            floatMgsDanmuInputView?.onClickFloatingBall(v, metaApp,
                                it
                            )
                        }
                    }
                }

                MotionEvent.ACTION_CANCEL  -> {
                    if (isStartDragging) {
                        isStartDragging = false
                    }
                }
            }
            return true
        }
    }





    override fun onAfterApplicationCreated(app: Application) {
        super.onAfterApplicationCreated(app)
        mgsFloatBallPresenter.init(metaApp, getPackageName(app), isMwGame)
        if (isOpenGameRecord && isGameMainProcess(app, Process.myPid())) {
            screenRecordDelegate = floatRecordView?.let {
                ScreenRecordDelegate(
                    metaApp, app, getGameId(), getPackageName(app),
                    onMgsRecordListener = recordViewListener, it, ::getGameName
                ).apply {
                    registerRecordReceiver()
                }
            }
        }
    }


    override fun onActivityResumed(activity: Activity) {
        mgsMessageExpandView?.onActivityResumed(activity)
        screenRecordDelegate?.onActivityResumed(activity)
        mgsFloatBallPresenter.onActivityResumed(activity)
        super.onActivityResumed(activity)
    }

    override fun onActivityCreated(activity: Activity) {
        super.onActivityCreated(activity)
        createDanMuView()
        addDanMuView(activity)
    }

    override fun onActivityPaused(activity: Activity) {
        mgsMessageExpandView?.onActivityPaused()
        super.onActivityPaused(activity)
    }

    override fun onActivityDestroyed(activity: Activity) {
        MgsDialogManager.destroyAllDialog(activity)
        mgsMessageExpandView?.close()
        mgsMessageExpandView = null
        removeDanMuView(activity)
        super.onActivityDestroyed(activity)
    }
    override fun onActivityDestroyRemoveViews(): Boolean {
        return isMwGame
    }
    /**
     * 当前页面是否添加弹幕view
     */
    private fun addDanMuView(activity: Activity){
        val root = activity.findViewById<ViewGroup>(android.R.id.content)
        try {
            danmuView?.let {
                val count = (screenHeight / 2) / ScreenUtil.dp2px(metaApp, 45f)
                mController?.config?.scroll?.lineCount = count
                mController?.config?.scroll?.lineHeight = ScreenUtil.dp2px(metaApp, 45f).toFloat()
                root.addView(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 当前页面移除弹幕view
     */
    private fun removeDanMuView(activity: Activity){
        try {
            val root = activity.findViewById<ViewGroup>(android.R.id.content)
            mController?.stop()
            danmuView?.let { root.removeView(it) }
            danmuView = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 创建悬浮层view
     */
    private fun createExpandView(selectTabEnum: MgsTabEnum) {
        mgsMessageExpandView = MgsMessageExpandView(app, metaApp, selectTabEnum)
        mgsMessageExpandView?.onMgsExpandListener = this
    }

    /**
     * 创建悬浮消息view
     */
    private fun createMessageView(): View {
        floatMessageView =
            MgsFloatMessageView(app, metaApp, floatMessageListener, floatInputListener)
        messageY = messageMinTopY
        floatMessageView.gone()
        return floatMessageView
    }

    /**
     * 创建悬浮输入框
     */
    private fun createInputView(): View {
        if(PandoraToggle.enableBulletChat) {
            return createMgsDanmuInputView()
        }
        return createNormalInputView()
    }
    private fun createNormalInputView():View{
        floatInputView = MgsInputView(metaApp,"button1")
        floatInputView!!.initHint(
            metaApp.getString(R.string.mgs_float_input_hint),
            metaApp.getColor(R.color.white_90)
        )
        floatInputView!!.setInputListener(floatInputListener)
        floatInputView!!.gone()
        return floatInputView!!
    }
    private fun createMgsDanmuInputView(): View {
        floatMgsDanmuInputView = MgsDanmuInputView(metaApp)
        floatMgsDanmuInputView?.setOnTouchListener(onDanmuTouchListener)
        floatMgsDanmuInputView?.setBallOnTouchListener(onDanmuTouchListener)
        floatMgsDanmuInputView!!.setInputListener(floatInputListener)
        floatMgsDanmuInputView!!.gone()
        return floatMgsDanmuInputView!!
    }

    private fun createRecordPlayerIdView(): View {
        floatRecordPlayerIdView = MgsRecordPlayerIdView(metaApp)
        floatRecordPlayerIdView.gone()
        return floatRecordPlayerIdView
    }

    /**
     * 创建录屏中的布局
     */
    private fun createRecordView(): View {
        floatRecordView = MgsRecordView(app, metaApp)
        floatRecordView?.setRecordOnTouchListener(onRecordTouchListener)
        floatRecordView?.setOnTouchListener(onRecordTouchListener)
        floatRecordView?.gone()
        return floatRecordView!!
    }

    /**
     * 创建悬浮球view
     */
    private fun createBallView(): LinearLayout {
        floatBallView = MgsFloatBallView(app, metaApp, isOpenGameRecord, floatBallListener)
        floatBallView.setBallOnTouchListener(onBallTouchListener)
        floatBallView.setOnTouchListener(onBallTouchListener)
        ballY = minTopY
        return floatBallView
    }

    /**
     * 弹幕view配置
     */
    private fun createDanMuView(): DanmakuView? {
        if (!PandoraToggle.enableBulletChat) {
            return null
        }
        danmuView = DanmakuView(metaApp)
        mController = danmuView!!.controller
        val mDanmakuData = emptyList<DanmakuData>()
        mController?.setData(mDanmakuData)
        mController?.registerDrawItemFactory(AdvancedDanmakuFactory())
        mController?.registerDrawItemFactory(GradientDanmakuFactory())
        mController?.addRenderLayer(ScrollLayer())
        mController?.config?.scroll?.marginTop = 10f
        mController?.start()
        return danmuView!!
    }



    /**
     * 展开悬浮层
     */
    private fun expandPanelView(selectTabEnum: MgsTabEnum = MgsTabEnum.ROOM_PLAYER_TAB) {
        // 如果有悬浮层正在展示，先关闭
        mgsMessageExpandView?.close()
        createExpandView(selectTabEnum)
        mgsFloatBallPresenter.changeFloatMessageVisible(
            floatMessageOpen = false,
            detailMessageExpand = true
        )
        mgsMessageExpandView?.let {
            GamePageStarter.create(curResumedActivity)
                .start(it, metaApp)
        }
        Analytics.track(EventConstants.EVENT_CLICK_MGS_FLOAT) {
            putAll(getGameCommonAnalytic())
            put("state", "mgs")
        }
    }


    /**
     * 标记未读
     */
    override fun updateUnreadMessageCount(unReadCount: Int) {
        if (!mgsFloatBallPresenter.floatMessageOpen())
            floatBallView.updateUnreadMessageCount(unReadCount)
    }

    /**
     * 更新悬浮消息列表
     */
    override fun viewCallUpdateMessageList(value: MutableList<MGSMessage>?) {
        floatMessageView.updateMessageList(value)
    }

    /**
     * mgs悬浮求是否展示
     */
    fun updateMgsRoomStatus(inRoom: Boolean, isApplicationCreated:Boolean = false) {
        updateView()
        floatBallView.setOrdinary(!inRoom, isApplicationCreated)
        if (PandoraToggle.enableBulletChat) {
            floatMgsDanmuInputView?.setInputViewVisible(inRoom)
            if (inRoom) {
                floatMgsDanmuInputView?.visible()
                floatMgsDanmuInputView?.updateUserInfo(mgsFloatBallPresenter.getSelUserInfoByUuid())
            }
        } else {
            floatInputView?.setInputViewVisible(inRoom)
        }
        // 悬浮消息列表 默认隐藏
        setFloatMessageShow(false)
        floatBallView.updateUserView(inRoom)
    }



    /**
     * 悬浮消息增加一条消息
     */
    override fun addMessageList(data: MGSMessage) {
        if (PandoraToggle.enableBulletChat) {
            if (floatBallView.getDanmuStatus()) {
                data.content?.let { mgsFloatBallPresenter.getMemberInfoByUuid(data.mgsMessageExtra?.imUser?.id?:"")
                    ?.let { it1 -> addDanMuData(it1, it) } }
            }
            return
        }
        floatMessageView.addMessageList(data)
    }


    override fun showBlockedDialog(reason: String) {
        curResumedActivity?.let {
            MgsDialogManager.showBanBlockDialog(metaApp, it, BanBlockDialog.TYPE_BLOCK, reason)
        }
    }

    override fun showBannedDialog(reason: String) {
        curResumedActivity?.let {
            MgsDialogManager.showBanBlockDialog(metaApp, it, BanBlockDialog.TYPE_BAN, reason)
        }
    }

    /**
     * 展示用户资料卡片
     */
    override fun viewCallShowUserCardDialog(data: MgsPlayerInfo?, isMe: Boolean) {
        if (data == null) {
            ToastUtil.gameShowShort(
                metaApp.getString(R.string.mgs_fetch_user_card_failed)
            )
            return
        }
        if (PandoraToggle.isOpenMGSCardOptimize) {
            showOptimizeDialog(data, isMe)
        } else {
            showNormalDialog(data, isMe)
        }

    }
    private fun showNormalDialog(data: MgsPlayerInfo, isMe: Boolean) {
        curResumedActivity?.let {
            MgsDialogManager.showPlayerInfoDialog(
                data,
                it,
                metaApp,
                mgsFloatBallPresenter.getGameInfo(),
                listener = getListener(data.roleEditingGameId)

            )
        }
    }
    private fun getListener(roleEditingGameId: String): OnMgsPlayerInfoDialogListener {
        return object : OnMgsPlayerInfoDialogListener {
            override fun onClickAddFriend(data: MgsPlayerInfo) {
                mgsFloatBallPresenter.addFriendByUuid(data.uuid ?: "")
            }

            override fun onDismissDialog() {
            }

            override fun onClickDressUp() {
                // 通知ts游戏跳转角色编辑器
                jumpGame(metaKV.tsKV.roleGameId)
                EventBus.getDefault().post(CloseRechargeDialogEvent())
            }

            override fun onClickJumpGame(itemGameId: String) {
                // 通知ts游戏跳转角色编辑器
                jumpGame(itemGameId)
            }


            override fun onChangeVoiceState(isOpen: Boolean, openId: String, from: String) {
                mgsFloatBallPresenter.changeOtherVoiceState(isOpen, openId, from)
            }

            override fun getMemberInfo(uuid: String): Member? {
                return mgsFloatBallPresenter.getMemberInfoByUuid(uuid)
            }

            override fun onClickJumpGameDetail(ugcId: String, gameid: String) {
                val resIdBean = ResIdBean().setGameId(ugcId)
                    .setClickGameTime(System.currentTimeMillis())
                    .setTsType(ResIdBean.TS_TYPE_UCG)
                    .setCategoryID(CategoryId.MGS_UGC_CARD)
                    .setGameCode(gameid)
                MetaRouter.Main.openUGCGameDetailFromGame(app, ugcId, null, resIdBean)
            }
        }
    }

    private fun jumpGame(targetGameId: String) {
        val gameInfo = mgsFloatBallPresenter.getGameInfo()
        val gameId = gameInfo?.id ?: ""
        val packageName = gameInfo?.packageName
        if (gameId.isNotEmpty() && !packageName.isNullOrEmpty() && targetGameId.isNotEmpty()) {
            Timber.d("notify ts game jump other game : ${targetGameId}, currGameId: $gameId, currPackageName: $packageName")
            MgsBiz.jumpGameEvent(gameId, packageName, targetGameId)
            mgsMessageExpandView?.close()
        }
    }

    /**
     * 用户资料卡片申请好友回调
     */
    override fun viewCallOnAddFriendResult(isSuccess: Boolean, reason: String?) {
        if (!isSuccess) {
            ToastUtil.gameShowShort(
                reason ?: metaApp.getString(R.string.apply_for_friend_failed)
            )
            return
        }
        MgsDialogManager.playerInfoDialogApplied()
    }

    /**
     * 单独控制悬浮消息的UI展示
     */
    private fun setFloatMessageShow(visible: Boolean, isFocus: Boolean = false) {
        if(PandoraToggle.enableBulletChat){
            //弹幕聊天样式不需要展开
            addOrRemoveView(floatMessageView, false)
            return
        }
        if (visible && !floatMessageView.isVisible) {
            sendChatMessageShow()
        }
        floatBallView.updateMessageViewStatus(visible)
        addOrRemoveView(floatMessageView, visible)
        floatMessageView.focusMessageView(isFocus)
        mgsFloatBallPresenter.changeFloatMessageVisible(visible, detailMessageExpand = false)
        if (visible) {
            floatBallView.updateUnreadMessageCount(0)
        }
    }

    /**
     * 悬浮UI拖动，悬浮信息设为不可见状态
     */
    private fun actionMove() {
        setFloatMessageShow(mgsFloatBallPresenter.floatMessageOpen())
        updateView()
    }

    /**
     * 展示退出游戏弹窗
     */
    override fun viewCallShowQuitGame() {
        curResumedActivity?.let {
            MgsDialogManager.showQuitGame(it, metaApp, object : OnMgsQuitGameDialogListener {
                override fun onLeftClick() {
                    screenRecordDelegate?.checkStopRecord()
                    mgsFloatBallPresenter.remindCpWhenQuitGame(
                        reallyQuit = true,
                        normalBall = false
                    )
                    MainScope().launch {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            onMWGameLifecycleInterceptor?.quitGame(metaApp) ?: MetaRouter.Main.floatBallGameBack(
                                metaApp,
                                mgsFloatBallPresenter.getGameInfo()?.id
                            )
                        }
                    }
                }

                override fun onRightClick() {
                    mgsFloatBallPresenter.remindCpWhenQuitGame(
                        reallyQuit = false,
                        normalBall = false
                    )
                }
            })
        }
    }

    override fun showQuitGame() {
        viewCallShowQuitGame()
    }

    override fun updateVoiceState(isOpen: Boolean, lastVolume: Int, nowVolume: Int) {
        if (PandoraToggle.enableBulletChat) {
            floatMgsDanmuInputView?.changeVoiceState(isOpen, lastVolume, nowVolume)
        } else {
            floatInputView?.changeVoiceState(isOpen, lastVolume, nowVolume)
        }
        floatBallView.changeVoiceState(isOpen, lastVolume, nowVolume)
    }

    override fun updateAudioVisible(
        ballAudioVisible: Boolean,
        inputAudioVisible: Boolean,
        isOpenMic: Boolean
    ) {
        if (PandoraToggle.enableBulletChat) {
            floatMgsDanmuInputView?.changeVoiceVisible(ballAudioVisible)
        } else {
            floatInputView?.changeVoiceVisible(inputAudioVisible, isOpenMic)
        }
        floatBallView.changeVoiceVisible(ballAudioVisible, isOpenMic)
    }

    /**
     * 现在是否在房间内
     */
    private fun isInRoom(): Boolean {
        return mgsFloatBallPresenter.getMgsRoomInfo() != null
    }

    /**
     * 获取包名
     */
    private fun getPackageName(context: Context): String {
        return onMWGameLifecycleInterceptor?.getPackageName() ?: context.packageName
    }

    /**
     * 关闭悬浮层
     */
    override fun closeExpandView() {
        setTalkUIOpen(mgsFloatBallPresenter.shouldOpenFloatMessage())
        mgsFloatBallPresenter.closeExpandView()
        mgsMessageExpandView = null
    }

    /**
     * 控制 悬浮消息 与 消息悬浮球 的UI
     */
    private fun setTalkUIOpen(open: Boolean) {
        if (isInRoom()) {
            setFloatMessageShow(open, open)
        }
    }

    private fun sendChatMessageShow() {
        Analytics.track(EventConstants.EVENT_SHOW_MGS_ROOM) {
            putAll(getGameCommonAnalytic())
            put("source", "box")
        }
    }

    private fun getGameCommonAnalytic(): HashMap<String, String> {
        val gameInfo = mgsFloatBallPresenter.getGameInfo() ?: return hashMapOf()
        return hashMapOf(
            "gameid" to gameInfo.id,
            "gamepkg" to gameInfo.packageName
        )
    }

    /**
     * 改变悬浮消息可见性
     */
    private fun changeFloatMessageVisible(visible: Boolean) {
        if (mgsFloatBallPresenter.floatMessageOpen() == visible) {
            floatMessageView.focusMessageView(true)
            return
        }
        Analytics.track(EventConstants.EVENT_CLICK_MESSAGE_EXPAND_OR_CLOSE) {
            putAll(getGameCommonAnalytic())
            put("isclose", if (!visible) "1" else "2")
            put("type", "mgs")
        }
        mgsFloatBallPresenter.changeFloatMessageVisible(visible, false)
        setTalkUIOpen(mgsFloatBallPresenter.floatMessageOpen())
    }

    /**
     * 更新消息列表的位置
     */
    private fun updateMessageLocation(ballTopY: Int) {
        floatMessageView.setMessageParams(ballTopY, messageAnchorY, screenHeight)
    }

    private fun getGameId(): String {
        return onMWGameLifecycleInterceptor?.getGameId() ?: ""
    }

    fun getGameName(): String {
        val gameName =
            mgsFloatBallPresenter.getGameInfo()?.name ?: onMWGameLifecycleInterceptor?.getAppName()
        return gameName ?: ""
    }

    private fun showMenuPop() {
        curResumedActivity?.let {
            MgsMoreMenuView(
                it,
                ballY,
                floatBallView.height,
                screenHeight,
                screenRecordDelegate?.isRecording ?: false,
               mgsFloatBallPresenter.hasSettingPanel()
            ) { viewId ->
                when (viewId) {
                    R.id.flParentRecord -> {
                        startRecord()
                    }

                    R.id.flParentExit   -> {
                        showQuitGame()
                    }
                    R.id.flParentSet -> {
                        mgsFloatBallPresenter.openSettingView()
                    }
                }
            }.apply {
                show()
            }
        }
    }

    /**
     * 点击悬浮球
     */
    private fun onClickFloatingBall(v: View) {
        when (v.id) {
            R.id.quiteGame         -> {
                floatBallView.showOrdinaryQuit()
            }

            R.id.setting           -> {
                mgsFloatBallPresenter.openSettingView()
            }

            R.id.ordinaryFloatBall -> {
                // 展开普通悬浮球
                floatBallView.showOrdinaryQuit()
            }

            R.id.vMgsMemberBall    -> {
                // 展开悬浮层
                expandPanelView()
            }

            R.id.vMessageBall      -> {
                // 展开/隐藏消息区域
                changeFloatMessageVisible(!mgsFloatBallPresenter.floatMessageOpen())
            }
            R.id.danmuSwitch      -> {
                // 弹幕开启屏蔽点击
                updateDanmuStatus()
            }

            R.id.vMgsExitBall      -> {
                showQuitMenu()
            }

        }
    }
    private fun showQuitMenu() {
        if (isOpenGameRecord || mgsFloatBallPresenter.hasSettingPanel()) {
            showMenuPop()
        } else {
            showQuitGame()
        }
    }

    private fun updateDanmuStatus() {
        floatBallView.updateDanmuStatus()
        floatBallView.updateMessageBallStatus()
        floatMgsDanmuInputView?.updateInputViewVisible(floatBallView.getDanmuStatus())
        if (floatBallView.getDanmuStatus()) {
            mController?.start()
        } else {
            mController?.stop()
        }
        Analytics.track(EventConstants.EVENT_CLICK_MESSAGE_EXPAND_OR_CLOSE) {
            putAll(getGameCommonAnalytic())
            put("isclose", if (!floatBallView.getDanmuStatus()) "1" else "2")
            put("type", "bullet_chat")
        }
    }

    private fun isGameMainProcess(cxt: Context, pid: Int): Boolean {
        val am: ActivityManager = cxt.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningApps: List<ActivityManager.RunningAppProcessInfo> =
            am.runningAppProcesses ?: return false
        for (procInfo in runningApps) {
            if (procInfo.pid == pid) {
                return onMWGameLifecycleInterceptor?.isGameProcessName(procInfo.processName)
                    ?: (app.packageName == procInfo.processName)
            }
        }
        return false
    }

    override fun onActivityStarted(activity: Activity) {
        super.onActivityStarted(activity)
        countActivity++
        if (countActivity == 1 && isBackground) {
            Timber.e("MGS onActivityStarted: App comes to foreground")
            isBackground = false
            //说明应用重新进入了前台,重新加入声网频道
            mgsInteractor.onApplicationStart()
        }
    }

    override fun onActivityStopped(activity: Activity) {
        super.onActivityStopped(activity)
        countActivity--
        Timber.d("MGS onStop $countActivity")
        if (countActivity <= 0 && !isBackground) {
            Timber.e("MGS onActivityStarted: App comes to background")
            isBackground = true
            //说明应用进入了后台,离开声网频道
            mgsInteractor.onApplicationStop()
        }
    }

    override fun joinRoom() {
        updateMgsRoomStatus(true)
        //加入房间成功,展示mgs的UI
        val gameInfo = mgsFloatBallPresenter.getGameInfo()
        val map = mapOf<String, Any>(
            "gameid" to (gameInfo?.id ?: 0L),
        )
        Analytics.track(EventConstants.EVENT_MGS_UI_SHOW) { putAll(map) }
    }

    override fun leaveRoom() {
        updateMgsRoomStatus(false)
    }


    override fun viewCallShowScreenshot(
        data: MgsShareScreenshot,
        jumpUrl: String,
        userNumber: String?
    ) {
        curResumedActivity?.let {
            MgsDialogManager.showScreenshotDialog(
                it,
                metaApp,
                data,
                jumpUrl,
                userNumber
            )
        }
    }

     private fun showOptimizeDialog(data: MgsPlayerInfo, me: Boolean) {
         data
        mgsFloatBallPresenter.getGameInfo()?.let { it1 ->
            curResumedActivity?.let {
                MgsDialogManager.showOptimizeDialog(
                    data, it,
                    metaApp,
                    it1,
                    listener =  getListener(data.roleEditingGameId)
                )
            }
        }
    }

    override fun shrinkMessageLayer(open: Boolean) {
//        setFloatMessageShow(open)
    }

    override fun changeAllMuteStatus(mute: Boolean) {
        if (PandoraToggle.enableBulletChat) {
            floatMgsDanmuInputView?.changeVoiceVisible(!mute)
        } else {
            floatInputView?.changeVoiceStatus(mute)
        }
        floatBallView.changeVoiceStatus(mute)
    }

    override fun showCheckAgeLimitDialog(age: Int) {
        //不符合年龄限制
        Analytics.track(EventConstants.EVENT_VOICE_CHAT_DIALOG_SHOW,mapOf("gameid" to getGameId()))
        curResumedActivity?.let { it ->
            MgsDialogManager.showAgeLimitDialog(
                it,
                metaApp,
                age,
                object : OnMgsQuitGameDialogListener {
                    override fun onLeftClick() {
                        // 取消
                        Analytics.track(EventConstants.EVENT_VOICE_CHAT_DIALOG_CANCEL_CLICK,mapOf("gameid" to getGameId()))
                    }

                    override fun onRightClick() {
                        Analytics.track(EventConstants.EVENT_VOICE_CHAT_DIALOG_CHECK_CLICK,mapOf("gameid" to getGameId()))
                        //编辑用户信息
                        val gameInfo = mgsFloatBallPresenter.getGameInfo()
                        gameInfo?.packageName?.let {
                            MetaRouter.Main.gameToEditProfile(metaApp, it, gameInfo.id)
                        }
                    }
                })
        }
    }

    override fun closeFloatViewFromCP(data: String) {
        closeExpandView()
    }

    override fun setFloatBallVisible(visible: Boolean) {
        if (visible && mgsFloatBallPresenter.getMgsRoomInfo() == null) {
            //不在mgs房间的情况不允许展示其他悬浮器
            return
        }
        updateMgsRoomStatus(visible)
    }

    override fun expandFloatViewFromCP(data: String) {
        if (data == MgsTabEnum.MY_FRIEND_TAB.position.toString()) {
            expandPanelView(MgsTabEnum.MY_FRIEND_TAB)
        } else {
            expandPanelView()
        }
    }

    override fun updateSelfAudioState(open: Boolean) {
        if (open) {
            ToastUtil.gameShowShort(metaApp.getString(R.string.voice_shield_cancel))
        } else {
            ToastUtil.gameShowShort(metaApp.getString(R.string.voice_shield))
        }
       // 更改自己的声音状态
        floatBallView.updateSelfAudioState(open)
        floatInputView?.updateSelfAudioState(open)
        floatMgsDanmuInputView?.updateSelfAudioState(open)

    }

    override fun startRecord() {
        if (screenRecordDelegate == null) {
            screenRecordDelegate = floatRecordView?.let {
                ScreenRecordDelegate(
                    metaApp, app, getGameId(), getPackageName(app),
                    onMgsRecordListener = recordViewListener, it, ::getGameName
                ).apply {
                    registerRecordReceiver()
                }
            }
        }
        GamePermissionActivity.start(
            app,
            getPackageName(app),
            getGameId(),
            PackageUtil.getAppName(app),
            GamePermissionActivity.LOGIC_FROM_SCREEN_RECORD
        )
    }
    override fun removeAllFloatView() {
        mgsMessageExpandView?.close()
        addOrRemoveView(floatBallView, false)
        addOrRemoveView(floatMessageView, false)
        floatInputView?.let {addOrRemoveView(it, false)  }
        floatMgsDanmuInputView?.let { addOrRemoveView(it, false) }
        floatRecordView?.let { addOrRemoveView(it, false) }
        floatRecordPlayerIdView?.let { addOrRemoveView(it,false) }
        danmuView?.let { addOrRemoveView(it, false) }
    }
    override fun addFloatView() {
        addOrRemoveView(floatBallView, true)
        floatBallView.setOrdinary(true)
    }
}