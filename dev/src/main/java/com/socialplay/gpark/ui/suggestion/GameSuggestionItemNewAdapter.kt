package com.socialplay.gpark.ui.suggestion

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.RequestManager
import com.bumptech.glide.request.RequestListener
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.databinding.AdapterGameSuggestionItemNewBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/07/29
 *     desc   :
 */
class GameSuggestionItemNewAdapter(
    private val glide: RequestManager,
    private val glideListener: RequestListener<Drawable>
) :
    BaseAdapter<GameSuggestionInfo, AdapterGameSuggestionItemNewBinding>() {

    class VH<T : ViewBinding>(val binding: T) : RecyclerView.ViewHolder(binding.root)

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int,
    ): AdapterGameSuggestionItemNewBinding {
        return AdapterGameSuggestionItemNewBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BindingViewHolder<AdapterGameSuggestionItemNewBinding>,
        item: GameSuggestionInfo,
        position: Int
    ) {
        val suggestedGameInfo = getItem(position)
        val binding = holder.binding
        binding.tvName.text = suggestedGameInfo.name
        binding.tvDesc.text = suggestedGameInfo.desc
        binding.tvUserName.text = suggestedGameInfo.gameAuthor?.name
        glide.load(item.icon).into(binding.ivIcon)
        glide.load(suggestedGameInfo.gameAuthor?.avatar).into(binding.ivUserAvatar)
    }



}