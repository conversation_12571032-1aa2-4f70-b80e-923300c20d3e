package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.databinding.AdapterRecommendCreatorBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.setTextColorByRes

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: ugc Kol创作者列表-样式2
 */
class RecommendCreatorAdapter(
    private val glide: RequestManager
) : BaseDifferAdapter<KolCreatorInfo, AdapterRecommendCreatorBinding>(DIFF_CALLBACK) {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<KolCreatorInfo>() {
            override fun areItemsTheSame(
                oldItem: KolCreatorInfo,
                newItem: KolCreatorInfo
            ): Boolean {
                return oldItem.uuid == newItem.uuid
            }

            override fun areContentsTheSame(
                oldItem: KolCreatorInfo,
                newItem: KolCreatorInfo
            ): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterRecommendCreatorBinding>,
        item: KolCreatorInfo
    ) {
        glide.load(item.avatar).circleCrop().placeholder(R.drawable.icon_default_avatar).into(holder.binding.iv)
        updateTvFollow(item, holder)
        holder.binding.tvUserName.text = item.nickname
    }

    private fun updateTvFollow(
        item: KolCreatorInfo,
        holder: BaseVBViewHolder<AdapterRecommendCreatorBinding>
    ) {
        if (item.followUser) {
            holder.binding.tvFollow.setText(R.string.following_cap)
            holder.binding.tvFollow.setBackgroundResource(R.drawable.bg_round_16_666666_stroke_05)
            holder.binding.tvFollow.setTextColorByRes(R.color.color_333333)
        } else {
            holder.binding.tvFollow.setText(R.string.follow)
            holder.binding.tvFollow.setBackgroundResource(R.drawable.bg_b884ff_round_16)
            holder.binding.tvFollow.setTextColorByRes(R.color.white)
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterRecommendCreatorBinding {
        return AdapterRecommendCreatorBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}