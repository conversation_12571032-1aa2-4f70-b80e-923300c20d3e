package com.socialplay.gpark.ui.aibot.ugc

import com.bumptech.glide.Glide
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/22
 *     desc   :
 *
 */
class AIBotUGCGenderDialog : BaseDialogFragment() {
    override val binding by viewBinding(com.socialplay.gpark.databinding.DialogAiBotUgcGenderBinding::inflate)
    private var selectGender: String? = null
    override fun init() {
        updateGender()
        binding.tvStart.setOnAntiViolenceClickListener {
            if(selectGender==null){
                return@setOnAntiViolenceClickListener
            }
            MetaRouter.AiBot.gotoAIBotCreate(this, selectGender!!)
            Analytics.track(EventConstants.EVENT_AI_BOT_GENDER_CLICK, map = mapOf("gender_type" to getGenderString()))
            dismissAllowingStateLoss()
        }
        binding.imgClose.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
            Analytics.track(EventConstants.EVENT_AI_BOT_GENDER_CLOSE)
        }
        binding.clMale.setOnAntiViolenceClickListener {
            selectGender = EditorHomeDataViewModel.GENDER_MALE.toString()
            updateGender()
        }
        binding.clFemale.setOnAntiViolenceClickListener {
            selectGender = EditorHomeDataViewModel.GENDER_FEMALE.toString()
            updateGender()
        }
        binding.clNone.setOnAntiViolenceClickListener {
            selectGender = EditorHomeDataViewModel.GENDER_NON.toString()
            updateGender()
        }
        //todo 线上链接需要替换
        //https://qn-basic-content.gpark.io/online/VQ9EmRtOAIhS1724919955679.png
        Glide.with(this).load(BuildConfig.CDN_AI_BOT_GENDER_NON).into(binding.imgNone)
        //https://qn-basic-content.gpark.io/online/aOw0igs56ysX1724919955679.png
        Glide.with(this).load(BuildConfig.CDN_AI_BOT_GENDER_FEMALE).into(binding.imgFemale)
        //https://qn-basic-content.gpark.io/online/jDM8oVN12MNl1724919955679.png
        Glide.with(this).load(BuildConfig.CDN_AI_BOT_GENDER_MALE).into(binding.imgMale)


        //显示
        //https://qn-basic-content.gpark.io/online/cBs9lPf8kDHa1725254646766.png
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_TOP_BG).preload()
        //https://qn-basic-content.gpark.io/online/WfDZc9ALDi4i1725254646766.png
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_BOTTOM_BG).preload()
    }
    private fun getGenderString(): String {
        return when (selectGender) {
            EditorHomeDataViewModel.GENDER_MALE.toString()   -> {
                "male"
            }

            EditorHomeDataViewModel.GENDER_FEMALE.toString() -> {
                "female"
            }

            EditorHomeDataViewModel.GENDER_NON.toString()-> {
                "non-binary"
            }
            else ->{
                ""
            }
        }
    }

    private fun updateGender() {
        when (selectGender) {
            EditorHomeDataViewModel.GENDER_MALE.toString()   -> {
                binding.clMale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12_s_b884ff)
                binding.clFemale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clNone.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.tvStart.alpha = 1f
                binding.tvStart.isEnabled = true
            }

            EditorHomeDataViewModel.GENDER_FEMALE.toString() -> {
                binding.clMale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clFemale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12_s_b884ff)
                binding.clNone.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.tvStart.alpha = 1f
                binding.tvStart.isEnabled = true
            }

            EditorHomeDataViewModel.GENDER_NON.toString()    -> {
                binding.clMale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clFemale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clNone.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12_s_b884ff)
                binding.tvStart.alpha = 1f
                binding.tvStart.isEnabled = true
            }
            else->{
                binding.clMale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clFemale.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.clNone.setBackgroundResource(R.drawable.shape_f5f5f5_corner_12)
                binding.tvStart.alpha = 0.5f
                binding.tvStart.isEnabled = false
            }
        }
    }

    override fun loadFirstData() {

    }
}