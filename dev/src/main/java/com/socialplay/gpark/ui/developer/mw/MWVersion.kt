package com.socialplay.gpark.ui.developer.mw

import android.text.TextUtils
import androidx.annotation.Keep
import timber.log.Timber
import java.io.File

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Keep
class MWVersion {
    companion object {
        const val TAG = "MWVersion::"
    }

    var version: String = ""
    var abi: String = ""
    var ms: Long = 0
    var plugins: List<PluginVersion> = mutableListOf()
    var encode: Boolean = true
    var tag: Int = 0

    fun check(versionDir: File): Bo<PERSON>an {
        if (TextUtils.isEmpty(abi)) {
            Timber.d("$TAG abi err")
            return false
        }
        if (TextUtils.isEmpty(version)) {
            Timber.d("$TAG version err")
            return false
        }
        if (ms < 0L) {
            Timber.d("$TAG ms err")
            return false
        }
        if (plugins.isEmpty()) {
            Timber.d("$TAG plugin isEmpty")
            return false
        }
        for (plugin in plugins) {
            if (plugin.files.isEmpty()) {
                Timber.d("$TAG plugin files isEmpty")
                return false
            }
            for (file in plugin.files) {
                val temp = File(versionDir, file.name)
                if (!temp.exists() || temp.length() != file.uncompressedSize) {
                    Timber.d("$TAG file err ${file.name} ${temp.length()} ${file.uncompressedSize}")
                    return false
                }
            }
        }
        return true
    }
}

@Keep
class PluginVersion {
    var download: String = ""
    var name: String = ""
    var hash: String = ""
    var compressedSize: Long = 0
    var uncompressedSize: Long = 0
    var files: List<FileVersion> = mutableListOf()
}
@Keep
class FileVersion {
    var name: String = ""
    var priority: Int = 0
    var hash: String = ""
    var compressedSize: Long = 0
    var uncompressedSize: Long = 0
}