package com.socialplay.gpark.ui.editorschoice.adapter

import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.databinding.AdapterChoiceRecommendVideoListBinding
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeCardAdapter
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener

class RecommendVideoFeedListProvider(
    private val glide: RequestManager,
    private val itemCallback: ChoiceHomeCardAdapter.HomeItemCallback? = null
) : BaseItemProvider<ChoiceCardInfo>() {

    override val itemViewType: Int = ChoiceCardType.RECOMMEND_VIDEO_FEED_LIST

    override val layoutId = R.layout.adapter_choice_recommend_video_list

    private val itemDecoration: DividerItemDecoration by lazy {
        DividerItemDecoration(context, LinearLayoutManager.HORIZONTAL).apply {
            ResourcesCompat.getDrawable(context.resources, R.drawable.divider_transparent_10, null)
                ?.let { setDrawable(it) }
        }
    }

    override fun convert(helper: BaseViewHolder, card: ChoiceCardInfo) {
        val videoFeedApiResult = card.videoFeeds ?: return
        AdapterChoiceRecommendVideoListBinding.bind(helper.itemView).apply {

            val data: MutableList<ChoiceHomeVideoItem> = videoFeedApiResult.items?.map {
                ChoiceHomeVideoItem(it, videoFeedApiResult.reqId.orEmpty())
            }?.toMutableList() ?: mutableListOf()

            rvVideoItemList.setHasFixedSize(false)

            rvVideoItemList.removeItemDecoration(itemDecoration)
            rvVideoItemList.addItemDecoration(itemDecoration)

            rvVideoItemList.adapter = ChoiceVideoListAdapter(data,glide).apply {
                setOnAntiViolenceItemClickListener { adapter, _, itemPosition ->
                    adapter.getItemOrNull(itemPosition)?.let {
                        itemCallback?.onItemClick(
                            helper.absoluteAdapterPosition,
                            card,
                            itemPosition,
                            it,
                            false
                        )
                    }
                }
                setOnItemShowListener { item, itemPosition ->
                    itemCallback?.onItemShow(
                        helper.absoluteAdapterPosition,
                        card,
                        itemPosition,
                        item,
                        false
                    )
                }
            }
        }
    }
}