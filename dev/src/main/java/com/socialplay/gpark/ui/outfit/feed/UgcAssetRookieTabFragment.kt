package com.socialplay.gpark.ui.outfit.feed

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.databinding.FragmentUgcAssetRookieTabBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.outfit.IUgcDesignFeedListener
import com.socialplay.gpark.ui.outfit.UgcDesignDetailFragment
import com.socialplay.gpark.ui.outfit.UgcDesignFeedState
import com.socialplay.gpark.ui.outfit.UgcDesignFeedViewModel
import com.socialplay.gpark.ui.outfit.ugcDesignFeedItem
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.view.MStaggeredLayoutManager
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth

class UgcAssetRookieTabFragment :
    BaseRecyclerViewFragment<FragmentUgcAssetRookieTabBinding>(R.layout.fragment_ugc_asset_rookie_tab) {

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val vm: UgcAssetRookieTabViewModel by fragmentViewModel()
    private val tabVM: UgcDesignFeedViewModel by parentFragmentViewModel()

    private val itemListener = UgcDesignFeedListener()

    private var spanSize = 2
    private var layoutManagerState: Parcelable? = null

    private var scrollToTop = false

    private var needCheckGuide = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcAssetRookieTabState::loadMore
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcAssetRookieTabBinding? {
        return FragmentUgcAssetRookieTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        spanSize = if (isPad) {
            ((screenWidth - dp(20)) / dp(177.5f)).coerceAtLeast(2)
        } else {
            2
        }
        EpoxyVisibilityTracker().attachV2(viewLifecycleOwner, recyclerView)
        recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)
        recyclerView.itemAnimator = null
        recyclerView.layoutManager =
            MStaggeredLayoutManager(spanSize, RecyclerView.VERTICAL).apply {
                setOnDetachedCallback {
                    layoutManagerState = it
                }
                setOnRestoreCallback {
                    return@setOnRestoreCallback layoutManagerState
                }
            }
        recyclerView.adapter?.stateRestorationPolicy?.let {
            epoxyController.adapter.stateRestorationPolicy = it
        }

        vm.setupRefreshLoading(
            UgcAssetRookieTabState::assets,
            binding.lv,
            binding.rl
        ) {
            vm.getFeed(true)
        }
        vm.onEach(UgcAssetRookieTabState::uniqueTag, uniqueOnly()) {
            scrollToTop = true
        }
        vm.onEach(UgcAssetRookieTabState::guideInvoke, deliveryMode = uniqueOnly()) {
            it ?: return@onEach
            UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_FIRST_ASSET_INTERACT)
        }
        tabVM.onEach(UgcDesignFeedState::event, deliveryMode = uniqueOnly()) {
            when (it?.first) {
                UgcDesignFeedViewModel.EVENT_ASSET_DETAIL_LIKE -> {
                    val bundle = it.third as? Bundle ?: return@onEach
                    val itemId = bundle.getString(UgcDesignDetailFragment.KEY_ITEM_ID)
                        ?: return@onEach
                    val likeCount = bundle.getLong(UgcDesignDetailFragment.KEY_LIKE_COUNT)
                    val isLike = bundle.getBoolean(UgcDesignDetailFragment.KEY_IS_LIKE)
                    vm.updateLikeCount(itemId, isLike, likeCount)
                }
            }
        }
        vm.registerAsyncErrorToast(UgcAssetRookieTabState::loadMore)

        epoxyController.addModelBuildListener(viewLifecycleOwner) {
            if (scrollToTop) {
                scrollToTop = false
                recyclerView.scrollToPosition(0)
            }
        }

        needCheckGuide = true
    }

    private fun initModuleGuideDialog() {
        if (!needCheckGuide) return
        needCheckGuide = false
        if (!PandoraToggle.enableModuleGuideRookieTab) return
        val status = vm.accountInteractor.moduleGuideStatus
        if (status > BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO) return
        if (!tabVM.showGuide) return
        UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_FIRST_ROOKIE_TAB) {
            if (it) {
                tabVM.showGuide = false
            }
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcAssetRookieTabState::assets,
        UgcAssetRookieTabState::loadMore,
        UgcAssetRookieTabState::uniqueTag
    ) { assets, loadMore, uniqueTag ->
        val items = assets()
        if (items.isNullOrEmpty()) return@simpleController
        val uuid = vm.curUuid
        items.forEachIndexed { index, item ->
            ugcDesignFeedItem(item, index, uuid, uniqueTag, "UgcAssetRookieTab", itemListener)
        }
        loadMoreFooter(
            loadMore,
            idStr = "UgcAssetRookieTabFooter-${uniqueTag}",
            spanSize = spanSize,
            showEnd = false,
            staggerFullSpan = true
        ) {
            vm.getFeed(loadMore()?.needRefresh == true)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_ASSET_ROOKIE_TAB

    override fun onResume() {
        super.onResume()
        initModuleGuideDialog()
    }

    private inner class UgcDesignFeedListener : IUgcDesignFeedListener {
        override fun clickOutfit(item: UgcDesignFeed, position: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_CLICK,
                "metrialidid" to item.trackId,
                "type" to item.feedType,
                "authorid" to item.uuid.orEmpty(),
                "tab_id" to 1
            )
            MetaRouter.UgcDesign.detail(
                this@UgcAssetRookieTabFragment,
                item.feedId,
                CategoryId.UGC_DESIGN_FEED,
                tabId = 1
            )
        }

        override fun clickLike(item: UgcDesignFeed, position: Int) {
            vm.likeFeed(item, position)
        }

        override fun more(item: UgcDesignFeed, position: Int) {
            val report = SimpleListData(getString(R.string.report))
            val cancel = SimpleListData(getString(R.string.cancel))

            ListDialog()
                .list(listOf(report, cancel))
                .clickCallback {
                    when (it) {
                        report -> {
                            MetaRouter.Report.postReport(
                                this@UgcAssetRookieTabFragment,
                                item.feedId,
                                ReportType.UgcClothing
                            ) {
                                if (it) {
                                    ReportReasonDialog.showReportSuccessDialog(
                                        this@UgcAssetRookieTabFragment,
                                        ReportSuccessDialogAnalyticsParams.Clothing(
                                            feedId = item.feedId
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
                .show(childFragmentManager, "UgcDesignFeedMoreDialog")
        }

        override fun showDesign(item: UgcDesignFeed, position: Int) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_SHOW,
                "metrialidid" to item.trackId,
                "type" to item.feedType,
                "authorid" to item.uuid.orEmpty(),
                "tab_id" to 1,
                "is_recreate" to item.trackIsRecreate
            )
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}