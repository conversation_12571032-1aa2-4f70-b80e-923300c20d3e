package com.socialplay.gpark.ui.plot.chooseimage

import android.os.Parcelable
import androidx.core.view.isVisible
import com.airbnb.mvrx.asMavericksArgs
import com.bin.cpbus.CpEventBus
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.event.PlotChooseImageEvent
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.util.extension.likelyPath
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2024/5/11
 * Desc: 选择图片，裁剪
 */
@Parcelize
data class PlotClipImageActivityArgs(
    val ratioWidth: Int,
    val ratioHeight: Int,
    val useClip: Boolean,
    val messageId: Int,
    val gameId: String,
) : Parcelable

class PlotChooseImageActivity : BaseChooseImageActivity() {
    var args: PlotClipImageActivityArgs? = null

    companion object {
        const val EXTRA_KEY_PARAMS = "choose_image_extra_params"
    }

    override fun initArgs() {
        args = intent.getParcelableExtra(EXTRA_KEY_PARAMS)
        if (args == null) {
            callbackError("params is null", 2004)
            return
        }
        binding.fcvClip.isVisible = true
    }

    override fun onSelectResult(result: ArrayList<LocalMedia>) {
        args?.let {
            val imgInfo = result.firstOrNull()
            val path = imgInfo?.likelyPath
            if (imgInfo == null || path.isNullOrEmpty()) {
                callbackError("user cancelled", 2003)
                return
            }
            showClip(
                PlotClipImageFragmentArgs(
                    path,
                    it.ratioWidth,
                    it.ratioHeight,
                    it.useClip,
                )
            )
        }
    }

    private fun showClip(args: PlotClipImageFragmentArgs) {
        supportFragmentManager.beginTransaction()
            .replace(
                R.id.fcv_clip,
                PlotClipImageFragment::class.java,
                args.asMavericksArgs(),
                "PlotChooseImageActivity"
            ).commitAllowingStateLoss()
    }

    fun callUEResult(url: String?, message: String?, code: Int) {
        if (StartupContext.get().processType != StartupProcessType.M) {
            CpEventBus.post(
                PlotChooseImageEvent(
                    args?.messageId ?: 0,
                    args?.gameId ?: "",
                    url,
                    message,
                    code
                )
            )
        }
        finish()
    }

    override fun callbackError(message: String?, code: Int) {
        callUEResult(null, message, code)
    }
}