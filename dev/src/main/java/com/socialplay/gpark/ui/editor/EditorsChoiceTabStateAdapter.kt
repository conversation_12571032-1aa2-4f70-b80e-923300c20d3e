package com.socialplay.gpark.ui.editor

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 *
 * <AUTHOR>
 * @date 2021/07/03
 */
class EditorsChoiceTabStateAdapter(
    private val fragmentCreators: List<() -> Fragment>,
    fragmentManager: FragmentManager,
    lifecycle: Lifecycle
) : FragmentStateAdapter(fragmentManager, lifecycle) {

    override fun getItemCount() = fragmentCreators.size

    override fun createFragment(position: Int) = fragmentCreators[position].invoke()
}