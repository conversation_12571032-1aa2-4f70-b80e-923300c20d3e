package com.socialplay.gpark.ui.mgs.message

import android.app.Activity
import android.app.Application
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.meta.biz.mgs.data.model.MGSMessage
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.MetaMgsViewMessageAftertenBinding
import com.socialplay.gpark.ui.mgs.adapter.MgsMessageAdapter
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatInputListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatMessageListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*


/**
 * Created by bo.li
 * Date: 2022/1/18
 * Desc: 悬挂消息UI
 */
class MgsFloatMessageView(
    val app: Application,
    val metaApp: Context,
    val listener: OnMgsFloatMessageListener,
    val inputListener: OnMgsFloatInputListener
) : RelativeLayout(metaApp) {

    lateinit var binding: MetaMgsViewMessageAftertenBinding
    private lateinit var messageAdapter: MgsMessageAdapter

    private var timer: Job? = null

    init {
        initView()
        initData()
    }

    private fun initView() {
        binding = MetaMgsViewMessageAftertenBinding.inflate(LayoutInflater.from(metaApp), this, true)
        messageAdapter = MgsMessageAdapter()
        binding.rvFloatMgsMessage.adapter = messageAdapter
        binding.vInput.setInputListener(inputListener)
        binding.vInput.initHint(metaApp.getString(R.string.mgs_input_hint), metaApp.getColor(R.color.white_50))
        binding.vCover.setOnClickListener {
            focusMessageView(true)
        }
    }

    private fun initData() {
        messageAdapter.clearList()
        messageAdapter.addData(listener.getRoomMessageList() ?: emptyList())
    }

    /**
     * 设置消息列表的高度
     */
    fun setMessageParams(ballTopY: Int, messageAnchorY: Int, screenHeight: Int) {
        binding.root.postDelayed({
            updateFloatMessageLayout(ballTopY, messageAnchorY, screenHeight)
            messageSmoothScrollToBottom()
        }, 100)
    }

    /**
     * 更新消息列表
     */
    fun updateMessageList(value: MutableList<MGSMessage>?) {
        val newData = value ?: ArrayList()
        messageAdapter.clearList()
        messageAdapter.addData(newData)
    }

    /**
     * 增加一条消息
     */
    fun addMessageList(value: MGSMessage) {
        messageAdapter.addData(value)
        if (isSlideToBottom(binding.rvFloatMgsMessage)) {
            binding.rvFloatMgsMessage.scrollToPosition(messageAdapter.data.size - 1)
        }
    }

    private fun isSlideToBottom(recyclerView: RecyclerView?): Boolean {
        return recyclerView != null
    }

    fun setInputViewVisible(isFocus: Boolean) {
        binding.vInput.setInputViewVisible(isFocus)
    }

    fun messageSmoothScrollToBottom() {
        if (messageAdapter.data.size > 1) {
            binding.rvFloatMgsMessage.smoothScrollToPosition(messageAdapter.data.size)
        }
    }

    fun showFocusMessageView(isFocus: Boolean) {
        setInputViewVisible(isFocus)
        val background = if (isFocus) R.drawable.bg_corner_8_black_30 else R.color.transparent
        binding.metaMgsRlMessageAfter.setBackgroundResource(background)
        binding.vCover.visible(!isFocus)
    }

    fun focusMessageView(isFocus: Boolean) {
        showFocusMessageView(isFocus)
        timeMessageFocus(isFocus)
    }

    private fun timeMessageFocus(isFocus: Boolean = true) {
        timer?.cancel()
        timer = null
        if (!isFocus) return
        timer = countDownCoroutines(3, onFinish = {
            if (!binding.vInput.isShow()) {
                focusMessageView(false)
            }
            timer?.cancel()
            timer = null
        })
        timer?.start()
    }

    private fun countDownCoroutines(
        total: Int, onTick: ((Int) -> Unit)? = null, onFinish: () -> Unit,
        scope: CoroutineScope = GlobalScope
    ): Job {
        return flow {
            for (i in total downTo 0) {
                emit(i)
                delay(1000)
            }
        }.flowOn(Dispatchers.Default)
            .onCompletion { onFinish.invoke() }
            .onEach { onTick?.invoke(it) }
            .flowOn(Dispatchers.Main)
            .launchIn(scope)
    }

    /**
     * 更新消息列表的位置
     */
    private fun updateFloatMessageLayout(ballTopY: Int, messageAnchorY: Int, screenHeight: Int) {
        // 消息在球上面
        val isTop = ballTopY > (screenHeight / 2)
        // 如果消息在球下面，就是message的y定位点
        val anchorY = ballTopY + messageAnchorY

        val newY = if (isTop) {
            if (anchorY - binding.root.height < 0) 0 else (ballTopY - binding.root.height - 20)
        } else {
            anchorY
        }
        listener.notifyParams(newY)
    }
}
