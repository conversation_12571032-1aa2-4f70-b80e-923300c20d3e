package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.youth.banner.Banner
import com.youth.banner.adapter.BannerAdapter
import com.youth.banner.listener.OnPageChangeListener
import com.youth.banner.util.BannerUtils

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/25
 *     desc   :
 * </pre>
 */
open class EpoxyBanner<T, BA : BannerAdapter<T, *>> @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defaultStyleAttr: Int = 0
) : Banner<T, BA>(context, attributeSet, defaultStyleAttr) {

    // 用于onPause就停止动画的情况，默认为false(就只会在onStop才会停止动画)。 如果onPause停止动画生效时，onStop就不停止了，因为重复了。
    private var onPauseStop = false
    private var mOnPageChangeListener: OnPageChangeListener? = null
    private var mPageChangeCallback: BannerOnPageChangeCallback? = null
    private var owner: LifecycleOwner? = null

    init {
        destroy()
    }

    fun addBannerResumeLifecycleObserver(owner: LifecycleOwner): EpoxyBanner<T, BA> {
        onPauseStop = true
        owner.lifecycle.addObserver(this)
        return this;
    }

    fun removeBannerResumeLifecycleObserver(): EpoxyBanner<T, BA> {
        onPauseStop = false
        owner?.lifecycle?.removeObserver(this)
        owner = null
        return this;
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        if (onPauseStop) {
            start()
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    fun onPause() {
        if (onPauseStop) {
            stop()
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        if (onPauseStop) {
            destroy()
            owner?.lifecycle?.removeObserver(this)
            owner = null
        }
    }

    override fun onStart(owner: LifecycleOwner) {
        if (!onPauseStop) {
            super.onStart(owner);
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        if (!onPauseStop) {
            super.onStop(owner);
        }
    }

    override fun addOnPageChangeListener(pageListener: OnPageChangeListener?): EpoxyBanner<T, BA> {
        this.mOnPageChangeListener = pageListener
        return this
    }

    fun registerOnPageCallback(): EpoxyBanner<T, BA> {
        val vp = viewPager2 ?: return this
        if (mPageChangeCallback == null) {
            val callback = BannerOnPageChangeCallback()
            vp.registerOnPageChangeCallback(callback)
            mPageChangeCallback = callback
        }
        return this
    }

    fun unregisterOnPageCallback(): EpoxyBanner<T, BA> {
        val vp = viewPager2 ?: return this
        mPageChangeCallback?.let {
            vp.unregisterOnPageChangeCallback(it)
            mPageChangeCallback = null
        }
        return this
    }

    inner class BannerOnPageChangeCallback : OnPageChangeCallback() {
        private var mTempPosition = INVALID_VALUE
        private var isScrolled = false

        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
            val realPosition =
                BannerUtils.getRealPosition(isInfiniteLoop, position, getRealCount())
            mOnPageChangeListener?.let {
                if (realPosition == currentItem - 1) {
                    it.onPageScrolled(
                        realPosition,
                        positionOffset,
                        positionOffsetPixels
                    )
                }
            }
            if (indicator != null && realPosition == currentItem - 1) {
                indicator.onPageScrolled(realPosition, positionOffset, positionOffsetPixels)
            }
        }

        override fun onPageSelected(position: Int) {
            if (isScrolled) {
                mTempPosition = position
                val realPosition =
                    BannerUtils.getRealPosition(isInfiniteLoop, position, getRealCount())
                mOnPageChangeListener?.onPageSelected(realPosition)
                if (indicator != null) {
                    indicator.onPageSelected(realPosition)
                }
            }
        }

        override fun onPageScrollStateChanged(state: Int) {
            if (state == ViewPager2.SCROLL_STATE_DRAGGING || state == ViewPager2.SCROLL_STATE_SETTLING) {
                isScrolled = true
            } else if (state == ViewPager2.SCROLL_STATE_IDLE) {
                isScrolled = false
                if (mTempPosition != INVALID_VALUE && isInfiniteLoop) {
                    if (mTempPosition == 0) {
                        setCurrentItem(getRealCount(), false)
                    } else if (mTempPosition == getItemCount() - 1) {
                        setCurrentItem(1, false)
                    }
                }
            }
            mOnPageChangeListener?.onPageScrollStateChanged(state)
            if (indicator != null) {
                indicator.onPageScrollStateChanged(state)
            }
        }
    }
}
