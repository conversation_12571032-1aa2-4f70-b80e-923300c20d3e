package com.socialplay.gpark.ui.gamedetail.sendflower

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.gift.SendGiftCondition
import com.socialplay.gpark.databinding.ItemSendFlowerConditionBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

interface ISendFlowerConditionJumpClickedListener {
    fun onClicked(condition: SendGiftCondition)
}

data class ItemSendFlowerCondition(
    val condition: SendGiftCondition,
    val clickListener: ISendFlowerConditionJumpClickedListener
) : ViewBindingItemModel<ItemSendFlowerConditionBinding>(
    R.layout.item_send_flower_condition,
    ItemSendFlowerConditionBinding::bind
) {
    override fun ItemSendFlowerConditionBinding.onBind() {
        tvTitle.text = condition.name ?: ""
        if (condition.isComplete()) {
            // 条件满足
            ivOk.visible()
            tvNotMet.gone()
            tvJumpBtn.gone()
        } else {
            // 条件不满足
            ivOk.gone()
            val schema = condition.schema
            tvNotMet.visible(schema == null)
            if (schema == null) {
                tvJumpBtn.gone()
                tvJumpBtn.unsetOnClick()
            } else {
                tvJumpBtn.visible()
                tvJumpBtn.text = schema.name ?: ""
                tvJumpBtn.setOnAntiViolenceClickListener {
                    clickListener.onClicked(condition)
                }
            }
        }
    }
}