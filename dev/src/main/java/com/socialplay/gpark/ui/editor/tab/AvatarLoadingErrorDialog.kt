package com.socialplay.gpark.ui.editor.tab

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.AvatarLoadingErrorDialogArgs
import com.socialplay.gpark.databinding.DialogFragmentAvatarLoadErrBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.setFragmentResultListener
import com.socialplay.gpark.util.property.viewBinding

class AvatarLoadingErrorDialog : BaseDialogFragment() {

    companion object {
        const val REQUEST_KEY = "SimpleDialogFragment_Request_Key_Result"
        const val RESULT_KEY = "SimpleDialogFragment_Result_Key"

        const val LEAVE = 0
        const val CONFIRM = 1
        const val DISMISS = 2

        fun isShowing(fragmentManager: FragmentManager): Boolean {
            val fragment = fragmentManager.findFragmentByTag("AvatarLoadingErrorDialog")
            return fragment != null
        }

        fun show(
            context: Context,
            fragmentManager: FragmentManager,
            title: String,
            content: String,
            confirmText: String? = null,
            errorCode: Int,
            lifecycleOwner: LifecycleOwner,
            callback: (result: Int) -> Unit
        ) {
            val dialog = AvatarLoadingErrorDialog()
            dialog.arguments = Bundle().apply {

                val realContent = if (!PandoraInit.checkEsVersion(context)) {
                    context.getString(R.string.opengl_es_tips)
                } else {
                    content
                }

                val args = AvatarLoadingErrorDialogArgs(
                    title = title,
                    content = realContent,
                    confirmText = confirmText,
                    errorCode = errorCode,
                )
                putParcelable("args", args)
            }

            dialog.showNow(fragmentManager, "AvatarLoadingErrorDialog")

            dialog.setFragmentResultListener(REQUEST_KEY, lifecycleOwner) { key, data ->
                if (key == REQUEST_KEY) {
                    val result = data.getInt(RESULT_KEY)
                    callback.invoke(result)
                }
            }
        }

        fun dismiss(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag("AvatarLoadingErrorDialog")
            if(fragment != null && fragment is AvatarLoadingErrorDialog) {
                fragment.dismissNow()
            }
        }
    }


    override val binding by viewBinding(DialogFragmentAvatarLoadErrBinding::inflate)

    private val args by lazy {
        arguments?.getParcelable<AvatarLoadingErrorDialogArgs>("args")
    }

    private var pendingResult: Int = DISMISS

    override fun gravity() = Gravity.CENTER

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Analytics.track(EventConstants.EVENT_LOADING_AVATAR_FAILSHOW, mapOf(
            "errorcode" to (args?.errorCode ?: ""),
        ))
    }

    override fun init() {
        binding.apply {
            title.text = args?.title
            content.text = args?.content
            val confirmText = args?.confirmText
            if (!confirmText.isNullOrBlank()) {
                btnConfirm.text = confirmText
            }

            btnCancel.setOnClickListener {
                pendingResult = LEAVE
                Analytics.track(EventConstants.EVENT_LOADING_AVATAR_FAILCLICK){
                    put("button","2")
                }
                dismiss()
            }

            btnConfirm.setOnClickListener {
                pendingResult = CONFIRM
                Analytics.track(EventConstants.EVENT_LOADING_AVATAR_FAILCLICK) {
                    put("button", "1")
                }
                dismiss()
            }
        }
    }

    override fun loadFirstData() {

    }

    override fun onDismiss(dialog: DialogInterface) {
        setFragmentResult(REQUEST_KEY, Bundle().apply {
            putInt(RESULT_KEY, pendingResult)
        })
        super.onDismiss(dialog)
    }

}
