package com.socialplay.gpark.ui.gamedetail.room

import android.view.Gravity
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.*
import androidx.recyclerview.widget.LinearLayoutManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.GameRoomDetail
import com.socialplay.gpark.databinding.DialogDetailRoomListBinding
import com.socialplay.gpark.databinding.DialogTsRoomWarningBinding
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import com.meta.pandora.Pandora
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * xingxiu.hou
 * 2022/4/15
 */

class GameRoomObserver(val fragment: Fragment) : LifecycleEventObserver {


    private var dialog: GameRoomDialog? = null
    private var listener: ((roomId: String) -> Unit)? = null

    init {
        fragment.lifecycle.addObserver(this)
    }

    fun showRoomListPage(appInfo: GameDetailInfo) {
        Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_CLICK).put("gameid", appInfo.id)
            .send()
        dialog = GameRoomDialog().apply {
            clickGameJoinListener { gameId, roomId ->
                Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_LIST_CLICK_JOIN)
                    .put("gameid", gameId)
                    .send()
                listener?.invoke(roomId)
            }
            arguments = bundleOf(GameRoomDialog.GAME_ID to appInfo.id)
            show(fragment.childFragmentManager, "DetailRoomList")
        }
    }

    fun clickGameJoinListener(listener: (roomId: String) -> Unit) {
        this.listener = listener
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            if ((dialog?.dialog != null && dialog?.dialog?.isShowing == true) || dialog?.isVisible == true) {
                dialog?.dismiss()
            }
            dialog = null
        }
    }
}

class GameRoomDialog : BaseDialogFragment() {

    companion object {
        const val GAME_ID = "game_id"
    }

    override var navColorRes = R.color.white

    private val adapter by lazy { GameRoomListAdapter() }
    private val viewModel: GameRoomViewModel by viewModel()
    private var loadMoreIsOpen = false
    private var maxId = "0"
    private var gameId = ""
    private var listener: ((gameId: String, roomId: String) -> Unit)? = null

    override val binding by viewBinding(DialogDetailRoomListBinding::inflate)

    fun clickGameJoinListener(listener: (gameId: String, roomId: String) -> Unit) {
        this.listener = listener
    }

    override fun init() {
        binding.ivRoomListClose.setOnAntiViolenceClickListener { dismiss() }
        binding.ivRoomListRefresh.setOnAntiViolenceClickListener {
            binding.ivRoomListRefresh.animate().rotation(720F).setDuration(600).withEndAction {
                binding.ivRoomListRefresh.rotation = 0F
            }.start()
            Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_LIST_REFRESH)
                .put("gameid", gameId)
                .send()
            binding.rvRoomList.smoothScrollToPosition(0)
            if (gameId.isNotEmpty()) {
                viewModel.refresh(gameId)
            }
        }
        binding.rvRoomList.layoutManager = LinearLayoutManager(context)
        binding.rvRoomList.adapter = adapter
        adapter.setOnItemClickListener { adapter, _, position ->
            val data = adapter.data[position]
            if (data != null && data is GameRoomDetail) {
                getGameRoomInfo(data.id, data.roomId) { dismiss() }
            } else {
                ToastUtil.showShort(requireContext(), R.string.common_error)
                dismiss()
            }
        }

        viewModel.gameRoomListLiveData.observe(viewLifecycleOwner) {
            binding.tvNoData.visible(false)
            maxId = it.second
            when (it.first) {
                STATUS.REFRESH -> {
                    //可以加载更多
                    loadMoreIsOpen = it.third.size >= GameRoomViewModel.PAGE_SIZE
                    openLoadMore(loadMoreIsOpen)
                    refresh(it.third)
                }
                STATUS.MORE -> {
                    if (loadMoreIsOpen) {
                        adapter.loadMoreModule.loadMoreComplete()
                    }
                    loadMore(it.third)
                }
                STATUS.END -> {
                    if (loadMoreIsOpen) {
                        adapter.loadMoreModule.loadMoreEnd(true)
                    }
                    loadMore(it.third)
                }
                STATUS.FAILED -> {
                    if (loadMoreIsOpen) {
                        adapter.loadMoreModule.loadMoreFail()
                    }
                }
                STATUS.EMPTY -> {
                    refresh(listOf())
                    binding.tvNoData.visible(true)
                }
            }
            if (adapter.data.isEmpty()) {
                binding.tvNoData.visible(true)
            }
        }
    }

    private fun openLoadMore(open: Boolean) {
        adapter.loadMoreModule.isEnableLoadMore = open
        adapter.loadMoreModule.enableLoadMoreEndClick = open
        if (open) {
            adapter.loadMoreModule.setOnLoadMoreListener {
                if (!NetUtil.isNetworkAvailable()) {
                    adapter.loadMoreModule.loadMoreFail()
                } else {
                    if (gameId.isNotEmpty()) {
                        viewModel.loadMore(gameId, maxId)
                    }
                }
            }
        } else {
            adapter.loadMoreModule.setOnLoadMoreListener(null)
        }
    }

    fun refresh(list: List<GameRoomDetail>) {
        adapter.setList(list)
    }

    private fun loadMore(list: List<GameRoomDetail>) {
        adapter.addData(list)
    }

    private fun getGameRoomInfo(id: Int, roomId: String, dismissCallback: () -> Unit) {
        lifecycleScope.launch(Dispatchers.IO) {
            viewModel.getTsGameRoomInfo("$id", roomId).collect {
                if (it.succeeded && it.data != null) {
                    when {
                        it.data?.isRoomDestroy() == true -> {
                            //已解散
                            lifecycleScope.launch(Dispatchers.Main) {
                                GameRoomWarningDialog.showDestroyWarning(childFragmentManager)
                            }
                            Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_LIST_DIALOG_SHOW)
                                .put("gameid", gameId).put("type", "2").send()
                        }
                        it.data?.isUserFull() == true -> {
                            //当前人员已满
                            lifecycleScope.launch(Dispatchers.Main) {
                                GameRoomWarningDialog.showUserFullWarning(childFragmentManager)
                            }
                            Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_LIST_DIALOG_SHOW)
                                .put("gameid", gameId).put("type", "1").send()
                        }
                        else -> {
                            lifecycleScope.launch(Dispatchers.Main) {
                                listener?.invoke(gameId, roomId)
                                dismissCallback.invoke()
                            }
                        }
                    }
                } else {
                    lifecycleScope.launch(Dispatchers.Main) {
                        listener?.invoke(gameId, roomId)
                        dismissCallback.invoke()
                    }
                }
            }
        }
    }

    override fun loadFirstData() {
        gameId = arguments?.getString(GAME_ID, "") ?: ""
        if (gameId.isNotEmpty()) {
            viewModel.refresh(gameId)
        }
        Pandora.kind(EventConstants.EVENT_GAME_DETAIL_TS_ROOM_LIST_SHOW).put("gameid", gameId)
            .send()
    }

    override fun windowHeight(): Int = WindowManager.LayoutParams.MATCH_PARENT

    override fun isFullScreen(): Boolean = true

    override fun dimAmount(): Float = 0F

    override fun onDestroyView() {
        //防止动画运行退出，造成的崩溃
        binding.ivRoomListRefresh.animate().cancel()
        super.onDestroyView()
        if (loadMoreIsOpen) {
            adapter.loadMoreModule.setOnLoadMoreListener(null)
        }
    }

}

class GameRoomWarningDialog : BaseDialogFragment() {

    companion object {
        private const val ARG_TYPE_KEY = "arg_type_key"
        private const val WARNING_ROOM_DESTROY = 0
        private const val WARNING_USER_FULL = 1

        fun showDestroyWarning(fragmentManager: FragmentManager) {
            GameRoomWarningDialog().apply {
                arguments = bundleOf(ARG_TYPE_KEY to WARNING_ROOM_DESTROY)
            }.show(fragmentManager, "GameRoomWarning")
        }

        fun showUserFullWarning(fragmentManager: FragmentManager) {
            GameRoomWarningDialog().apply {
                arguments = bundleOf(ARG_TYPE_KEY to WARNING_USER_FULL)
            }.show(fragmentManager, "GameRoomWarning")
        }
    }

    override val binding by viewBinding(DialogTsRoomWarningBinding::inflate)

    override fun init() {
        val desc = when (arguments?.getInt(ARG_TYPE_KEY) ?: -1) {
            WARNING_ROOM_DESTROY -> resources.getString(R.string.game_detail_room_warning_destroy)
            WARNING_USER_FULL -> resources.getString(R.string.game_detail_room_warning_user_full)
            else -> resources.getString(R.string.game_detail_room_warning_destroy)
        }

        binding.tvDes.text = desc
        binding.tvDone.setOnAntiViolenceClickListener {
            dismiss()
        }
    }

    override fun loadFirstData() {

    }

    override fun windowHeight(): Int = WindowManager.LayoutParams.MATCH_PARENT

    override fun isFullScreen(): Boolean = true

    override fun gravity(): Int = Gravity.CENTER

    override fun dimAmount(): Float = 0F

}