package com.socialplay.gpark.ui.view.preference

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewPreferenceEditareaBinding
import com.socialplay.gpark.databinding.ViewPreferenceIconBinding
import com.socialplay.gpark.databinding.ViewPreferenceTextBinding
import com.socialplay.gpark.databinding.ViewPreferenceToggleBinding
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.visible

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/03/10
 *     desc   :
 */
class PreferenceItemView @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defaultStyleAttr: Int = 0
) : ConstraintLayout(context, attributeSet, defaultStyleAttr) {

    private var style: PreferenceItemStyle? = null
    private var view: View? = null


    init {
        if (attributeSet != null) {
            val typedArray = context.obtainStyledAttributes(attributeSet, R.styleable.PreferenceItemView)

            try {
                val label = typedArray.getString(R.styleable.PreferenceItemView_label)
                val text = typedArray.getString(R.styleable.PreferenceItemView_text)
                val hint = typedArray.getString(R.styleable.PreferenceItemView_hint)

                val navigatiable = typedArray.getBoolean(R.styleable.PreferenceItemView_navigatiable, true)
                val showDivider = typedArray.getBoolean(R.styleable.PreferenceItemView_show_divider, true)
                val toggled = typedArray.getBoolean(R.styleable.PreferenceItemView_toggled, true)
                val url = typedArray.getString(R.styleable.PreferenceItemView_icon_url)

                val style: PreferenceItemStyle? = when (PreferenceItemStyleEnum.create(typedArray.getInt(R.styleable.PreferenceItemView_style, -1))) {
                    PreferenceItemStyleEnum.Custom -> { //Custom
                        check(childCount == 1)
                        val directChild = getChildAt(0)
                        removeAllViews()
                        PreferenceItemStyle.Custom(directChild)
                    }
                    PreferenceItemStyleEnum.Text                                   -> { //Text
                        PreferenceItemStyle.Text(label ?: "", text, navigatiable, showDivider)
                    }
                    PreferenceItemStyleEnum.Toggle    -> { //Toggle
                        PreferenceItemStyle.Toggle(label ?: "", toggled, showDivider)
                    }
                    PreferenceItemStyleEnum.Icon    -> { //Icon
                        PreferenceItemStyle.Icon(label ?: "", url ?: "", showDivider)
                    }
                    PreferenceItemStyleEnum.EditArea    -> { //Edit
                        PreferenceItemStyle.EditArea(label ?: "", text ?: "", hint ?: "")
                    }
                    else -> {
                        null
                    }
                }

                style?.let { setStyle(style) }

            }finally {
                typedArray.recycle()
            }
        }
    }

    fun setStyle(style: PreferenceItemStyle?) {
        if (this.style != style) {
            this.style = style
            view = invalidateStyle(style)
        }

        bindStyle(style)
    }

    fun getStyle(): PreferenceItemStyle? {
        return style
    }

    private fun invalidateStyle(style: PreferenceItemStyle?): View? {
        removeAllViews()

        val rootView = when (style) {
            is PreferenceItemStyle.EditArea -> {
                ViewPreferenceEditareaBinding.inflate(LayoutInflater.from(context), this, true).root
            }
            is PreferenceItemStyle.Icon     -> {
                ViewPreferenceIconBinding.inflate(LayoutInflater.from(context), this, true).root
            }
            is PreferenceItemStyle.Text     -> {
                ViewPreferenceTextBinding.inflate(LayoutInflater.from(context), this, true).root
            }
            is PreferenceItemStyle.Toggle   -> {
                ViewPreferenceToggleBinding.inflate(LayoutInflater.from(context), this, true).root
            }
            is PreferenceItemStyle.Custom   -> {
                addView(
                    style.view,
                    ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                )
                style.view
            }
            else                            -> {
                null
            }
        }
        return rootView
    }

    private fun bindStyle(style: PreferenceItemStyle?) {
        when (style) {
            is PreferenceItemStyle.Custom   -> {

            }
            is PreferenceItemStyle.EditArea -> {
                ViewPreferenceEditareaBinding.bind(view!!).apply {
                    this.tvLabel.text = style.label
                    this.etInput.hint = style.hint
                    this.etInput.setText(style.text)
                }
            }
            is PreferenceItemStyle.Icon     -> {
                ViewPreferenceIconBinding.bind(view!!).apply {
                    this.tvLabel.text = style.label
                    this.ivNavigate.visible(style.navigatiable)
                    this.vDivider.visible(style.showDivider)

                    Glide.with(this@PreferenceItemView)
                        .load(style.url)
                        .into(this.ivIcon)
                }
            }
            is PreferenceItemStyle.Text     -> {
                ViewPreferenceTextBinding.bind(view!!).apply {
                    this.tvLabel.text = style.label
                    this.tvText.text = style.text
                    this.labelRedDot.invisible(!style.showLabelRedDot)
                    this.ivNavigate.visible(style.navigatiable)
                    this.vDivider.visible(style.showDivider)
                }
            }
            is PreferenceItemStyle.Toggle   -> {
                ViewPreferenceToggleBinding.bind(view!!).apply {
                    this.tvLabel.text = style.label
                    this.mscSwitch.isChecked = style.toggled
                    this.vDivider.visible(style.showDivider)
                    this.mscSwitch.setOnCheckedChangeListener(style.toggleListener)
                }
            }
            else                            -> {

            }
        }
    }

    inline fun <reified T> updateStyleParams(configBlock: T.() -> Unit) {
        if (this.getStyle() is T) {
            configBlock(this.getStyle() as T)
            setStyle(this.getStyle())
        }
    }
}


sealed interface PreferenceItemStyle {

    sealed class Navigation(
        var navigatiable: Boolean,
        var showDivider: Boolean
    ) : PreferenceItemStyle

    class Icon(
        var label: String,
        var url: String,
        navigatiable: Boolean = true,
        showDivider: Boolean = true
    ) : Navigation(navigatiable, showDivider)

    class Text(
        var label: String,
        var text: String? = null,
        navigatiable: Boolean = true,
        showDivider: Boolean = true,
        var showLabelRedDot:Boolean = false
    ) : Navigation(navigatiable, showDivider)

    class Toggle(
        var label: String,
        var toggled: Boolean,
        var showDivider: Boolean = true,
        var toggleListener: CompoundButton.OnCheckedChangeListener? = null
    ) : PreferenceItemStyle

    class EditArea(
        var label: String,
        var text: String,
        var hint: String
    ) : PreferenceItemStyle

    class Custom(var view: View) : PreferenceItemStyle
}

enum class PreferenceItemStyleEnum(val value: Int) {
    Custom(0), Text(1), Toggle(2), Icon(3), EditArea(4);

    companion object{
        fun create(value: Int): PreferenceItemStyleEnum? {
            return PreferenceItemStyleEnum.values().find { it.value == value }
        }
    }
}

