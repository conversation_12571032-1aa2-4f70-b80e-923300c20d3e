package com.socialplay.gpark.ui.im.groupchat

import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseTabParentFragment

class MyGroupsParentFragment : BaseTabParentFragment() {
    private val args by navArgs<MyGroupsParentFragmentArgs>()
    override val tabItems: List<CommonTabItem> by lazy {
        listOf(
            CommonTabItem(
                R.string.my_group_tab_title_i_created,
                MyGroupChatListFragment.TYPE_I_CREATED.toString()
            ) {
                MyGroupChatListFragment.newInstance(
                    GroupChatListFragmentArgs(
                        MyGroupChatListFragment.TYPE_I_CREATED,
                        args.targetUid
                    )
                )
            },
            CommonTabItem(
                R.string.my_group_tab_title_i_joined,
                MyGroupChatListFragment.TYPE_I_JOINED.toString()
            ) {
                MyGroupChatListFragment.newInstance(
                    GroupChatListFragmentArgs(
                        MyGroupChatListFragment.TYPE_I_JOINED,
                        args.targetUid
                    )
                )
            }
        )
    }
    override val destId: Int = R.id.myGroupsPage

    override fun getPageName(): String = PageNameConstants.FRAGMENT_MY_GROUPS_PARENT
}