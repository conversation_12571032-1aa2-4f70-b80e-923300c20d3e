package com.socialplay.gpark.ui.editor.home.datalist

import android.content.ComponentCallbacks
import androidx.lifecycle.Observer
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.data.model.aibot.BotLabelInfo.Companion.TAG_ALL
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.editor.GroupedData
import com.socialplay.gpark.data.model.user.RedBadgeData
import com.socialplay.gpark.data.model.editor.GroupedData.Companion.CHOICE_CARD_TYPE_AI_BOT
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.usecase.GetDailyTaskRewardStatusUseCase
import com.socialplay.gpark.util.extension.collectIn
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber
import java.io.IOException


data class EditorHomeDataViewModelState(
    val refresh: Async<List<GroupedData<*>>> = Uninitialized,
    val list: List<GroupedData<*>> = emptyList(),
    val flyWheelData: Async<List<UniJumpConfig>> = Uninitialized,
    val bannerListData: Async<List<UniJumpConfig>> = Uninitialized,
    val pageNum: Int = 1,
    val tagId: Int? = 0,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val gender: Int? = null,
    val oldTagList:List<BotLabelInfo> = emptyList(),
    val mergedRefreshStatus: Async<List<*>> = Uninitialized,
) : MavericksState {

}

class EditorHomeDataViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    private val getDailyTaskRewardStatusUseCase: GetDailyTaskRewardStatusUseCase,
    initialState: EditorHomeDataViewModelState
) : BaseViewModel<EditorHomeDataViewModelState>(initialState) {

    private var redBadgeObserver: Observer<RedBadgeData?>? = null
    private val _dailyTaskTipsShowFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)

    companion object :
        KoinViewModelFactory<EditorHomeDataViewModel, EditorHomeDataViewModelState>() {
        const val GENDER_ALL = 0
        const val GENDER_FEMALE = 2 //女
        const val GENDER_MALE = 1 // 男
        const val GENDER_NON =-1
        const val PAGE_SIZE = 50

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: EditorHomeDataViewModelState
        ): EditorHomeDataViewModel {
            return EditorHomeDataViewModel(get(), get(), get(), state)
        }
    }

    init {
        initObserver()
        refresh(true)
        refreshDailyTaskTipsStatus()
    }

    private fun initObserver() {
        if (PandoraToggle.roleFlyWheel) {
            redBadgeObserver = Observer<RedBadgeData?> { badge ->
                updateFlyWheel(badge)
            }.apply {
                accountInteractor.badgeLiveData.observeForever(this)
            }

            viewModelScope.launch {
                _dailyTaskTipsShowFlow.collectLatest {
                    updateFlyWheel(accountInteractor.badgeLiveData.value)
                }
            }
        }

        onEach(
            EditorHomeDataViewModelState::refresh,
            EditorHomeDataViewModelState::flyWheelData
        ) { listData, flyWheelData ->

            val existsData = !(flyWheelData.invoke() ?: listData.invoke()).isNullOrEmpty()
            val data = if (existsData) listOf(Unit) else emptyList()

            val status: Async<List<*>> = when {
                flyWheelData is Loading || listData is Loading -> {
                    Loading(data)
                }

                flyWheelData is Fail && listData is Fail -> {
                    Fail(flyWheelData.error)
                }

                flyWheelData is Success && listData is Success -> {
                    Success(data)
                }

                flyWheelData is Success -> {
                    Success(data)
                }

                listData is Success -> {
                    Success(data)
                }

                else -> {
                    Uninitialized
                }
            }
            setState { copy(mergedRefreshStatus = status) }
        }
    }

    private fun updateFlyWheel(badge: RedBadgeData?) {
        val (list, changed) = buildFlyWheelWithBadge(badge, oldState.flyWheelData)
        if (changed) {
            setState {
                copy(flyWheelData = list)
            }
        }

        onEach(
            EditorHomeDataViewModelState::refresh,
            EditorHomeDataViewModelState::flyWheelData
        ) { listData, flyWheelData ->

            val existsData = !(flyWheelData.invoke() ?: listData.invoke()).isNullOrEmpty()
            val data = if (existsData) listOf(Unit) else emptyList()

            val status: Async<List<*>> = when {
                flyWheelData is Loading || listData is Loading -> {
                    Loading(data)
                }

                flyWheelData is Fail && listData is Fail -> {
                    Fail(flyWheelData.error)
                }

                flyWheelData is Success && listData is Success -> {
                    Success(data)
                }

                flyWheelData is Success -> {
                    Success(data)
                }

                listData is Success -> {
                    Success(data)
                }

                else -> {
                    Uninitialized
                }
            }
            setState { copy(mergedRefreshStatus = status) }
        }
    }

    fun refresh(isRefresh: Boolean, tagId: Int? = null, gender: Int? = null) = withState { oldState ->
        val groupedDataFlow = repository.getChoiceCardListForAvatar().map {
            it.data
        }.combine(repository.getEditorHomeDataList()) { choiceCardList, homeDataList ->
            choiceCardList to homeDataList
        }.combine(repository.getChoiceAiBotCardList(tagId,
            gender,1, PAGE_SIZE)) { first, sencond ->
            val homeDataList = first.second
            val choiceCardList = first.first?.dataList ?: emptyList()
            val aiBotList = sencond.data?.dataList ?: emptyList()
            val newTagList = if (aiBotList.isNullOrEmpty()) {
                oldState.list.find { it.type == CHOICE_CARD_TYPE_AI_BOT }?.tag
            } else {
                aiBotList[0].aiBotList?.title?.label ?: oldState.list.find { it.type == CHOICE_CARD_TYPE_AI_BOT }?.tag
            }
            homeDataList + choiceCardList.filter { ChoiceCardType.isUgcCreate(it.cardType) }.map {
                GroupedData(
                    id = it.cardId.toString(),
                    title = it.cardName,
                    items = it.gameList,
                    type = it.cardType
                )
            } + aiBotList.map {
                GroupedData(
                    id = it.cardId.toString(),
                    title = it.cardName,
                    items = it.aiBotList?.dataList?: emptyList(),
                    type = CHOICE_CARD_TYPE_AI_BOT,
                    tag= newTagList?: emptyList<BotLabelInfo>()
                )
            }

        }
        groupedDataFlow.execute(retainValue = EditorHomeDataViewModelState::refresh) { result ->
            when (result) {
                is Success -> {
                    val aiList = result.invoke().find { it.type== CHOICE_CARD_TYPE_AI_BOT }?.items
                    if (isRefresh) {
                        copy(
                            refresh = result,
                            list = result.invoke(),
                            pageNum = 1,
                            tagId = tagId ?: TAG_ALL,
                            gender = gender ?: GENDER_ALL,
                            loadMore = result.map { LoadMoreState((aiList?.size?:0) < PAGE_SIZE) }
                        )
                    } else {
                        copy(
                            list = result.invoke(),
                            pageNum = 1,
                            tagId = tagId ?: TAG_ALL,
                            gender = gender ?: GENDER_ALL,
                            loadMore = result.map { LoadMoreState((aiList?.size?:0)  < PAGE_SIZE) }
                        )
                    }
                }

                is Loading -> {
                    if (isRefresh) {
                        copy(refresh = result)
                    } else {
                        this
                    }
                }

                else -> {
                    copy(refresh = result)
                }
            }
        }

        if (PandoraToggle.roleFlyWheel) {
            repository.getRoleFlyWheel().map {
                if (it.succeeded) {
                    it.data ?: emptyList()
                } else {
                    throw it.exception ?: IOException(it.message)
                }
            }.execute {
                if (it is Success) {
                    copy(flyWheelData = buildFlyWheelWithBadge(accountInteractor.badgeLiveData.value, it).first)
                } else {
                    this
                }
            }
        }

        repository.getRoleBannerList().execute {
            if (it is Success) {
                copy(bannerListData = it)
            } else {
                this
            }
        }
    }

    /**
     * @return 列表，是否改变信息
     */
    private fun buildFlyWheelWithBadge(
        badge: RedBadgeData?,
        asyncFlyWheelList: Async<List<UniJumpConfig>>
    ): Pair<Async<List<UniJumpConfig>>, Boolean> {
        val flyWheelList = asyncFlyWheelList()
        if (flyWheelList.isNullOrEmpty()) return asyncFlyWheelList to false
        var changed = false
        val ocShortsShowBadge = badge?.newTemplate?.hasNew == true
        return asyncFlyWheelList.copyEx(
            flyWheelList.map {
                if (it.jumpType == UniJumpConfig.JUMP_TYPE_OC_SHORTS && it.localShowRedBadge != ocShortsShowBadge) {
                    changed = true
                    it.copy(
                        localShowRedBadge = ocShortsShowBadge
                    )
                } else if (it.jumpType == UniJumpConfig.POS_DAILY_TASK_REWARD && it.localShowRedBadge != _dailyTaskTipsShowFlow.value){
                    changed = true
                    it.copy(localShowRedBadge = _dailyTaskTipsShowFlow.value)
                } else it
            }
        ) to changed
    }

    fun loadMore() = withState { oldState ->
        if (oldState.refresh is Loading || oldState.loadMore is Loading) return@withState
        repository.getChoiceAiBotCardList(oldState.tagId, oldState.gender, oldState.pageNum + 1, PAGE_SIZE).execute {data->
            if (data is Success) {
                val newList = data.invoke().data?.dataList ?: emptyList()

                val result = newList.map {
                    GroupedData(
                        id = null,
                        title = null,
                        items = it.aiBotList?.dataList,
                        type = CHOICE_CARD_TYPE_AI_BOT,
                        tag = oldState.list.find { it.type == CHOICE_CARD_TYPE_AI_BOT }?.tag?: emptyList<BotLabelInfo>()
                    )
                }
                copy(
                    list = oldState.list + result,
                    pageNum = oldState.pageNum + 1,
                    loadMore = data.map { LoadMoreState(newList.isNullOrEmpty() || newList[0].aiBotList?.dataList?.isEmpty() == true) })
            } else {
                this
            }
        }
    }

    fun recordTemplateClickedEvent(templateId: String) {
        repository.plotTemplateLoveDo(templateId).collectIn(viewModelScope) {}
    }

    fun setGender(gender: Int?) = withState { oldState ->
        if (gender == null || gender == GENDER_ALL) {
            refresh(false,oldState.tagId, GENDER_ALL)
        } else {
            refresh(false, oldState.tagId, gender)
        }
    }
    fun setTag(tagId: Int?) = withState { oldState ->
        refresh(false, tagId, oldState.gender)
    }

    fun refreshDailyTaskTipsStatus() {
        viewModelScope.launch {
            val result = kotlin.runCatching {
                getDailyTaskRewardStatusUseCase().invoke()
            }.getOrElse {
                Timber.e(it, "refreshDailyTaskTipsStatus error")
                false
            }
            _dailyTaskTipsShowFlow.update {
                result
            }
        }
    }

    override fun onCleared() {
        redBadgeObserver?.let { accountInteractor.badgeLiveData.removeObserver(it) }
        super.onCleared()
    }
}
