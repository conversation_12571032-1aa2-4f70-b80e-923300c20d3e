package com.socialplay.gpark.ui.im

import android.content.ComponentCallbacks
import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bin.cpbus.CpEventBus
import com.ly123.tes.mgs.metacloud.IConversationListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.ImMessageEvent
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.meta.box.biz.friend.FriendBiz
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.interactor.OfflineInteractor
import com.socialplay.gpark.data.interactor.SystemNoticeInteractor
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.aibot.AiBotConversation
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.im.AbsConversationMessage
import com.socialplay.gpark.data.model.im.ConversationMessage
import com.socialplay.gpark.data.model.im.GroupConversationMessage
import com.socialplay.gpark.data.model.im.IConversationMessage
import com.socialplay.gpark.data.model.im.ImUpdate
import com.socialplay.gpark.data.model.im.MetaSimpleUserEntity
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.aibot.AiBotConversationItem
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.SingleReplyMutableSharedFlow
import com.socialplay.gpark.util.extension.isOnline
import com.socialplay.gpark.util.toLongOrZero
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.coroutines.resume

/**
 * @author: ning.wang
 * @date: 2021-06-21 5:12 下午
 * @desc:
 */
data class ConversationListState(
    val aiConversationList: List<AiBotConversation> = emptyList(),
    val pageNum: Int = 0,
    val count: Int = 0,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val needShowNotification: Boolean = false,
) : MavericksState {

}

class ConversationListViewModel(
    initialState: ConversationListState,
    private val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    private val imInteractor: ImInteractor,
    val accountInteractor: AccountInteractor,
    val friendInteractor: FriendInteractor,
    val editorInteractor: EditorInteractor,
    val noticeInteractor: SystemNoticeInteractor
) : BaseViewModel<ConversationListState>(initialState) {


    companion object : KoinViewModelFactory<ConversationListViewModel, ConversationListState>() {
        val pageSize = 10
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ConversationListState
        ): ConversationListViewModel {
            return ConversationListViewModel(state, get(), get(), get(), get(), get(), get())
        }
    }

    private val _haveCreateGroupPower: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val haveCreateGroupPower: StateFlow<Boolean> = _haveCreateGroupPower

    private val _groupChatCount: MutableStateFlow<GroupChatCount?> = MutableStateFlow(null)
    val groupChatCount: StateFlow<GroupChatCount?> = _groupChatCount

    val showCreateGroupChatRedDot: Flow<Boolean> =
        _haveCreateGroupPower.combine(_groupChatCount) { haveCreateGroupPower, groupChatCount ->
            haveCreateGroupPower && (groupChatCount?.createCount ?: 0) <= 0
        }

    private val _groupRequestUnreadCountFlow: MutableStateFlow<Int> = MutableStateFlow(0)

    private val _conversationList =
        SingleReplyMutableSharedFlow<List<MetaConversation>>(emptyList())
    val conversationList: Flow<List<MetaConversation>> = _conversationList

    private val _conversationChangedEvent: MutableLiveData<Any> = MutableLiveData()
    val conversationChangedEvent: LiveData<Any> = _conversationChangedEvent

    private val _conversationMessageList = MutableStateFlow<List<AbsConversationMessage>>(emptyList())
    val conversationMessageList: StateFlow<List<AbsConversationMessage>> = _conversationMessageList

    val friendRequestUnreadCountFlow = friendInteractor.friendRequestUnreadCount.asFlow()

    val friendAndGroupRequestUnreadCountFlow =
        friendRequestUnreadCountFlow.combine(_groupRequestUnreadCountFlow) { friend, group ->
            friend + group
        }

    // 运营位通知未读数
    val operationNoticeUnreadNumFlow: Flow<Int> = noticeInteractor.noticeUnreadNumFlow

    // 移动编辑器通知未读数
    val editorNoticeUnreadNumFlow: Flow<Int> = editorInteractor.noticeNumLiveData.asFlow()

    // 运营位通知最近一条消息
    val latestOperationNoticeFlow: StateFlow<EditorNotice.OuterShowNotice> =
        noticeInteractor.latestSystemNoticeFlow.map {
            EditorNotice.OuterShowNotice(
                it?.title,
                it?.effectiveTimeBegin.toLongOrZero,
                EditorNotice.OuterShowNotice.TYPE_OPERATION,
                if (it == null) false else !it.isRead
            )
        }.stateIn(
            viewModelScope,
            SharingStarted.Lazily,
            EditorNotice.OuterShowNotice(type = EditorNotice.OuterShowNotice.TYPE_OPERATION)
        )

    // 移动编辑器通知最近一条消息
    val latestEditorNoticeFlow: StateFlow<EditorNotice.OuterShowNotice> =
        editorInteractor.newestNoticeLiveData.asFlow().stateIn(
            viewModelScope,
            SharingStarted.Lazily,
            EditorNotice.OuterShowNotice(type = EditorNotice.OuterShowNotice.TYPE_EDITOR)
        )

    private val _sysHeaderList = MutableStateFlow<List<SysHeaderInfo>>(emptyList())
    val sysHeaderList: StateFlow<List<SysHeaderInfo>> = _sysHeaderList

    private val conversationListener = object : IConversationListener {

        override fun onConversationChanged(conversationList: MutableList<MetaConversation>) {
            _conversationChangedEvent.postValue(Unit)
        }

        override fun onNewConversation(conversationList: MutableList<MetaConversation>) {
            _conversationChangedEvent.postValue(Unit)
        }
    }

    var curFilter = SysListAdapter.ConversationFilter.ALL

    init {
        CpEventBus.register(this)

        MetaCloud.registerConversationListener(conversationListener)

        viewModelScope.launch {
            _conversationList.collect {
                val friendList = friendInteractor.friendList.value ?: return@collect
                combineFriendConversationAndEmit(it)
            }
        }

        viewModelScope.launch {
            friendInteractor.friendList.asFlow().collect {
                combineFriendConversationAndEmit(_conversationList.value)
            }
        }

        //运营消息卡片
        if (PandoraToggle.controlSystemMessage) {
            viewModelScope.launch {
                latestOperationNoticeFlow.collect {
                    combineFriendConversationAndEmit(_conversationList.value)
                }
            }

            viewModelScope.launch {
                operationNoticeUnreadNumFlow.collect {
                    combineFriendConversationAndEmit(_conversationList.value)
                }
            }
        }

        //编辑器消息
        if (editorInteractor.hasEditorExploreFormalTab) {
            viewModelScope.launch {
                latestEditorNoticeFlow.collect {
                    combineFriendConversationAndEmit(_conversationList.value)
                }
            }

            viewModelScope.launch {
                editorNoticeUnreadNumFlow.collect {
                    combineFriendConversationAndEmit(_conversationList.value)
                }
            }
        }

        viewModelScope.launch {
            editorInteractor.postNoticeLiveData.asFlow().collect {
                combineFriendConversationAndEmit(_conversationList.value)
            }
        }
    }

    fun loadSys() = viewModelScope.launch {
        metaRepository.getSysHeaderInfo().collect {
            if (it.succeeded && it.data != null) {
                if (curFilter == SysListAdapter.ConversationFilter.ALL || curFilter == SysListAdapter.ConversationFilter.GAME) {
                    _sysHeaderList.value = it.data!!
                    val friendList = friendInteractor.friendList.value ?: emptyList()
                    if (PandoraToggle.isNewMessage) {
                        combineFriendConversationAndEmit(_conversationList.value)
                    }
                } else {
                    _sysHeaderList.value = it.data!!.filter { it.type != 1L }
                }
            }
        }

    }

    /**
     * 检查创建群的权限
     */
    fun checkCreateGroupPower() {
        viewModelScope.launch {
            metaRepository.checkCreateGroupPower().collect { haveCreateGroupPower ->
                _haveCreateGroupPower.emit(haveCreateGroupPower)
            }
        }
    }

    fun getGroupChatCount() {
        viewModelScope.launch {
            metaRepository.getGroupChatCount().collect { haveCreateGroupPower ->
                if (haveCreateGroupPower.succeeded && haveCreateGroupPower.data != null) {
                    _groupChatCount.emit(haveCreateGroupPower.data)
                }
            }
        }
    }

    fun getGroupPendingRequestCount() {
        viewModelScope.launch {
            val result = metaRepository.getGroupChatPendingRequestCount()
            if (result.succeeded && result.data != null) {
                _groupRequestUnreadCountFlow.value = result.data!!
            }
        }
    }

    private suspend fun combineFriendConversationAndEmit(
        conversations: List<MetaConversation>
    ) = withContext(Dispatchers.IO) {
        val isOfficial = accountInteractor.isOfficial
        val conversationList: MutableList<AbsConversationMessage> = conversations.mapNotNull {
            if (it.conversationType == Conversation.ConversationType.PRIVATE) {
                if (it.targetId == OfflineInteractor.OFFLINE_MANAGER) {
                    null
                } else {
                    val friendInfo = FriendBiz.getFriendByUuid(it.targetId)
                    val userInfo = if (friendInfo == null) {
                        val temp = getSimpleUserInfo(it.targetId)
                        if (temp != null && (isOfficial || temp.isOfficial)) {
                            temp.toFriendInfo()
                        } else {
                            null
                        }
                    } else {
                        friendInfo
                    }
                    if (userInfo == null) null else ConversationMessage(userInfo, it)
                }
            } else {
                GroupConversationMessage(it)
            }
        }.toMutableList()
        if (PandoraToggle.isNewMessage) {
            _sysHeaderList.value?.forEach {
                if (it.topping == false) {
                    conversationList.add(AbsConversationMessage.SysConversation(it))
                }
            }
        }

        val sortedList = conversationList
            .asSequence()
            .map {
                it to getSortWeight(it)
            }
            .sortedByDescending { it.second }
            .onEach { Timber.d("Element:(name=${it.first},weight=${it.second})") }
            .map { it.first }
            .toMutableList()

        when (curFilter) {
            SysListAdapter.ConversationFilter.ALL -> {
                _conversationMessageList.emit(sortedList)
            }

            SysListAdapter.ConversationFilter.FRIEND -> {
                _conversationMessageList.emit(sortedList.filterIsInstance<ConversationMessage>())
            }

            else -> {
                _conversationMessageList.emit(sortedList.filterIsInstance<AbsConversationMessage.SysConversation>())
            }
        }
    }

    private fun getSortWeight(conversation: AbsConversationMessage): Double {
        var weight = 0.0

        if (conversation is IConversationMessage) {
            val con = conversation.conversation
            if (con != null) {
                if (con.isTop == true) {
                    weight += 10
                }

                val lasMessageTime: Long = ((con.receivedTime ?: 0)
                    .coerceAtLeast(con.sentTime ?: 0))
                    .div(1000)
                    .coerceAtLeast(1L)

                weight += 1 - (1.0 / lasMessageTime)
            }
            if (conversation is ConversationMessage && conversation.friendInfo.isOnline()) {
                weight += 5
            }
        } else if (conversation is AbsConversationMessage.NoticeConversation) {
            weight += 5 //系统消息总是在线，+5权重

            if (conversation.notice.hasUnread) {//系统消息如果未读，则权重再+1
                weight += 1
            }

            val lasMessageTime = conversation.notice.sendTime.div(1000).coerceAtLeast(1L)
            weight += 1 - (1.0 / lasMessageTime)
        } else if (conversation is AbsConversationMessage.SysConversation) {

            val lasMessageTime = conversation.info.lastModifyTime.div(1000).coerceAtLeast(1L)
            weight += 1 - (1.0 / lasMessageTime)
        }
        return weight
    }

    private suspend fun getSimpleUserInfo(uuid: String?): MetaSimpleUserEntity? {
        if (uuid.isNullOrEmpty()) return null
        return imInteractor.getSimpleUserInfo(uuid)
    }

    fun setConversationToTop(metaConversation: MetaConversation) = viewModelScope.launch {
        val isTop = metaConversation.isTop ?: false
        imInteractor.setConversationToTop(
            metaConversation.conversationType,
            metaConversation.targetId,
            !isTop
        ) {
            refreshConversationList()
        }
    }

    fun removeConversation(metaConversation: MetaConversation) = viewModelScope.launch {
        imInteractor.removeConversation(
            metaConversation.conversationType,
            metaConversation.targetId
        ) {
            refreshConversationList()
        }
    }

    fun getUnReadCount() = viewModelScope.launch {
        imInteractor.getUnReadCount()
    }

    fun getEditorNoticeUnreadCount() {
        if (editorInteractor.hasEditorExploreFormalTab) {
            editorInteractor.getUnreadNoticeCount()
            editorInteractor.fetchNewestNotice()
        }
    }

    fun getPostUnread() = editorInteractor.getPostUnread()

    fun refreshFriendsUnreadRequests() {
        friendInteractor.refreshFriendsUnreadRequestsAsync()
    }

    fun refreshConversationList() = viewModelScope.launch {
        _conversationList.emit(metaRepository.fetchAllConversationList())
    }

    fun refreshFriends() = viewModelScope.launch {
        friendInteractor.refreshFriends()
    }

    fun getNotice() = viewModelScope.launch {
        noticeInteractor.loadLatestOperationNotice()
    }

    fun getAiBotConversationList(isRefresh: Boolean) = withState { oldState ->
        if (!PandoraToggle.isOpenAiBot) {
            return@withState
        }
        var pageNum = oldState.pageNum + 1
        if (isRefresh) {
            pageNum = 1
            setState { copy(pageNum = 1) }
        }
        metaRepository.getAiBotConversionList(
            mapOf(
                "pageSize" to pageSize.toString(),
                "pageNum" to (pageNum).toString()
            )
        ).execute {
            when (it) {
                is Success -> {
                    val result = it.invoke()?.data
                    val list = if (isRefresh) (result?.dataList
                        ?: emptyList()) else oldState.aiConversationList + (result?.dataList
                        ?: emptyList())
                    copy(
                        aiConversationList = list,
                        pageNum = pageNum,
                        count = list.size,
                        loadMore = it.map {
                            LoadMoreState(
                                (result?.dataList?.size ?: 0) < pageSize
                            )
                        })
                }

                is Fail -> {
                    copy(
                        aiConversationList = oldState.aiConversationList,
                        count = oldState.aiConversationList.size,
                        loadMore = it.map { LoadMoreState(true) })

                }

                else -> {
                    this
                }

            }

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEvent(messageEvent: ImMessageEvent) {
        refreshConversationList()
        getUnReadCount()
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEventMainThread(message: Message) {
        refreshConversationList()
        getUnReadCount()
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEventMainThread(imUpdate: ImUpdate) {
        Timber.d("event sticky : ${imUpdate.targetId}, ${imUpdate.updateType}, ${imUpdate.value}")
        refreshConversationList()
        getUnReadCount()
    }

    override fun onCleared() {
        CpEventBus.unregister(this)
        MetaCloud.unregisterConversationListener(conversationListener)
        super.onCleared()
    }

    fun deleteAiConversation(modelBeingMoved: AiBotConversationItem) = withState {
        oldState
        metaRepository.deleteAiBotConversion(
            modelBeingMoved.aiMessageEntity.botId,
            accountInteractor.curUuid
        ).execute {
            if (it is Success) {
                val oldList = ArrayList<AiBotConversation>(oldState.aiConversationList)
                val item = oldList.find { it.botId == modelBeingMoved.aiMessageEntity.botId }
                oldList.remove(item)
                copy(aiConversationList = oldList.toList(), count = oldList.size)
            } else {
                this
            }
        }

    }

    fun checkNotificationPermission(context: Context, isGoSet: Boolean) = withState {
        oldState
        if (PandoraToggle.isChatPushNotification) {
            val enabled = NotificationManagerCompat.from(context).areNotificationsEnabled()
            if (isGoSet) {
                Analytics.track(
                    EventConstants.EVENT_FIRST_PUSH_POST_CLICK,
                    map = mapOf("result" to if (enabled) "0" else "1")
                )
            }
            if (!enabled && NotificationPermissionManager.notificationShow
            ) {
                //没有开启权限，并且没有关闭提示
                setState { copy(needShowNotification = true) }
            } else {
                setState { copy(needShowNotification = false) }
            }
            Timber.d(
                "checkNotification" + (!NotificationManagerCompat.from(context)
                    .areNotificationsEnabled() && NotificationPermissionManager.notificationShow)
            )
        }
    }

    fun updateNotificationStatus(isShow: Boolean) = withState {
        oldState
        NotificationPermissionManager.notificationShow = isShow
        setState { copy(needShowNotification = isShow) }
    }

    suspend fun clearAllUnread(): Boolean {
        val imCleared = suspendCancellableCoroutine { c ->
            MetaCloud.cleanConversationUnreadMessageCount {
                c.resume(it)
            }
        }
        val isSuccess = imCleared && kotlin.runCatching {
            GlobalContext.get().get<MetaApi>().postClearRed().data == true
        }.getOrNull() ?: false
        loadSys()
        return isSuccess
    }

    fun setCurrentFilter(index: Int) {
        curFilter = SysListAdapter.ConversationFilter.entries[index]
        loadSys()
        refreshConversationList()
    }
}