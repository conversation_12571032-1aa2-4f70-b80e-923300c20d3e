package com.socialplay.gpark.ui.friend.contacts

import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.animation.doOnEnd
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.PagingData
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.meta.box.biz.friend.model.FriendInfo
import com.meta.lib.api.resolve.data.model.failed
import com.meta.lib.api.resolve.data.model.loading
import com.meta.lib.api.resolve.data.model.succeeded
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.databinding.FragmentContactListBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.im.conversation.GroupChatState
import com.socialplay.gpark.ui.im.conversation.GroupChatViewModel
import com.socialplay.gpark.ui.im.groupchat.GroupChatConfig
import com.socialplay.gpark.ui.im.groupchat.GroupChatMembersModelState
import com.socialplay.gpark.ui.im.groupchat.GroupChatMembersViewModel
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.displayName
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.friendListIndexChar
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/21
 *     desc   : 好友列表
 */
class ContactListFragment : BaseFragment<FragmentContactListBinding>(R.layout.fragment_contact_list) {

    private val vm: ContactListViewModel by viewModel()
    private val groupChatViewModel: GroupChatViewModel by fragmentViewModel()
    private val groupMembersViewModel: GroupChatMembersViewModel by fragmentViewModel()
    private val args: ContactListFragmentArgs by navArgs<ContactListFragmentArgs>()

    private lateinit var friendListAdapter: ContactListAdapter
    private var isShare = false
    private var selectedUuid: String? = null
    private var isSelectFriends: Boolean = false
    override val destId: Int = R.id.contact_list_fragment
    private var maxSelectCount = GroupChatConfig.MAX_GROUP_MEMBERS_INVITE_COUNT

    companion object {
        const val SHARE_CALLBACK: String = "SHARE_CALLBACK"
        const val KEY_SELECT_UUID: String = "select_uuid"
        const val KEY_REQUEST_INVITE_MEMBERS: String = "key_request_invite_members"
        const val KEY_RESULT_INVITE_MEMBERS_SUCCESS: String = "key_result_invite_members_success"
    }

    private val backPressCallback by lazy {
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                goBack()
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentContactListBinding? {
        return FragmentContactListBinding.inflate(inflater, container, false)
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_NAME_FRIEND_LIST

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isSelectFriends = args.isCreateGroup || args.isInviteGroupMembers
        if (args.maxSelectCount >= 0) {
            maxSelectCount = args.maxSelectCount
        }
        initViews()
        initListeners()
        initObservers()
    }

    private fun initViews() {
        friendListAdapter = ContactListAdapter(
            isSelectFriends,
            maxCheckCount = maxSelectCount,
            Glide.with(requireContext()),
            enableLabel = !isSelectFriends,
            isGroupMember = { uuid ->
                groupMembersViewModel.getMemberInfo(uuid) != null
            },
        ) {
            UserLabelView.showDescDialog(this, it)
        }

        val concatAdapter = ConcatAdapter(friendListAdapter)
        val linearLayoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)

        friendListAdapter.withStatusAndRefresh(viewLifecycleOwner, null, binding.slRefreshLayout) {
            binding.vEmptyLayout.visible(it)
        }

        binding.rvFriendList.layoutManager = linearLayoutManager
        binding.rvFriendList.adapter = concatAdapter

        binding.aivIndexBar.bind(binding.rvFriendList, friendListAdapter) {
            val displayName = friendListAdapter.peek(it)?.displayName ?: ""
            val c = displayName.friendListIndexChar('#')
            if (c in 'A'..'Z') {
                c
            } else {
                '#'
            }
        }
        if (isSelectFriends) {
            binding.confirmLayout.visible(args.isCreateGroup)
            binding.spaceConfirmLayout.visible(args.isCreateGroup)
            updateCheckFriendsCount(0, maxSelectCount)
            binding.loadingBtn.setOnAntiViolenceClickListener {
                if (binding.loadingBtn.isLoading) {
                    return@setOnAntiViolenceClickListener
                }
                if (args.isCreateGroup) {
                    groupChatViewModel.createGroupChat(friendListAdapter.checkedFriends.keys.toList())
                } else if (args.isInviteGroupMembers && args.groupId != -1L) {
                    groupChatViewModel.inviteMembers(
                        args.groupId,
                        friendListAdapter.checkedFriends
                    )
                }
            }
            groupChatViewModel.onAsync(
                GroupChatState::createGroupChatResult,
                deliveryMode = uniqueOnly("$destId"),
                onFail = { _, result ->
                    val membersCount = result?.first
                    if (membersCount != null) {
                        Analytics.track(
                            EventConstants.GROUP_CREATE_CONFIRM_CLICK,
                            "result" to "2",
                            "members" to membersCount
                        )
                    }
                    binding.loadingBtn.showButton()
                    binding.slRefreshLayout.enableAllWithAlpha(true, 1f)
                    toast(R.string.toast_create_group_chat_failed)
                },
                onLoading = {
                    binding.loadingBtn.showLoading()
                    binding.slRefreshLayout.enableAllWithAlpha(false, 0.5f)
                }
            ) { result ->
                val membersCount = result.first
                val dataResult = result.second
                binding.loadingBtn.showButton()
                binding.slRefreshLayout.enableAllWithAlpha(true, 1f)
                if (dataResult.succeeded && dataResult.data != null) {
                    Analytics.track(
                        EventConstants.GROUP_CREATE_CONFIRM_CLICK,
                        "result" to "1",
                        "members" to membersCount
                    )
                    val groupDetail = dataResult.data!!
                    val groupId = groupDetail.id ?: return@onAsync
                    MetaRouter.IM.goGroupChat(
                        this@ContactListFragment,
                        groupChatId = groupId.toString(),
                        groupName = groupDetail.name ?: "",
                        navOptions = MetaRouter.Control.getCommonNavOptionsBuilder().apply {
                            setPopUpTo(destId, true)
                        }.build()
                    )
                } else {
                    Analytics.track(
                        EventConstants.GROUP_CREATE_CONFIRM_CLICK,
                        "result" to "2",
                        "members" to membersCount
                    )
                    toast(dataResult.message ?: getString(R.string.toast_create_group_chat_failed))
                }
            }

            groupChatViewModel.onAsync(
                GroupChatState::inviteMembersResult,
                deliveryMode = uniqueOnly("$destId"),
                onFail = { _, _ ->
                    binding.loadingBtn.showButton()
                    binding.slRefreshLayout.enableAllWithAlpha(true, 1f)
                    toast(R.string.toast_group_invite_members_failed)
                },
                onLoading = {
                    binding.loadingBtn.showLoading()
                    binding.slRefreshLayout.enableAllWithAlpha(false, 0.5f)
                }
            ) { inviteResult ->
                val members = inviteResult.first
                val result = inviteResult.second
                binding.loadingBtn.showButton()
                binding.slRefreshLayout.enableAllWithAlpha(true, 1f)
                if (result.succeeded && result.data != null) {
                    val data = result.data!!
                    val failedInviteMembers = mutableListOf<String>()
                    if (!data.failExistMember.isNullOrEmpty()) {
                        failedInviteMembers.addAll(data.failExistMember)
                    }
                    if (!data.failMaxGroupMember.isNullOrEmpty()) {
                        failedInviteMembers.addAll(data.failMaxGroupMember)
                    }
                    if (!data.failMaxJoinLimit.isNullOrEmpty()) {
                        failedInviteMembers.addAll(data.failMaxJoinLimit)
                    }
                    val failedInviteMembersName = failedInviteMembers.mapNotNull { memberId ->
                        members[memberId]?.name
                    }
                    if (failedInviteMembersName.isNotEmpty()) {
                        // 邀请加群失败一共有3种原因失败:
                        // 1. 群成员已存在;
                        // 2. 群成员已达上限;
                        // 3. 被邀请的人加群数量达到上限;
                        // 1 和 2 已在客户端做了判断拦截, 只有极端情况会出现
                        // 所以只提醒 3 的错误类型即可, 错误类型和提醒在极端情况下对不上没有关系(已和产品沟通过了)
                        val toastString = String.format(
                            getString(R.string.toast_invite_members_failed_join_group_count_maximum_limit),
                            failedInviteMembersName.joinToString(", ")
                        )
                        toast(toastString)
                    }
                    setFragmentResult(
                        KEY_REQUEST_INVITE_MEMBERS,
                        Bundle().apply {
                            putBoolean(KEY_RESULT_INVITE_MEMBERS_SUCCESS, true)
                        }
                    )
                    navigateUp()
                } else {
                    toast(result.message ?: getString(R.string.toast_group_invite_members_failed))
                }
            }

            groupMembersViewModel.onAsync(
                GroupChatMembersModelState::members,
                deliveryMode = uniqueOnly("$destId"),
                onFail = { _, _ ->
                },
                onLoading = {
                }
            ) { members ->
                friendListAdapter.notifyDataSetChanged()
            }
            if (args.isInviteGroupMembers) {
                groupMembersViewModel.getGroupChatMembers(args.groupId)
            }
        }
    }

    private var oldCheckedCount = 0

    private fun updateCheckFriendsCount(checked: Int, max: Int) {
        binding.tvCheckCount.text = SpannableHelper.Builder()
            .text("($checked/")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsSemiBold600)
            .text("$max)")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
            .build()
        if (args.isInviteGroupMembers) {
            // 选中好友后, 确认按钮才会弹出来
            val confirmLayoutHeight = dp(90)
            if (oldCheckedCount == 0 && checked > 0) {
                val animator = ObjectAnimator.ofFloat(0f, 1f)
                animator.setDuration(300)
                var lastProgress = 0f
                animator.addUpdateListener(viewLifecycleOwner,true) { animationValue->
                    val progress = animationValue.animatedValue as Float
                    binding.confirmLayout.translationY = confirmLayoutHeight * (1 - progress)
                    binding.rvFriendList.scrollBy(
                        0,
                        (confirmLayoutHeight * (progress - lastProgress)).toInt()
                    )
                    lastProgress = progress
                }
                animator.start()
                binding.confirmLayout.visible(true)
                binding.spaceConfirmLayout.visible(true)
                binding.spaceConfirmLayout.setHeight(confirmLayoutHeight)
            } else if (oldCheckedCount > 0 && checked == 0) {
                val animator = ObjectAnimator.ofFloat(0f, 1f)
                animator.setDuration(300)
                var lastProgress = 0f
                animator.addUpdateListener(viewLifecycleOwner,true) { animationValue->
                    val progress = animationValue.animatedValue as Float
                    binding.confirmLayout.translationY = confirmLayoutHeight * progress
                    binding.spaceConfirmLayout.setHeight((confirmLayoutHeight * (1 - progress)).toInt())
                    binding.rvFriendList.scrollBy(
                        0,
                        -(confirmLayoutHeight * (progress - lastProgress)).toInt()
                    )
                    lastProgress = progress
                }
                animator.doOnEnd {
                    binding.confirmLayout.visible(false)
                    binding.spaceConfirmLayout.visible(false)
                }
                animator.start()
            }
        }
        oldCheckedCount = checked
    }

    private fun initListeners() {
        friendListAdapter.setOnItemClickListener { _, position ->
            if (isSelectFriends) {
                updateCheckFriendsCount(
                    friendListAdapter.checkedFriends.size,
                    maxSelectCount
                )
            } else {
                toChatting(friendListAdapter.peek(position)!!)
            }
        }
        binding.tblTitleBar.setTitle(args.title ?: "")

        binding.tblTitleBar.setOnBackClickedListener {
            goBack()
        }

        binding.vEmptyLayout.setOnClickListener {
            refreshData()
        }

        binding.slRefreshLayout.setOnRefreshListener {
            refreshData()
        }
    }

    private fun toChatting(friendInfo: FriendInfo) {
        Analytics.track(EventConstants.EVENT_FRIEND_SEND_MESSAGE_CLICK)
        Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 2) }
        val tag = friendInfo.tags?.firstOrNull()
        if (tag?.id == BotInfo.TAG_ID) {
            MetaRouter.AiBot.gotoAiBotDetail(
                this,
                friendInfo.metaNumber ?: "",
                friendInfo.uuid,
                "2"
            )
            return
        }
        if (args.shareContent?.isNotEmpty() == true) {
            selectedUuid = friendInfo.uuid
            MetaRouter.IM.gotoConversation(
                fragment = this,
                otherUid = friendInfo.uuid,
                title = friendInfo.name,
                tagIds = friendInfo.tagIds,
                labelInfo = friendInfo.labelInfo,
                navigatorExtras = null,
                source = null,
                shareContent = args.shareContent,
                shareCallBack = {
                    isShare = it
                    goBack()
                }
            )
        } else {
            MetaRouter.IM.gotoConversation(
                fragment = this,
                otherUid = friendInfo.uuid,
                title = friendInfo.name,
                tagIds = friendInfo.tagIds,
                labelInfo = friendInfo.labelInfo,
                navigatorExtras = null,
                source = null,
                shareContent = null
            )
        }

    }

    private fun initObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            vm.friendListRefreshStatusFlow.collect {
                if (it.loading) {
                    if (!binding.slRefreshLayout.isRefreshing) {
                        binding.slRefreshLayout.isRefreshing = true
                    }
                } else if (it.failed || it.succeeded) {
                    if (binding.slRefreshLayout.isRefreshing) {
                        binding.slRefreshLayout.isRefreshing = false
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            vm.friendListFlow.collect { newData ->
                friendListAdapter.submitData(viewLifecycleOwner.lifecycle, PagingData.from(newData))

                if (newData.isEmpty()) {
                    if (!NetUtil.isNetworkAvailable()) {
                        binding.ivEmptyTipImg.setImageResource(R.drawable.icon_no_network_connection)
                    } else {
                        binding.ivEmptyTipImg.setImageResource(R.drawable.icon_no_contacts)
                    }
                    binding.vEmptyLayout.visible(true)
                } else {
                    binding.vEmptyLayout.visible(false)
                }

                binding.aivIndexBar.visible(newData.isNotEmpty())
            }
        }

    }

    private fun refreshData() {
        vm.loadFriendList()
        vm.loadUnreadFriendRequestsCount()
    }

    override fun onResume() {
        super.onResume()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressCallback)
        Analytics.track(EventConstants.EVENT_IM_CONTACTS_TAB_SHOW)
    }

    override fun onDestroyView() {
        binding.rvFriendList.adapter = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (args.shareContent?.isNotEmpty() == true && !args.shareRequestKey.isNullOrEmpty()) {
            args.shareRequestKey?.let {
                if (args.needBackRole || (args.needBackGame && !args.fromGameId.isNullOrEmpty())) {
                    if (isShare) {
                        ShareResult.notifySuccess(
                            it,
                            SharePlatform.PLATFORM_FRIEND,
                            extra = selectedUuid
                        )
                    } else if (selectedUuid != null) {
                        ShareResult.notifyFail(
                            it,
                            SharePlatform.PLATFORM_FRIEND,
                            ShareHelper.CODE_SDK_ERROR,
                            null,
                            extra = selectedUuid
                        )
                    } else {
                        ShareResult.notifyCancel(
                            it,
                            SharePlatform.PLATFORM_FRIEND
                        )
                    }
                } else {
                    setFragmentResultByActivity(
                        it, bundleOf(
                            SHARE_CALLBACK to isShare,
                            KEY_SELECT_UUID to selectedUuid
                        )
                    )
                }
            }
        }
    }

    private fun goBack() {
        if (requireActivity().isFinishing) return
        findNavController().popBackStack()
        if (args.needBackRole) {
            MetaRouter.Main.resumeToRole(requireActivity())
        } else if (args.needBackGame && !args.fromGameId.isNullOrEmpty()) {
            resumeGameById(args.fromGameId.orEmpty())
        }
    }

    override fun invalidate() {
    }
}