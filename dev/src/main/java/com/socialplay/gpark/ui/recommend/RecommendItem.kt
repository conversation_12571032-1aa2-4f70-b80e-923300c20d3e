package com.socialplay.gpark.ui.recommend

import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.viewbinding.ViewBinding
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.Priority
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.HomeCustomRecommend
import com.socialplay.gpark.databinding.HeaderRecommendBinding
import com.socialplay.gpark.databinding.ItemRecommendGameBinding
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.databinding.ViewChoiceFriendsViewBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.editorschoice.header.friends.ChoiceHomeHeaderFriends
import com.socialplay.gpark.ui.main.HomeImageShowAnalytics
import com.socialplay.gpark.ui.main.startup.HomeStartupProject
import com.socialplay.gpark.ui.recommend.choice.IChoiceListener
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

fun EpoxyController.addFriendHeader(header: ChoiceHomeHeaderFriends) {
    add(FriendHeader(header).apply {
        id("friendHeader")
        spanSizeOverride { _, _, _ -> 2 }
    })
}

fun EpoxyController.addRecommendHeader() {
    add(RecommendHeader().apply {
        id("RecommendHeader")
        spanSizeOverride { _, _, _ -> 2 }
    })
}

fun EpoxyController.addContinueHeader(
    playedCount: Int,
    listener: IChoiceListener
) {
    add(ContinueHeader(playedCount, listener).apply {
        id("continueHeader")
        spanSizeOverride { _, _, _ -> 2 }
    })
}

fun EpoxyController.addGameItem(
    index: Int,
    item: HomeCustomRecommend.RecommendList,
    onShow: () -> Unit,
    onClick: () -> Unit,
    getGlide: GlideGetter
) {
    add(GameItem(index, item, onShow, onClick, getGlide).apply {
        id("GameItem${item.gameId}")
        spanSizeOverride { _, _, _ -> 1 }
    })
}

private fun <T> RequestBuilder<T>.loadImageListener(url: String?, position: Int): RequestBuilder<T> {
    HomeStartupProject.onHomeStartLoadImage(url, position)
    HomeImageShowAnalytics.onHomeStartLoadImage(url, position)
    return this.listener(object : RequestListener<T> {
        override fun onLoadFailed(
            e: GlideException?,
            model: Any?,
            target: Target<T>,
            isFirstResource: Boolean
        ): Boolean {
            HomeStartupProject.onHomeFinishLoadImage(url, position)
            HomeImageShowAnalytics.onHomeFinishLoadImage(url, position)
            return false
        }

        override fun onResourceReady(
            resource: T & Any,
            model: Any,
            target: Target<T>?,
            dataSource: DataSource,
            isFirstResource: Boolean
        ): Boolean {
            HomeStartupProject.onHomeFinishLoadImage(url, position)
            HomeImageShowAnalytics.onHomeFinishLoadImage(url, position)
            return false
        }
    })
}

abstract class BaseRecommendItem<VB : ViewBinding>(
    @LayoutRes layoutRes: Int,
    binder: (View) -> VB
) : ViewBindingItemModel<VB>(layoutRes, binder) {

    protected fun VB.updateHorizontalGap(index: Int) {
        if (index % 2 == 0) {
            root.setPaddingEx(left = dp(16), right = dp(5))
        } else {
            root.setPaddingEx(left = dp(5), right = dp(16))
        }
    }
}

data class GameItem(
    val index: Int,
    val item: HomeCustomRecommend.RecommendList,
    val onShow: () -> Unit,
    val onClick: () -> Unit,
    val getGlide: GlideGetter
) : BaseRecommendItem<ItemRecommendGameBinding>(
    R.layout.item_recommend_game,
    ItemRecommendGameBinding::bind
) {

    override fun ItemRecommendGameBinding.onBind() {
        updateHorizontalGap(index)
        getGlide()?.run {
            load(item.banner).loadImageListener(item.banner, index)
                .priority(Priority.HIGH)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(iv)

            load(item.userIcon).placeholder(R.drawable.placeholder_round)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivIcon)
        }
        tvNew.visible(false)
        tvScore.visible(false)
        val likeVisible = item.likeCount != null && item.likeCount!! >= 0
        tvLike.visible(likeVisible)
        val playerVisible = item.pvCount != null && item.pvCount!! >= 0
        tvPlayerNum.visible(playerVisible)

        tvLike.text = UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0L)
        tvPlayerNum.text = UnitUtil.formatKMCount(item.pvCount ?: 0L)

        tvTitle.text = item.gameName
        tvName.text = item.userName
        root.setOnAntiViolenceClickListener {
            onClick.invoke()
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.FULL_IMPRESSION_VISIBLE) {
            onShow.invoke()
        }
    }

    override fun ItemRecommendGameBinding.onUnbind() {
        root.unsetOnClick()
    }

}

data class ContinueHeader(
    val playedCount: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<ViewChoiceContinueGameBinding>(
    R.layout.view_choice_continue_game,
    ViewChoiceContinueGameBinding::bind
) {

    override fun createView(parent: ViewGroup): View {
        val createView = super.createView(parent).apply {
            updatePadding(11.dp)
        }
        return createView
    }

    override fun ViewChoiceContinueGameBinding.onBind() {
        val showContinue = playedCount > 0
        rvRecentlyPlayed.isVisible = showContinue
        tvPlayedTitle.isVisible = showContinue
        vContinueSpace.isVisible = showContinue
        tvPlayedManage.isVisible = showContinue
        listener.bindPlayed(playedCount, this)
    }

    override fun ViewChoiceContinueGameBinding.onUnbind() {
        listener.unbindPlayed(playedCount, this)
    }
}

data class FriendHeader(
    val header: ChoiceHomeHeaderFriends
) : ViewBindingItemModel<ViewChoiceFriendsViewBinding>(
    R.layout.view_choice_friends_view,
    ViewChoiceFriendsViewBinding::bind
) {
    override fun createView(parent: ViewGroup): View {
        return header.getView().apply {
            updatePadding(left = dp(11))
        }
    }

    override fun ViewChoiceFriendsViewBinding.onBind() {
        // 由于我们直接返回了 header.getView()，这里不需要做任何绑定
        // 所有的逻辑都在 ChoiceHomeHeaderFriends 中处理
    }
}

class RecommendHeader : ViewBindingItemModel<HeaderRecommendBinding>(
    R.layout.header_recommend,
    HeaderRecommendBinding::bind
) {
    override fun createView(parent: ViewGroup): View {
        return super.createView(parent)
            .apply { updatePadding(left = dp(12), top = dp(12), bottom = 0) }
    }

    override fun HeaderRecommendBinding.onBind() {}
}