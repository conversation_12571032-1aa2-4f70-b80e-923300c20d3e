package com.socialplay.gpark.ui.room

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import kotlinx.coroutines.launch

/**
 * create by: bin on 2023/7/14
 */
class RoomDetailViewModel(val repository: IMetaRepository): ViewModel() {
    private val _room: MutableLiveData<ChatRoomInfo> = MutableLiveData<ChatRoomInfo>()
    val room: LiveData<ChatRoomInfo> = _room

    fun load(roomId: String) = viewModelScope.launch {
        repository.getRoomInfo(roomId).collect {
            if (it.succeeded && it.data != null) _room.value = it.data
        }
    }

}