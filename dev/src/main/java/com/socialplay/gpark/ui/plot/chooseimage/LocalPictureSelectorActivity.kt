package com.socialplay.gpark.ui.plot.chooseimage

import android.os.Parcelable
import androidx.core.view.isVisible
import com.bin.cpbus.CpEventBus
import com.luck.picture.lib.basic.PictureSelectionModel
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.data.model.event.ts.LocalChooseImageEvent
import com.socialplay.gpark.util.CustomMediaPlayerEngine
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import kotlinx.parcelize.Parcelize


/**
 * Created by bo.li
 * Date: 2024/6/3
 * Desc: 游戏内-选择图片
 */
@Parcelize
data class LocalPictureSelectorActivityArgs(
    val count: Int,
    val mimeType: Int,
    val useCamera: <PERSON><PERSON><PERSON>,
    val videoMaxSecond: Int?,
) : Parcelable

class LocalPictureSelectorActivity : BaseChooseImageActivity() {

    private var args: LocalPictureSelectorActivityArgs? = null

    companion object {
        const val EXTRA_KEY_PARAMS_PICTURE_SELECTOR = "local_picture_selector_extra_params"
    }

    override fun initArgs() {
        args = intent.getParcelableExtra(EXTRA_KEY_PARAMS_PICTURE_SELECTOR)
        if (args == null) {
            finish()
            return
        }
        binding.fcvClip.isVisible = false
        binding.lv.isVisible = false
    }

    private fun getSelectCount() = args?.count ?: 1

    override fun assemblePictureBuilder(builder: PictureSelectionModel): PictureSelectionModel {
        return if (args?.mimeType == SelectMimeType.ofVideo()) {
            chooseVideos()
        } else {
            choosePictures()
        }
    }

    private fun choosePictures(): PictureSelectionModel {
        return PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(getSelectCount())
            .isDisplayCamera(args?.useCamera == true)
            .setImageEngine(GlideEngine)
            .setCompressEngine(LubanCompressEngine())
            .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(this))
    }

    private fun chooseVideos(): PictureSelectionModel {
        val model = PictureSelector.create(this)
            .openGallery(SelectMimeType.ofVideo())
            .isDisplayCamera(args?.useCamera == true)
        val videoMaxSecond = args?.videoMaxSecond ?: 0
        if (videoMaxSecond > 0) {
            model.setFilterVideoMaxSecond(videoMaxSecond)
                .setRecordVideoMaxSecond(videoMaxSecond)
        }
        return model.setMaxSelectNum(getSelectCount())
            .setImageEngine(GlideEngine)
            .setCompressEngine(LubanCompressEngine())
            .setVideoPlayerEngine(CustomMediaPlayerEngine())
            .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(this))
    }

    override fun callbackError(message: String?, code: Int) {
        finish()
    }

    override fun onSelectResult(result: ArrayList<LocalMedia>) {
        CpEventBus.post(LocalChooseImageEvent(result))
        finish()
    }
}