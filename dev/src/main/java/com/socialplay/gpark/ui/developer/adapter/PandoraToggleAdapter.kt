package com.socialplay.gpark.ui.developer.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import com.socialplay.gpark.databinding.AdapterDeveloperPandoraToggleBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.developer.bean.PandoraToggleBean
import com.socialplay.gpark.ui.main.MainBottomNavigationItem

/**
 * xingxiu.hou
 * 2021/6/7
 */
class PandoraToggleAdapter :
    BaseAdapter<PandoraToggleBean, AdapterDeveloperPandoraToggleBinding>() {

    var listener: ((PandoraToggleBean, (String) -> Unit) -> Unit)? = null

    override fun convert(holder: BindingViewHolder<AdapterDeveloperPandoraToggleBinding>, item: PandoraToggleBean, position: Int) {
        holder.binding.tvName.text = item.name
        holder.binding.tvToggleKey.text = item.key.let { "Key: $it" }
        holder.binding.tvDesc.text = fetchItemDesc(item)
        holder.binding.tvPandoraSelectValue.text = item.localValue
        holder.binding.etPandoraValue.setText(item.localValue)
        val onlineText = "Online: ${if (item.onlineValue.isEmpty()) "-" else item.onlineValue}"
        holder.binding.tvOnlineValue.text = onlineText

        holder.binding.tvPandoraSelectValue.setOnClickListener {
            listener?.invoke(item) {
                holder.binding.tvPandoraSelectValue.text = it
                holder.binding.etPandoraValue.setText(it)
            }
        }

        when {
            item.valueType == Boolean::class || !item.selectArray.isNullOrEmpty() -> {
                holder.binding.tvPandoraSelectValue.visibility = View.VISIBLE
                holder.binding.etPandoraValue.visibility = View.INVISIBLE
                holder.binding.etPandoraValue.inputType = EditorInfo.TYPE_NULL
            }
            item.valueType == Float::class                                        -> {
                holder.binding.tvPandoraSelectValue.visibility = View.INVISIBLE
                holder.binding.etPandoraValue.visibility = View.VISIBLE
                holder.binding.etPandoraValue.inputType =
                    EditorInfo.TYPE_CLASS_NUMBER or EditorInfo.TYPE_NUMBER_FLAG_DECIMAL or EditorInfo.TYPE_NUMBER_FLAG_SIGNED
            }
            item.valueType == Number::class                                       -> {
                holder.binding.tvPandoraSelectValue.visibility = View.INVISIBLE
                holder.binding.etPandoraValue.visibility = View.VISIBLE
                holder.binding.etPandoraValue.inputType =
                    EditorInfo.TYPE_CLASS_NUMBER or EditorInfo.TYPE_NUMBER_FLAG_SIGNED
            }
            else                                                                  -> {
                holder.binding.tvPandoraSelectValue.visibility = View.INVISIBLE
                holder.binding.etPandoraValue.visibility = View.VISIBLE
                holder.binding.etPandoraValue.inputType = EditorInfo.TYPE_CLASS_TEXT
            }
        }
    }

    private fun fetchItemDesc(toggle: PandoraToggleBean): String {
        if (PandoraToggle.isBottomTabToggle(toggle.key)) {
            return MainBottomNavigationItem.fetchDevTabDesc()
        }
        return toggle.desc
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterDeveloperPandoraToggleBinding {
        return AdapterDeveloperPandoraToggleBinding.inflate(
            layoutInflater,
            parent,
            false
        )
    }

}