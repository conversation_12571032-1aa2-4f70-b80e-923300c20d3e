package com.socialplay.gpark.ui.editorschoice.ugc

import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentUgcAllBinding
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.editor.BaseEditorFragmentV2
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import kotlinx.parcelize.Parcelize

@Parcelize
data class UgcAllFragmentArgs(val cardId: String, val title:String) : Parcelable

class UgcAllFragment : BaseEditorFragmentV2<FragmentUgcAllBinding>(R.layout.fragment_ugc_all) {

    companion object{
        private const val GRID_COL_COUNT = 2
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val viewModel: UgcAllViewModel by fragmentViewModel()

    private val args by args<UgcAllFragmentArgs>()

    override fun epoxyController() = simpleController(
        viewModel,
        UgcAllViewModelState::list,
        UgcAllViewModelState::loadMore
    ) { list, loadMore ->
        list.forEach {
            ugcGameItem(it,viewLifecycleOwner.lifecycleScope, ::glide) {
                val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.UGC_ALL)
                if (PandoraToggle.enableUgcDetail) {
                    MetaRouter.MobileEditor.ugcDetail(
                        this@UgcAllFragment,
                        it.code.toString(),
                        null,
                        resIdBean
                    )
                } else {
                    editorGameLaunchHelper?.startUgcGame(
                        this@UgcAllFragment,
                        it.code ?: "", it.packageName, it.displayName ?: "", null,
                        resIdBean
                    )
                }
            }
        }
            loadMoreFooter(loadMore, spanSize = GRID_COL_COUNT) {
                viewModel.loadMore()
            }
    }

    val decoration = object :ItemDecoration(){
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            outRect.left = 8.dp
            outRect.right = 8.dp
            outRect.bottom = 15.dp
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcAllBinding? {
        return FragmentUgcAllBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.rv.layoutManager = GridLayoutManager(requireContext(),
            GRID_COL_COUNT, GridLayoutManager.VERTICAL, false)
        binding.rv.removeItemDecoration(decoration)
        binding.rv.addItemDecoration(decoration)
        binding.title.setTitle(args.title)
        binding.title.setOnBackClickedListener {
            navigateUp()
        }
        viewModel.setupRefreshLoading(
            UgcAllViewModelState::refresh,
            binding.loadingView,
            binding.refresh
        ) {
            viewModel.refresh()
        }
    }

    override fun getPageName(): String {
        return "UgcAllFragment"
    }
}