package com.socialplay.gpark.ui.gamedetail.lightup

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.editor.LightUpBody
import com.socialplay.gpark.data.model.editor.SparkEvent
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.flow.map
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/08
 *     desc   :
 * </pre>
 */
data class SparkState(
    val gameId: String = "",
    val authorId: String = "",
    val sparkCount2Work: Long? = null,
    val sparkTotal2Work: Long? = null,
    val sparkTimes2Work: Long? = null,
    val lightUpResult: Async<Pair<Long, String?>> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
    val status: Int = STATUS_INIT
) : MavericksState {

    companion object {
        const val STATUS_INIT = 0
        const val STATUS_CACHE = 1
        const val STATUS_NET = 2
    }

    val canCache get() = status < STATUS_CACHE
    val canNet get() = status < STATUS_NET

    fun canUpdate(isCache: Boolean): Boolean {
        return if (isCache) {
            canCache
        } else {
            canNet
        }
    }
}

class SparkViewModel(
    initState: SparkState,
    private val accountInteractor: AccountInteractor,
    private val h5PageConfigInteractor: H5PageConfigInteractor,
    private val ugcRepository: UgcRepository,
) : BaseViewModel<SparkState>(initState) {

    val instructionPageUrl get() = h5PageConfigInteractor.getH5PageUrl(
        H5PageConfigInteractor.SPARK_INSTRUCTION
    )

    val sparkBalanceLiveData get() = accountInteractor.sparkBalanceLiveData

    init {
        registerHermes()
    }

    fun init(
        gameId: String,
        authorId: String,
        sparkCount2Work: Long,
        sparkTotal2Work: Long,
        sparkTimes2Work: Long,
        isCache: Boolean = false
    ) = withState { s ->
        if (PandoraToggle.enableLightUp && s.canUpdate(isCache)) {
            if (!isCache) {
                SparkEvent.gameSpark(
                    gameId,
                    sparkCount2Work,
                    sparkTotal2Work,
                    sparkTimes2Work
                )
            }
            setState {
                copy(
                    gameId = gameId,
                    authorId = authorId,
                    sparkCount2Work = sparkCount2Work,
                    sparkTotal2Work = sparkTotal2Work,
                    sparkTimes2Work = sparkTimes2Work,
                    status = if (isCache) {
                        SparkState.STATUS_CACHE
                    } else {
                        SparkState.STATUS_NET
                    }
                )
            }
        }
    }

    fun lightUp(count: Long) = withState { s ->
        if (accountInteractor.isMe(s.authorId)) {
            trackLightUpOwn(s, count)
            return@withState
        }
        if (s.lightUpResult is Loading) return@withState
        val consumable = sparkBalanceLiveData.value?.consumable ?: return@withState
        when (s.sparkCount2Work) {
            0L -> {
                if (count > consumable) {
                    trackLightInsufficient(s, count)
                    return@withState
                }
            }

            1L -> {
                if (count == 2L) {
                    trackLightUpExceed(s, count)
                    return@withState
                } else if (count > consumable) {
                    trackLightInsufficient(s, count)
                    return@withState
                }
            }

            else -> {
                trackLightUpExceed(s, count)
                return@withState
            }
        }
        val targetLightUpCount = s.sparkCount2Work + count
        val targetLightUpTotal = (s.sparkTotal2Work ?: 0) + count
        val currentLightUpTimes = s.sparkTimes2Work ?: 0L
        val targetLightUpTimes = currentLightUpTimes + 1L
        ugcRepository.lightUp(LightUpBody(s.gameId, count, currentLightUpTimes)).map {
            if (it.succeeded) {
                Analytics.track(
                    EventConstants.UGC_LIGHT_UP_CHOOSE,
                    "success" to 1,
                    "number" to count,
                    "mapid" to s.gameId,
                    "author" to s.authorId
                )
                SparkEvent.gameSpark(
                    s.gameId,
                    targetLightUpCount,
                    targetLightUpTotal,
                    targetLightUpTimes
                )
                accountInteractor.updateSparkBalance(it.data?.balanceList)
            } else {
                Analytics.track(
                    EventConstants.UGC_LIGHT_UP_CHOOSE,
                    "success" to 2,
                    "number" to count,
                    "failmsg" to (it as? DataResult.Error)?.message.orEmpty(),
                    "failcode" to (it.code ?: 0),
                    "mapid" to s.gameId,
                    "author" to s.authorId
                )
            }
            it
        }.execute { result ->
            if (result is Success) {
                val data = result()
                if (data.succeeded) {
                    data.data?.let {
                        copy(
                            sparkTotal2Work = targetLightUpTotal,
                            sparkCount2Work = targetLightUpCount,
                            sparkTimes2Work = targetLightUpTimes,
                            lightUpResult = Success(count to null)
                        )
                    } ?: copy(lightUpResult = Success(count to null))
                } else {
                    copy(lightUpResult = Success(0L to data.message))
                }
            } else {
                copy(lightUpResult = Loading())
            }
        }
    }

    private fun trackLightUpOwn(s: SparkState, count: Long) {
        Analytics.track(
            EventConstants.UGC_LIGHT_UP_CHOOSE,
            "success" to 2,
            "number" to count,
            "failmsg" to "3",
            "failcode" to 3,
            "mapid" to s.gameId,
            "author" to s.authorId
        )
        setState { copy(toast = toast.toResMsg(R.string.light_up_failed_own)) }
    }

    private fun trackLightUpExceed(s: SparkState, count: Long) {
        Analytics.track(
            EventConstants.UGC_LIGHT_UP_CHOOSE,
            "success" to 2,
            "number" to count,
            "failmsg" to "2",
            "failcode" to 2,
            "mapid" to s.gameId,
            "author" to s.authorId
        )
        setState { copy(toast = toast.toResMsg(R.string.light_up_failed_exceed_max)) }
    }

    private fun trackLightInsufficient(s: SparkState, count: Long) {
        Analytics.track(
            EventConstants.UGC_LIGHT_UP_CHOOSE,
            "success" to 2,
            "number" to count,
            "failmsg" to "1",
            "failcode" to 1,
            "mapid" to s.gameId,
            "author" to s.authorId
        )
        setState { copy(toast = toast.toResMsg(R.string.light_up_failed_insufficient)) }
    }


    @Subscribe
    fun onSparkEvent(event: SparkEvent) = withState { s ->
        if (event.gameId == s.gameId &&
            (event.lightUpMyCount != s.sparkCount2Work
                    || event.lightUpTotalCount != s.sparkTotal2Work
                    || event.lightUpTimes != s.sparkTimes2Work)
        ) {
            setState {
                copy(
                    sparkCount2Work = event.lightUpMyCount,
                    sparkTotal2Work = event.lightUpTotalCount,
                    sparkTimes2Work = event.lightUpTimes
                )
            }
        }
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }

    companion object : KoinViewModelFactory<SparkViewModel, SparkState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SparkState
        ): SparkViewModel {
            return SparkViewModel(state, get(), get(), get())
        }
    }
}