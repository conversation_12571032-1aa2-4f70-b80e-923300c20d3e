package com.socialplay.gpark.ui.main.startup

import com.socialplay.gpark.function.exoplayer.VideoFeedPreloadInteractor
import kotlinx.coroutines.suspendCancellableCoroutine
import org.koin.core.context.GlobalContext
import kotlin.coroutines.resume

class PreloadCommunityVideoTask : HomeStartupTask("PreloadCommunityVideoTask") {
    override suspend fun run() {

        val e = suspendCancellableCoroutine { cont ->
            val videoFeedPreloadInteractor =
                GlobalContext.get().get<VideoFeedPreloadInteractor>()
            videoFeedPreloadInteractor.preloadCommunityVideoFeeds {
                if (cont.isActive) {
                    cont.resume(it)
                }
            }
        }
        if (e != null) {
            throw e
        }
    }
}