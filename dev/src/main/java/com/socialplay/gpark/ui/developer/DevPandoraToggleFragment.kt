package com.socialplay.gpark.ui.developer

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.core.text.isDigitsOnly
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterDeveloperPandoraToggleBinding
import com.socialplay.gpark.databinding.FragmentDeveloperPandoraBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.developer.adapter.PandoraToggleAdapter
import com.socialplay.gpark.ui.developer.bean.ChooseStringItem
import com.socialplay.gpark.ui.developer.bean.PandoraToggleBean
import com.socialplay.gpark.ui.developer.viewmodel.DevPandoraToggleViewModel
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import kotlin.reflect.KClass

class DevPandoraToggleFragment : BaseFragment<FragmentDeveloperPandoraBinding>() {

    companion object {
        private const val TAG = "DEV:PandoraToggle"
    }

    private val viewModel: DevPandoraToggleViewModel by viewModel()
    private val adapter by lazy { PandoraToggleAdapter() }

    private var originListData = mutableListOf<PandoraToggleBean>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperPandoraBinding? {
        return FragmentDeveloperPandoraBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.swToggle.text = getString(R.string.debug_local_toggle, if (DeveloperPandoraToggle.isEnable()) getString(R.string.debug_open) else getString(R.string.debug_closed))
        binding.tvSearch.setOnClickListener { search(binding.etSearch.text.toString()) }
        binding.etSearch.setOnEditorActionListener { v, actionId, event ->
            search(v.text.toString())
            true
        }
        binding.tvSave.setOnClickListener {
            saveCustomToggleValue()
            viewModel.requestPandoraToggleList()
        }

        binding.refreshLayout.setOnRefreshListener {
            hideKeyboard(binding.etSearch)
            viewModel.requestPandoraToggleList()
        }

        binding.swToggle.setOnCheckedChangeListener { buttonView, isChecked ->
            buttonView.text = getString(R.string.debug_local_toggle, if (isChecked) getString(R.string.debug_open) else getString(R.string.debug_closed))
            DeveloperPandoraToggle.setEnable(isChecked)
        }
        viewModel.pandoraToggleStatusLivedata.observe(viewLifecycleOwner) {
            binding.swToggle.isChecked = it
        }

        binding.rv.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        viewModel.pandoraToggleLivedata.observe(viewLifecycleOwner) {
            binding.refreshLayout.isRefreshing = false
            binding.etSearch.setText("")
            originListData.clear()
            originListData.addAll(it)
            adapter.setList(it)
        }

        adapter.listener = { bean, callback ->
            var selectList = bean.selectArray
            if (selectList.isNullOrEmpty() && bean.valueType == Boolean::class) {
                selectList = arrayOf("TRUE", "FALSE")
            }
            if (!selectList.isNullOrEmpty()) {
                val showInput = bean.valueType != Boolean::class
                val toList = selectList.toList()
                DeveloperSelectDialog.show(this, bean.name, bean.key, toList.map { ChooseStringItem(it) }, showInput, false,
                    object : OnSelectCallback<ChooseStringItem> {
                        override fun convert(data: String): ChooseStringItem? {
                            return ChooseStringItem.convert(data)
                        }

                        override fun on(data: ChooseStringItem?) {
                            if (data != null && data.data().isNotEmpty()) {
                                callback.invoke(data.data())
                            } else {
                                toast(R.string.debug_empty_value)
                            }
                        }

                    })
            }
        }
    }

    private fun search(text: String) {
        hideKeyboard(binding.etSearch)
        if (text.isNotEmpty()) {
            val filterData = originListData.filter {
                it.key.contains(text) || it.name.contains(text) || it.desc.contains(text)
            }
            adapter.setList(filterData)
        }
    }

    /*保存自定义开关值*/
    private fun saveCustomToggleValue() {
        hideKeyboard(binding.etSearch)
        var hasError = false
        for (i in 0 until adapter.itemCount) {
            val key = adapter.data[i].key
            val valueType = adapter.data[i].valueType
            binding.rv.findViewHolderForAdapterPosition(i)?.let {
                if (it is BindingViewHolder<*> && it.binding is AdapterDeveloperPandoraToggleBinding) {
                    val et = it.binding.etPandoraValue
                    val value = et.text.toString()
                    if (value.isNotEmpty()) {
                        if (checkType(value, valueType)) {
                            et.setBackgroundColor(Color.parseColor("#B2DFDB"))
                            DeveloperPandoraToggle.setValue(key, value)
                        } else {
                            hasError = true
                            et.setBackgroundColor(Color.parseColor("#FF0000"))
                        }
                    }
                    Timber.d("$TAG $key, $value")
                }
            }
        }
        toast(if (hasError) "save failed" else "save successfully")
    }

    private fun checkType(value: String, type: KClass<*>): Boolean {
        return when (type) {
            Boolean::class -> {
                arrayOf("TRUE", "FALSE").any { it.equals(value, true) }
            }
            Number::class -> {
                value.isDigitsOnly()
            }
            Float::class -> {
                value.matches(Regex("^[-\\+]?[.\\d]*$"))
            }
            else -> {
                true
            }
        }
    }

    override fun loadFirstData() {
        viewModel.requestPandoraToggleList()
        viewModel.getPandoraToggleStatus()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_DEV_CONFIG_PANDORA_TOGGLE

    private fun hideKeyboard(view: View) {
        val manager: InputMethodManager = view.context
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        manager.hideSoftInputFromWindow(view.windowToken, 0)
    }
}