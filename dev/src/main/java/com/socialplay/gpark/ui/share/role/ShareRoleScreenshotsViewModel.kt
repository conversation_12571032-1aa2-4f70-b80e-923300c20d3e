package com.socialplay.gpark.ui.share.role

import android.content.ComponentCallbacks
import android.content.Context
import android.os.SystemClock
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.model.share.RoleMyInfo
import com.socialplay.gpark.data.model.share.RoleScreenshot
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.share.GlobalSharePlatformHelper
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.role.RoleScreenshotShareHelper
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.replaceAt
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/28
 *     desc   :
 * </pre>
 */
data class ShareRoleScreenshotsState(
    val platforms: List<SharePlatform>,
    val gameId: String,
    val screenshots: List<RoleScreenshot>,
    val myInfo: RoleMyInfo,
    val shareConfig: Async<ShareConfig> = Uninitialized,
    val sharePlatform: Pair<String, Long>? = null,
    val toast: ToastData = ToastData.EMPTY
) : MavericksState

class ShareRoleScreenshotsViewModel(
    initialState: ShareRoleScreenshotsState,
    val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<ShareRoleScreenshotsState>(initialState) {

    companion object :
        KoinViewModelFactory<ShareRoleScreenshotsViewModel, ShareRoleScreenshotsState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ShareRoleScreenshotsState
        ): ShareRoleScreenshotsViewModel {
            return ShareRoleScreenshotsViewModel(state, get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): ShareRoleScreenshotsState {
            val args = viewModelContext.args as ShareRoleScreenshotsDialogV2Args
            return ShareRoleScreenshotsState(
                buildList {
                    RoleScreenshotShareHelper.initThirdPartyPlatforms(this)

                },
                args.event.gameId,
                args.event.screenshots.map { RoleScreenshot(it) },
                RoleMyInfo(args.myInfo)
            )
        }
    }

    val requestId = "share_role_screenshot_${SystemClock.elapsedRealtime()}"

    val amount: Int
        get() {
            return oldState.screenshots.size + 1
        }

    init {
        registerHermes()
        fetchShareConfig()
    }

    fun fetchShareConfig() = withState { s ->
        val scene = ShareWrapper.mapScene2ConfigScene(ShareHelper.SCENE_AVATAR_SCREENSHOT)
        tTaiInteractor.getSceneShareConfig(scene).execute {
            copy(shareConfig = it)
        }
    }

    fun share2Platform(context: Context, platform: String) = withState { s ->
        if (!GlobalSharePlatformHelper.checkInstallation(context, platform)) {
            setState { copy(toast = toast.toResMsg(R.string.not_installed)) }
            return@withState
        }
        if (s.shareConfig.shouldLoad) {
            fetchShareConfig()
        }
        val ts = SystemClock.elapsedRealtime()

        val pair = platform to ts
        setState { copy(sharePlatform = pair) }
    }

    fun updateScreenshotCheck(position: Int) {
        val oldScreenshots = oldState.screenshots
        val newScreenshot = oldScreenshots[position].switch()
        val newScreenshots = oldScreenshots.replaceAt(position, newScreenshot).orEmpty()
        setState { copy(screenshots = newScreenshots) }
    }

    fun updateMyInfoCheck() {
        val newMyInfo = oldState.myInfo.switch()
        setState { copy(myInfo = newMyInfo) }
    }

    @Subscribe
    fun onEvent(shareResult: ShareResult) {
        if (shareResult.requestId != requestId) return
        trackShareResult(shareResult.platform, shareResult.isSuccess, shareResult.fullCode)
    }


    fun trackShareResult(platform: String, result: Boolean, code: String) = withState { s ->
        Analytics.track(
            EventConstants.EVENT_ROLE_SHARE_AVATAR_RESULT,
            "reason" to code,
            "result" to (if (result) "1" else "2"),
            "way" to platform
        )
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }
}