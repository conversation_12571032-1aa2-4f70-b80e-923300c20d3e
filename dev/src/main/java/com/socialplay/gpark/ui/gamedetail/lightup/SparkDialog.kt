package com.socialplay.gpark.ui.gamedetail.lightup

import android.view.WindowManager
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogLightUpBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.property.viewBinding

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/30
 *     desc   :
 * </pre>
 */
class SparkDialog : BaseDialogFragment() {

    override val binding by viewBinding(DialogLightUpBinding::inflate)
    private val vm: SparkViewModel by parentFragmentViewModel()

    override fun init() {
        vm.oldState.let {
            Analytics.track(
                EventConstants.UGC_LIGHT_UP_SHOW,
                "mapid" to it.gameId,
                "author" to it.authorId
            )
        }
        binding.ivCloseBtn.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.UGC_LIGHT_UP_CLOSE
            )
            dismissAllowingStateLoss()
        }
        binding.vBg1.setOnAntiViolenceClickListener {
            vm.lightUp(1)
            dismissAllowingStateLoss()
        }
        binding.vBg2.setOnAntiViolenceClickListener {
            vm.lightUp(2)
            dismissAllowingStateLoss()
        }
        binding.vTipsClick.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.UGC_LIGHT_UP_TIPS_DETAIL
            )
            parentFragment?.let {
                MetaRouter.Web.navigate(
                    it,
                    title = getString(R.string.what_is_spark),
                    url = vm.instructionPageUrl,
                    showTitle = true
                )
            }
            dismissAllowingStateLoss()
        }

        vm.sparkBalanceLiveData.observe(viewLifecycleOwner) {
            binding.tvTitle2.text = it.consumable.toString()
            binding.tvContent.setTextWithArgs(
                R.string.balance_desc,
                it.consumable + it.unusable,
                it.consumable
            )
        }
    }

    override fun isClickOutsideDismiss(): Boolean {
        return false
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun getStyle() = R.style.DialogStyleNonFullScreen
}