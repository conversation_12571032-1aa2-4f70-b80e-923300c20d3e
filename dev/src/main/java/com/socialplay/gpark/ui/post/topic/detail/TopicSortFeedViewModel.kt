package com.socialplay.gpark.ui.post.topic.detail

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc:
 */
data class TopicSortFeedModelState(
    override val refresh: Async<List<CommunityFeedInfo>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val scrollToTop: Async<Boolean> = Uninitialized,
    val sortType: Int,
    val tagInfo: PostTag
) : ICommunityFeedModelState {

    constructor(args: TopicSortFeedFragmentArgs): this(sortType = args.sortType, tagInfo = args.tagInfo)

    override fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelState {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState {
        return copy(notifyCheckVideo = checkVideo)
    }

    override fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        val newRefresh = result.map { wrapper ->
            wrapper.dataList.distinctBy { it.postId }
        }
        return copy(
            refresh = newRefresh,
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }

    override fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke()
                result.map { wrapper ->
                    if (oldList.isNullOrEmpty()) {
                        wrapper.dataList
                    } else {
                        oldList + wrapper.dataList
                    }.distinctBy { it.postId }
                }
            } else {
                refresh
            },
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }
}

class TopicSortFeedViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: TopicSortFeedModelState
) : BaseCommunityFeedViewModel<TopicSortFeedModelState>(repository, accountInteractor, initialState) {

    init {
        // [java.lang.reflect.InvocationTargetException] 继承BaseCommunityFeedViewModel时，init代码块里加东西，可能会导致fragmentViewModel找不到类
    }

    // 话题排序
    fun refresh() {
        val state = oldState
        repository.getCommunityFeed(
            state.sortType,
            null,
            state.tagInfo.tagId,
            null,
            PAGE_SIZE,
            1
        ).map {
            notifyCheckVideo()
            it
        }.execute { result ->
            feedRefresh(result) as TopicSortFeedModelState
        }
    }

    fun loadMoreFeed() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            val nextPage = oldState.nextPage
            val state = oldState
            repository.getCommunityFeed(
                state.sortType,
                null,
                state.tagInfo.tagId,
                null,
                PAGE_SIZE,
                nextPage
            ).execute { result ->
                feedLoadMore(result) as TopicSortFeedModelState
            }
        }
    }

    fun removePublishingItem(ts: Long?) {
        withState { oldState ->
            if (oldState.list.isEmpty()) return@withState
            val newList = ArrayList(oldState.list).apply {
                if (ts != null) {
                    removeAll { it.localId == ts }
                } else {
                    removeAll { it.localPublishing }
                }
            }
            Timber.d("checkcheck_publish, removePublishingItem ts: ${ts}")
            setState {
                copy(refresh = refresh.copyEx(newList))
            }
        }
    }

    fun addPublishingItem(info: CommunityFeedInfo, publishing: Boolean) = viewModelScope.launch {
        val state = awaitState()
        val skip = state.list.any { it.localId == info.localId } && publishing
        if (skip) {
            return@launch
        }
        val newList = ArrayList(state.list)
        val oldIndex = newList.indexOfFirst { it.localId == info.localId || it.postId == info.postId }
        if (oldIndex in 0..newList.lastIndex) {
            // todo 容易落下
            newList[oldIndex] = newList[oldIndex].copy(
                localPublishing = publishing,
                postId = info.postId,
                mediaList = info.mediaList,
                tagList = info.tagList,
                status = if (!publishing) {
                    PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                } else {
                    PostDetail.STATUS_OK
                }
            )
            if (!publishing) {
                scrollToTop()
            }
        } else {
            scrollToTop()
            newList.add(
                0,
                info.copy(
                    localPublishing = publishing, status = if (!publishing) {
                        PostDetail.STATUS_REVIEW_AUTO_IN_PROGRESS
                    } else {
                        PostDetail.STATUS_OK
                    }
                )
            )
        }
        Timber.d("checkcheck_publish, addPublishingItem localId: ${info.localId}, postId: ${info.postId}, publishing: ${publishing}")
        setState {
            copy(refresh = refresh.copyEx(newList))
        }
    }

    fun removeScrollToTop() {
        suspend {
            false
        }.execute {
            copy(scrollToTop = it)
        }
    }

    private fun scrollToTop() {
        withState {
            if (it.scrollToTop is Loading) return@withState
            suspend {
                delay(500)
                true
            }.execute {
                copy(scrollToTop = it)
            }
        }
    }

    companion object : KoinViewModelFactory<TopicSortFeedViewModel, TopicSortFeedModelState>() {

        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TopicSortFeedModelState
        ): TopicSortFeedViewModel {
            return TopicSortFeedViewModel(get(), get(), state)
        }
    }
}