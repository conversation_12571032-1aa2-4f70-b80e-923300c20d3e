package com.socialplay.gpark.ui.editorschoice.adapter

import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.IChoiceItem
import com.socialplay.gpark.databinding.AdapterChoiceRecommendBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.NumberUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible

/**
 *
 * <AUTHOR>
 * @date 2021/07/05
 */
class RecommendProvider(
    private val glide: RequestManager,
) : BaseItemProvider<ChoiceCardInfo>() {

    override val itemViewType: Int = ChoiceCardType.RECOMMEND

    override val layoutId = R.layout.adapter_choice_recommend

    override fun convert(helper: BaseViewHolder, card: ChoiceCardInfo) {
        val item = card.recommend ?: return
        AdapterChoiceRecommendBinding.bind(helper.itemView).apply {
            ivAvatar.visible()
            tvName.visible()
            vBgBottom.visible()
            tvLike.visible()
            glide.load(item.avatar).transform(CircleCrop()).into(ivAvatar)
            tvName.text = item.nickname
            tvLike.text = UnitUtil.formatKMCount(item.likeCount ?: 0)
            glide.load(item.iconUrl).transform(CenterCrop(), RoundedCorners(12.dp)).into(ivBg)
            tvDisplayName.text = item.displayName
        }
    }
}