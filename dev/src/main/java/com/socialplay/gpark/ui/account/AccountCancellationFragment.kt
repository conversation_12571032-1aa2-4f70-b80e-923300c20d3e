package com.socialplay.gpark.ui.account

import android.graphics.text.LineBreaker
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.databinding.FragmentAccountCancellationBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/23 2:17 下午
 * @describe:账号注销
 */
class AccountCancellationFragment : BaseFragment<FragmentAccountCancellationBinding>() {

    private val viewModel by viewModel<AccountSettingViewModel>()
    private val args by navArgs<AccountCancellationFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAccountCancellationBinding? {
        return FragmentAccountCancellationBinding.inflate(inflater, container, false)
    }

    override fun init() {

        viewModel.logoutStateCallback.observe(viewLifecycleOwner) {
            if (it.succeeded) {
                toast(R.string.account_cancellation_success)
                findNavController().popBackStack(R.id.main, false)
            } else {
                toast(getString(R.string.account_cancellation_failed))
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            binding.tvItem1.justificationMode = LineBreaker.JUSTIFICATION_MODE_INTER_WORD
            binding.tvItem2.justificationMode = LineBreaker.JUSTIFICATION_MODE_INTER_WORD
            binding.tvItem3.justificationMode = LineBreaker.JUSTIFICATION_MODE_INTER_WORD
            binding.tvItem4.justificationMode = LineBreaker.JUSTIFICATION_MODE_INTER_WORD
            binding.tvItem5.justificationMode = LineBreaker.JUSTIFICATION_MODE_INTER_WORD
        }

        binding.tbl.setOnBackClickedListener {
            navigateUp()
        }

        binding.tvSure.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_SHOW_PAGE_CONFIRM_CLICK) {
                putAll(getCommonAnalyticParams())
            }
            Analytics.track(EventConstants.EVENT_ACCOUNT_CANCEL)
            viewModel.ditout(args.code)
        }
    }

    override fun loadFirstData() {

    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_LOGIN_SHOW_PAGE) {
            putAll(getCommonAnalyticParams())
        }
    }

    private fun getCommonAnalyticParams(): Map<String, Any> {
        return hashMapOf<String, Any>().apply {
            put(EventConstants.KEY_LOGIN_PAGE_NAME, getFragmentName())
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginPageSource.Unknown.source)
            args.gameId?.let {
                put(EventConstants.KEY_LOGIN_GAME_CODE, it)
            }
        }
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_ACCOUNT_CANCELLATION
}