package com.socialplay.gpark.ui.gamereview

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.view.isVisible
import androidx.fragment.app.clearFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.gamereview.AppraiseReply
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.data.model.gamereview.ReportData
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_AI_BOT
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_GAME_ONLY
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.databinding.FragmentReviewListBinding
import com.socialplay.gpark.databinding.ItemGameReviewAllBinding
import com.socialplay.gpark.databinding.PopUpMoreMyReviewBinding
import com.socialplay.gpark.databinding.PopUpMostFavorableReviewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.gamereview.GameReviewAnalyticHelper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_COMMENT
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_REPLAY
import com.socialplay.gpark.ui.gamereview.dialog.ReplyCommentDialog
import com.socialplay.gpark.ui.gamereview.dialog.SelfReviewAdapter
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.collectLatest
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/7/13
 * Desc:
 */
@ExperimentalCoroutinesApi
class ReviewListFragment : BaseFragment<FragmentReviewListBinding>() {

    private val params by lazy {
        hashMapOf(
            "gameid" to gameId
        )
    }
    private var onFinishedCallback: (() -> Unit)? = null

    private val uuid by lazy {
        GlobalContext.get().get<AccountInteractor>().accountLiveData.value?.uuid ?: ""
    }

    private var gameReviewAdapter: GameAppraiseAdapter? = null
    private val appraiseItemListener: GameAppraiseAdapter.AppraiseItemListener by lazy {
        object : GameAppraiseAdapter.AppraiseItemListener {
            override fun moreCallback(
                view: View,
                commentId: String,
                replyId: String?,
                type: Int,
                isMe: Boolean
            ) {
                Timber.d("moreCallback")
                showMoreDialog(view, commentId, replyId, type, isMe)
            }

            //更多回复
            override fun moreReplyCallback(view: View, gameReview: GameAppraiseData) {
                Timber.d("moreReplyCallback")
                gameReviewViewModel.loadMoreReply(gameReview)
            }

            override fun replyCommentCallback(
                gameReview: GameAppraiseData,
                position: Int,
                commentId: String,
                replyId: String,
                replyUid: String,
                replyNickname: String
            ) {
                Timber.d("replyCommentCallback $uuid $replyUid $replyNickname")
                ReplyCommentDialog.show(
                    this@ReviewListFragment,
                    uuid,
                    commentId,
                    replyId,
                    replyUid,
                    replyNickname,
                    "1",
                    gameId
                ) { success, msg ->
                    if (success) {
                        ToastUtil.showShort(requireContext(), R.string.post_reply_success)
                        gameReviewViewModel.getMyReply(gameReview, position, replyId, msg)
                    } else {
                        ToastUtil.showShort(requireContext(), msg)
                    }
                }
            }

            override fun likeCallback(gameReview: GameAppraiseData, attitude: Int) {
                Timber.d("likeCallback")
                gameReviewViewModel.attitudeGameReview(gameReview, false, attitude)
                if (attitude == GameAppraiseData.OPTION_LIKE) {
                    DialogShowManager.triggerLike(this@ReviewListFragment)
                }
            }

            override fun likeReplyCallback(replyId: String, isLike: Boolean) {
                Timber.d("likeReplyCallback")
                gameReviewViewModel.likeAppraiseReply(replyId, isLike)
            }

            override fun sendEventCallback(commentId: String) {
                Timber.d("sendEventCallback")
                Analytics.track(EventConstants.GAME_REVIEW_ITEM_SHOW) {
                    put("gameid", gameId)
                    put("reviewid", commentId)
                    put("from", "2") //2 游戏详情页
                }
            }

            override fun onClickComment(
                justJumpDetail: Boolean,
                commentItem: GameAppraiseData,
                replyItem: AppraiseReply?,
                position: Int
            ) {
            }

            override fun jumpHomePage(uid: String) {
                Timber.d("jumpHomePage")
                MetaRouter.Profile.other(
                    this@ReviewListFragment,
                    uid,
                    "game_review"
                )
            }

            override fun clickLabel(data: Pair<Int, LabelInfo?>) {
                UserLabelView.showDescDialog(this@ReviewListFragment, data)
            }
        }
    }

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val selfReviewAdapter: SelfReviewAdapter by lazy {
        SelfReviewAdapter(Glide.with(this)) {
            UserLabelView.showDescDialog(this, it)
        }
    }
    private val gameReviewViewModel: GameReviewViewModel by viewModel<GameReviewViewModel>()

    private var analyticHelper: GameReviewAnalyticHelper<GameAppraiseData, ItemGameReviewAllBinding>? =
        null
    private var concatAdapter: ConcatAdapter? = null

    private val gameId: String by lazy { arguments?.getString(KEY_GAME_ID) ?: "0" }
    private val authorId: String by lazy { arguments?.getString(KEY_AUTHOR_ID) ?: "" }
    private val modeCode: Int by lazy { arguments?.getInt(KEY_MODE_CODE) ?: MODULE_TYPE_GAME_ONLY }

    private var queryType: Int = RequestGameReviewsParam.QUERY_TYPE_MOST_FAVORABLE

    private val dp12 = 12.dp

    private val reviewDecoration = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            outRect.bottom = dp12
        }
    }

    // 更多弹出框
    private lateinit var morePopUpWindow: PopupWindowCompat
    private lateinit var mostPopUpWindow: PopupWindowCompat
    private val popUpBinding by lazy { getPopupBinding() }
    private val popUpMostBinding by lazy { getPopupMostBinding() }

    companion object {

        const val REVIEW_AUTHOR = 0
        const val REVIEW_REPLY = 1
        const val TYPE_SORT_FAVORABLE = 0
        const val TYPE_SORT_RECENT = 1

        private const val KEY_GAME_PKG = "gamePkg"
        private const val KEY_GAME_ID = "gameId"
        private const val KEY_AUTHOR_ID = "authorId"
        private const val KEY_MODE_CODE = "modeCode"
        fun newInstance(
            gamePkg: String,
            gameId: String,
            authorId: String? = "",
            modeCode: Int? = MODULE_TYPE_GAME_ONLY,
            backColor: Int? = null,
            onFinishedCallback: (() -> Unit)? = null
        ) =
            ReviewListFragment().apply {
                arguments = Bundle().apply {
                    putString(KEY_GAME_PKG, gamePkg)
                    putString(KEY_GAME_ID, gameId)
                    putString(KEY_AUTHOR_ID, authorId)
                    putInt(KEY_MODE_CODE, modeCode ?: MODULE_TYPE_GAME_ONLY)
                }
                this.onFinishedCallback = onFinishedCallback
            }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentReviewListBinding? {
        return FragmentReviewListBinding.inflate(inflater, container, false)
    }

    override fun loadFirstData() {
        Timber.d("loadFirstData gameId=${gameId}")
        gameReviewViewModel.queryGameScoreData(GameSuggestionViewModel.TYPE_GAME, gameId)
        gameReviewViewModel.setGameId(
            gameId,
            RequestGameReviewsParam.QUERY_TYPE_MOST_FAVORABLE,
            modeCode
        )

    }

    private fun loadData() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            gameReviewViewModel.reviewList.collectLatest {
                gameReviewAdapter!!.submitData(it)
            }
        }
    }

    override fun init() {
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            Timber.d("handleOnBackPressed 22 ${gameReviewViewModel.appraiseFragmentLiveData.value}")
            gameReviewViewModel.updateAppraisePageVisible(false)
            onFinishedCallback?.invoke()
        }
        initView()
        initData()
    }

    private fun initView() {
        initReviewAdapter()
        initMorePopUp()
        initMostPopUp()
        if (modeCode == MODULE_TYPE_AI_BOT) {
            binding.titleBarReview.gone()
            binding.ivTitleIcon.gone()
            binding.reviewStatus.gone()
        } else {
            context?.let {
                Glide.with(this).load(gameId)
                    .placeholder(R.drawable.placeholder_corner_10)
                    .transform(RoundedCorners(ScreenUtil.dp2px(it, 10F)))
                    .into(binding.ivTitleIcon)
            }
            binding.reviewStatus.visible()
        }
        binding.dpbDownloadGame.setCurrentText(getString(R.string.play))

        initEvent()
        Analytics.track(EventConstants.GAME_REVIEW_SHOW) {
            putAll(params)
        }
    }

    private fun initEvent() {
        binding.rvReviews.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                analyticHelper?.onScrolled()
            }
        })
        binding.tvSortType.setOnAntiViolenceClickListener {
            Timber.d("tvSortType click")
            showMostPop(it)
        }
        requireActivity().supportFragmentManager.setFragmentResultListener(
            ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG,
            viewLifecycleOwner
        ) { key, bundle ->
            Timber.d("setFragmentResultListener key $key")
            if (key == ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG) {
                ReportReasonDialog.showReportSuccessDialog(this@ReviewListFragment)
                val reportTargetId = bundle.getString(ReportReasonDialog.REPORT_TARGET_ID)
                gameReviewViewModel.operatingItemReqId?.let { reportData ->
                    Timber.d("operatingItemReqId $reportData")
                    if (reportTargetId == reportData.commentId || reportTargetId == reportData.replyId) {
                        val position = gameReviewAdapter?.snapshot()
                            ?.indexOfFirst { it?.commentId == reportData.commentId } ?: return@let
                        if (reportData.type == TYPE_COMMENT) {
                            metaKV.reviewKv.saveReportCommentId(gameId, reportData.commentId)
                            shieldItem(position)
                        } else {
                            metaKV.reviewKv.saveReportCommentId(gameId, reportData.replyId ?: "")
                            shieldReplyItem(position)
                        }
                    }
                }
                gameReviewViewModel.operatingItemReqId = null
            }
        }
    }

    private fun rotateView() {
        val rotation = binding.ivArrow.rotation
        if (rotation == 180f) {
            binding.ivArrow.animate().rotation(0f)
        } else {
            binding.ivArrow.animate().rotation(180f)
        }
    }

    // TODO PopUpMoreMyReviewV2Binding
    private fun getPopupBinding(): PopUpMoreMyReviewBinding {
        return PopUpMoreMyReviewBinding.inflate(LayoutInflater.from(requireContext()))
    }

    private fun getPopupMostBinding(): PopUpMostFavorableReviewBinding {
        return PopUpMostFavorableReviewBinding.inflate(LayoutInflater.from(requireContext()))
    }

    /**
     * 点击更多的popWindow
     */
    private fun initMorePopUp() {
        morePopUpWindow = PopupWindowCompat(
            popUpBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
        }



        popUpBinding.root.setOnClickListener {
            morePopUpWindow.dismiss()
        }

        popUpBinding.tvReport.setOnAntiViolenceClickListener {
            gameReviewViewModel.operatingItemReqId?.let {
                val type = if (it.type == TYPE_COMMENT) {
                    ReportType.PgcReview
                } else {
                    ReportType.PgcReply
                }
                MetaRouter.Report.pgcCommentReport(
                    this@ReviewListFragment,
                    commentId = it.replyId ?: it.commentId,
                    type,
                    gameId = gameId
                )
                Analytics.track(EventConstants.EVENT_REVIEW_REPORT_CLICK) {
                    putAll(params)
                    "type" to it.type
                }
            }
            morePopUpWindow.dismiss()
        }
        popUpBinding.tvDelete.setOnAntiViolenceClickListener {
            gameReviewViewModel.operatingItemReqId?.let { p ->
                showDeleteEvaluationConfirmDialog(p)
            }
            morePopUpWindow.dismiss()
        }

    }

    /**
     * 点击排序的popWindow
     */
    private fun initMostPopUp() {
        if (queryType == RequestGameReviewsParam.QUERY_TYPE_MOST_FAVORABLE) {
            binding.tvSortType.text = requireContext().getString(R.string.most_favorable)
        } else {
            binding.tvSortType.text = requireContext().getString(R.string.most_recent)
        }
        mostPopUpWindow = PopupWindowCompat(
            popUpMostBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
        }
        mostPopUpWindow.setOnDismissListener {
            rotateView()
        }
        popUpMostBinding.root.setOnClickListener {
            mostPopUpWindow.dismiss()
        }
        popUpMostBinding.tvFavorable.setOnAntiViolenceClickListener {
            Timber.d("tvFavorable")
            binding.tvSortType.text = requireContext().getString(R.string.most_favorable)
            if (queryType != RequestGameReviewsParam.QUERY_TYPE_MOST_FAVORABLE) {
                queryType = RequestGameReviewsParam.QUERY_TYPE_MOST_FAVORABLE
                gameReviewViewModel.setGameId(gameId, queryType, modeCode)
                gameReviewAdapter?.refresh()
            }

            mostPopUpWindow.dismiss()
            Analytics.track(EventConstants.GAME_REVIEW_SORT_CLICK) {
                putAll(params)
                put("type", TYPE_SORT_FAVORABLE)
            }
        }
        popUpMostBinding.tvRecent.setOnAntiViolenceClickListener {
            Timber.d("tvRecent")
            binding.tvSortType.text = requireContext().getString(R.string.most_recent)
            if (queryType != RequestGameReviewsParam.QUERY_TYPE_MOST_RECENT) {
                queryType = RequestGameReviewsParam.QUERY_TYPE_MOST_RECENT
                gameReviewViewModel.setGameId(gameId, queryType, modeCode)
                gameReviewAdapter?.refresh()
            }
            mostPopUpWindow.dismiss()
            Analytics.track(EventConstants.GAME_REVIEW_SORT_CLICK) {
                putAll(params)
                put("type", TYPE_SORT_RECENT)
            }
        }

    }

    private fun showMoreDialog(
        view: View,
        commentId: String,
        replyId: String?,
        type: Int,
        isMe: Boolean
    ) {
        gameReviewViewModel.operatingItemReqId = ReportData(type, commentId, replyId)
        updatePopUpData(isMe)
        popUpBinding.llMore.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        // 展示小弹窗
        val offset = popUpBinding.llMore.measuredWidth - 22.dp - view.measuredWidth
        morePopUpWindow.showAsDropDownByLocation(view, -offset, -(20).dp)
    }

    private fun showMostPop(view: View) {
        Timber.d("showMostPop")
        rotateView()
        popUpMostBinding.llMost.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val offset = popUpMostBinding.llMost.measuredWidth - 32.dp - view.measuredWidth
        mostPopUpWindow.showAsDropDownByLocation(view, -offset, -(10).dp)
    }

    private fun updatePopUpData(isMe: Boolean) {
        popUpBinding.tvReport.isVisible = !isMe
        popUpBinding.tvDelete.isVisible = isMe
    }

    private fun updateMyReviewView(data: GameAppraiseData?) {
        val list = if (data != null) listOf(data) else emptyList()
        selfReviewAdapter.setList(list)
        analyticHelper?.updateHeaderCount(list.size)
    }

    private fun initData() {
        analyticHelper = GameReviewAnalyticHelper(
            ScreenUtil.getScreenHeight(requireContext()),
            viewLifecycleOwner,
            binding.rvReviews,
            0,
            1,
            gameReviewAdapter
        ) {
            Timber.d("GameReviewAnalyticHelper ${it?.commentId}")
        }

        gameReviewViewModel.attitudeLiveData.observe(viewLifecycleOwner) { result ->
            Timber.d("attitudeLiveData result $result")
            result ?: return@observe
            if (result.isMe) {
                gameReviewViewModel.fetchMyReviewData(gameId, modeCode)
            } else {
                val position =
                    gameReviewAdapter!!.snapshot().indexOfFirst { it?.commentId == result.commentId }
                if (position >= 0) {
                    gameReviewAdapter!!.peek(position)?.opinion = result.nowAttitude
                    gameReviewAdapter!!.peek(position)?.likeCount = result.nowLikeCount
                    gameReviewAdapter!!.notifyItemChanged(
                        position,
                        GameAppraiseAdapter.PAYLOAD_COMMENT_OPINION
                    )
                }
            }
            if (!result.isSuccess) {
                ToastUtil.showShort(
                    requireContext(),
                    result.message ?: getString(R.string.common_error)
                )
            }
        }

        gameReviewViewModel.gameScoreResultLiveData.observe(viewLifecycleOwner) {
            it?.let { result ->
                val allRating = result.ratingData.values.sum()
                val ratingData5 = result.ratingData[5]?.div(allRating)?.times(100) ?: 40
                val ratingData4 = result.ratingData[4]?.div(allRating)?.times(100) ?: 30
                val ratingData3 = result.ratingData[3]?.div(allRating)?.times(100) ?: 20
                val ratingData2 = result.ratingData[2]?.div(allRating)?.times(100) ?: 10
                val ratingData1 = result.ratingData[1]?.div(allRating)?.times(100) ?: 0
                binding.includeRating.apply {
                    tvScore.text = result.getFormatAvg()
                    pbProportion5.progress = ratingData5.toInt()
                    pbProportion4.progress = ratingData4.toInt()
                    pbProportion3.progress = ratingData3.toInt()
                    pbProportion2.progress = ratingData2.toInt()
                    pbProportion1.progress = ratingData1.toInt()
                }
            }
        }

        loadData()

        gameReviewViewModel.reviewsCountLiveData.observe(viewLifecycleOwner) {
            if (it > 0) {
                binding.tvRatingNum.visibility = View.VISIBLE
                binding.tvRatingNum.text = UnitUtil.formatKMCount(it ?: 0L)
            } else {
                binding.tvRatingNum.visibility = View.INVISIBLE
                binding.tvRatingNum.text = "0"
            }
        }

        gameReviewViewModel.replyList.observe(viewLifecycleOwner) { data ->
            val commentId = data.first
            val result = data.second
            val replyList = result?.dataList
            if (replyList.isNullOrEmpty()) {
                return@observe
            }
            val position = gameReviewAdapter!!.snapshot().indexOfFirst { it?.commentId == commentId }
            Timber.d("replyList position$position")
            if (position >= 0) {
                val replyPage = gameReviewAdapter!!.peek(position)?.replyCommonPage
                replyPage?.dataList?.addAll(replyList)
                val count = replyPage?.total ?: 0
                replyPage?.total = count + 1
                replyPage?.end = result.end
                gameReviewAdapter!!.notifyItemChanged(
                    position,
                    GameAppraiseAdapter.ADD_MORE_REPLY
                )
            }
        }

        gameReviewViewModel.myReviewLiveData.observe(viewLifecycleOwner) {
            updateMyReviewView(it)
            if (it != null) {
                binding.loading.hide()
            }
        }

        gameReviewViewModel.myReplyInfo.observe(viewLifecycleOwner) { data ->
            val pos = data.first
            val result = data.second ?: return@observe
            val position =
                gameReviewAdapter!!.snapshot().indexOfFirst { it?.commentId == result.commentId }
            Timber.d("replyList position$position")
            if (position >= 0) {
                gameReviewAdapter!!.peek(position)?.let {
                    it.temporaryDataList = arrayListOf()
                    it.temporaryDataList.add(result)
                }
                gameReviewAdapter!!.peek(position)?.replyCommonPage?.dataList?.add(result)
                gameReviewAdapter!!.notifyItemChanged(
                    position,
                    GameAppraiseAdapter.add_SELF_REPLY
                )
            }
        }

        gameReviewViewModel.deleteReviewLiveData.observe(viewLifecycleOwner) {
            it ?: return@observe
            val isSuccess = it.first
            val message = it.second
            Timber.d("deleteReviewLiveData isSuccess$isSuccess")
            if (isSuccess) {
                gameReviewAdapter?.refresh()
                (parentFragment as? IGameReviewCommon)?.onDeleteReview(it)
            } else {
                toast(message)
            }
            gameReviewViewModel.clearDeleteReviewData()
        }
        gameReviewViewModel.deleteReplyLiveData.observe(viewLifecycleOwner) { data ->
            val commentId = data.first
            val replyId = data.second ?: return@observe
            val position = gameReviewAdapter!!.snapshot().indexOfFirst { it?.commentId == commentId }
            if (position >= 0) {
                val replyPosition =
                    gameReviewAdapter!!.peek(position)?.replyCommonPage?.dataList?.indexOfFirst { it.replyId == replyId }
                deleteReplayItem(position, replyPosition ?: -1)
            }
        }


    }

    private fun deleteReplayItem(position: Int, replyPosition: Int) {
        if (replyPosition >= 0) {
            gameReviewAdapter!!.peek(position)?.replyCommonPage?.dataList?.removeAt(replyPosition)
            val count = gameReviewAdapter!!.peek(position)?.replyCommonPage?.total ?: 0
            gameReviewAdapter!!.peek(position)?.replyCommonPage?.total = (count - 1)
            gameReviewAdapter!!.notifyItemChanged(
                position,
                GameAppraiseAdapter.DELETE_REPLY
            )
        }

    }

    private fun initReviewAdapter() {

        gameReviewAdapter = GameAppraiseAdapter(
            Glide.with(this),
            uuid,
            gameId,
            metaKV.reviewKv,
            authorId,
            appraiseItemListener, false
        )
        val loadMoreAdapter = HomeLoadMoreFooterAdapter {
            gameReviewAdapter!!.retry()
        }
        gameReviewAdapter!!.apply {
            withStatusAndRefresh(viewLifecycleOwner, binding.loading, null, getString(R.string.no_other_reviews))
            addLoadStateListener { loadStates ->
                loadMoreAdapter.loadState = loadStates.append
            }
        }
        concatAdapter = ConcatAdapter(gameReviewAdapter, loadMoreAdapter)
        binding.rvReviews.addItemDecoration(reviewDecoration)
        binding.rvReviews.adapter = concatAdapter

        val params = binding.rvReviews.layoutParams
        params.height = ScreenUtil.getScreenHeight(requireContext())
        binding.rvReviews.layoutParams = params
    }

    private fun shieldItem(position: Int) {
        Timber.d("shieldItem position $position")
        if (position >= 0) {
            gameReviewAdapter!!.notifyItemChanged(
                position,
                GameAppraiseAdapter.SHIELD_OR_REPORT_OPINION
            )
        }
    }

    private fun shieldReplyItem(position: Int) {
        Timber.d("shieldReplyItem position $position")
        if (position >= 0) {
            gameReviewAdapter!!.notifyItemChanged(
                position,
                GameAppraiseAdapter.SHIELD_OR_REPORT_REPLY_OPINION
            )
        }
    }

    /**
     * 删除自己的游戏评论确认弹窗
     */
    private fun showDeleteEvaluationConfirmDialog(reportData: ReportData) {
        val reviewid = reportData.replyId ?: reportData.commentId
        ConfirmDialog.Builder(this)
            .content(getString(R.string.confirm_delete_evaluation))
            .cancelBtnTxt(getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.delete_cap), lightBackground = true)
            .isRed(true)
            .cancelCallback {
                Analytics.track(EventConstants.YOUR_REVIEW_DELETE_CANCEL) {
                    putAll(params)
                    put("reviewid", reviewid)
                    put("type", reportData.type)
                }
                Timber.d("deleteMyReview cancel")
            }
            .confirmCallback {
                Timber.d("deleteMyReview $reviewid")
                if (reportData.type == TYPE_COMMENT) {
                    gameReviewViewModel.deleteMyReview(reportData.commentId, gameId)
                }
                if (reportData.type == TYPE_REPLAY) {
                    gameReviewViewModel.deleteMyReply(reportData.replyId ?: "", reportData.commentId)
                }
                Analytics.track(EventConstants.MY_GAME_REVIEW_DELETE_SUCCESS) {
                    putAll(params)
                    put("reviewid", reviewid)
                    put("type", reportData.type)
                }
                Analytics.track(EventConstants.YOUR_REVIEW_DELETE) {
                    putAll(params)
                    put("reviewid", reviewid)
                    put("type", reportData.type)
                }
            }
            .navigate(requireActivity(), "showDeleteEvaluationConfirmDialog")
    }

    override fun onDestroyView() {
        analyticHelper?.clear()
        analyticHelper = null
        clearFragmentResultListener(ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG)
        binding.rvReviews.adapter = null
        gameReviewAdapter?.setOnItemChildClickListener(null)
        mostPopUpWindow.dismiss()
        super.onDestroyView()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_REVIEW_LIST
}