package com.socialplay.gpark.ui.editor.module

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.outfit.UgcDesignFeedRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcModuleMyWorkSortState(
    val tags: Async<List<UgcDesignProfileTag>> = Uninitialized,
    val orderType: Int = UgcDesignFeedRequest.ORDER_DEFAULT,
    val updateFlag: Boolean = false,
    val filterVisibility: Boolean? = null
) : MavericksState

class UgcModuleMyWorkSortViewModel(
    initState: UgcModuleMyWorkSortState
) : BaseViewModel<UgcModuleMyWorkSortState>(initState) {

    private val filterTags = HashSet<Int>()

    val tags get() = filterTags.toList()

    fun init(vm: UgcModuleMyWorkViewModel) {
        val s = vm.oldState
        filterTags.clear()
        filterTags.addAll(s.filterTags)
        setState {
            copy(
                tags = s.tags,
                orderType = s.orderType,
                filterVisibility = s.filterVisibility,
                updateFlag = !updateFlag
            )
        }
    }

    fun updateTags(tags: Async<List<UgcDesignProfileTag>>) = withState { s ->
        setState { copy(tags = tags) }
    }

    fun select(entrance: Int, tag: Int, idx: Int, content: String) = withState { s ->
        when (entrance) {
            UgcAssetEntrance.ENTRANCE_SORT -> {
                if (s.orderType != tag) {
                    setState { copy(orderType = tag, updateFlag = !updateFlag) }
                }
            }

            UgcAssetEntrance.ENTRANCE_VISIBILITY -> {
                val visibility = tag == 0
                Analytics.track(
                    EventConstants.MOD_PUBLIC_CLASSIFICATION_CHOICE_CLICK,
                    "result" to (if (visibility) "public" else "private")
                )
                if (oldState.filterVisibility == visibility) {
                    setState { copy(filterVisibility = null, updateFlag = !updateFlag) }
                } else {
                    setState { copy(filterVisibility = visibility, updateFlag = !updateFlag) }
                }
            }

            else -> {
                Analytics.track(
                    EventConstants.MOD_CLASSIFICATION_CHOICE_CLICK,
                    "result" to content
                )
                if (!filterTags.remove(tag)) {
                    filterTags.add(tag)
                }
                setState { copy(updateFlag = !updateFlag) }
            }
        }
    }

    fun isSelected(entrance: Int, tag: Int): Boolean {
        return when (entrance) {
            UgcAssetEntrance.ENTRANCE_SORT -> {
                oldState.orderType == tag
            }

            UgcAssetEntrance.ENTRANCE_VISIBILITY -> {
                val visibility = tag == 0
                oldState.filterVisibility == visibility
            }

            else -> {
                filterTags.contains(tag)
            }
        }
    }

    companion object : KoinViewModelFactory<UgcModuleMyWorkSortViewModel, UgcModuleMyWorkSortState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcModuleMyWorkSortState
        ): UgcModuleMyWorkSortViewModel {
            return UgcModuleMyWorkSortViewModel(state)
        }
    }
}