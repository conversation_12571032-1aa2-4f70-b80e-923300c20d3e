package com.socialplay.gpark.ui.editor.module.guide

import android.os.Bundle
import android.view.WindowManager
import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.databinding.DialogUgcModuleAddGuideBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.main.MainAddDialog
import com.socialplay.gpark.ui.main.MainFragment
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import timber.log.Timber

class UgcModuleAddGuideDialog : BaseDialogFragment(), IDialogManager {

    override val binding by viewBinding(DialogUgcModuleAddGuideBinding::inflate)
    private val accountInteractor: AccountInteractor by inject()
    var onDismissCallback: ((Boolean) -> Unit)? = null

    override fun init() {
        binding.ivAdd.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.LOOP_GUIDE_ADD_CLICK
            )
            willNestDialog = true
            dismissAllowingStateLoss()
        }
        binding.root.setOnClickListener {
            onDismissCallback?.invoke(true)
            onDismissCallback = null
            dismissAllowingStateLoss()
        }
    }

    override fun dimAmount() = 0.6f
    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override fun getStyle() = R.style.DialogStyleNonFullScreen
    override fun onBackPressed() = nestFading
    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback.invoke(true)
    }

    override fun afterNestFaded() {
        parentFragment?.let {
            if (it.canShowDialog) {
                Analytics.track(
                    EventConstants.LOOP_GUIDE_SECOND_SHOW
                )
                MainAddDialog.show(
                    it,
                    GuideInfo(null, null, null, GuideInfo.TYPE_MODULE_RECYCLE),
                    onDismissCallback
                )
            }
        }
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        val isNeedShow = accountInteractor.moduleGuideStatus == BaseAccountInteractor.MODULE_GUIDE_STATUS_NEWBIE
        needShowCallback(isNeedShow)
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        if (fragment is MainFragment) {
            Analytics.track(
                EventConstants.LOOP_GUIDE_FIRST_SHOW
            )
            this.onDismissCallback = onDismissCallback
            show(fragment.childFragmentManager, "UgcModuleAddGuideDialog")
            accountInteractor.moduleGuideStatus = BaseAccountInteractor.MODULE_GUIDE_STATUS_CYCLE
        } else {
            Timber.d("UgcModuleAddGuideDialog show fail,because fragment is not MainFragment")
        }
    }

    override fun exeDismiss() {
        this.dismissAllowingStateLoss()
    }
}