package com.socialplay.gpark.ui.editor.module

import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.activity.addCallback
import androidx.core.view.isVisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.databinding.FragmentUgcModuleTabBinding
import com.socialplay.gpark.databinding.TabIndicatorUgcModuleBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize

@Parcelize
data class UgcModuleTabArgs(
    val fromGuide: Int = GUIDE_TYPE_NONE
) : Parcelable {

    companion object {
        const val GUIDE_TYPE_NONE = 0
        const val GUIDE_TYPE_NEWBIE = 1
    }

    val showNewbieGuide get() = fromGuide == GUIDE_TYPE_NEWBIE
}

class UgcModuleTabFragment :
    BaseFragment<FragmentUgcModuleTabBinding>(R.layout.fragment_ugc_module_tab) {

    private val vm: UgcModuleTabViewModel by fragmentViewModel()

    private var onBackPressedCallback: OnBackPressedCallback? = null

    private val args by args<UgcModuleTabArgs>()

    private var needCheckGuide = true

    private val tabListener = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab?) {
            tab ?: return
            val tabPosition = tab.position
            val isMyWorkTab = tabPosition == 1
            binding.ivSortBtn.visible(isMyWorkTab)
            if (isMyWorkTab) {
                Analytics.track(
                    EventConstants.MOD_PUBLISHED_TAB_VIEW
                )
            }
            updateTabView(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab?) {
            tab ?: return
            updateTabView(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab?) {}
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcModuleTabBinding? {
        return FragmentUgcModuleTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tbl.setOnBackAntiViolenceClickedListener {
            back()
        }
        binding.ivSortBtn.setOnAntiViolenceClickListener {
            val rect = Rect()
            binding.tl.getTabAt(0)?.customView?.getGlobalVisibleRect(rect)
            vm.invokeEvent(UgcModuleTabViewModel.EVENT_CLICK_SORT, rect)
        }
        binding.vp.adapterAllowStateLoss = CommonTabStateAdapter(
            listOf({
                UgcModuleHomeFragment().apply {
                    arguments = <EMAIL>
                }
            }, {
                UgcModuleMyWorkFragment()
            }),
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        binding.tl.addOnTabSelectedListener(viewLifecycleOwner, tabListener)
        TabLayoutMediator(binding.tl, binding.vp) { tab, position ->
            val tabViewBinding = TabIndicatorUgcModuleBinding.inflate(layoutInflater)
            val titleRes = if (position == 0) {
                R.string.ugc_module_tab_template
            } else {
                R.string.ugc_module_tab_my_work
            }
            val title = getString(titleRes)
            tabViewBinding.tvNormal.text = title
            tabViewBinding.tvSelected.text = title
            tab.customView = tabViewBinding.root
        }.attach(viewLifecycleOwner)
        vm.onEach(UgcModuleTabState::event, deliveryMode = uniqueOnly()) {
            when (it?.first) {
                UgcModuleTabViewModel.EVENT_GUIDE -> {
                    val rect = it.third as? Rect ?: return@onEach
                    initGuide(rect)
                }

                UgcModuleTabViewModel.EVENT_HIDE_MASK -> {
                    hideMask()
                }
            }
        }
        needCheckGuide = true
    }

    private fun initModuleGuideDialog() {
        if (!needCheckGuide) return
        needCheckGuide = false
        if (!PandoraToggle.enableModuleGuideTemplatePage) return
        if (vm.accountInteractor.moduleGuideStatus > BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO) return
        if (!vm.showGuide) return
        if (args.showNewbieGuide) return
        UgcModuleGuideDialog.show(this, PandoraToggle.MODULE_GUIDE_TEMPLATE_PAGE) {
            if (it) {
                vm.showGuide = false
            }
        }
    }

    private fun initGuide(rect: Rect) {
        Analytics.track(
            EventConstants.BUILD_NEWBIE_GUIDE_BUILD_MODULE_SHOW
        )
        val rectRoot = Rect()
        binding.root.getGlobalVisibleRect(rectRoot)
        binding.ivGuideFigure.setMargin(top = rect.bottom - dp(24))
        binding.tvGuideContent.setMargin(top = rect.bottom + dp(48))
        binding.ivGuideTri.setMargin(top = rect.bottom + dp(62))
        binding.vGuideClickArea.setMargin(top = rect.top)
        binding.vGuideClickArea.setHeight(rect.height())
        binding.flGuide.visible()
        binding.flGuide.alpha = 0.0f
        binding.mlGuide.setClipArea(
            rect.left - dp(8),
            rect.top - dp(8),
            rect.right + dp(8),
            rect.bottom + dp(8),
            rx = dp(16).toFloat(),
            ry = dp(16).toFloat(),
        )
        binding.vGuideClickArea.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.BUILD_NEWBIE_GUIDE_BUILD_MODULE_START_CLICK
            )
            vm.invokeEvent(UgcModuleTabViewModel.EVENT_START_TEMPLATE)
        }
        navColorRes = R.color.black_60
        requireActivity().window.navColor = getColorByRes(navColorRes)
        binding.flGuide.animate().alpha(1.0f)
    }

    private fun updateTabView(tab: TabLayout.Tab, isSelected: Boolean) {
        val cv = tab.customView ?: return
        TabIndicatorUgcModuleBinding.bind(cv).apply {
            tvNormal.invisible(isSelected)
            tvSelected.invisible(!isSelected)
        }
    }

    private fun hideMask() {
        navColorRes = R.color.white
        requireActivity().window.navColor = getColorByRes(navColorRes)
        binding.flGuide.gone()
    }

    private fun back() {
        if (binding.flGuide.isVisible) {
            return
        }
        navigateUp()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_MODULE_TAB

    override fun onResume() {
        super.onResume()
        onBackPressedCallback =
            requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
                back()
            }
        initModuleGuideDialog()
    }

    override fun onPause() {
        onBackPressedCallback?.remove()
        onBackPressedCallback = null
        super.onPause()
    }
}