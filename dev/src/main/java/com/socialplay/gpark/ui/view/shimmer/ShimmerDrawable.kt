package com.socialplay.gpark.ui.view.shimmer

import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.LinearGradient
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.Rect
import android.graphics.Shader
import android.graphics.drawable.Drawable
import kotlin.math.sin

class ShimmerDrawable : Drawable() {
    private val updateListener = AnimatorUpdateListener { invalidateSelf() }
    private val shimmerPaint = Paint()
    private val drawRect = Rect()
    private val shaderMatrix = Matrix()

    private val positions = FloatArray(COMPONENT_COUNT)
    private val colors = IntArray(COMPONENT_COUNT)

    private var valueAnimator: ValueAnimator? = null

    private var shimmerWidth: Float = 0F
    private val shimmerDegrees = 30

    init {
        shimmerPaint.isAntiAlias = true
        updateShader()
        updateValueAnimator()
        invalidateSelf()
        updateColors()
        updatePositions()
    }

    fun setShimmerWidth(shimmerWidth: Float) {
        this.shimmerWidth = shimmerWidth
    }

    /** Starts the shimmer animation.  */
    fun startShimmer() {
        if (valueAnimator != null && !isShimmerStarted && callback != null) {
            valueAnimator!!.start()
        }
    }

    /** Stops the shimmer animation.  */
    fun stopShimmer() {
        if (valueAnimator != null && isShimmerStarted) {
            valueAnimator!!.cancel()
        }
    }

    private val isShimmerStarted: Boolean
        /** Return whether the shimmer animation has been started.  */
        get() = valueAnimator != null && valueAnimator!!.isStarted

    public override fun onBoundsChange(bounds: Rect) {
        super.onBoundsChange(bounds)
        val width = bounds.width()
        val height = bounds.height()
        drawRect.set(0, 0, width, height)

        updateShader()
        maybeStartShimmer()
    }

    override fun draw(canvas: Canvas) {
        if (shimmerPaint.shader == null) {
            return
        }
        val tiltTan = (sin(Math.toRadians(shimmerDegrees.toDouble())) * drawRect.height()).toFloat()

        val translateWidth = drawRect.width() + tiltTan
        val animatedValue = if (valueAnimator != null) valueAnimator!!.animatedFraction else 0f


        val dx= offset(-tiltTan, translateWidth, animatedValue)

        shaderMatrix.reset()
        shaderMatrix.setRotate(shimmerDegrees.toFloat(), drawRect.width() / 2f, drawRect.height() / 2f)

        shaderMatrix.postTranslate(dx, 0F)
        shimmerPaint.shader.setLocalMatrix(shaderMatrix)

        canvas.save()
        canvas.drawRect(drawRect, shimmerPaint)
        canvas.restore()
    }

    override fun setAlpha(alpha: Int) {
        // No-op, modify the Shimmer object you pass in instead
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        // No-op, modify the Shimmer object you pass in instead
    }

    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }

    private fun offset(start: Float, end: Float, percent: Float): Float {
        return start + (end - start) * percent
    }

    private fun updateValueAnimator() {
        val started: Boolean
        if (valueAnimator != null) {
            started = valueAnimator!!.isStarted
            valueAnimator!!.cancel()
            valueAnimator!!.removeAllUpdateListeners()
        } else {
            started = false
        }
        valueAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            repeatMode = ValueAnimator.RESTART
            repeatCount = ValueAnimator.INFINITE
            duration = 1600
            addUpdateListener(updateListener)

            if (started) {
                start()
            }
        }
    }

    private fun maybeStartShimmer() {
        if (valueAnimator != null && !valueAnimator!!.isStarted && callback != null) {
            valueAnimator!!.start()
        }
    }

    private fun updateColors() {
        colors[0] = Color.TRANSPARENT
        colors[1] = 0xCCFFFFFF.toInt()
        colors[2] = Color.TRANSPARENT
    }

    private fun updatePositions() {
        positions[0] = 0F
        positions[1] = 0.5F
        positions[2] = 1F
    }

    private fun updateShader() {
        val shader: Shader = LinearGradient(
            0f,
            0f,
            shimmerWidth,
            0F,
            colors,
            positions,
            Shader.TileMode.CLAMP
        )
        shimmerPaint.setShader(shader)
    }

    companion object {
        private const val COMPONENT_COUNT = 3
    }
}
