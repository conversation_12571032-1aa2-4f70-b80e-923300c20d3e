package com.socialplay.gpark.ui.gamedetail.sendflower

import android.os.Bundle
import androidx.annotation.StyleRes
import androidx.core.net.toUri
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.gift.SendGiftCondition
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.databinding.DialogSendFlowerConditionBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

/**
 * 弹出条件:
 * 用户主态首次进入游戏详情, 不满足赠礼条件, 或者满足赠礼条件切赠礼条件开启时
 */
class SendFlowerConditionDialog : BaseRecyclerViewDialogFragment(), IDialogManager {
    private val h5PageConfigInteractor: H5PageConfigInteractor = GlobalContext.get().get<H5PageConfigInteractor>()
    private val viewModel: SendFlowerConditionViewModel by fragmentViewModel()
    private val metaKV: MetaKV = GlobalContext.get().get<MetaKV>()
    private val api: IMetaRepository = GlobalContext.get().get<IMetaRepository>()
    private val args by navArgs<SendFlowerConditionDialogArgs>()
    private var onDismissCallback: ((Boolean) -> Unit)? = null
    override val binding: DialogSendFlowerConditionBinding by viewBinding(
        DialogSendFlowerConditionBinding::inflate
    )
    override val recyclerView: EpoxyRecyclerView get() = binding.rvConditions

    override fun getFragmentName() = PageNameConstants.DIALOG_SEND_FLOWER_CONDITIONS

    override fun isClickOutsideDismiss() = false

    override fun maxWidth(): Int {
        val ctx = context
        ctx ?: return 0
        return (ScreenUtil.getScreenWidth(ctx) * 2 / 3).coerceAtLeast(dp(344))
            .coerceAtMost(ScreenUtil.getScreenWidth(ctx) - dp(30))
    }

    @StyleRes
    override fun getStyle() = R.style.DialogStyleFloating

    companion object {
        const val FEATURE_UNSUPPORTED = 1
        const val FEATURE_OPEN = 2
        const val FEATURE_CLOSE = 3
        const val REQUEST_KEY_SEND_FLOWER_CONDITIONS_INFO = "request_key_send_flower_conditions_info"
        const val KEY_SEND_FLOWER_CONDITIONS_INFO = "key_send_flower_conditions_info"

        fun show(
            fragment: Fragment,
            gameId: String,
            gameType: Int,
            conditionsInfo: SendGiftConditionsInfo? = null,
        ) {
            SendFlowerConditionDialog().apply {
                arguments = SendFlowerConditionDialogArgs(
                    gameId = gameId,
                    gameType = gameType,
                    conditionsInfo = conditionsInfo,
                ).toBundle()
            }.show(fragment.childFragmentManager, "SendFlowerConditionDialog")
        }
    }

    private var hasJumpScheme = false
    private val conditionJumpClickedListener = object : ISendFlowerConditionJumpClickedListener {
        override fun onClicked(condition: SendGiftCondition) {
            val schema = condition.schema
            if (schema != null) {
                val schemaLink = schema.link ?: return
                val schemaUri = runCatching { schemaLink.toUri() }.getOrNull() ?: return
                MetaRouter.Scheme.jumpScheme(
                    this@SendFlowerConditionDialog,
                    schemaUri,
                    getFragmentName()
                )
                hasJumpScheme = true
            }
        }
    }

    override fun onResume() {
        if (hasJumpScheme) {
            // 从绑定账号页面回到当前页面后, 需要重新刷新数据
            hasJumpScheme = false
            viewModel.loadSendGiftConditions(args.gameId)
        }
        super.onResume()
    }

    override fun init() {
        binding.ivClose.setOnAntiViolenceClickListener {
            safeDismiss()
        }
        binding.tvOk.setOnAntiViolenceClickListener {
            safeDismiss()
        }
        binding.tvTurnOff.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_CONDITION_CLICK,
                "gameid" to args.gameId,
                "creatortype" to args.gameType.toString(),
                "pop_type" to "opening",
                "click" to "turn_off",
            )
            // 关闭送花功能
            showDisableConfirmDialog()
        }
        binding.tvEnableFeature.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_CONDITION_CLICK,
                "gameid" to args.gameId,
                "creatortype" to args.gameType.toString(),
                "pop_type" to "closing",
                "click" to "open",
            )
            // 打开送花功能
            viewModel.switchSendGift(args.gameId, true)
        }

        binding.loadingView.setRetry {
            viewModel.loadSendGiftConditions(args.gameId)
        }

        viewModel.onAsync(
            SendFlowerConditionState::switchGiftEnableResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                }
            },
            onLoading = {
                // ignore
            }
        ) { resultPair ->
            val switchSuccess = resultPair.second.succeeded
            if (resultPair.first) {
                // 尝试打开送花功能
                if (switchSuccess) {
                    toast(R.string.toast_flower_gifting_enable_success)
                    safeDismiss()
                } else {
                    toast(R.string.toast_flower_gifting_enable_failed)
                }
            } else {
                // 尝试关闭送花功能
                if (switchSuccess) {
                    toast(R.string.toast_flower_gifting_disable_success)
                    safeDismiss()
                } else {
                    toast(R.string.toast_flower_gifting_disable_failed)
                }
            }
        }

        viewModel.onAsync(
            SendFlowerConditionState::conditionsInfo,
            onLoading = { _ ->
                // 网络请求过程中
                binding.loadingView.visible()
                binding.loadingView.showLoading()
            },
            onFail = { _, _ ->
                // 网络请求失败
                binding.loadingView.visible()
                binding.loadingView.showError()
            }) { conditionsInfo ->
            binding.loadingView.gone()
            binding.tvTitle.visible(true)
            val feature = if (conditionsInfo.flag == true) {
                // 条件满足, 并且送花条件开启
                binding.tvTitle.setText(R.string.dialog_send_flowers_feature_title)
                metaKV.account.showSendFlowerFeatureDialog = false
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_FLOWER_CONDITION_SHOW,
                    "gameid" to args.gameId,
                    "creatortype" to args.gameType.toString(),
                    "pop_type" to "opening",
                )
                FEATURE_OPEN
            } else if (conditionsInfo.met == true) {
                // 条件满足, 只是被用户手动关闭了送花功能
                binding.tvTitle.setText(R.string.dialog_send_flowers_feature_disable_title)
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_FLOWER_CONDITION_SHOW,
                    "gameid" to args.gameId,
                    "creatortype" to args.gameType.toString(),
                    "pop_type" to "closing",
                )
                FEATURE_CLOSE
            } else {
                // 条件不满足, 展示所有的送礼条件
                binding.tvTitle.setText(R.string.dialog_send_flowers_conditions_title)
                metaKV.account.showSendFlowerConditionsDialog = false
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_FLOWER_CONDITION_SHOW,
                    "gameid" to args.gameId,
                    "creatortype" to args.gameType.toString(),
                    "pop_type" to "unqualified",
                )
                FEATURE_UNSUPPORTED
            }
            binding.layoutConditionContent.visible(feature == FEATURE_UNSUPPORTED)
            binding.tvConditionGiftingTerms.visible(feature == FEATURE_UNSUPPORTED)
            bindingConditionGiftingTerms(feature == FEATURE_UNSUPPORTED)
            binding.tvSendFlowerEnableDesc1.visible(feature == FEATURE_OPEN)
            binding.tvSendFlowerEnableDesc2.visible(feature == FEATURE_OPEN)
            bindingSendFlowerEnableDesc2(feature == FEATURE_OPEN)
            binding.tvSendFlowerDisableDesc1.visible(feature == FEATURE_CLOSE)
            binding.tvSendFlowerDisableDesc2.visible(feature == FEATURE_CLOSE)
            binding.tvSendFlowerDisableDesc3.visible(feature == FEATURE_CLOSE)
            bindingSendFlowerDisableDesc3(feature == FEATURE_CLOSE)
            binding.tvOk.visible(feature == FEATURE_UNSUPPORTED || feature == FEATURE_OPEN)
            binding.tvEnableFeature.visible(feature == FEATURE_CLOSE)
            binding.tvTurnOff.visible(feature == FEATURE_OPEN)
        }
        if (args.conditionsInfo == null) {
            viewModel.loadSendGiftConditions(args.gameId)
        } else {
            viewModel.updateGiftConditionsInfo(args.conditionsInfo!!)
        }
    }

    private fun showDisableConfirmDialog() {
        ListDialog()
            .title(
                title = getString(R.string.dialog_disable_send_flowers_feature_confirm_title),
                marginTop = 20.dp
            )
            .content(
                content = getString(R.string.dialog_disable_send_flowers_feature_confirm_content),
                marginTop = 8.dp
            )
            .list(
                mutableListOf(
                    SimpleListData(
                        getString(R.string.dialog_confirm),
                        R.drawable.selector_button_warn,
                    ),
                    SimpleListData(getString(R.string.dialog_cancel))
                )
            ).clickCallback {
                if (it?.text.equals(getString(R.string.dialog_cancel))) {
                    // 点击取消按钮, 不做任何事
                } else if (it?.text.equals(getString(R.string.dialog_confirm))) {
                    // 点击确认按钮
                    viewModel.switchSendGift(args.gameId, false)
                }
            }.show(childFragmentManager, "SendFlowerConditionDialog")
    }

    /**
     * 赠花条件不满足时, 底部的赠花协议数据绑定
     */
    private fun bindingConditionGiftingTerms(enable: Boolean) {
        if (!enable) {
            return
        }
        val termsText = getString(R.string.dialog_send_flowers_conditions_terms)
        val split = termsText.split("%s")
        if (split.isNotEmpty()) {
            val builder = SpannableHelper.Builder()
                .text(split[0])
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_1A1A1A)
                .text(getString(R.string.dialog_send_flowers_terms_link))
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_9242FF)
                .click {
                    val item = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_FEATURE_URL)
                    MetaRouter.Web.navigate(
                        this,
                        title = item.title,
                        url = item.url,
                    )
                }
                .underline()
            if (split.size > 1) {
                builder.text(split[1])
                    .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                    .colorRes(R.color.color_1A1A1A)
            }
            binding.tvConditionGiftingTerms.movementMethod =
                InterceptClickEventLinkMovementMethod(binding.tvConditionGiftingTerms)
            binding.tvConditionGiftingTerms.text = builder.build()
        }
    }

    /**
     * 赠花条件不满足时, 底部的赠花协议数据绑定
     */
    private fun bindingSendFlowerEnableDesc2(enable: Boolean) {
        if (!enable) {
            return
        }
        val termsText = getString(R.string.dialog_send_flowers_feature_text2)
        val split = termsText.split("%s")
        if (split.isNotEmpty()) {
            val builder = SpannableHelper.Builder()
                .text(split[0])
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_1A1A1A)
                .text(getString(R.string.dialog_send_flowers_terms_link))
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_9242FF)
                .click {
                    val item = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_FEATURE_URL)
                    MetaRouter.Web.navigate(
                        this,
                        title = item.title,
                        url = item.url,
                    )
                }
                .underline()
            if (split.size > 1) {
                builder.text(split[1])
                    .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                    .colorRes(R.color.color_1A1A1A)
            }
            binding.tvSendFlowerEnableDesc2.movementMethod =
                InterceptClickEventLinkMovementMethod(binding.tvSendFlowerEnableDesc2)
            binding.tvSendFlowerEnableDesc2.text = builder.build()
        }
    }

    private fun bindingSendFlowerDisableDesc3(enable: Boolean) {
        if (!enable) {
            return
        }
        val termsText = getString(R.string.dialog_send_flowers_feature_disable_text3)
        val split = termsText.split("%s")
        if (split.isNotEmpty()) {
            val builder = SpannableHelper.Builder()
                .text(split[0])
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_1A1A1A)
                .text(getString(R.string.dialog_send_flowers_terms_link))
                .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                .colorRes(R.color.color_9242FF)
                .click {
                    val item = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_FEATURE_URL)
                    MetaRouter.Web.navigate(
                        this,
                        title = item.title,
                        url = item.url,
                    )
                }
                .underline()
            if (split.size > 1) {
                builder.text(split[1])
                    .textAppearance(context, R.style.MetaTextView_S14_PoppinsRegular400)
                    .colorRes(R.color.color_1A1A1A)
            }
            binding.tvSendFlowerDisableDesc3.movementMethod =
                InterceptClickEventLinkMovementMethod(binding.tvSendFlowerDisableDesc3)
            binding.tvSendFlowerDisableDesc3.text = builder.build()
        }
    }

    override fun epoxyController() = simpleController(
        viewModel,
        SendFlowerConditionState::conditionsInfo
    ) { conditionsInfoAsync ->
        if (conditionsInfoAsync is Success) {
            val conditions = conditionsInfoAsync.invoke().conditions
            if (!conditions.isNullOrEmpty()) {
                conditions.forEachIndexed { index, condition ->
                    spacer(height = dp(8))
                    add {
                        ItemSendFlowerCondition(condition, conditionJumpClickedListener)
                            .id("ItemSendFlowerCondition-$index-${condition.name}")
                    }
                }
            }
        }
    }

    private fun safeDismiss() {
        setFragmentResult(
            REQUEST_KEY_SEND_FLOWER_CONDITIONS_INFO,
            bundleOf(KEY_SEND_FLOWER_CONDITIONS_INFO to viewModel.getGiftConditionsInfo())
        )
        callbackDismiss()
        dismissAllowingStateLoss()
    }

    override fun onBackPressed(): Boolean {
        safeDismiss()
        return super.onBackPressed()
    }

    private fun callbackDismiss() {
        onDismissCallback?.invoke(true)
        onDismissCallback = null
    }

    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback(true)
    }

    private val ioScope by lazy { CoroutineScope(Dispatchers.IO) }
    override fun needShow(
        fragment: Fragment,
        scene: DialogScene,
        args: Bundle?,
        needShowCallback: (Boolean) -> Unit
    ) {
        if (args == null) {
            needShowCallback(false)
            return
        }
        // 不是游戏详情页
        if (scene != DialogScene.GAME_DETAIL_PAGE) {
            needShowCallback(false)
            return
        }
        // 功能没开启
        if (!PandoraToggle.enableGameGiftOption) {
            needShowCallback(false)
            return
        }
        val isMyGame = args.getBoolean("isMyGame", false)
        // 游戏详情客态
        if (!isMyGame) {
            needShowCallback(false)
            return
        }
        // 如果显示过送花功能已开启的弹窗, 就不用再显示了
        if (!metaKV.account.showSendFlowerFeatureDialog) {
            needShowCallback(false)
            return
        }
        val gameId = args.getString("gameId")
        if (gameId.isNullOrEmpty()) {
            needShowCallback(false)
            return
        }
        var conditionsInfo = args.getParcelable<SendGiftConditionsInfo>("conditionsInfo")
        ioScope.launch {
            if (conditionsInfo == null) {
                val result = api.getSendGiftConditions(gameId)
                if (result.succeeded) {
                    conditionsInfo = result.data
                }
            }
            if ((conditionsInfo?.met == false || conditionsInfo?.flag == true)) {
                if (conditionsInfo?.met == false && !metaKV.account.showSendFlowerConditionsDialog) {
                    // 送花条件不满足, 并且已经展示过了送花条件不满足的弹窗了
                    needShowCallback(false)
                    return@launch
                }
                arguments = SendFlowerConditionDialogArgs(
                    gameId = gameId,
                    gameType = args.getInt("gameType"),
                    conditionsInfo = conditionsInfo,
                ).toBundle()
                needShowCallback(true)
            } else {
                needShowCallback(false)
            }
        }
    }

    override fun showByDialogManager(
        fragment: Fragment,
        onDismissCallback: (Boolean) -> Unit
    ) {
        if (!fragment.isAdded || fragment.isDetached) {
            return
        }
        this.onDismissCallback = onDismissCallback
        show(fragment.childFragmentManager, "SendFlowerConditionDialog")
    }

    override fun exeDismiss() {
        dismissAllowingStateLoss()
    }
}