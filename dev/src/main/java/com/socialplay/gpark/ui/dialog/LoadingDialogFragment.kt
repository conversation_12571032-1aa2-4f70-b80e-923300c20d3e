package com.socialplay.gpark.ui.dialog

import android.content.Context
import android.view.Gravity
import android.view.WindowManager
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogFragmentLoadingBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.property.bundleProperty
import com.socialplay.gpark.util.property.viewBinding

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/08/23
 * desc   :
 * </pre>
 */


class LoadingDialogFragment : BaseDialogFragment() {

    override val binding by viewBinding(DialogFragmentLoadingBinding::inflate)
    private val loadingMsg by bundleProperty("")

    override fun init() {
        if (!loadingMsg.isNullOrEmpty()) {
            binding.tvMsg.text = loadingMsg
        }
    }

    override fun loadFirstData() {}

    override fun gravity() = Gravity.CENTER
    override fun marginHorizontal(context: Context) = context.dp(16)
    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override fun getStyle() = R.style.DialogStyleNonFullScreen
    override fun isClickOutsideDismiss() = false
    override fun isBackPressedDismiss() = false
    override fun isTransparent() = true
}