package com.socialplay.gpark.ui.profile.link

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.databinding.ItemSelectLinkBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick

interface ILinkSelectListener : IBaseEpoxyItemListener {
    fun click(item: ProfileLinkInfo)
}

data class LinkSelectItem(
    val item: ProfileLinkInfo,
    val listener: ILinkSelectListener
) : ViewBindingItemModel<ItemSelectLinkBinding>(
    R.layout.item_select_link,
    ItemSelectLinkBinding::bind
) {
    override fun ItemSelectLinkBinding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.icon).placeholder(R.drawable.placeholder_circle)
                .circleCrop()
                .into(ivLogo)
        }
        tvTitle.text = item.title
        tvContent.text = item.url
        root.setOnAntiViolenceClickListener {
            listener.click(item)
        }
    }

    override fun ItemSelectLinkBinding.onUnbind() {
        root.unsetOnClick()
    }
}