package com.socialplay.gpark.ui.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.LoadState
import com.socialplay.gpark.databinding.CustomLoadMoreLayoutBinding
import com.socialplay.gpark.ui.base.adapter.BaseLoadStateAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.profile.recent.ProfileTabRecentFragment
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/13
 * desc   :
 * </pre>
 */


class HomeLoadMoreFooterAdapter(retryListener: () -> Unit) : BaseLoadStateAdapter<CustomLoadMoreLayoutBinding>(retryListener) {
    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, loadState: LoadState): CustomLoadMoreLayoutBinding {
        return CustomLoadMoreLayoutBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<CustomLoadMoreLayoutBinding>, loadState: LoadState) {
        Timber.tag(ProfileTabRecentFragment.TAG).d("----------> convert $loadState ${loadState.endOfPaginationReached}")
        holder.binding.ivProgress.pauseAnimation()
        when (loadState) {
            is LoadState.NotLoading -> {
                if (loadState.endOfPaginationReached) {
                    holder.binding.loadMoreLoadEndView.isVisible = true
                    holder.binding.loadMoreLoadCompleteView.isVisible = false
                    holder.binding.loadMoreLoadFailView.isVisible = false
                    holder.binding.loadMoreLoadingView.isVisible = false
                } else {
                    holder.binding.loadMoreLoadEndView.isVisible = false
                    holder.binding.loadMoreLoadCompleteView.isVisible = true
                    holder.binding.loadMoreLoadFailView.isVisible = false
                    holder.binding.loadMoreLoadingView.isVisible = false
                }
            }
            LoadState.Loading       -> {
                holder.binding.loadMoreLoadEndView.isVisible = false
                holder.binding.loadMoreLoadCompleteView.isVisible = false
                holder.binding.loadMoreLoadFailView.isVisible = false
                holder.binding.loadMoreLoadingView.isVisible = true
                holder.binding.ivProgress.resumeAnimation()
            }
            is LoadState.Error      -> {
                holder.binding.loadMoreLoadEndView.isVisible = false
                holder.binding.loadMoreLoadCompleteView.isVisible = false
                holder.binding.loadMoreLoadFailView.isVisible = true
                holder.binding.loadMoreLoadingView.isVisible = false
            }
        }
    }
}