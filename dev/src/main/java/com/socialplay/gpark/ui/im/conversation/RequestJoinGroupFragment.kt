package com.socialplay.gpark.ui.im.conversation

import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.databinding.FragmentRequestJoinGroupChatBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.im.groupchat.GroupChatListModelState
import com.socialplay.gpark.ui.im.groupchat.GroupChatListViewModel
import com.socialplay.gpark.util.LengthCallbackFilter
import com.socialplay.gpark.util.LineBreakCallbackFilter
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SoftKeyboardUtil
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.ime.KeyboardListener
import com.socialplay.gpark.util.ime.WindowInsetsHelper

class RequestJoinGroupFragment :
    BaseFragment<FragmentRequestJoinGroupChatBinding>(R.layout.fragment_request_join_group_chat) {
    companion object {
        const val MAX_LENGTH = 256
        const val REQUEST_KEY_JOIN_GROUP = "request_key_join_group"
        const val KEY_JOIN_GROUP_INFO_RESULT = "key_join_group_info_result"
        const val KEY_JOIN_GROUP_RESULT = "key_join_group_result"
    }

    private val args by navArgs<RequestJoinGroupFragmentArgs>()
    private lateinit var groupInfo: GroupChatInfo
    private val vm: GroupChatListViewModel by fragmentViewModel()

    private var textWatcher: TextWatcher? = null

    private var toastTs = 0L

    private var windowInsetsHelper: WindowInsetsHelper = WindowInsetsHelper()

    private val keyboardListener = object : KeyboardListener() {
        override fun onHideProgress(keyboardHeight: Int) {
            if (isBindingAvailable()) {
                val bottomSpaceHeight = binding.keyboardLine2.y - binding.keyboardLine.y
                if (bottomSpaceHeight < keyboardHeight) {
                    binding.layoutContent.translationY = (bottomSpaceHeight - keyboardHeight).coerceAtLeast(binding.layoutContent.translationY)
                } else {
                    binding.layoutContent.translationY = 0f
                }
            }
        }

        override fun onShowProgress(keyboardHeight: Int) {
            if (isBindingAvailable()) {
                val bottomSpaceHeight = binding.keyboardLine2.y - binding.keyboardLine.y
                if (bottomSpaceHeight < keyboardHeight) {
                    binding.layoutContent.translationY = (bottomSpaceHeight - keyboardHeight).coerceAtMost(binding.layoutContent.translationY)
                } else {
                    binding.layoutContent.translationY = 0f
                }
            }
        }
        override fun onProgress(keyboardHeight: Int) {
        }

        override fun onKeyboardHideEnd() {
            if (isBindingAvailable()) {
                binding.layoutContent.translationY = 0f
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRequestJoinGroupChatBinding? {
        return FragmentRequestJoinGroupChatBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = requireActivity().window
        if (window != null) {
            windowInsetsHelper.apply(
                window,
                binding.root,
                keyboardListener
            )
            // 旧版兼容方案, 需要将 statusBar 显示出来
            binding.statusBar.visible(Build.VERSION.SDK_INT < Build.VERSION_CODES.R)
        }
        val groupInfo = args.groupInfo
        if (groupInfo == null) {
            navigateUp()
            return
        }
        this.groupInfo = groupInfo

        binding.layoutTitleBar.setOnBackClickedListener {
            navigateUp()
        }

        binding.etInput.filters = arrayOf(
            LineBreakCallbackFilter(),
            LengthCallbackFilter(MAX_LENGTH) {
            context?.let {
                val curTs = SystemClock.elapsedRealtime()
                if (curTs - toastTs > 2000) {
                    toastTs = curTs
                    toast(getString(R.string.up_to_x_chars, MAX_LENGTH))
                }
            }
        })

        textWatcher = binding.etInput.doAfterTextChanged { text ->
            val hasContent = !text.isNullOrBlank()
            binding.loadingBtn.enableAllWithAlpha(hasContent)
        }
        binding.etInput.setText("")

        Glide.with(this)
            .load(groupInfo.icon)
            .placeholder(R.drawable.icon_item_group_chat_avatar)
            .into(binding.ivGroupAvatar)
        binding.tvGroupName.text = groupInfo.name ?: ""
        binding.tvGroupId.setTextWithArgs(
            R.string.request_join_group_id,
            groupInfo.id ?: ""
        )
        binding.tvGroupDescContent.text =
            groupInfo.describe.ifNullOrEmpty { getString(R.string.default_group_desc) }
        binding.tvGroupDescContent.setOnClickListener {
            if (binding.tvGroupDescContent.hasExpandText()) {
                val params = binding.tvGroupDescContentScrollView.layoutParams as ConstraintLayout.LayoutParams
                params.constrainedHeight = true
                params.matchConstraintMaxHeight = 1
                val bottomSpaceHeight = binding.keyboardLine2.y - binding.keyboardLine.y
                params.matchConstraintMaxHeight = binding.tvGroupDescContent.height + bottomSpaceHeight.toInt()

                binding.tvGroupDescContent.toggle()
                SoftKeyboardUtil.hideSoftKeyboard(this)
            }
        }

        binding.loadingBtn.setOnAntiViolenceClickListener {
            if (!binding.loadingBtn.isEnabled || binding.loadingBtn.isLoading) {
                return@setOnAntiViolenceClickListener
            }
            val reason = binding.etInput.text?.toString()?.trim()
            if (reason.isNullOrEmpty()) {
                return@setOnAntiViolenceClickListener
            }
            Analytics.track(
                EventConstants.GROUP_JOIN,
                "group_id" to groupInfo.id.toString()
            )
            vm.applyJoinGroup(groupInfo, reason)
        }
        vm.onAsync(
            GroupChatListModelState::applyJoinResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                binding.loadingBtn.showButton()
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.loading_net_error)
                } else {
                    toast(R.string.toast_apply_join_group_request_send_failed)
                }
            },
            onLoading = {
                binding.loadingBtn.showLoading()
            },
            onSuccess = { applyJoinResult ->
                binding.loadingBtn.showButton()
                val result = applyJoinResult.second
                if (result.succeeded && result.data == true) {
                    toast(R.string.toast_apply_join_group_request_send_success)
                    // 申请加群成功
                    dismissWithResult(applyJoinResult.first, true)
                } else {
                    toast(
                        result.message
                            ?: getString(R.string.toast_apply_join_group_request_send_failed)
                    )
                }
            })
    }

    override fun onResume() {
        super.onResume()
    }

    private fun dismissWithResult(groupInfo: GroupChatInfo, result: Boolean) {
        setFragmentResult(REQUEST_KEY_JOIN_GROUP, Bundle().apply {
            putParcelable(
                KEY_JOIN_GROUP_INFO_RESULT,
                if (result) {
                    groupInfo.copy(
                        joinStatus = if (groupInfo.joinStatus == GroupChatInfo.JOIN_STATUS_CAN_JOIN) {
                            GroupChatInfo.JOIN_STATUS_JOINED
                        } else {
                            GroupChatInfo.JOIN_STATUS_REQUESTED
                        },
                        requestJoinStatus = GroupChatInfo.REQUEST_JOIN_STATUS_DEFAULT
                    )
                } else {
                    groupInfo
                },
            )
            putBoolean(KEY_JOIN_GROUP_RESULT, result)
        })
        navigateUp()
    }

    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInput.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        windowInsetsHelper.unApply()
        super.onDestroyView()
    }

    override fun invalidate() {

    }

    override fun getPageName() = PageNameConstants.FRAGMENT_JOIN_GROUP_REQUEST
}