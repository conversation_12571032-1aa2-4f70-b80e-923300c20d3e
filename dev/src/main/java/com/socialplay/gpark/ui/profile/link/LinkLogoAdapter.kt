package com.socialplay.gpark.ui.profile.link

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.databinding.ItemProfileLinkLogoBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setWidth

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/19
 *     desc   :
 *
 */
class LinkLogoAdapter : BaseAdapter<ProfileLinkInfo, ItemProfileLinkLogoBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemProfileLinkLogoBinding {
        return ItemProfileLinkLogoBinding.inflate(LayoutInflater.from(context))
    }

    override fun convert(
        holder: BindingViewHolder<ItemProfileLinkLogoBinding>,
        item: ProfileLinkInfo,
        position: Int
    ) {
        Glide.with(context).load(item.icon).into(holder.binding.imgLogo)
        holder.binding.tvTitle.text = item.title
    }
}