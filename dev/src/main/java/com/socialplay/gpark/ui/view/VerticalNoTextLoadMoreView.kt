package com.socialplay.gpark.ui.view

import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.chad.library.adapter.base.loadmore.BaseLoadMoreView
import com.chad.library.adapter.base.loadmore.LoadMoreStatus
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.databinding.ViewVerticalLoadMoreNoTextBinding
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible

/**
 * create by: bin on 2021/5/20
 */
class VerticalNoTextLoadMoreView: BaseLoadMoreView() {
    lateinit var binding: ViewVerticalLoadMoreNoTextBinding

    override fun getRootView(parent: ViewGroup): View {
        binding = parent.createViewBinding(ViewVerticalLoadMoreNoTextBinding::inflate)
        return binding.root
    }

    override fun getLoadingView(holder: BaseViewHolder): View = binding.loadMoreLoadingView

    override fun getLoadComplete(holder: BaseViewHolder): View = binding.root

    override fun getLoadEndView(holder: BaseViewHolder): View = binding.root

    override fun getLoadFailView(holder: BaseViewHolder): View = binding.root

    override fun convert(holder: BaseViewHolder, position: Int, loadMoreStatus: LoadMoreStatus) {
        when (loadMoreStatus) {
            LoadMoreStatus.Loading -> {
                binding.loadMoreLoadingView.visible()
            }
            else -> {
                binding.loadMoreLoadingView.gone()
            }
        }
    }

}

