package com.socialplay.gpark.ui.moments.main

import android.content.ComponentCallbacks
import android.content.Context
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.moments.PlotMainList
import com.socialplay.gpark.data.model.moments.PlotTemplate
import com.socialplay.gpark.data.model.moments.PlotTemplateCollection
import com.socialplay.gpark.data.model.moments.PlotTemplateStyle.Companion.TYPE_1_ROW_N_COLUMN
import com.socialplay.gpark.data.model.moments.PlotTemplateStyle.Companion.TYPE_N_ROW_2_COLUMN
import com.socialplay.gpark.data.model.moments.PlotTitle
import com.socialplay.gpark.data.model.user.BadgeInfo
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.util.copy
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import kotlin.reflect.KProperty1

/**
 * 2023/9/19
 */

data class MomentsMainUiState(
    val mainList: Async<List<SimMultiItem>> = Uninitialized,
) : MavericksState

class MomentsMainViewModel(
    private val app: Context,
    private val metaRepository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: MomentsMainUiState,
) : MavericksViewModel<MomentsMainUiState>(initialState) {

    companion object : KoinViewModelFactory<MomentsMainViewModel, MomentsMainUiState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: MomentsMainUiState,
        ): MomentsMainViewModel {
            return MomentsMainViewModel(get(), get(), get(), state)
        }
    }

    init {
        featPlotMainList()
    }

    fun plotTemplateLoveDo(
        templateId: String,
        asyncProp: KProperty1<MomentsMainUiState, Async<List<SimMultiItem>>>,
        copyInvoke: MomentsMainUiState.(Async<List<SimMultiItem>>) -> MomentsMainUiState,
    ) = withState { state ->
        metaRepository.plotTemplateLoveDoV2(templateId, "6").execute {
            if (it is Success && it.invoke()) {
                val get = asyncProp.get(state)
                val newList = get.invoke()?.copy { item ->
                    if (item is PlotTemplate && "${item.templateId}" == templateId) {
                        item.copy(useCount = item.useCount + 1)
                    } else {
                        item
                    }
                }
                copyInvoke.invoke(this, get.copyEx(newList))
            } else {
                copy()
            }
        }
    }

    fun featPlotMainList() = withState { state ->
        if (state.mainList is Loading) return@withState
        metaRepository.featPlotMainList().execute {
            if (it is Success) {
                copy(mainList = mapperMainList(it))
            } else {
                copy(mainList = if (mainList is Success) mainList else it.map { emptyList() })
            }
        }
    }

    private fun mapperMainList(list: Success<PlotMainList>): Async<List<SimMultiItem>> {
        return list.map {
            val result = mutableListOf<SimMultiItem>()
            // 置顶模板
            val pinList = mutableListOf<PlotTemplate>()
            val templateList =
                it.topList.filter { item -> item.gameId != null && item.templateId != null && item.isTop }
            pinList.addAll(templateList)
            if (pinList.isNotEmpty()) {
                result.add(
                    PlotTitle(
                        app.getString(R.string.new_oc_shorts_template),
                        null,
                        R.drawable.ic_plot_new_template
                    )
                )
                result.add(PlotTemplateCollection(pinList.sortedByDescending {
                    it.weightTop
                }.distinctBy {
                    it.templateId
                }.map {
                    it.copy(
                        localPaddingType = MomentsListAdapter.PADDING_TYPE_RV,
                        localStyle = TYPE_1_ROW_N_COLUMN
                    )
                }))
            }
            // 其他模板
            it.mergeList.forEach { itemStyle ->
                val templateList =
                    itemStyle.templateList?.filter { item -> item.gameId != null && item.templateId != null }
                if (!templateList.isNullOrEmpty()) {
                    val type = templateList.firstNotNullOfOrNull { item -> item.templateType }
                    val showMore = when (itemStyle.pstyle) {
                        TYPE_1_ROW_N_COLUMN -> false
                        TYPE_N_ROW_2_COLUMN -> templateList.size > 2
                        else -> templateList.size > 3
                    }
                    result.add(
                        PlotTitle(
                            itemStyle.pname ?: "",
                            itemStyle.picon ?: "",
                            null,
                            type = itemStyle.id,
                            showMore = showMore
                        )
                    )
                    when (itemStyle.pstyle) {
                        TYPE_1_ROW_N_COLUMN -> {
                            result.add(PlotTemplateCollection(templateList.map {
                                it.copy(
                                    localPaddingType = MomentsListAdapter.PADDING_TYPE_RV,
                                    localStyle = TYPE_1_ROW_N_COLUMN
                                )
                            }))
                        }

                        else -> {
                            val column = if (itemStyle.pstyle == TYPE_N_ROW_2_COLUMN) 2 else 3
                            result.addAll(templateList.filterIndexed { index, _ -> index < column }
                                .mapIndexed { index, plotTemplate ->
                                    plotTemplate.copy(
                                        localPaddingType = when (index % column) {
                                            0 -> MomentsListAdapter.PADDING_TYPE_LEFT
                                            column - 1 -> MomentsListAdapter.PADDING_TYPE_RIGHT
                                            else -> MomentsListAdapter.PADDING_TYPE_CENTER
                                        }, localStyle = itemStyle.pstyle
                                    )
                                })
                        }
                    }
                }
            }
            result
        }
    }

    fun clearUnRead() = viewModelScope.launch {
        accountInteractor.clearRedBadge(BaseAccountInteractor.TYPE_OC_SHORTS_NEW_TEMPLATE) {
            it?.copy(newTemplate = BadgeInfo(false, 0, null))
        }
    }
}