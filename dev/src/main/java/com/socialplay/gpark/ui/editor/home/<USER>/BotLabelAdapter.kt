package com.socialplay.gpark.ui.editor.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.databinding.AdapterAibotLabelItemBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.sp
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 * Created by liujiang
 * Date: 2023/12/13 16:16
 * Desc:
 * Version:
 */
class BotLabelAdapter(private val isTitle: Boolean) :
    BaseDifferAdapter<BotLabelInfo, AdapterAibotLabelItemBinding>(DIFF_ITEM_CALLBACK) {

    private var selected = -1

    companion object {

        private val DIFF_ITEM_CALLBACK = object : DiffUtil.ItemCallback<BotLabelInfo>() {

            override fun areItemsTheSame(oldItem: BotLabelInfo, newItem: BotLabelInfo): Boolean {
                return oldItem.tagId == newItem.tagId
            }

            override fun areContentsTheSame(oldItem: BotLabelInfo, newItem: BotLabelInfo): Boolean {
                return oldItem.tagId == newItem.tagId
                        && oldItem.selected == newItem.selected
            }
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterAibotLabelItemBinding {
        return AdapterAibotLabelItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterAibotLabelItemBinding>,
        item: BotLabelInfo
    ) {
        holder.binding.iconTag.visible(!item.icon.isNullOrEmpty())
        Glide.with(context).load(item.icon).into(holder.binding.iconTag)
        if (isTitle){
            if (item.selected == true) {
                holder.binding.tvTableName.setTextColor(context.getColorByRes(R.color.white))
                holder.binding.root.setBackgroundResource(R.drawable.bg_corner_360_black)
            } else {
                holder.binding.tvTableName.visible()
                holder.binding.tvTableName.setTextColor(context.getColorByRes(R.color.color_666666))
                holder.binding.root.setBackgroundResource(R.drawable.bg_f0f0f0_corner_360)
            }
            holder.binding.tvTableName.textSize=  12f
            holder.binding.root.setPadding(14.dp, 6.dp, 14.dp, 6.dp)
        } else {
            holder.binding.tvTableName.setTextColor(context.getColorByRes(R.color.white))
            holder.binding.tvTableName.textSize=  10f
            holder.binding.root.setBackgroundResource(R.drawable.bg_white_20_corner_360)
            holder.binding.root.setPadding(8.dp, 2.dp, 8.dp, 2.dp)
        }

        holder.binding.tvTableName.text = item.tagName
    }

}