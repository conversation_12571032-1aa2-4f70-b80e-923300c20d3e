package com.socialplay.gpark.ui.view.center

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan
import androidx.annotation.DrawableRes

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/07
 *     desc   :
 * </pre>
 */

class CenterImageSpan : ImageSpan {
    private val left: Int
    private val right: Int

    constructor(
        drawable: Drawable,
        left: Int = 0,
        right: Int = 0
    ) : super(drawable) {
        this.left = left
        this.right = right
    }

    constructor(
        context: Context,
        bitmap: Bitmap,
        left: Int = 0,
        right: Int = 0
    ) : super(context, bitmap) {
        this.left = left
        this.right = right
    }

    constructor(
        context: Context,
        @DrawableRes resId: Int,
        left: Int = 0,
        right: Int = 0
    ) : super(context, resId) {
        this.left = left
        this.right = right
    }

    private var isSmallImage = false

    override fun getSize(
        paint: Paint,
        text: CharSequence,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        val drawable = drawable
        val rect = drawable.bounds
        if (fm != null) {
            val fmPaint = paint.fontMetricsInt
            val fontH = fmPaint.descent - fmPaint.ascent
            val imageH = rect.bottom - rect.top

            if (imageH > fontH) {
                isSmallImage = false
                fm.ascent = fmPaint.ascent - (imageH - fontH) / 2
                fm.top = fmPaint.ascent - (imageH - fontH) / 2
                fm.bottom = fmPaint.descent + (imageH - fontH) / 2
                fm.descent = fmPaint.descent + (imageH - fontH) / 2
            } else {
                isSmallImage = true
                fm.ascent = -rect.bottom
                fm.descent = 0

                fm.top = fm.ascent
                fm.bottom = 0
            }
        }
        return rect.right + left + right
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val b = drawable
        canvas.save()

        var transY = bottom - b.bounds.bottom
        if (isSmallImage) {
            transY -= ((bottom - top) / 2 - b.bounds.height() / 2)
        }

        canvas.translate(x + left, transY.toFloat())
        b.draw(canvas)
        canvas.restore()
    }
}