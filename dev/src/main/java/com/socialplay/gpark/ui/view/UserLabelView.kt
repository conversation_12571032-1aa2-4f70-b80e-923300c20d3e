package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.user.UserTagInfo
import com.socialplay.gpark.databinding.ViewUserLabelBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.editor.module.guide.UgcModuleGuideDialog
import com.socialplay.gpark.ui.profile.badge.BadgeDialog
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidthEx
import com.socialplay.gpark.util.extension.visible


/**
 * Created by bo.li
 * Date: 2020/11/26
 */
class UserLabelView : LinearLayout {

    companion object {
        const val TYPE_OFFICIAL = 1
        const val TYPE_MEDAL = 2
        const val TYPE_CREATOR = 3

        fun showDescDialog(fragment: Fragment, data: Pair<Int, LabelInfo?>) {
            if (!fragment.canShowDialog) return
            val (type, labelInfo) = data
            val imgRes: Int
            val titleRes: Int
            val contentRes: Int
            when (type) {
                TYPE_OFFICIAL -> {
                    imgRes = R.drawable.icon_official_verification_big
                    titleRes = R.string.verified_account_title
                    contentRes = R.string.verified_account_content
                }

                TYPE_MEDAL -> {
                    labelInfo ?: return
                    UgcModuleGuideDialog.show(
                        fragment,
                        PandoraToggle.MODULE_GUIDE_LABEL_CLICK,
                        labelInfo
                    ) {
                        if (!it) {
                            BadgeDialog.show(
                                fragment,
                                labelInfo.icon,
                                labelInfo.name,
                                labelInfo.desc
                            )
                        }
                    }
                    return
                }

                TYPE_CREATOR -> {
                    imgRes = R.drawable.ic_label_creator_desc
                    titleRes = R.string.creator_cap
                    contentRes = R.string.creator_label_desc
                }

                else -> {
                    return
                }
            }
            ConfirmDialog.Builder(fragment)
                .image(imgRes)
                .title(fragment.getString(titleRes))
                .content(fragment.getString(contentRes))
                .cancelBtnTxt(null, false)
                .confirmBtnTxt(fragment.getString(R.string.close), lightBackground = true)
                .confirmCallback {}
                .show()
        }

        fun loadMedal(
            glide: RequestManager?,
            medal: String?,
            view: ImageView,
            callback: (Drawable) -> Int = {
                (view.dp(14) * (it.minimumWidth / it.minimumHeight.toFloat())).toInt()
            }
        ) {
            (glide ?: Glide.with(view)).load(medal)
                .placeholder(R.drawable.placeholder_circle)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        view.setWidthEx(callback(resource))
                        return false
                    }
                })
                .into(view)
        }
    }

    private lateinit var binding: ViewUserLabelBinding

    private var mListener: UserLabelListener? = null
    private var mLabelInfo: LabelInfo? = null

    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(attrs, defStyleAttr)
    }

    private fun init(attrs: AttributeSet?, defStyleAttr: Int) {
        binding = ViewUserLabelBinding.inflate(LayoutInflater.from(context), this)
        gravity = Gravity.CENTER_VERTICAL
        orientation = HORIZONTAL
        binding.ivLabelOfficial.setOnAntiViolenceClickListener {
            mListener?.invoke(TYPE_OFFICIAL to null)
        }
        binding.ivLabelNet.setOnAntiViolenceClickListener {
            mListener?.invoke(TYPE_MEDAL to mLabelInfo)
        }
        binding.ivLabelCreator.setOnAntiViolenceClickListener {
            mListener?.invoke(TYPE_CREATOR to null)
        }
    }

    fun show(
        tagIds: List<Int>?,
        label: LabelInfo?,
        isMe: Boolean = false,
        isOfficial: Boolean = false,
        isCreator: Boolean = false,
        isUnderReview: Boolean = false,
        glide: RequestManager? = null
    ) {
        val mIsOfficial = isOfficial || tagIds?.any { UserTagInfo.isOfficial(it) } == true
        val mHasLabelInfo = label != null && !label.icon.isNullOrEmpty()
        val mHasCreator = isCreator || tagIds?.any { UserTagInfo.isCreator(it) } == true
        val showLabel = mIsOfficial || mHasLabelInfo || mHasCreator || isMe || isUnderReview
        if (showLabel) {
            binding.root.visible()
            binding.ivLabelOfficial.visible(mIsOfficial)
            if (mHasLabelInfo) {
                binding.ivLabelNet.visible()
                loadMedal(glide, label?.icon, binding.ivLabelNet)
            } else {
                binding.ivLabelNet.gone()
            }
            binding.ivLabelCreator.visible(mHasCreator)
            // 创作者和我同时存在时, 只显示创作者
            binding.tvAuthorLabel.visible(isCreator)
            binding.tvLabelMe.visible(isMe && !isCreator)
            binding.tvLabelUnderReview.visible(isUnderReview)
        } else {
            binding.root.gone()
        }
        mLabelInfo = label
    }

    fun hide() {
        binding.root.gone()
    }

    fun setListener(listener: UserLabelListener?) {
        mListener = listener
        if (listener == null) {
            mLabelInfo = null
        }
    }

    fun setListener(owner: LifecycleOwner, listener: UserLabelListener?) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                mListener = listener
            },
            unregister = {
                mListener = null
                mLabelInfo = null
            }
        )
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mLabelInfo = null
    }
}

typealias UserLabelListener = (data: Pair<Int, LabelInfo?>) -> Unit