package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.view.ViewCompat

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/11/22
 *     desc   :
 * </pre>
 */
class CallbackCoordinatorLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : VerticalCoordinatorLayout(context, attrs, defStyleAttr) {

    private var invokeStopScrollCallbackByTouch = true
    var stopScrollCallback: (() -> Unit)? = null

    override fun onStartNestedScroll(child: View, target: View, axes: Int, type: Int): Boolean {
        if ((axes and ViewCompat.SCROLL_AXIS_HORIZONTAL) != 0) {
            return false
        }
        return super.onStartNestedScroll(child, target, axes, type)
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                invokeStopScrollCallbackByTouch = false
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                invokeStopScrollCallbackByTouch = true
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onNestedScrollAccepted(
        child: View,
        target: View,
        nestedScrollAxes: Int,
        type: Int
    ) {
        if (type == ViewCompat.TYPE_NON_TOUCH) {
            invokeStopScrollCallbackByTouch = false
        }
        super.onNestedScrollAccepted(child, target, nestedScrollAxes, type)
    }

    override fun onStopNestedScroll(target: View, type: Int) {
        if (invokeStopScrollCallbackByTouch || type == ViewCompat.TYPE_NON_TOUCH) {
            stopScrollCallback?.invoke()
        }
        super.onStopNestedScroll(target, type)
    }

}