package com.socialplay.gpark.ui.editor.home.datalist

import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.post.MomentLocalTSStartUp
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.editor.IEditorLaunchCallback
import com.socialplay.gpark.function.editor.LaunchOverResult
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchOptionAppendTsStartUp
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSEngineNotMatchDialog
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import timber.log.Timber

open class TSGameLaunchHelper(
    private val fragment: Fragment
) : KoinComponent {

    protected var editorGameLaunchHelper: EditorGameLaunchHelper? = null
    private val gameStartScenes by lazy { MWGameStartScenes(fragment) }

    private val tsLaunch by lazy {
        TSLaunch().apply {
            onLaunchListener {
                onLaunchGameEnd { params, e ->
                    TSLaunchFailedWrapper.show(fragment, params, e)
                }
            }
        }
    }

    init {
        fragment.lifecycle.addObserver(object : LifecycleEventObserver{
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if(event == Lifecycle.Event.ON_CREATE){
                    init()
                }else if(event == Lifecycle.Event.ON_DESTROY){
                    dispose()
                }
            }
        })
    }

    private fun init() {
        editorGameLaunchHelper = EditorGameLaunchHelper(editorDownloadCallback)
        editorGameLaunchHelper?.init(fragment)


        val invoke: (Pair<String, String>) -> Unit = { data: Pair<String,String> ->
            Timber.d("checkcheck onReceivedStartGame errorMsg:${data.first}")
            fragment.lifecycleScope.launch(Dispatchers.Main) {
                if (data.first.isEmpty()) {
                    gameStartScenes.show()
                } else {
                    hideLoadingUI(false, null)
                }
            }
        }

        MWLifeCallback.startGame.observe(fragment as LifecycleOwner, false, observer = invoke)
        MWLifeCallback.startLocalGame.observe(fragment  as LifecycleOwner, false, observer = invoke)
    }

    open val editorDownloadCallback: IEditorLaunchCallback = object : IEditorLaunchCallback {
        override fun onLaunchOver(result: LaunchOverResult) {
            Timber.d("checkcheck onLaunchOver, ${result.launchSuccess}, ${if (!result.launchSuccess) result.msg else null}")
            if (fragment.view != null) {
                if (result.e is TSUserCancelledException) {
                    hideLoadingUI(result.launchSuccess, result.msg)
                } else if (result.e is TSEngineVersionNotMatchException) {
                    TSEngineNotMatchDialog.show(fragment, result.gameInfo?.icon)
                    hideLoadingUI(result.launchSuccess, "")
                } else {
                    hideLoadingUI(result.launchSuccess, result.msg)
                }
            }
        }

        override fun onChecking(gameInfo: GameDetailInfo?, id: String?, path: String?, type: String?) {
            Timber.d("checkcheck onLaunchingGame type:$type")
            if (fragment.view != null && type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
                showLoadingUI()
            }
        }
    }

    /**
     * 隐藏加载界面
     */
    protected open fun hideLoadingUI(launchSuccess: Boolean, msg: String?) {
        fragment.lifecycleScope.launchWhenResumed {
            if (!launchSuccess) {
                fragment.toast(msg ?: fragment.getString(R.string.verse_download_failed))
                gameStartScenes.hide()
            }
        }
    }

    /**
     * 展示加载界面
     */
    protected fun showLoadingUI() {
        gameStartScenes.show()
    }

    private fun dispose() {
        editorGameLaunchHelper?.onDestroyHelper()
        editorGameLaunchHelper = null
    }

    fun startUgcGame(params: EditorUGCLaunchParams, resIdBean: ResIdBean){
        editorGameLaunchHelper?.startUgcGame(fragment, params, resIdBean)
    }


    fun startPlotGame(
        gid: String,
        gameName: String,
        templateId: String,
        source: String,
        expand: String,
        resIdBean: ResIdBean
    ) {
        val info = tsLaunch.createTSGameDetailInfo(gid, "", gameName)
        resIdBean.setGameId(gid).setTypeID("$templateId")
        val tsLaunchParams = TSLaunchParams(info, resIdBean, option = TSLaunchOptionAppendTsStartUp())
        tsLaunchParams.localTsStartUp = MomentLocalTSStartUp(source, templateId).toMap()
        tsLaunchParams.tsStartUp = expand
        tsLaunchParams.setGameUniqueKey("${gid}_${templateId}")
        tsLaunch.launchPlot(fragment.requireContext(), tsLaunchParams)
    }

}