package com.socialplay.gpark.ui.developer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentDeveloperMmkvManagementBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.developer.adapter.DevMMKVDBManagementAdapter
import com.socialplay.gpark.ui.developer.adapter.DevMMKVItemManagementAdapter
import com.socialplay.gpark.ui.developer.viewmodel.DevMMKVManagementViewModel
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.collect
import org.koin.androidx.viewmodel.ext.android.viewModel


class MmkvManagementFragment : BaseFragment<FragmentDeveloperMmkvManagementBinding>() {

    private val vm by viewModel<DevMMKVManagementViewModel>()

    private val dbAdapter = DevMMKVDBManagementAdapter()
    private val itemAdapter = DevMMKVItemManagementAdapter()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperMmkvManagementBinding? {
        return FragmentDeveloperMmkvManagementBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.rvGroupList.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = dbAdapter
        }

        binding.rvItemList.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = itemAdapter
        }

        dbAdapter.setOnItemClickListener { view, position ->
            vm.getItemList(dbAdapter.getItem(position))
        }

        itemAdapter.addChildClickViewIds(R.id.btn_delete)
        itemAdapter.setOnItemChildClickListener { view, position ->
            if (view.id == R.id.btn_delete) {
                vm.removeItem(itemAdapter.getItem(position))
            }
        }

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            vm.dbListFlow.collect {
                dbAdapter.setList(it)
            }
        }

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            vm.itemListFlow.collect {
                itemAdapter.setList(it)
            }
        }


    }

    override fun loadFirstData() {
        vm.getDBList()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_DEV_CONFIG
}