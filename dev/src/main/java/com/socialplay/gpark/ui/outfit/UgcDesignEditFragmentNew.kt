package com.socialplay.gpark.ui.outfit

import android.os.Bundle
import android.os.Parcelable
import android.text.InputFilter.LengthFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.addCallback
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.databinding.FragmentUgcDesignEditNewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/05
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcDesignEditFragmentNewArgs(
    val itemId: String,
    val cover: String?,
    val title: String?,
    val desc: String?,
    val feedType: Int
) : Parcelable {
    val isModule get() = feedType == UgcDesignFeed.FEED_TYPE_UGC_MODULE
    val isDesign get() = feedType == UgcDesignFeed.FEED_TYPE_UGC_DESIGN
}

class UgcDesignEditFragmentNew :
    BaseFragment<FragmentUgcDesignEditNewBinding>(R.layout.fragment_ugc_design_edit_new) {

    companion object {
        const val KEY = "UgcDesignEditFragment"
        const val KEY_ITEM_ID = "itemId"
        const val KEY_TITLE = "title"
        const val KEY_DESC = "desc"
    }

    private val vm: UgcDesignEditViewModel by fragmentViewModel()
    private val args: UgcDesignEditFragmentArgs by args()
    private var initFlag = true
    private var result: Boolean = false
    private var onBackPressedCallback: OnBackPressedCallback? = null
    private var prevSoftInputMode = 0

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcDesignEditNewBinding? {
        return FragmentUgcDesignEditNewBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Analytics.track(
            EventConstants.LIBRARY_METARIAL_EDIT_PAGE_SHOW
        )

        if (args.isModule) {
            binding.etTitle.filters = arrayOf(LengthFilter(24))
            binding.et.filters = arrayOf(LengthFilter(60))
        } else {
            binding.etTitle.filters = arrayOf(LengthFilter(50))
            binding.et.filters = arrayOf(LengthFilter(500))
        }

        if (initFlag) {
            binding.etTitle.setText(args.title)
            binding.et.setText(args.desc)
        }

        if (args.isModule) {
            glide?.run {
                load(args.cover).transform(CenterCrop(), RoundedCorners(dp(12)))
                    .into(binding.ivOutfit)
            }
        } else {
            glide?.run {
                load(args.cover).fitCenter()
                    .into(binding.ivOutfit)
            }
        }

        binding.tbl.setOnBackClickedListener {
            goBack()
        }
        binding.tvReplyBtn.setOnAntiViolenceClickListener {
            val title = binding.etTitle.text?.toString().orEmpty()
            val desc = binding.et.text?.toString().orEmpty()
            if (title.isBlank()) {
                toast(getString(R.string.cannot_empty, getString(R.string.profile_link_title)))
                return@setOnAntiViolenceClickListener
            } else if (desc.isBlank()) {
                toast(getString(R.string.cannot_empty, getString(R.string.description)))
                return@setOnAntiViolenceClickListener
            }
            vm.edit(
                binding.etTitle.text?.toString().orEmpty(),
                binding.et.text?.toString().orEmpty()
            )
        }

        vm.onAsync(UgcDesignEditState::editResult, onFail = { _, _ ->
            Analytics.track(
                EventConstants.LIBRARY_METARIAL_EDIT_PAGE_PUBLISH_CLICK,
                "result" to "1"
            )
            binding.tvReplyBtn.enableWithAlpha(true)
        }, onLoading = {
            binding.tvReplyBtn.enableWithAlpha(false)
        }) {
            result = true
            Analytics.track(
                EventConstants.LIBRARY_METARIAL_EDIT_PAGE_PUBLISH_CLICK,
                "result" to "0"
            )
            toast(R.string.publish_succeeded)
            goBack()
        }
        vm.registerAsyncErrorToast(UgcDesignEditState::editResult)
    }

    private fun goBack() {
        if (result) {
            navigateUp()
        } else {
            ConfirmDialog.Builder(this)
                .title(getString(R.string.notice))
                .content(getString(R.string.ugc_design_edit_exit_tips), contentSpace = dp(10))
                .cancelBtnTxt(getString(R.string.keep_editing))
                .confirmBtnTxt(getString(R.string.exit_cap))
                .isRed(true)
                .dismissCallback { action ->
                    when (action) {
                        ConfirmDialog.CONFIRM -> {
                            navigateUp()
                        }
                    }
                }
                .show()
        }
    }

    override fun onResume() {
        super.onResume()
        onBackPressedCallback =
            requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
                goBack()
            }
        activity?.window?.let {
            prevSoftInputMode = it.attributes.softInputMode
            if (prevSoftInputMode != WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN) {
                it.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            }
        }
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.root)
        onBackPressedCallback?.remove()
        onBackPressedCallback = null
        activity?.window?.let {
            if (prevSoftInputMode != it.attributes.softInputMode) {
                it.setSoftInputMode(prevSoftInputMode)
            }
        }
        super.onPause()
    }

    override fun onDestroy() {
        val s = vm.oldState
        setFragmentResult(
            KEY,
            bundleOf(KEY_ITEM_ID to args.itemId, KEY_TITLE to s.title, KEY_DESC to s.desc)
        )
        super.onDestroy()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DESIGN_EDIT
}