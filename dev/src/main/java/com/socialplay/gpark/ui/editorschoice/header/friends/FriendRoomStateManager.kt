package com.socialplay.gpark.ui.editorschoice.header.friends

import android.view.View
import androidx.core.view.isVisible
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.databinding.AdapterItemFriendsBinding
import com.socialplay.gpark.R

/**
 * 管理好友跟房的状态显示
 * 支持空白状态和有数据状态的切换
 */
object FriendRoomStateManager {
    
    /**
     * 更新好友跟房的显示状态
     * @param binding 布局绑定
     * @param friendList 好友列表
     * @param showEmptyState 是否显示空白状态
     */
    fun updateFriendRoomState(
        binding: AdapterItemFriendsBinding,
        friendList: List<ChoiceFriendInfo>,
        showEmptyState: Boolean
    ) {
        if (showEmptyState || friendList.isEmpty()) {
            showEmptyState(binding)
        } else {
            showFriendList(binding, friendList)
        }
    }
    
    /**
     * 显示空白状态
     */
    private fun showEmptyState(binding: AdapterItemFriendsBinding) {
        // 隐藏好友相关视图
        binding.iv.isVisible = false
        binding.ivOnline.isVisible = false
        binding.tvUserName.isVisible = false
        binding.tvGameName.isVisible = false
        binding.tvStatus.isVisible = false

        // 显示空白状态布局
        val emptyStateLayout = binding.root.findViewById<View>(R.id.layout_empty_state)
        emptyStateLayout?.isVisible = true
    }
    
    /**
     * 显示好友列表状态
     */
    private fun showFriendList(binding: AdapterItemFriendsBinding, friendList: List<ChoiceFriendInfo>) {
        // 隐藏空白状态布局
        val emptyStateLayout = binding.root.findViewById<View>(R.id.layout_empty_state)
        emptyStateLayout?.isVisible = false
        
        // 这里应该设置RecyclerView来显示好友列表
        // 由于当前布局结构的限制，我们暂时只显示第一个好友
        if (friendList.isNotEmpty()) {
            val firstFriend = friendList.first()
            showSingleFriend(binding, firstFriend)
        }
    }
    
    /**
     * 显示单个好友信息
     */
    private fun showSingleFriend(binding: AdapterItemFriendsBinding, friend: ChoiceFriendInfo) {
        binding.iv.isVisible = true
        binding.tvUserName.isVisible = true
        
        // 这里需要加载头像和设置状态
        // 具体实现需要在适配器中完成
    }
}
