package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceRecommendEnhancedBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.gone

/**
 * Enhanced Recommendation card item with tag support
 */
data class ChoiceEnhancedRecommendCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceRecommendEnhancedBinding>(
    R.layout.adapter_choice_recommend_enhanced,
    AdapterChoiceRecommendEnhancedBinding::bind
) {

    override fun AdapterChoiceRecommendEnhancedBinding.onBind() {
        // Load game image
        listener.getGlideOrNull()?.let { glide ->
            glide.load(item.iconUrl)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivBg)
        }
        
        // Set game title
        tvDisplayName.text = item.displayName
        
        // Set creator info
        if (!item.nickname.isNullOrEmpty() && !item.avatar.isNullOrEmpty()) {
            ivAvatar.visible()
            tvName.visible()
            vBgBottom.visible()
            
            listener.getGlideOrNull()?.let { glide ->
                glide.load(item.avatar)
                    .transform(CircleCrop())
                    .into(ivAvatar)
            }
            tvName.text = item.nickname
        } else {
            ivAvatar.gone()
            tvName.gone()
            vBgBottom.gone()
        }
        
        // Set like count
        item.likeCount?.let { count ->
            if (count > 0) {
                tvLike.visible()
                tvLike.text = UnitUtil.formatKMCount(count)
            } else {
                tvLike.gone()
            }
        } ?: run {
            tvLike.gone()
        }
        
        // Set tags if available
        if (!item.tagList.isNullOrEmpty()) {
            val firstTag = item.tagList?.firstOrNull()
            if (!firstTag.isNullOrEmpty()) {
                tvTag.visible()
                tvTag.text = firstTag
            } else {
                tvTag.gone()
            }
        } else {
            tvTag.gone()
        }
        
        // Set new game indicator
        if (item.isNewGame == true) {
            tvNewIndicator.visible()
        } else {
            tvNewIndicator.gone()
        }
        
        // Set playing count if available
        item.playingCount?.let { count ->
            if (count > 0) {
                tvPlayingCount.visible()
                tvPlayingCount.text = "${count} playing"
            } else {
                tvPlayingCount.gone()
            }
        } ?: run {
            tvPlayingCount.gone()
        }
        
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}
