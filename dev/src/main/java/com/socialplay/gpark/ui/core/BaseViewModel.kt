package com.socialplay.gpark.ui.core

import android.content.ComponentCallbacks
import android.os.Looper
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.FragmentViewModelContext
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.airbnb.mvrx.withState
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.getPageMonitorTag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.ext.getFullName
import kotlin.reflect.KProperty1

abstract class BaseViewModel<S : MavericksState>(initialState: S) :
    MavericksViewModel<S>(initialState) {

    companion object {
        private val mainThread by lazy { Looper.getMainLooper().thread }
    }

    val oldState: S get() = withState(this) { it }

    override fun toString(): String {
        return super.toString() + ":" + Integer.toHexString(hashCode())
    }

    private fun isMainThread() = mainThread == Thread.currentThread()

    protected fun runMainThread(block: () -> Unit) {
        runCatching {
            if (isMainThread()) {
                block()
            } else {
                viewModelScope.launch(Dispatchers.Main) {
                    block()
                }
            }
        }
    }

    fun <A: Async<*>> apiMonitor(fragment: Fragment, property: KProperty1<S, A>) {
        val tag = fragment.getPageMonitorTag()
        val name = fragment::class.getFullName()
        val hashCode = fragment.hashCode()
        onEach(property) {
            when (it) {
                is Success<*> -> {
                    PageMonitor.apiRequestEnd(name, hashCode, tag)
                }

                is Fail<*> -> {
                    PageMonitor.apiRequestError(name, hashCode, tag)
                }

                is Loading<*> -> {
                    PageMonitor.apiRequestStart(name, hashCode, tag)
                }

                else -> {

                }
            }
        }
    }
}

abstract class KoinViewModelFactory<VM : MavericksViewModel<S>, S : MavericksState> :
    MavericksViewModelFactory<VM, S> {
    final override fun create(viewModelContext: ViewModelContext, state: S): VM? {
        return viewModelContext.targetComponentCallbacks().create(viewModelContext, state)
    }

    final override fun initialState(viewModelContext: ViewModelContext): S? {
        return viewModelContext.targetComponentCallbacks().initialState(viewModelContext)
    }

    private fun ViewModelContext.targetComponentCallbacks(): ComponentCallbacks =
        if (this is FragmentViewModelContext) fragment else activity

    open fun ComponentCallbacks.create(viewModelContext: ViewModelContext, state: S): VM? = null

    open fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): S? = null

}