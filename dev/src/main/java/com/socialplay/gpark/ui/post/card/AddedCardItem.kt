package com.socialplay.gpark.ui.post.card

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.databinding.HeaderAddedCardBinding
import com.socialplay.gpark.databinding.ItemAddedCardBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController

/**
 * Created by bo.li
 * Date: 2023/9/28
 * Desc:
 */

interface ICommunityAddedCardListener : IBaseEpoxyItemListener {
    fun onClickDelete(card: PostCardInfo)
}

fun MetaEpoxyController.addedCard(
    maxNum: Int,
    list: List<PostCardInfo>,
    listener: ICommunityAddedCardListener
) {
    val currentNum = list.size
    if (currentNum == 0) {
        return
    }
    add(AddedCardHeader(currentNum, maxNum).id("AddedCardHeader"))
    list.forEachIndexed { index, communityCardInfo ->
        add(AddedCardItem(communityCardInfo, listener).id("AddedCardItem-$index"))
    }
}

data class AddedCardHeader(
    val currentNum: Int,
    val maxNum: Int,
) : ViewBindingItemModel<HeaderAddedCardBinding>(
    R.layout.header_added_card,
    HeaderAddedCardBinding::bind
) {
    override fun HeaderAddedCardBinding.onBind() {
        root.text = getItemView().context.getString(
            R.string.added_cap_with_progress_param,
            currentNum,
            maxNum
        )
    }
}

data class AddedCardItem(
    val item: PostCardInfo,
    val listener: ICommunityAddedCardListener
) : ViewBindingItemModel<ItemAddedCardBinding>(
    R.layout.item_added_card,
    ItemAddedCardBinding::bind
) {
    override fun ItemAddedCardBinding.onBind() {
        tvGameTitle.text = item.gameName
        listener.getGlideOrNull()?.run {
            load(item.gameIcon).placeholder(R.drawable.placeholder_corner_18)
                .centerCrop()
                .into(ivIcon)
        }
        tvDelete.setOnClickListener {
            listener.onClickDelete(item)
        }
    }
}