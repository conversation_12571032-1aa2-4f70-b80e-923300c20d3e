package com.socialplay.gpark.ui.main.startup

import com.socialplay.gpark.function.exoplayer.VideoFeedPreloadInteractor
import kotlinx.coroutines.suspendCancellableCoroutine
import org.koin.core.context.GlobalContext
import kotlin.coroutines.resume

class PreloadRecommendVideoTask : HomeStartupTask("PreloadRecommendVideoTask") {
    override suspend fun run() {

        val e = suspendCancellableCoroutine { cont ->
            val videoBackgroundLoadInteractor =
                GlobalContext.get().get<VideoFeedPreloadInteractor>()
            videoBackgroundLoadInteractor.preloadPartyVideoFeeds {
                if (cont.isActive) {
                    cont.resume(it)
                }
            }
        }
        if (e != null) {
            throw e
        }
    }
}