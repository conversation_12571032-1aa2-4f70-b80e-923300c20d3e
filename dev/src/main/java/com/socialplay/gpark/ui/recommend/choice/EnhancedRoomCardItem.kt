package com.socialplay.gpark.ui.recommend.choice

import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.databinding.AdapterItemRoomBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Enhanced Room card implementation with empty state support
 */
fun MetaModelCollector.choiceEnhancedRoomCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val rooms = card.roomList
    
    // Title
    textItem(
        text = card.cardName,
        textSize = 18.0f,
        fontFamily = R.font.poppins_bold_700,
        textColorRes = R.color.textColorPrimary,
        isSingleLine = true,
        ellipsize = TextUtils.TruncateAt.END,
        gravity = Gravity.START or Gravity.CENTER_VERTICAL,
        paddingLeftDp = 16.0f,
        paddingTopDp = 16.0f,
        paddingRightDp = 12.0f,
        paddingBottomDp = 8.0f,
        idStr = "ChoiceEnhancedRoomCardTitle-$cardPosition",
        spanSize = spanSize
    )
    
    if (rooms.isNullOrEmpty()) {
        // Empty state
        empty(
            iconRes = R.drawable.placeholder_corner_12,
            desc = "No active rooms right now",
            top = 16.dp,
            height = 120.dp,
            gravity = Gravity.CENTER,
            idStr = "ChoiceEnhancedRoomCardEmpty-$cardPosition",
            spanSize = spanSize
        ) {
            // Optional: Handle empty state click
            listener.onCardShow(cardPosition, card)
        }
    } else {
        // Data state - horizontal scrolling list
        carouselNoSnapWrapBuilder {
            id("ChoiceEnhancedRoomCardList-$cardPosition")
            padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
            hasFixedSize(true)
            initialPrefetchItemCount(3)
            onVisibilityStateChanged { _, _, visibilityState ->
                if (visibilityState == VisibilityState.VISIBLE) {
                    listener.onCardShow(cardPosition, card)
                }
            }
            spanSizeOverride { _, _, _ ->
                spanSize
            }
            
            val itemWidth = RoomCardUtil.getRoomCardWidth()
            rooms.forEachIndexed { position, room ->
                add(
                    ChoiceEnhancedRoomCardItem(
                        room,
                        position,
                        card,
                        cardPosition,
                        false,
                        itemWidth,
                        spanSize,
                        listener
                    ).id("ChoiceEnhancedRoomCard-$cardPosition-$position-${room.roomId}")
                )
            }
        }
    }
}

/**
 * Enhanced Room card item with improved styling
 */
data class ChoiceEnhancedRoomCardItem(
    val item: ChatRoomInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val matchParent: Boolean,
    val fitItemWidth: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterItemRoomBinding>(
    R.layout.adapter_item_room,
    AdapterItemRoomBinding::bind
) {

    override fun AdapterItemRoomBinding.onBind() {
        root.setMargin(right = 10.dp)
        
        // Use existing room card utility for consistent styling
        RoomCardUtil.setImageBg(listener.getGlideOrNull(), this, item)
        RoomCardUtil.setRoomName(matchParent, fitItemWidth, context, this, item)
        RoomCardUtil.setRoomTag(context, this, item)
        RoomCardUtil.setRoomPlayerCount(context, this, item)
        RoomCardUtil.setRoomMember(listener.getGlideOrNull(), this, item)
        RoomCardUtil.setRoomStyle(this, item)
        
        // Enhanced styling for better visual hierarchy
        enhanceRoomCardStyling()
        
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    private fun AdapterItemRoomBinding.enhanceRoomCardStyling() {
        // Add subtle shadow or border enhancement
        clContent.elevation = 2.dp.toFloat()

        // Improve text contrast
        tvRoomName.setShadowLayer(
            2f, 0f, 1f,
            context.getColor(android.R.color.black)
        )

        // Enhance player count visibility
        tvPlayerCount.background = context.getDrawable(R.drawable.bg_black_50_corner_4)
        tvPlayerCount.setPadding(6.dp, 2.dp, 6.dp, 2.dp)
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}
