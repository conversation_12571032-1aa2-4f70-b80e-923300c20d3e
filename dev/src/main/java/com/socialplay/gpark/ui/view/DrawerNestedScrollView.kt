package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.tabs.TabLayout
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed
import kotlin.math.abs

class DrawerNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr) {

    private var startX = 0
    private var startY = 0
    private var hasIntercepted = false // ACTION_MOVE 拦截后不再处理

    private var mOnEventListener: OnEventListener? = null

    interface OnEventListener {
        fun onEvent(ev: MotionEvent, dnsv: DrawerNestedScrollView, intercept: Boolean)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        handleInterceptEvent(ev)
        return super.onInterceptTouchEvent(ev)
    }

    /**
     * 根据滑动方向、距离来[requestDisallowInterceptTouchEvent]
     */
    private fun handleInterceptEvent(ev: MotionEvent) {
        val noNeedToIntercept = !canScrollHorizontally(1) && !canScrollHorizontally(-1)
        if (noNeedToIntercept) {
            return
        }

        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                hasIntercepted = false
                startX = ev.x.toInt()
                startY = ev.y.toInt()
                parent.requestDisallowInterceptTouchEvent(false)
            }

            MotionEvent.ACTION_MOVE -> {
                if (!hasIntercepted) {
                    hasIntercepted = true
                    val endX = ev.x.toInt()
                    val endY = ev.y.toInt()
                    val disX = abs(endX - startX)
                    val disY = abs(endY - startY)
                    val intercept = onHorizontalActionMove(this, endX, disX, disY)
                    mOnEventListener?.onEvent(ev, this, intercept)
                    parent.requestDisallowInterceptTouchEvent(intercept)
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        handleDispatchTouchEvent(ev)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 防止子布局[TabLayout]调用[requestDisallowInterceptTouchEvent]导致接收不到[MotionEvent.ACTION_UP]
     * 或[MotionEvent.ACTION_CANCEL]
     */
    private fun handleDispatchTouchEvent(ev: MotionEvent?) {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mOnEventListener?.onEvent(ev, this, false)
            }
        }
    }

    private fun onHorizontalActionMove(
        dnsv: DrawerNestedScrollView,
        endX: Int,
        disX: Int,
        disY: Int
    ): Boolean {
        return if (disX >= disY) {
            dnsv.canScrollHorizontally(-1) || endX - startX < 0
        } else {
            false
        }
    }

    fun setOnEventListener(owner: LifecycleOwner, onEventListener: OnEventListener) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                setOnEventListener(onEventListener)
            },
            unregister = {
                unsetOnEventListener()
            }
        )
    }

    fun setOnEventListener(onEventListener: OnEventListener) {
        mOnEventListener = onEventListener
    }

    fun unsetOnEventListener() {
        mOnEventListener = null
    }
}