package com.socialplay.gpark.ui.moments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.ui.moments.list.MomentsListFragment
import com.socialplay.gpark.ui.moments.list.MomentsListFragmentArgs
import com.socialplay.gpark.ui.moments.main.MomentsMainFragment
import com.socialplay.gpark.ui.moments.main.MomentsMainFragmentArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentMomentBinding
import com.socialplay.gpark.databinding.ViewTabCreateV2Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.addBackPressedDispatcherCallback
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.registerOnPageChangeCallback
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * 2024/7/8
 */
class MomentsFragment : BaseFragment<FragmentMomentBinding>(R.layout.fragment_moment) {

    private val args by navArgs<MomentsFragmentArgs>()

    private val momentViewModel: MomentViewModel by fragmentViewModel()

    private var tabLayoutMediator: TabLayoutMediator? = null
    private val tabListener by lazy {
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTab(tab, true)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTab(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentMomentBinding? {
        return FragmentMomentBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addBackPressedDispatcherCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                findNavController().popBackStack()
                if (args.finishMain) {
                    requireActivity().finish()
                }
            }
        })
        initView()
    }

    private fun initView() {
        binding.ivClose.setOnAntiViolenceClickListener {
            navigateUp()
            if (args.finishMain) {
                requireActivity().finish()
            }
        }

        momentViewModel.onEach(MomentUiState::tabs) {
            initTab(it)
        }
        momentViewModel.featTabs()
    }


    private fun initTab(tabs: List<String>) {
        val capacity = 2
        binding.vp.adapterAllowStateLoss = MomentsViewPagerAdapter(
            buildList(capacity) {
                add {
                    MomentsMainFragment().apply {
                        arguments = MomentsMainFragmentArgs(args.categoryId).toBundle()
                    }
                }
                add {
                    MomentsListFragment().apply {
                        arguments = MomentsListFragmentArgs(args.categoryId).toBundle()
                    }
                }
            }, childFragmentManager, viewLifecycleOwner.lifecycle
        )
        binding.tabLayout.addOnTabSelectedListener(tabListener)
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.vp) { tab, position ->
            val tabBinding = ViewTabCreateV2Binding.inflate(layoutInflater)
            val title = if (tabs.isNotEmpty()) tabs.getOrNull(position) ?: "" else ""
            tabBinding.tvNormal.text = title
            tabBinding.tvSelected.text = title
            tab.view.isClickable = false
            tab.customView = tabBinding.root.apply {
                setOnAntiViolenceClickListener(100) {
                    tab.view.performClick()
                }
            }
        }
        tabLayoutMediator?.attach()

        momentViewModel.onEach(MomentUiState::index){
            binding.vp.currentItem = it
        }

        binding.vp.registerOnPageChangeCallback(
            viewLifecycleOwner,
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    momentViewModel.updateTab(position)
                    Analytics.track(EventConstants.EVENT_MOMENTS_TEMPLATE_SHOW) {
                        put("show_categoryid", args.categoryId)
                        put("page", "${position + 1}")
                    }

                }
            })
    }

    private fun updateTab(tab: TabLayout.Tab, isSelected: Boolean) {
        tab.customView?.let {
            it.findViewById<TextView>(R.id.tv_normal)?.invisible(isSelected)
            it.findViewById<TextView>(R.id.tv_selected)?.invisible(!isSelected)
        }
    }


    override fun onDestroyView() {
        binding.vp.adapterAllowStateLoss = null
        binding.tabLayout.removeOnTabSelectedListener(tabListener)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        super.onDestroyView()
    }

    override fun invalidate() {}

    override fun getPageName(): String = PageNameConstants.FRAGMENT_MOMENTS
}