package com.socialplay.gpark.ui.main.startup

import kotlinx.coroutines.withTimeoutOrNull
import kotlin.jvm.Throws

abstract class HomeStartupTask(val taskName: String) {

    var startTime = 0L
    var timeout = 0L

    var useTime = 0L
    var isCompleted: Boolean = false
        private set
    var error: Throwable? = null
        private set

    private fun complete(error: Throwable? = null) {
        if (isCompleted) return
        isCompleted = true
        this.error = error
        useTime = System.currentTimeMillis() - startTime
    }

    suspend fun runTask() {
        if (timeout > 0) {
            withTimeoutOrNull(timeout) {
                internalRun()
            }
            complete(IllegalStateException("Task timeout($timeout)"))
        } else {
            internalRun()
        }
    }

    private suspend fun internalRun() {
        kotlin.runCatching {
            startTime = System.currentTimeMillis()
            run()
            complete()
        }.onFailure {
            complete(it)
        }
    }

    @Throws
    protected abstract suspend fun run()
}