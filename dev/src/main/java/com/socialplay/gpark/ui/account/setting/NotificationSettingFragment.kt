package com.socialplay.gpark.ui.account.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.app.NotificationManagerCompat
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentNotificationSettingBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

class NotificationSettingFragment: BaseFragment<FragmentNotificationSettingBinding>(){
    private val vm by viewModel<SettingViewModel>()

    var isGoSys = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentNotificationSettingBinding? {
        return FragmentNotificationSettingBinding.inflate(inflater, container, false)
    }

    override fun onResume() {
        super.onResume()
        if (isGoSys) {
            val enabled = NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()
            Analytics.track(
                EventConstants.EVENT_NOTIFICATION_APPLICATION,
                "result" to enabled.toString()
            )
            isGoSys = false
            initNotificationSet()
        }

    }

    override fun init() {
        vm.switch.observe(viewLifecycleOwner) {
            binding.metaSwitch.isChecked = it
        }
        binding.metaSwitch.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            Analytics.track(EventConstants.EVENT_NOTIFICATION_SWITCH, "state" to isChecked.toString())

            vm.setNotificationSwitch(isChecked)
        }
        binding.llGoSystemSetting.setOnAntiViolenceClickListener {
            isGoSys = true
            PermissionRequest.goNotification(requireActivity())
        }
        binding.titleBar.setOnBackClickedListener {
            navigateUp()
        }
        initNotificationSet()
        binding.tvNotificationSet.text = getString(R.string.notification_setting)
        binding.tvNotificationSetNext.text = getString(R.string.notification_setting_next)
    }
    private fun initNotificationSet() {
        if (NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()) {
            binding.llGoSystemSetting.gone()
        } else {
            binding.llGoSystemSetting.visible()
        }
    }

    override fun loadFirstData() {
        vm.loadNotification()
    }

    override fun getFragmentName(): String {
        return "NotificationSettingFragment"
    }
}