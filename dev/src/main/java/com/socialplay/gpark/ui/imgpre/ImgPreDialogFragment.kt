package com.socialplay.gpark.ui.imgpre

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogImgPreBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.observer.LifecycleObserver
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.ImageUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle

/**
 * create by: bin on 2021/6/29
 */
class ImgPreDialogFragment : BaseDialogFragment() {

    companion object {
        fun show(
            activity: FragmentActivity,
            imgUrls: Array<String>,
            from: String,
            position: Int = 0,
            showSaveBtn: Boolean = false,
            imgAction: String? = null,
            skipCache: Boolean = false,
        ): ImgPreDialogFragment? {
            val manager = activity.supportFragmentManager
            return if (!manager.isStateSaved) {
                ImgPreDialogFragment().apply {
                    arguments = ImgPreDialogFragmentArgs(
                        imgUrls,
                        position,
                        showSaveBtn,
                        imgAction,
                        from,
                        skipCache
                    ).toBundle()
                    show(manager, "img_pre")
                }
            } else null
        }
    }

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun windowHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override val binding by viewBinding(DialogImgPreBinding::inflate)

    private val args: ImgPreDialogFragmentArgs by navArgs()

    private var onImageAction: (ImgPreDialogFragment.() -> Unit)? = null

    override var navColorRes = R.color.black

    @SuppressLint("SetTextI18n")
    override fun init() {
        LifecycleObserver(this, PageNameConstants.PROFILE_USER_AVATAR)
        val imgUrls = args.imgUrls
        val position = args.position
        val showSave = args.showSave
        val imgAction = args.imgAction
        val skipCache = args.skipCache
        binding.viewPager2.adapterAllowStateLoss = ImgPreAdapter(
            Glide.with(this).apply {
                if (skipCache) {
                    setDefaultRequestOptions(
                        RequestOptions.skipMemoryCacheOf(true)
                            .diskCacheStrategy(DiskCacheStrategy.NONE)
                    )
                }
            },
            viewLifecycleOwner.lifecycleScope,
            ArrayList(imgUrls.toList())
        ).apply {
            setList(imgUrls.toMutableList())
            addChildClickViewIds(R.id.pv, R.id.ssiv, R.id.pb)
            setOnItemClickListener { _, _ ->
                kotlin.runCatching { dismissAllowingStateLoss() }
            }
            setOnItemChildClickListener { _, _ ->
                kotlin.runCatching { dismissAllowingStateLoss() }
            }
        }

        val size = imgUrls.size
        initIndicatorView(size)
        binding.viewPager2.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.indicatorImg.onPageSelected(position)
            }
        })

        binding.viewPager2.setCurrentItem(position, false)

        binding.ivImageDetailSave.isVisible = showSave
        binding.ivImageDetailSave.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_DOWNLOAD_PROFILE_PHOTO)
            val imagePath = imgUrls[binding.viewPager2.currentItem]
            if (imagePath.isEmpty()) {
                toast(R.string.save_failed)
            } else {
                saveCurrentImageWithPermission(imagePath)
            }
        }

        binding.tvImgAction.isVisible = !imgAction.isNullOrEmpty()
        binding.tvImgAction.text = imgAction ?: ""
        binding.tvImgAction.setOnAntiViolenceClickListener {
            onImageAction?.invoke(this)
        }
        binding.ibBack.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
    }

    private fun initIndicatorView(imgSize: Int) {
        val dp8 = 8.dp.toFloat()
        binding.indicatorImg.isVisible = imgSize > 1
        binding.indicatorImg.apply {
            setIndicatorStyle(IndicatorStyle.CIRCLE)
            setSliderWidth(dp8)
            setSliderHeight(dp8)
            setSlideMode(IndicatorSlideMode.NORMAL)
            setSliderGap(dp8)
            setPageSize(imgSize)
            notifyDataChanged()
            setCurrentPosition(args.position)
        }
    }

    private fun saveCurrentImageWithPermission(imagePath: String) {
        saveCurrentImage(imagePath)
    }

    private fun saveCurrentImage(imagePath: String) {
        context?.let { ctx ->
            ImageUtil.saveImageToGalleryByUrl(
                ctx,
                imagePath,
                viewLifecycleOwner.lifecycleScope
            ) { isSuccess ->
                if (isSuccess) {
                    toast(R.string.saved)
                } else {
                    toast(R.string.save_failed)
                }
            }
        }
    }

    private fun getPositionText(position: Int, size: Int) = "${position + 1}/$size"

    fun setOnAction(onAction: ImgPreDialogFragment.() -> Unit) {
        onImageAction = onAction
    }

    override fun loadFirstData() {}

    override fun dimAmount(): Float = 1F


}