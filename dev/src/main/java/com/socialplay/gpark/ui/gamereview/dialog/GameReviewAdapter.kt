package com.socialplay.gpark.ui.gamereview.dialog

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.ReviewKv
import com.socialplay.gpark.data.model.gamereview.AppraiseReply
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.databinding.ItemGameReviewAllBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_COMMENT
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_REPLAY
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import com.socialplay.gpark.util.extension.addIf
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setBackgroundColorByRes
import com.socialplay.gpark.util.extension.setOnAntiViolenceChildItemClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/7/6
 * Desc:
 */
class GameAppraiseAdapter(
    private val glide: RequestManager,
    private val uuid: String,
    private val gameId: String,
    private val reviewKv: ReviewKv,
    private val authorId: String,
    private val listener: AppraiseItemListener?,
    private var isAiBot: Boolean? = false
) : BasePagingDataAdapter<GameAppraiseData, ItemGameReviewAllBinding>(DIFF_CALLBACK) {

    companion object {

        const val SHIELD_OR_REPORT_OPINION = "shield_or_report_opinion"
        const val SHIELD_OR_REPORT_REPLY_OPINION = "shield_or_report_reply_opinion"
        const val DELETE_REPLY = "delete_reply"
        const val PAYLOAD_COMMENT_OPINION = "payload_comment_opinion"
        const val PAYLOAD_REPLY_OPINION = "payload_reply_opinion"
        const val PAYLOAD_COMMENT_LIKE = "payload_comment_like"
        const val PAYLOAD_COMMENT_CONTENT = "payload_comment_content"
        const val PAYLOAD_COMMENT_IS_SHOW = "payload_comment_is_show"
        const val ADD_MORE_REPLY = "add_more_reply"
        const val add_SELF_REPLY = "add_self_reply"

        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<GameAppraiseData>() {

            override fun areItemsTheSame(oldItem: GameAppraiseData, newItem: GameAppraiseData) =
                oldItem == newItem

            override fun areContentsTheSame(
                oldItem: GameAppraiseData,
                newItem: GameAppraiseData
            ): Boolean {
                return oldItem == newItem
            }

            override fun getChangePayload(
                oldItem: GameAppraiseData,
                newItem: GameAppraiseData
            ): Any? {
                return arrayListOf<String>()
                    .addIf(PAYLOAD_COMMENT_OPINION) { oldItem.opinion != newItem.opinion }
                    .addIf(PAYLOAD_COMMENT_LIKE) { oldItem.likeCount != newItem.likeCount }
                    .addIf(PAYLOAD_COMMENT_CONTENT) { oldItem.content != newItem.content }
                    .addIf(PAYLOAD_COMMENT_IS_SHOW) { oldItem.isShow != newItem.isShow }
                    .ifEmptyNull()
            }
        }
    }

    /**
     * 是否是自己的评论
     */
    private var isMyReview = false
    private val firstShowReplyCount = 3

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemGameReviewAllBinding {
        return ItemGameReviewAllBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            return
        }
        payloads.forEach {
            when (it) {
                PAYLOAD_COMMENT_OPINION -> { //评论点赞或取消
                    updateMyOpinionView(holder, item)
                }

                SHIELD_OR_REPORT_OPINION -> {
                    shieldingItem(holder, item)
                }

                ADD_MORE_REPLY -> { //展示更多回复
                    showMoreReplyView(holder, item)
                }

                add_SELF_REPLY -> { //添加用户自己的回复
                    showSelfReplyView(holder, item, position)
                    updateReplayClick(holder.binding.rvAppraiseReply.adapter as AppraiseReplyAdapter, item)
                }

                PAYLOAD_REPLY_OPINION -> { //回复点赞或取消
                    updateReplyOpinionView(holder, item)
                }

                SHIELD_OR_REPORT_REPLY_OPINION -> { //举报
                    shieldingReplyItem(holder, item)
                }

                DELETE_REPLY -> { //删除回复
                    deleteReplyItem(holder, item)
                }
            }
        }
    }

    override fun convert(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData,
        position: Int
    ) {
        if (isAiBot == true) {
            glide.load(R.drawable.icon_ai_bot_more).into(holder.binding.includeAppraise.ivMyReviewMore)
            holder.binding.clBg.background = null
            holder.binding.vLine.setBackgroundColor(Color.parseColor("#0DFFFFFF"))
        } else {
            holder.binding.clBg.setBackgroundColorByRes(R.color.white)
            glide.load(R.drawable.ic_review_more)
                .into(holder.binding.includeAppraise.ivMyReviewMore)
        }

        item.position = position
        Timber.d("item.position ${item.position} uid ${item.uid}")
        if (!item.isShow) {
            item.isShow = true
            showItem(holder, item)
        }
        if (item.uid == uuid) { //self review
            item.isMyReview = true
            Timber.d("shieldingItem-self review uuid $uuid ")
        }

        if (reviewKv.isReportCommentId(gameId, item.commentId)) {
            Timber.d("shieldingItem-Shield or Report commentId${item.commentId}")
            shieldingItem(holder, item)
        }
        if (position == itemCount - 1) {
            holder.binding.vLine.visible(false)
        }
        updateMyOpinionView(holder, item)
        updateAppraiseView(holder, item)
        updateReplyView(holder, item)
        updateMoreReplay(holder, item)
        val canSend = !item.isSendEvent && item.isShow
        if (canSend) {
            item.isSendEvent = true
            listener?.sendEventCallback(item.commentId)
        }
    }

    private fun updateAppraiseView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val binding = holder.binding.includeAppraise
        if (isAiBot == true) {
            binding.tvGameReviewName.setTextColor(context.getColorByRes(R.color.white_40))
            binding.tvGameReviewContent.setTextColor(context.getColorByRes(R.color.white_90))
            binding.tvPostTime.setTextColor(context.getColorByRes(R.color.color_B3B3B3))
            binding.tvLike.setTextColor(context.getColorByRes(R.color.white_90))
            binding.ratingbar.invisible()
            binding.ratingbarAibot.visible()
            binding.root.background = null
        } else {
            binding.ratingbar.visible()
            binding.ratingbarAibot.gone()
            binding.root.setBackgroundColorByRes(R.color.white)
        }
        glide.load(item.avatar).placeholder(R.drawable.placeholder_white_round)
            .into(binding.ivGameReviewAvatar)
        binding.tvGameReviewName.text = item.nickname
        binding.tvLike.text = item.likeCount.toString()
        likeItemConvert(holder, item.isLike())
        binding.tvGameReviewContent.text = item.content
        binding.tvPostTime.text = item.commentTime.formatWholeDate()
        binding.ivMyReviewMore.isVisible = true
        binding.ratingbar.rating = item.score.toFloat()
        binding.ratingbarAibot.rating = item.score.toFloat()
        binding.tvTop.visible(item.top)
        binding.labelGroup.show(
            item.tagIds,
            item.userLabelInfo,
            isMe = item.uid == uuid,
            isCreator = item.uid == authorId
        )
        binding.ivMyReviewMore.setOnAntiViolenceClickListener {
            listener?.moreCallback(
                binding.ivMyReviewMore,
                item.commentId,
                null,
                TYPE_COMMENT,
                item.isMyReview
            )
        }
        binding.viewGameOpt.setOnAntiViolenceClickListener {
            listener?.jumpHomePage(item.uid)
        }
        binding.imgLike.setOnAntiViolenceClickListener {
            listener?.likeCallback(item, if (item.isLike()) GameAppraiseData.OPTION_NO_STATE else GameAppraiseData.OPTION_LIKE)
            likeItemConvert(holder, !item.isLike())
        }
        binding.tvGameReviewContent.setOnClickListener {
            listener?.replyCommentCallback(
                item,
                0,
                item.commentId,
                "",
                item.uid,
                item.nickname ?: ""
            )
        }
    }

    private fun updateMoreReplay(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val total = item.getReplyCount()
        val authorReplyTotal = item.authorReply?.size ?: 0
        val replyAdapter = holder.binding.rvAppraiseReply.adapter as AppraiseReplyAdapter
        holder.binding.tvExpand.setTextWithArgs(R.string.expand_replay, total)
        val showList = replyAdapter.data
        //是否有外显 (只显示作者回复)
        if (authorReplyTotal > 0) {
            Timber.d("have authorReply")
            holder.binding.tvCollapse.isVisible = false
            holder.binding.tvExpand.isVisible = showList.size < item.getReplyCount()
        } else if (total > 0) {
            Timber.d("have reply not visible externally")
            holder.binding.tvCollapse.isVisible = false
            holder.binding.tvExpand.isVisible = showList.size < item.getReplyCount()
        } else {
            holder.binding.tvCollapse.isVisible = false
            holder.binding.tvExpand.isVisible = false
        }
        Timber.d("all reply total $total")
        //展开更多
        holder.binding.tvExpand.setOnAntiViolenceClickListener {
            val showList = replyAdapter.data
            Timber.d("getReplyCount authorReplyTotal $authorReplyTotal showList ${showList.size}")
            val showSize = showList.size
            Timber.d("show size $showSize ,author reply size ${item.authorReply?.size} ,first request reply size ${item.replyCommonPage?.dataList?.size}")
            Timber.d("moreReplyClicked ${item.moreReplyClicked}")

            if (!item.moreReplyClicked) {
                item.moreReplyClicked = true
                val authorReplyIdList = item.authorReply?.map { it.replyId }
                item.replyCommonPage?.dataList?.map { reply ->
                    if (reply.uid == uuid) {
                        reply.isSelfReply = true
                    }
                    if ((authorReplyIdList?.contains(reply.replyId)) == false) {
                        showList.add(reply)
                    }
                }
                Timber.d("all reply size ${showList.size} ${item.getReplyCount()}")
                Analytics.track(EventConstants.EVENT_GAME_REVIEW_EXPAND)
                if (showList.isEmpty()) {
                    holder.binding.tvExpand.isVisible = false
                    if (total > 0) {
                        listener?.moreReplyCallback(it, item)
                    }
                    return@setOnAntiViolenceClickListener
                }
                if (showList.size < item.getReplyCount()) {
                    //还有更多的评论
                    Timber.d("expand more")
                    holder.binding.tvExpand.text = context.getString(R.string.expand_more)
                    holder.binding.tvExpand.isVisible = true
                } else {
                    holder.binding.tvExpand.isVisible = false
                }
                replyAdapter.setList(showList)
            } else {
                if (item.replyCommonPage?.end == true || (showList.size.toLong() == item.getReplyCount())) {
                    holder.binding.tvExpand.isVisible = false
                } else {
                    holder.binding.tvExpand.text = context.getString(R.string.expand_more)
                    Analytics.track(EventConstants.EVENT_GAME_REVIEW_EXPAND_USER_CLICK)
                    listener?.moreReplyCallback(it, item)
                }
            }

            holder.binding.tvCollapse.isVisible = showList.size != 0
            holder.binding.rvAppraiseReply.isVisible = true
        }

        //折叠
        holder.binding.tvCollapse.setOnAntiViolenceClickListener {
            holder.binding.rvAppraiseReply.isVisible = false
            holder.binding.tvExpand.isVisible = true
            holder.binding.tvCollapse.isVisible = false
            holder.binding.tvExpand.setTextWithArgs(
                R.string.expand_replay,
                item.replyCommonPage?.total
            )
        }

        if (!holder.binding.tvCollapse.isVisible && !holder.binding.tvExpand.isVisible) {
            val layoutParams = holder.binding.vLine.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 17.dp
            holder.binding.vLine.layoutParams = layoutParams
        } else {
            val layoutParams = holder.binding.vLine.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 45.dp
            holder.binding.vLine.layoutParams = layoutParams
        }
    }

    private fun updateReplyView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val replyList =
            runCatching {
                item.replyCommonPage?.dataList?.take(firstShowReplyCount)?.toMutableList()
            }.getOrNull()
        val authorReply =
            runCatching {
                item.getGameAuthorReply().take(firstShowReplyCount).toMutableList()
            }.getOrNull()

        holder.binding.rvAppraiseReply.isVisible =
            !replyList.isNullOrEmpty() && item.getGameAuthorReply().isNotEmpty()
        val replyAdapter = AppraiseReplyAdapter(
            glide, gameId, reviewKv, uuid, authorId, isAiBot ?: false, object : AppraiseReplyAdapter.ReplyItemListener {
                override fun likeReplyCallback(replyId: String, isLike: Boolean) {
                    listener?.likeReplyCallback(replyId, isLike)
                }

                override fun clickLabel(data: Pair<Int, LabelInfo?>) {
                    listener?.clickLabel(data)
                }
            }
        ).also {
            updateReplayClick(it, item)
            it.setList(authorReply)
        }
        holder.binding.rvAppraiseReply.adapter = replyAdapter
    }

    private fun updateReplayClick(it: AppraiseReplyAdapter, item: GameAppraiseData) {
        it.setOnItemClickListener { _, _, position ->
            Timber.d("OnItemClickListener")
            val replyItem = it.getItem(position)
            val myComment = replyItem.uid == uuid
            listener?.onClickComment(myComment, item, replyItem, position)
        }
        it.addChildClickViewIds(
            R.id.ivMyReplyMore,
            R.id.tvGameReplyContent,
            R.id.viewGameOpt,
        )
        it.setOnAntiViolenceChildItemClickListener { adapter, view, position ->
            val replyItem = it.getItem(position)
            Timber.d("click position $position")
            when (view.id) {
                R.id.viewGameOpt -> {
                    // 去个人主页
                    listener?.jumpHomePage(replyItem.uid)
                }

                R.id.ivMyReplyMore -> {
                    listener?.moreCallback(
                        view,
                        item.commentId,
                        replyItem.replyId,
                        TYPE_REPLAY,
                        replyItem.uid == uuid
                    )
                }

                R.id.tvGameReplyContent -> {
                    Timber.d("reply ${replyItem.uid} ${replyItem.nickname}")
                    Analytics.track(EventConstants.EVENT_GAME_REVIEW_REPLIES_CLICK)
                    listener?.replyCommentCallback(
                        item,
                        position + 1,
                        item.commentId,
                        replyItem.replyId,
                        replyItem.uid,
                        replyItem.nickname ?: "",
                    )
                }
            }
        }
    }

    private fun shieldingItem(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val binding = holder.binding
        binding.root.visibility = View.GONE
        item.isShow = false
        binding.root.layoutParams.height = 0
        binding.root.layoutParams.width = 0
    }

    private fun shieldingReplyItem(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val binding = holder.binding
        val adapter = (binding.rvAppraiseReply.adapter as AppraiseReplyAdapter)
        adapter.notifyDataSetChanged()
    }

    private fun deleteReplyItem(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {

        val replyAdapter = holder.binding.rvAppraiseReply.adapter as AppraiseReplyAdapter
        val data = replyAdapter.data
        Timber.d("showMoreReplyView showReply ${data.size}")
        replyAdapter.setList(item.replyCommonPage?.dataList)
        if (holder.binding.tvExpand.text.toString() != context.getString(R.string.expand_more)) {
            holder.binding.tvExpand.setTextWithArgs(
                R.string.expand_replay,
                item.replyCommonPage?.total ?: 1
            )
        }
        updateExpandTextView(holder, item)
        if (item.replyCommonPage?.dataList.isNullOrEmpty()) {
            holder.binding.tvExpand.gone()
            holder.binding.tvCollapse.gone()
        }
        if (!holder.binding.tvCollapse.isVisible && !holder.binding.tvExpand.isVisible) {
            val layoutParams = holder.binding.vLine.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 17.dp
            holder.binding.vLine.layoutParams = layoutParams
        } else {
            val layoutParams = holder.binding.vLine.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 45.dp
            holder.binding.vLine.layoutParams = layoutParams
        }
        replyAdapter.notifyDataSetChanged()
    }

    private fun showItem(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        Timber.d("showItem")
        item.isShow = true
        val binding = holder.binding
        binding.root.visibility = View.VISIBLE
        binding.root.layoutParams.height = WRAP_CONTENT
        binding.root.layoutParams.width = MATCH_PARENT
    }

    private fun likeItemConvert(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        isLike: Boolean
    ) {
        val binding = holder.binding.includeAppraise

        if (isAiBot == true) {
            if (isLike) {
                binding.imgLike.setImageResource(R.drawable.icon_like_yes_1a)
                binding.tvLike.setTextColorByRes(R.color.white_90)
            } else {
                binding.imgLike.setImageResource(R.drawable.icon_like_no_b3)
                binding.tvLike.setTextColorByRes(R.color.color_B3B3B3)
            }
        } else {
            if (isLike) {
                binding.imgLike.setImageResource(R.drawable.icon_like_yes_1a)
                binding.tvLike.setTextColorByRes(R.color.color_1A1A1A)
            } else {
                binding.imgLike.setImageResource(R.drawable.icon_like_no_b3)
                binding.tvLike.setTextColorByRes(R.color.color_B3B3B3)
            }
        }
    }

    private fun updateMyOpinionView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        Timber.d("updateMyOpinionView  ${item.likeCount}  ${item.isLike()}")
        val binding = holder.binding
        binding.includeAppraise.tvLike.text = item.likeCount.toString()
        likeItemConvert(holder, item.isLike())
    }

    private fun updateReplyOpinionView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        Timber.d("updateMyOpinionView  ${item.likeCount}  ${item.isLike()}")
        val binding = holder.binding
        binding.includeAppraise.tvLike.text = item.likeCount.toString()
        likeItemConvert(holder, item.isLike())
    }

    //展示更多回复
    private fun showMoreReplyView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData
    ) {
        val replyAdapter = holder.binding.rvAppraiseReply.adapter as AppraiseReplyAdapter
        val data = replyAdapter.data
        Timber.d("showMoreReplyView showReply ${data.size}")
        replyAdapter.setList(item.replyCommonPage?.dataList)
        holder.binding.rvAppraiseReply.visible(item.replyCommonPage?.dataList?.isEmpty() == false)
        if (item.replyCommonPage?.end == true || (item.replyCommonPage?.dataList?.size ?: 0).toLong() == item.getReplyCount()) {
            holder.binding.tvExpand.isVisible = false
        }
        updateExpandTextView(holder, item)
    }

    private fun updateExpandTextView(holder: BindingViewHolder<ItemGameReviewAllBinding>, item: GameAppraiseData) {
        if (!holder.binding.tvExpand.isVisible) {
            holder.binding.tvExpand.setTextWithArgs(
                R.string.expand_replay,
                item.replyCommonPage?.total ?: 1
            )
        }
    }


    private fun showSelfReplyView(
        holder: BindingViewHolder<ItemGameReviewAllBinding>,
        item: GameAppraiseData,
        position: Int
    ) {
        val replyAdapter = holder.binding.rvAppraiseReply.adapter as AppraiseReplyAdapter
        //如果是有外显或者没有外显的 添加到 replyAdapter.data后面
        val data = replyAdapter.data
        Timber.d("showSelfReplyView showReply ${data.size}")
        data.addAll(item.temporaryDataList)
        item.temporaryDataList.clear()
        Timber.d("temporaryDataList size ${replyAdapter.data.size}")
        replyAdapter.setList(data)
        if (replyAdapter.data.isNotEmpty()) {
            holder.binding.rvAppraiseReply.visible(true)
            holder.binding.tvExpand.setTextWithArgs(
                R.string.expand_replay,
                item.replyCommonPage?.total ?: 1
            )
        }
    }


    interface AppraiseItemListener {
        companion object {
            const val TYPE_COMMENT = 3 // 评论
            const val TYPE_REPLAY = 4  // 回复
        }

        fun moreCallback(view: View, commentId: String, replyId: String?, type: Int, isMe: Boolean)
        fun moreReplyCallback(view: View, gameReview: GameAppraiseData)
        fun replyCommentCallback(
            gameReview: GameAppraiseData,
            position: Int,
            commentId: String,
            replyId: String,
            replyUid: String,
            replyNickname: String,
        )

        fun likeCallback(gameReview: GameAppraiseData, attitude: Int)
        fun likeReplyCallback(replyId: String, isLike: Boolean)
        fun sendEventCallback(commentId: String)
        fun onClickComment(
            justJumpDetail: Boolean,
            commentItem: GameAppraiseData,
            replyItem: AppraiseReply?,
            position: Int
        )

        fun jumpHomePage(uid: String)
        fun clickLabel(data: Pair<Int, LabelInfo?>)
    }
}