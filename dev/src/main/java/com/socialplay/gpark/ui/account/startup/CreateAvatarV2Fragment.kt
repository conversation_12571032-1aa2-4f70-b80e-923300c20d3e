package com.socialplay.gpark.ui.account.startup

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.guide.GuideScene
import com.socialplay.gpark.databinding.FragmentStartupCreateAvatarV2Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.BaseJumpRoleFragmentV2
import com.socialplay.gpark.util.QuitAppUtil
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize
import org.koin.android.ext.android.inject

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/20
 *     desc   :
 * </pre>
 */
@Parcelize
data class CreateAvatarFragmentArgs(
    val showBack: Boolean = false,
    val showSkip: Boolean = false
) : Parcelable

class CreateAvatarV2Fragment :
    BaseJumpRoleFragmentV2<FragmentStartupCreateAvatarV2Binding>(R.layout.fragment_startup_create_avatar_v2) {

    override var navColorRes = R.color.color_0E0922

    private val vm: CreateAvatarV2ViewModel by fragmentViewModel()
    private val banBlockInteractor: BanBlockInteractor by inject()

    private val args by args<CreateAvatarFragmentArgs>()

    private var genderTransY = -(52.5f.dp).toFloat()
    private val expectHeight = 420.dp
    private var isShowBack: Boolean = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStartupCreateAvatarV2Binding? {
        return FragmentStartupCreateAvatarV2Binding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        statusBarViewModel.update(false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvNextBtn.enableWithAlpha(false)

//        isShowBack = args.showBack && !EnvConfig.isParty()
        isShowBack = false
        val isShowSkip = args.showSkip && !EnvConfig.isParty()
        binding.tbl.invisible(!isShowBack)
        binding.tvSkipBtn.visible(isShowSkip)
        binding.tbl.setOnBackAntiViolenceClickedListener {
            jumpBack()
        }
        binding.ivCharMale.setOnAntiViolenceClickListener(200) {
            vm.chooseRole(0)
        }
        binding.ivCharFemale.setOnAntiViolenceClickListener(200) {
            vm.chooseRole(1)
        }
        if (vm.fetchMaleAvatar() != null) {
            Glide.with(this).load(vm.fetchMaleAvatar()?.wholeBodyImage).into(binding.ivCharMale)
        }
        if (vm.fetchFemaleAvatar() != null) {
            Glide.with(this).load(vm.fetchFemaleAvatar()?.wholeBodyImage).into(binding.ivCharFemale)
        }
        binding.tvSkipBtn.setOnAntiViolenceClickListener(200) {
            if (vm.isBanned()) {
                banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_EDIT_PROFILE, this)
            } else {
                val (info, portraitRes) = vm.defaultAvatar
                jump2Next(info, portraitRes, true)
            }
        }
        binding.tvNextBtn.setOnAntiViolenceClickListener(200) {
            if (vm.isBanned()) {
                banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_EDIT_PROFILE, this)
            } else {
                val (position, info, portraitRes) = vm.selectedAvatar ?: return@setOnAntiViolenceClickListener
                val sexText = if (position == 0) "male" else "female"
                Analytics.track(
                    EventConstants.CREATE_AVATAR_NEXT_CLICK,
                    "avatar_choose" to sexText,
                    "type" to info.id
                )
                jump2Next(info, portraitRes)
            }
        }

        binding.clMale.doOnLayoutSized {
            if (it.height < expectHeight) {
                val scaleRatio = it.height.toFloat() / expectHeight
                genderTransY *= scaleRatio
            }

            binding.ivGenderMale.translationY = genderTransY
            binding.ivGenderFemale.translationY = genderTransY

            var first = true // no switch anim on first load
            vm.onEach(CreateAvatarV2State::avatar) { avatar ->
                val sexText = if (avatar == 0) "male" else "female"
                vm.saveUserSelectAvatarSex(sexText)
                when (avatar) {
                    0 -> {
                        select(
                            binding.ivCharMale,
                            binding.ivHoloMale,
                            binding.ivGenderMale,
                            first
                        )
                        unselect(
                            binding.ivCharFemale,
                            binding.ivHoloFemale,
                            binding.ivGenderFemale
                        )
                        binding.tvNextBtn.enableWithAlpha(true)
                    }

                    1 -> {
                        select(
                            binding.ivCharFemale,
                            binding.ivHoloFemale,
                            binding.ivGenderFemale,
                            first
                        )
                        unselect(
                            binding.ivCharMale,
                            binding.ivHoloMale,
                            binding.ivGenderMale
                        )
                        binding.tvNextBtn.enableWithAlpha(true)
                    }

                    else -> {

                    }
                }
                first = false
            }
        }

        Analytics.track(EventConstants.CREATE_AVATAR_NEXT_SHOW)
    }

    private fun jump2Next(info: DefaultRoleInfo, portraitRes: Int, skipAvatar: Boolean = false) {
        MetaRouter.Startup.guideNext(this, GuideScene.CREATE_AVATAR, info, portraitRes, skipAvatar)
    }

    private fun select(char: View, holo: View, gender: View, first: Boolean) {
        if (first) {
            char.scaleX = 1.0f
            char.scaleY = 1.0f
            holo.alpha = 1.0f
            gender.translationY = 0.0f
            gender.alpha = 1.0f
        } else {
            char.animate().scaleX(1.0f).scaleY(1.0f).setDuration(200L)
            holo.animate().alpha(1.0f).setDuration(200L)
            gender.animate().translationY(0.0f).alpha(1.0f).setDuration(200L)
        }
    }

    private fun unselect(char: View, holo: View, gender: View) {
        char.animate().scaleX(0.75f).scaleY(0.75f).setDuration(200L)
        holo.animate().alpha(0.0f).setDuration(200L)
        gender.animate().translationY(genderTransY).alpha(0.5f).setDuration(200L)
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_STARTUP_CREATE_AVATAR

    override fun jumpBack() {
        navigateUp()
    }

    override fun handleClickBack() {
        if (isShowBack) {
            // 如果有返回按钮，可以返回上一页
            jumpBack()
        } else {
            // 否则，返回就要退出应用
            activity?.let {
                QuitAppUtil.checkClickBackPressed(it)
            }
        }
    }

}