package com.socialplay.gpark.ui.web.webclients

import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.webkit.*
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.util.WebUtil
import timber.log.Timber

/**
 *
 * <AUTHOR>
 * @date 2021/05/19
 */
class DefaultWebViewClient(
    val fragment: Fragment,
    val callback: (<PERSON><PERSON><PERSON>, Int) -> Unit,
    val onRenderProcessGone: () -> Unit
) : WebViewClient() {
    var cur = System.currentTimeMillis()

    var isSuccess = false
    private var isError = false
    private var errorCode: Int = 0
    private var isFinished = false

    private var currentPageUrl: String? = null
    override fun shouldInterceptRequest(
        view: WebView?,
        request: WebResourceRequest?
    ): WebResourceResponse? {
        return super.shouldInterceptRequest(view, request)
    }

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
        super.onPageStarted(view, url, favicon)
        isFinished = false
        //页面开始加载的时候就直接注入
        Timber.i("onPageStarted: $url")
        cur = System.currentTimeMillis()
        currentPageUrl = url
    }

    override fun onPageFinished(view: WebView?, url: String?) {
        super.onPageFinished(view, url)
        isSuccess = !isError
        if (!isFinished){
            Analytics.track(
                EventConstants.EVENT_IAP_TIME_WEB_LOAD,
                "success" to isSuccess.toString(),
                "time" to (System.currentTimeMillis() - cur),
                "url" to url.toString()
            )
            isFinished = true
        }
        callback(isSuccess, errorCode)
        isError = false
        Timber.i("onPageFinished: $url")
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        val urlString: String = request?.url.toString()
        Timber.i("shouldOverrideUrlLoading2: $urlString")
        return shouldOverrideUrlLoading(view, urlString)
    }

    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
        Timber.i("shouldOverrideUrlLoading: $url")
        //判断是否拦截系统uri
        url?.let {
            val uri: Uri = Uri.parse(url)
            if (WebUtil.isHttpOrHttpsScheme(url)) {
                return super.shouldOverrideUrlLoading(view, url)
            } else {
                Intent(Intent.ACTION_VIEW, uri).apply {
                    if (resolveActivity(fragment.requireContext().packageManager) != null) {
                        fragment.startActivity(this)
                    }
                }
            }
            return true
        }
        return super.shouldOverrideUrlLoading(view, url)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onReceivedError(
        view: WebView?,
        request: WebResourceRequest?,
        error: WebResourceError?
    ) {
        onReceivedError(view,
            error?.errorCode ?: 0,
            error?.description.toString(),
            request?.url.toString())
    }

    override fun onReceivedError(
        view: WebView?,
        errorCode: Int,
        description: String?,
        failingUrl: String?
    ) {
        super.onReceivedError(view, errorCode, description, failingUrl)
        Timber.e("onReceivedError: $errorCode, description: $description, failingUrl: $failingUrl")
        if (currentPageUrl == failingUrl) {
            isError = true
            isSuccess = false
            this.errorCode = errorCode
        }
    }

    override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
        super.onReceivedSslError(view, handler, error)
        Timber.e("onReceivedSslError: called.")

        //允许所有的ssl
        handler?.proceed()
    }

    override fun onRenderProcessGone(view: WebView?, detail: RenderProcessGoneDetail?): Boolean {
        val crashType: Int
        val priority: Int
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            crashType = if (detail?.didCrash() == true) {
                1
            } else {
                2
            }
            priority = detail?.rendererPriorityAtExit() ?: -1
        } else {
            crashType = 0
            priority = -1
        }
        Analytics.track(
            EventConstants.WEB_VIEW_CRASH,
            "type" to crashType.toString(),
            "priority" to priority
        )
        onRenderProcessGone()
        return true
    }
}