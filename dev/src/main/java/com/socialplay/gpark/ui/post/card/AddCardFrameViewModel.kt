package com.socialplay.gpark.ui.post.card

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData

/**
 * Created by bo.li
 * Date: 2023/9/28
 * Desc:
 */

data class AddCardFrameModelState(
    val maxNum: Int,
    val addedCardList: List<PostCardInfo> = emptyList(),
    val toastMsg: ToastData = ToastData.EMPTY,
) : MavericksState {
    constructor(args: AddCardFragmentDialogArgs) : this(args.maxNum)
}

class AddCardFrameViewModel(
    initialState: AddCardFrameModelState
) : BaseViewModel<AddCardFrameModelState>(initialState) {

    fun addCard(card: PostCardInfo) {
        withState {
            if (it.addedCardList.size >= it.maxNum) {
                setState {
                    copy(toastMsg = toastMsg.toResMsg(R.string.delete_and_then_select))
                }
                return@withState
            }
            setState {
                copy(addedCardList = it.addedCardList + card)
            }
        }
    }

    fun deleteAddedCard(card: PostCardInfo) {
        withState {
            val list = ArrayList(it.addedCardList)
            list.remove(card)
            setState {
                copy(addedCardList = list)
            }
        }
    }

    fun initCards(list: List<PostCardInfo>?) {
        withState {
            setState {
                copy(addedCardList = list?.take(it.maxNum) ?: emptyList())
            }
        }
    }

    companion object : KoinViewModelFactory<AddCardFrameViewModel, AddCardFrameModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: AddCardFrameModelState
        ): AddCardFrameViewModel {
            return AddCardFrameViewModel(state)
        }
    }
}