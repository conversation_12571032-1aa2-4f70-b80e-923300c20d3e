package com.socialplay.gpark.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import com.socialplay.gpark.R


class QRCoverView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    View(context, attrs, defStyleAttr) {

    private companion object {
        private const val BOTTOM_TO_TOP_ADDER = -2
        private const val TOP_TO_BOTTOM_ADDER = 2
    }

    //背景色
    private val linePaint = Paint()

    //扫描边框角的颜色
    private val cornerPaint = Paint()

    //扫描边框的长度和厚度
    private var cornerLength = 0
    private var cornerWidth = 0
    private var lineWidth = 0

    //扫描框的Rect
    private val viewDrawingRect = Rect()

    private var useLaser = false
    private var laser: Drawable? = null
    private var laserPosition = 0

    private var laserPositionAdder = TOP_TO_BOTTOM_ADDER
    private var laserLineHeight = 0

    private var maskLayerRoundCorner = 0F
    private var maskLayerColor = 0x80000000.toInt()
    private var maskLayerClipOutPath = Path()

    private val maskLayerPaint = Paint()

    private val maskLeftTopCornerBmp: Bitmap
    private val maskRightTopCornerBmp: Bitmap
    private val maskRightBottomCornerBmp: Bitmap
    private val maskLeftBottomCornerBmp: Bitmap

    private var maskClipOutWidth: Int = 0
    private var maskClipOutHeight: Int = 0

    private var mode: Mode = Mode.Full

    private var gestureListener: GestureListener? = null

    private val gestureDetector = GestureDetectorCompat(getContext(), object : GestureDetector.SimpleOnGestureListener() {

        override fun onDown(e: MotionEvent): Boolean {
            return true
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return gestureListener?.onClick(e.x.toInt(), e.y.toInt()) != null
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            return gestureListener?.onDoubleClick() != null
        }
    })

    init {

        maskLayerPaint.style = Paint.Style.FILL
        maskLayerPaint.color = maskLayerColor.toInt()

        maskLeftTopCornerBmp = BitmapFactory.decodeResource(resources, R.drawable.icon_qr_code_mask_top_left_corner)
        maskRightTopCornerBmp = BitmapFactory.decodeResource(resources, R.drawable.icon_qr_code_mask_top_right_corner)

        maskRightBottomCornerBmp = BitmapFactory.decodeResource(resources, R.drawable.icon_qr_code_mask_bottom_right_corner)
        maskLeftBottomCornerBmp = BitmapFactory.decodeResource(resources, R.drawable.icon_qr_code_mask_bottom_left_corner)

        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.QRCoverView)
            try {
                cornerLength = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_cornerLength, dip2px(20f))
                cornerWidth = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_cornerWidth, dip2px(1.5f))
                lineWidth = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_lineWidth, dip2px(0.5f))
                maskLayerColor = typedArray.getColor(R.styleable.QRCoverView_maskLayerColor, 0x80000000.toInt())

                maskLayerRoundCorner = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_maskLayerCorner, dip2px(8F)).toFloat()
                maskClipOutWidth = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_maskLayerClipWidth, dip2px(200F))
                maskClipOutHeight = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_maskLayerClipHeight, dip2px(200F))
                val modeInt = typedArray.getInt(R.styleable.QRCoverView_mode, Mode.Full.value)
                mode = if (modeInt == Mode.Full.value) Mode.Full else Mode.Mask

                //默认背景色和扫描边框颜色
                linePaint.color = typedArray.getColor(R.styleable.QRCoverView_lineColor, Color.WHITE)
                useLaser = typedArray.getBoolean(R.styleable.QRCoverView_useLaser, useLaser)
                cornerPaint.color = typedArray.getColor(R.styleable.QRCoverView_cornerColor, ContextCompat.getColor(context, R.color.colorPrimaryDark))
                laser = typedArray.getDrawable(R.styleable.QRCoverView_laser)
                laserLineHeight = typedArray.getDimensionPixelOffset(R.styleable.QRCoverView_laserLineHeight, dip2px(3f))
                linePaint.style = Paint.Style.STROKE
                linePaint.strokeWidth = (lineWidth shl 1).toFloat()
            } finally {
                typedArray.recycle()
            }
        }

    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return gestureDetector.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        if (mode == Mode.Mask) {

            val scanRectWidth = maskClipOutWidth
            val scanRectHeight = maskClipOutHeight

            val cx = w / 2
            val cy = h / 2

            val l = cx - scanRectWidth / 2
            val r = cx + scanRectWidth / 2


            val t = cy - scanRectHeight / 2
            val b = cy + scanRectHeight / 2

            viewDrawingRect.set(l, t, r, b)

        } else {
            viewDrawingRect[paddingLeft, paddingTop, w - paddingRight] = h - paddingBottom
        }

        calcMaskLayerClipOutPath(maskLayerClipOutPath)

        if (isReachedBottom(viewDrawingRect)) {
            laserPosition = viewDrawingRect.bottom - cornerLength - laserLineHeight
        }

        if (isReachedTop(viewDrawingRect)) {
            laserPosition = viewDrawingRect.top + cornerLength
        }

        laser?.setBounds(viewDrawingRect.left + cornerWidth, 0, viewDrawingRect.right - cornerWidth, laserLineHeight)
    }

    override fun onDraw(canvas: Canvas) {
        if (mode == Mode.Full) {
            canvas.drawRect(viewDrawingRect, linePaint)
            drawScanCorner(canvas, viewDrawingRect)
            if (useLaser) {
                drawLaserLine(canvas, viewDrawingRect)
            }
        } else {
            drawMaskLayer(canvas)
            drawRoundCorerIndicator(canvas)
        }
    }

    private fun drawMaskLayer(canvas: Canvas) {
        canvas.save()

        canvas.drawPath(maskLayerClipOutPath, maskLayerPaint)

        canvas.restore()
    }


    private fun drawRoundCorerIndicator(canvas: Canvas) {
        canvas.drawBitmap(maskLeftTopCornerBmp, viewDrawingRect.left.toFloat(), viewDrawingRect.top.toFloat(), null)

        canvas.drawBitmap(maskRightTopCornerBmp, viewDrawingRect.right.toFloat() - maskRightTopCornerBmp.width, viewDrawingRect.top.toFloat(), null)

        canvas.drawBitmap(maskLeftBottomCornerBmp, viewDrawingRect.left.toFloat(), viewDrawingRect.bottom.toFloat() - maskLeftBottomCornerBmp.height, null)

        canvas.drawBitmap(maskRightBottomCornerBmp, viewDrawingRect.right.toFloat() - maskRightBottomCornerBmp.width, viewDrawingRect.bottom.toFloat() - maskRightBottomCornerBmp.height, null)
    }

    private fun calcMaskLayerClipOutPath(resultOutPath: Path): Path {
        val roundedRect = Path()
        roundedRect.addRoundRect(
            viewDrawingRect.left.toFloat(),
            viewDrawingRect.top.toFloat(),
            viewDrawingRect.right.toFloat(),
            viewDrawingRect.bottom.toFloat(),
            maskLayerRoundCorner, maskLayerRoundCorner,
            Path.Direction.CW)

        resultOutPath.reset()
        resultOutPath.addRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), Path.Direction.CW)
        resultOutPath.op(roundedRect, Path.Op.DIFFERENCE)
        return resultOutPath
    }


    private fun drawLaserLine(canvas: Canvas, viewRect: Rect) {
        val laser = laser ?: return

        if (isReachedBottom(viewRect)) {
            laserPositionAdder = BOTTOM_TO_TOP_ADDER
        } else if (isReachedTop(viewRect)) {
            laserPositionAdder = TOP_TO_BOTTOM_ADDER
        }

        laserPosition += laserPositionAdder

        canvas.save()
        canvas.translate(0f, laserPosition.toFloat())
        laser.draw(canvas)
        canvas.restore()
        postInvalidateOnAnimation()
    }

    internal fun isReachedTop(viewRect: Rect) = laserPosition <= viewRect.top + cornerLength

    private fun isReachedBottom(viewRect: Rect): Boolean {
        val isReachBottom = laserPosition + laserLineHeight >= viewRect.bottom - cornerLength
        return isReachBottom
    }

    /**
     * 绘制扫描边框
     *
     * @param canvas
     * @param viewFinderRect
     */
    private fun drawScanCorner(canvas: Canvas, viewFinderRect: Rect) {
        canvas.drawRect(viewFinderRect.left.toFloat(), viewFinderRect.top.toFloat(), (viewFinderRect.left + cornerWidth).toFloat(), (viewFinderRect.top + cornerLength).toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.left + cornerWidth).toFloat(), viewFinderRect.top.toFloat(), (viewFinderRect.left + cornerLength).toFloat(), (viewFinderRect.top + cornerWidth).toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.right - cornerLength).toFloat(), viewFinderRect.top.toFloat(), viewFinderRect.right.toFloat(), (viewFinderRect.top + cornerWidth).toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.right - cornerWidth).toFloat(), (viewFinderRect.top + cornerWidth).toFloat(), viewFinderRect.right.toFloat(), (viewFinderRect.top + cornerLength).toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.right - cornerWidth).toFloat(), (viewFinderRect.bottom - cornerLength).toFloat(), viewFinderRect.right.toFloat(), viewFinderRect.bottom.toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.right - cornerLength).toFloat(), (viewFinderRect.bottom - cornerWidth).toFloat(), (viewFinderRect.right - cornerWidth).toFloat(), viewFinderRect.bottom.toFloat(), cornerPaint)
        canvas.drawRect(viewFinderRect.left.toFloat(), (viewFinderRect.bottom - cornerLength).toFloat(), (viewFinderRect.left + cornerWidth).toFloat(), viewFinderRect.bottom.toFloat(), cornerPaint)
        canvas.drawRect((viewFinderRect.left + cornerWidth).toFloat(), (viewFinderRect.bottom - cornerWidth).toFloat(), (viewFinderRect.left + cornerLength).toFloat(), viewFinderRect.bottom.toFloat(), cornerPaint)
    }

    private fun dip2px(dip: Float): Int {
        return (TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dip, resources.displayMetrics) + 0.5f).toInt()
    }


    fun setGestureListener(listener:GestureListener){
        this.gestureListener = listener
    }


    private enum class Mode(val value: Int) {
        Full(0), Mask(1)
    }

    interface GestureListener {
        fun onClick(x: Int, y: Int)
        fun onDoubleClick()
        fun onZoomIn()
        fun onZoomOut()
    }
}
