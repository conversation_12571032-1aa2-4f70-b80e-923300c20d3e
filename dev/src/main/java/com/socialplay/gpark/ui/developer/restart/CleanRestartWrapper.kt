package com.socialplay.gpark.ui.developer.restart

import android.os.Environment
import com.socialplay.gpark.app.initialize.LibBuildConfigInit
import com.socialplay.gpark.data.kv.DeveloperKV
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.MetaWordKV
import com.socialplay.gpark.data.model.DevEnvType
import com.socialplay.gpark.data.model.SelectEnvItem
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.GsonUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

/**
 * xingxiu.hou
 * 2021/7/9
 */
object CleanRestartWrapper {

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    fun saveDevEnvType(env: DevEnvType, list: List<SelectEnvItem>) {

        val file = envFile()
        file.parentFile.mkdirs()
        val map = hashMapOf<String, String>()

        list.forEach {
            map[it.mmkvKey] = it.curValue
        }

        Timber.d("saveDevEnvType: env:$env,  map:$map")
        FileUtil.writeToFile(file.absolutePath, GsonUtil.gson.toJson(map))
    }

    fun checkExternalSdCardEnvConfig() {
        val envFile = envFile()
        Timber.d("checkExternalSdCardEnvConfig: isFirstOpen:${metaKV.developer.isFirstOpen}, exists:${envFile.exists()}")
        if (!metaKV.developer.isFirstOpen && !envFile.exists()) {
            return
        }
        metaKV.developer.isFirstOpen = false
        kotlin.runCatching {
            val json = FileUtil.readFile(envFile)
            val map = GsonUtil.gsonSafeParseCollection<HashMap<String, String>>(json)
            Timber.d("checkExternalSdCardEnvConfig:$map")
            LibBuildConfigInit.buildCfg.setDeveloperOpen(false)
            map?.forEach { (key, value) ->
                when (key) {
                    MetaWordKV.KEY_CORE_HOT_FIX_URL -> {
                        metaKV.mw.coreHotfixUrl = value
                    }
                    MetaWordKV.KEY_VERSE_ROOM_URL       -> {
                        metaKV.mw.verseRoomUrl = value
                    }
                    DeveloperKV.KEY_CUR_DEV_ENV_TYPE -> {
                        metaKV.developer.putString(key, value)
                    }
                    else                             -> {
                        LibBuildConfigInit.buildCfg.putString(key, value)
                    }
                }
            }
        }
        FileUtil.deleteFile(envFile)
    }

    private fun envFile() = File(
        Environment.getExternalStorageDirectory(),
        "MetaApp${File.separator}Developer${File.separator}Env.temp"
    )

}