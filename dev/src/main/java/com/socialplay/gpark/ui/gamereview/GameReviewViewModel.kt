package com.socialplay.gpark.ui.gamereview

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.gamereview.AddAppraiseReplyRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReply
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListResult
import com.socialplay.gpark.data.model.gamereview.AttentionRequest
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData.Companion.OPTION_LIKE
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData.Companion.OPTION_NO_STATE
import com.socialplay.gpark.data.model.gamereview.GameScoreResult
import com.socialplay.gpark.data.model.gamereview.ReportData
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_GAME_ONLY
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.QUERY_TYPE_MOST_FAVORABLE
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.QUERY_TYPE_NEW
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.QUERY_TYPE_TREE
import com.socialplay.gpark.data.model.gamereview.ResultAttitudeChange
import com.socialplay.gpark.data.model.gamereview.ReviewTTaiConfig
import com.socialplay.gpark.data.model.mw.PartData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_COMMENT
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter.AppraiseItemListener.Companion.TYPE_REPLAY
import com.socialplay.gpark.ui.post.v2.PostDetailViewModel
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/7/12
 * Desc:
 */
class GameReviewViewModel(
    private val repository: com.socialplay.gpark.data.IMetaRepository,
    val metaKV: MetaKV,
    private val accountInteractor: AccountInteractor,
) : ViewModel() {

    // 游戏评分
    private val _gameScoreResultLiveData: MutableLiveData<GameScoreResult?> = MutableLiveData()
    val gameScoreResultLiveData: LiveData<GameScoreResult?> = _gameScoreResultLiveData

    // 我的评论
    private val _myReviewLiveData: MutableLiveData<GameAppraiseData?> = MutableLiveData()
    val myReviewLiveData: LiveData<GameAppraiseData?> = _myReviewLiveData

    // 总评论数
    private val _reviewsCountLiveData: MutableLiveData<Long> = MutableLiveData()
    val reviewsCountLiveData: LiveData<Long> = _reviewsCountLiveData

    // 删除我的评论回调
    private val _deleteReviewLiveData: MutableLiveData<Pair<Boolean, String?>?> = MutableLiveData()
    val deleteReviewLiveData: LiveData<Pair<Boolean, String?>?> = _deleteReviewLiveData

    // 删除我的回复回调
    private val _deleteReplyLiveData: MutableLiveData<Pair<String, String?>> = MutableLiveData()
    val deleteReplyLiveData: LiveData<Pair<String, String?>> = _deleteReplyLiveData

    // 评论是否有用回调
    private val _attitudeLiveData: MutableLiveData<ResultAttitudeChange?> = MutableLiveData()
    val attitudeLiveData: LiveData<ResultAttitudeChange?> = _attitudeLiveData

    private val _appraiseFragmentLiveData = MutableLiveData<Boolean>()
    val appraiseFragmentLiveData: LiveData<Boolean> = _appraiseFragmentLiveData

    private val _configLiveData = MutableLiveData<ReviewTTaiConfig?>()
    val configLiveData: LiveData<ReviewTTaiConfig?> = _configLiveData

    private val _gameIdFlow = MutableStateFlow<Pair<String, Int>?>(null)

    private val _briefFlow = MutableStateFlow<Pair<String, Int>?>(null)
    private var targetCommentId: String? = null
    private var moduleTypeCode:Int?= MODULE_TYPE_GAME_ONLY

    private val _gameId = MutableStateFlow<String?>(null)

    private val _replyCommentLiveData = MutableLiveData<Pair<Boolean, String?>>()
    val replyCommentLiveData: LiveData<Pair<Boolean, String?>> = _replyCommentLiveData

    //回复的评论
    private val _replyList: MutableLiveData<Pair<String, AppraiseReplyListResult?>> by lazy { MutableLiveData<Pair<String, AppraiseReplyListResult?>>() }
    val replyList: LiveData<Pair<String, AppraiseReplyListResult?>> = _replyList

    //回复的评论
    private val _myReplyInfo: MutableLiveData<Pair<Int, AppraiseReply?>> by lazy { MutableLiveData<Pair<Int, AppraiseReply?>>() }
    val myReplyInfo: LiveData<Pair<Int, AppraiseReply?>> = _myReplyInfo

    // 去重Set
    private val replySet by lazy { HashSet<String>() }

    //请求回复的页数
    private var currentReplyPage: Int = 1

    private fun getCurUuid(): String = accountInteractor.accountLiveData.value?.uuid ?: ""
    //评论页的评论列表
    @ExperimentalCoroutinesApi
    @FlowPreview
    val reviewList: Flow<PagingData<GameAppraiseData>> = _gameIdFlow.flatMapLatest {
        repository.fetchAllReviewList(it?.first ?: "", it?.second, ALL_PAGE_SIZE, moduleTypeCode=moduleTypeCode)
    }

    //详情页的评论列表
    @ExperimentalCoroutinesApi
    @FlowPreview
    val briefReviewList: Flow<PagingData<GameAppraiseData>> = _briefFlow.flatMapLatest {
        repository.fetchAllReviewList(
            gameId = it?.first ?: "",
            queryType = QUERY_TYPE_MOST_FAVORABLE,
            pageSize = it?.second ?: BRIEF_PAGE_SIZE,
            false,
            targetCommentId,
            moduleTypeCode
        )
    }



    // 用于评论举报临时信息传递
    var operatingItemReqId: ReportData? = null

    companion object {
        const val BRIEF_PAGE_SIZE = 3
        const val ALL_PAGE_SIZE = 10
    }

    fun setGameId(gameId: String, queryType: Int, moduleTypeCode:Int?=MODULE_TYPE_GAME_ONLY) {
        this.moduleTypeCode = moduleTypeCode ?: MODULE_TYPE_GAME_ONLY
        _gameId.value = gameId
        _gameIdFlow.value = Pair(gameId, queryType)
    }

    fun setBriefGameId(gameId: String, querySize: Int = BRIEF_PAGE_SIZE, targetCommentId: String?, moduleTypeCode:Int?=MODULE_TYPE_GAME_ONLY) {
        this.targetCommentId = targetCommentId
        this.moduleTypeCode = moduleTypeCode ?: MODULE_TYPE_GAME_ONLY
        _gameId.value = gameId
        _briefFlow.value = Pair(gameId, querySize)

    }

    fun queryReviewConfig() = viewModelScope.launch {
        val json: String = metaKV.tTaiKV.gameReviewConfig
        Timber.d("queryReviewConfig json $json")
        if (json.isNotEmpty()) {
            _configLiveData.postValue(GsonUtil.gsonSafeParseCollection(json))
        } else {
            repository.getTTaiConfigById(TTaiKV.ID_GAME_REVIEW_CONFIG).collect {
                if (it.succeeded) {
                    val result: ReviewTTaiConfig? = GsonUtil.gsonSafeParseCollection(it.data?.value)
                    _configLiveData.postValue(result)
                }
            }
        }
    }

    //游戏评分
    fun queryGameScoreData(resType: String, resId: String) = viewModelScope.launch {
        repository.queryGameScore(resType, resId).collect {
            val isSuccess = it.succeeded && it.data != null
            if (isSuccess) {
                _gameScoreResultLiveData.value = it.data
                updateReviewsCount(it.data?.commentCount)
            }
        }
    }

    //我的游戏评价
    fun fetchMyReviewData(gameId: String,moduleTypeCode: Int?= MODULE_TYPE_GAME_ONLY) = viewModelScope.launch {
        repository.fetchAppraiseByUid(getCurUuid(), gameId, moduleTypeCode).collect { result ->
            result.data?.dataList?.let {
                if (it.isNotEmpty()) {
                    val myReview = it.firstOrNull()
                    updateMyReview(gameId, myReview)
                    Timber.d("fetchMyReviewData updateMyReview $myReview")
                } else {
                    updateMyReview(gameId, null)
                    Timber.d("fetchMyReviewData updateMyReview null")
                }
            }
            if (result.data?.dataList == null) {
                updateMyReview(gameId, null)
                Timber.d("fetchMyReviewData list is null")
            }
        }
    }

    fun deleteMyReview(commentId: String, gameId: String) = viewModelScope.launch {
        repository.deleteGameAppraise(commentId).collect {
            _deleteReviewLiveData.value = Pair(it.succeeded, it.message)
            Timber.d("deleteMyReview ${it.succeeded} ${it.data}")
            if (it.succeeded) {
                updateMyReview(gameId, null)
            }
        }
    }

    fun deleteMyReply(replyId: String, commentId: String) = viewModelScope.launch {
        repository.deleteReply(replyId).collect {
            Timber.d("deleteMyReview ${it.succeeded} ${it.data}")
            if (it.succeeded && it.data == true) {
                _deleteReplyLiveData.value = Pair(commentId, replyId)
                // todo 删除回复成功后，需要更新评论的回复数
            }
        }
    }

    private fun updateReviewsCount(netCount: Long?) {
        val count = netCount ?: 0
        if (_reviewsCountLiveData.value != count) {
            _reviewsCountLiveData.value = count
        }
    }

    private fun updateMyReview(gameId: String, gameReviewInfo: GameAppraiseData?) {
        gameReviewInfo?.isMyReview = true
        metaKV.gameReviewKV.saveIsGameReviewed(gameId,moduleTypeCode?:MODULE_TYPE_GAME_ONLY, !gameReviewInfo?.commentId.isNullOrEmpty())

        _myReviewLiveData.value = gameReviewInfo
    }

    fun updateAppraisePageVisible(isVisible: Boolean) {
        Timber.d("updateAppraisePageVisible: $isVisible")
        _appraiseFragmentLiveData.value = isVisible
    }

    fun replyComment(result: AddAppraiseReplyRequest) = viewModelScope.launch {
        repository.addAppraiseReply(result).collect {
            Timber.d("replyComment $it")
            if (it.succeeded) {
                _replyCommentLiveData.value = Pair(it.succeeded, it.data)
            } else {
                _replyCommentLiveData.value = Pair(it.succeeded, it.message)
            }
        }
    }

    /**
     * 加载更多回复
     */
    fun getMyReply(data: GameAppraiseData, position: Int, replyId: String, targetReplyId: String) =
        viewModelScope.launch {
            fetchMyReplyListFlow(data, replyId, targetReplyId).collect {
                Timber.d("getMyReply $it")
                if (it.succeeded) {
                    it.data?.dataList?.firstOrNull()?.let { reply ->
                        data.replyCommonPage?.total = data.replyCommonPage?.total!! + 1
                        if (reply.uid == getCurUuid()) {
                            reply.isSelfReply = true
                        }
                        reply.localIsExpand = true
                        _myReplyInfo.value = Pair(position, reply)
                    }
                }
            }
        }

    private suspend fun fetchMyReplyListFlow(
        data: GameAppraiseData,
        replyId: String,
        targetReplyId: String
    ): Flow<DataResult<AppraiseReplyListResult?>> {
        Timber.d("fetchReplyListFlow lastReplyId = $replyId targetReplyId = $targetReplyId")
        val body = AppraiseReplyListRequest(
            pageSize = 1,
            pageNum = 1,
            withOpinion = true,
            targetReplyId = targetReplyId,
            commentId = data.commentId,
            queryType = QUERY_TYPE_NEW,
        )
        return repository.getAppraiseReplyList(body)
    }

    /**
     * 加载更多回复
     */
    fun loadMoreReply(data: GameAppraiseData) = viewModelScope.launch {
        fetchReplyListFlow(data).collect {
            handleReplyList(data, it)
        }
    }

    private fun handleReplyList(
        gameAppraiseData: GameAppraiseData,
        result: DataResult<AppraiseReplyListResult?>
    ) {
        val commentId = gameAppraiseData.commentId
        if (_replyList.value?.first != commentId) {
            Timber.d("commentId is another one ${_replyList.value?.first} $commentId")
            currentReplyPage = 1
            replySet.clear()
            gameAppraiseData.replyCommonPage?.dataList?.map {
                if (it.uid == getCurUuid()) {
                    it.isSelfReply = true
                }
                it.replyId
            }?.let {
                replySet.addAll(it)
            }
        }
        val netList = result.data?.dataList?.filter { !replySet.contains(it.replyId) }
        netList?.map {
            if (it.uid == getCurUuid()) {
                it.isSelfReply = true
            }
            it.replyId
        }?.let {
            replySet.addAll(it)
        }
        if (result.succeeded && result.data?.end == false) {
            currentReplyPage++
        }
        result.data?.dataList = netList?.let { ArrayList(it) }
        Timber.d("netList to dataList  ${result.data?.dataList}")
        _replyList.value = Pair(commentId, result.data)
    }

    private suspend fun fetchReplyListFlow(
        data: GameAppraiseData,
        refresh: Boolean? = false
    ): Flow<DataResult<AppraiseReplyListResult?>> {
        if (refresh == true) {
            val commentId = data.commentId
            if (_replyList.value?.first != commentId) {
                Timber.d("commentId is another one ${_replyList.value?.first} $commentId")
                currentReplyPage = 1
                replySet.clear()
                data.replyCommonPage?.dataList?.map {
                    if (it.uid == getCurUuid()) {
                        it.isSelfReply = true
                    }
                    it.replyId
                }?.let {
                    replySet.addAll(it)
                }
            }
        }
        val lastReplyId =
            kotlin.runCatching { data.replyCommonPage?.dataList?.last()?.replyId }.getOrNull() ?: ""
        Timber.d("fetchReplyListFlow lastReplyId = $lastReplyId  currentReplyPage=$currentReplyPage")
        val body = AppraiseReplyListRequest(
            pageSize = PostDetailViewModel.LOAD_REPLY_SIZE,
            pageNum = currentReplyPage,
            commentId = data.commentId,
            queryType = if (currentReplyPage < 10) QUERY_TYPE_NEW else QUERY_TYPE_NEW,
        )
        return repository.getAppraiseReplyList(body)
    }

    fun attitudeGameReview(gameReview: GameAppraiseData, isMe: Boolean, attitude:Int) = viewModelScope.launch {
        var likeCount = gameReview.likeCount
        if (attitude == OPTION_NO_STATE) {
            likeCount -= 1
        } else {
            likeCount += 1
            Analytics.track(EventConstants.GAME_REVIEW_LIKE_CLICK) {
                put("gameid", _gameId.value ?: "")
                put("reviewid", gameReview.commentId)
                put("type", ReviewListFragment.REVIEW_AUTHOR)
            }
        }
        repository.attitudeGameReview(
            AttentionRequest(
                resId = gameReview.commentId,
                resType = TYPE_COMMENT,
                opinion = attitude
            )
        ).collect {
            val isSuccess = it.succeeded
            Timber.d("attitudeGameReview isSuccess$isSuccess")
            _attitudeLiveData.value =
                ResultAttitudeChange(
                    gameReview.position,
                    if (isSuccess) attitude else gameReview.opinion,
                    if (isSuccess) likeCount else gameReview.likeCount,
                    isSuccess,
                    gameReview.commentId,
                    it.message,
                    isMe
                )
        }
    }

    /**
     * 点赞/取消点赞游戏评价回复
     */
    fun likeAppraiseReply(replyId: String, isLike: Boolean) {
        Timber.d("likeAppraiseReply $replyId $isLike")
        viewModelScope.launch {
            repository.attitudeGameReview(
                AttentionRequest(
                    resId = replyId,
                    resType = TYPE_REPLAY,
                    opinion = if (isLike) OPTION_LIKE else OPTION_NO_STATE
                )
            ).collect {

            }
        }
    }

    fun clearDeleteReviewData() {
        _deleteReviewLiveData.value = null
    }

    fun deleteReviewData(data: Pair<Boolean, String?>) {
        _deleteReviewLiveData.value = data
        _myReviewLiveData.value = null
    }
}