package com.socialplay.gpark.ui.gamedetail.sendflower

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class SendFlowerConditionState(
    val conditionsInfo: Async<SendGiftConditionsInfo> = Uninitialized,
    val switchGiftEnableResult: Async<Pair<Boolean, DataResult<*>>> = Uninitialized,
) : MavericksState

class SendFlowerConditionViewModel(
    initialState: SendFlowerConditionState,
    private val api: IMetaRepository,
) : BaseViewModel<SendFlowerConditionState>(initialState) {

    fun getGiftConditionsInfo(): SendGiftConditionsInfo? {
        return oldState.conditionsInfo.invoke()
    }

    fun updateGiftConditionsInfo(conditionsInfo: SendGiftConditionsInfo) {
        setState {
            copy(
                conditionsInfo = Success(conditionsInfo)
            )
        }
    }

    fun loadSendGiftConditions(gameId: String) {
        if (oldState.conditionsInfo is Loading) {
            return
        }
        setState {
            copy(
                conditionsInfo = Loading()
            )
        }
        viewModelScope.launch {
            val result = api.getSendGiftConditions(gameId)
            if (result.succeeded && result.data != null) {
                setState {
                    copy(
                        conditionsInfo = Success(result.data!!)
                    )
                }
            } else {
                setState {
                    copy(
                        conditionsInfo = Fail(
                            result.exception ?: Exception("unknown exception")
                        )
                    )
                }
            }
        }
    }

    fun switchSendGift(gameId: String, giveaway: Boolean) {
        if (oldState.switchGiftEnableResult is Loading) {
            return
        }
        setState {
            copy(switchGiftEnableResult = Loading())
        }
        viewModelScope.launch {
            val result = api.switchSendGift(gameId, giveaway)
            if (result.succeeded && result.data != null) {
                val conditionsInfo = oldState.conditionsInfo.invoke()
                setState {
                    copy(
                        switchGiftEnableResult = Success(giveaway to result),
                        conditionsInfo = if (conditionsInfo == null) {
                            oldState.conditionsInfo
                        } else {
                            Success(
                                conditionsInfo.copy(
                                    flag = giveaway
                                )
                            )
                        }
                    )
                }
            } else {
                setState {
                    copy(
                        switchGiftEnableResult = Success(giveaway to result),
                    )
                }
            }
        }
    }

    companion object :
        KoinViewModelFactory<SendFlowerConditionViewModel, SendFlowerConditionState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SendFlowerConditionState
        ): SendFlowerConditionViewModel {
            return SendFlowerConditionViewModel(state, get())
        }
    }
}