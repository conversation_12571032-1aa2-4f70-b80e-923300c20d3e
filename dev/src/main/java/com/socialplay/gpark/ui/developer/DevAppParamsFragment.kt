package com.socialplay.gpark.ui.developer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.databinding.FragmentDeveloperAppParamsBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.developer.adapter.AppParamsAdapter
import com.socialplay.gpark.ui.developer.viewmodel.DevAppParamsViewModel
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * xingxiu.hou
 * 2021/9/16
 */
class DevAppParamsFragment : BaseFragment<FragmentDeveloperAppParamsBinding>() {

    companion object {
        private const val TAG = "DEV:AppParams"
    }

    private val viewModel: DevAppParamsViewModel by viewModel()
    private val adapter by lazy { AppParamsAdapter() }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperAppParamsBinding? {
        return FragmentDeveloperAppParamsBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.rv.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        viewModel.getAppParams(requireContext()).observe(viewLifecycleOwner) {
            adapter.setList(it)
            Timber.d("params: ${it}")
        }
    }

    override fun loadFirstData() {
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_DEV_APP_PARAMS
}