package com.socialplay.gpark.ui.kol.creator.type

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest
import com.socialplay.gpark.databinding.FragmentTypeKolCreatorBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.kol.creator.IKolMoreCreatorAction
import com.socialplay.gpark.ui.kol.creator.kolMoreCreatorItem
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 更多创作者
 */

/**
 * @param type 接口类型 [com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest.TYPE_FOLLOWED]
 */
@Parcelize
data class TypeKolCreatorFragmentArgs(val type: Int) : Parcelable

class TypeKolCreatorFragment :
    BaseRecyclerViewFragment<FragmentTypeKolCreatorBinding>(R.layout.fragment_type_kol_creator) {

    private val args: TypeKolCreatorFragmentArgs by args()
    private val viewModel: TypeKolMoreCreatorViewModel by fragmentViewModel()

    private var listListener: IKolMoreCreatorAction? = null
    private lateinit var epoxyVisibilityTracker: EpoxyVisibilityTracker

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvMoreCreator

    companion object {
        fun newInstance(args: TypeKolCreatorFragmentArgs): TypeKolCreatorFragment {
            return TypeKolCreatorFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentTypeKolCreatorBinding? {
        return FragmentTypeKolCreatorBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        listListener = getInitListener()
        epoxyVisibilityTracker = EpoxyVisibilityTracker().apply {
            attach(recyclerView)
        }
        initData()
    }

    private fun initData() {
        viewModel.registerAsyncErrorToast(TypeKolCreatorModelState::asyncList)
        viewModel.registerAsyncErrorToast(TypeKolCreatorModelState::followResult)
        viewModel.setupRefreshLoading(
            TypeKolCreatorModelState::asyncList,
            binding.loadingMoreCreator,
            binding.refresh
        ) {
            viewModel.refresh()
        }
    }

    private fun getInitListener() = object : IKolMoreCreatorAction {
        override fun goProfile(uuid: String) {
            MetaRouter.Profile.other(
                this@TypeKolCreatorFragment,
                uuid,
                getAnalyticFrom()
            )
        }

        override fun changeFollow(uuid: String, toFollow: Boolean) {
            viewModel.changeFollow(uuid, toFollow)
        }

        override fun onItemShow(uuid: String) {
            Analytics.track(
                EventConstants.EVENT_FOLLOW_CREATOR_SHOW,
                EventParamConstants.KEY_USERID to uuid,
                EventParamConstants.KEY_TYPE to getAnalyticType()
            )
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    private fun getAnalyticType() = when (args.type) {
        TypeCreatorRequest.TYPE_FOLLOWED -> {
            EventConstants.Type.TYPE_KOL_CREATOR_MORE_FOLLOWED
        }

        TypeCreatorRequest.TYPE_RECOMMEND -> {
            EventConstants.Type.TYPE_KOL_CREATOR_MORE_DISCOVERIES
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    private fun getAnalyticFrom() = when (args.type) {
        TypeCreatorRequest.TYPE_FOLLOWED -> {
            EventConstants.From.FROM_KOL_CREATOR_MORE_FOLLOWED
        }

        TypeCreatorRequest.TYPE_RECOMMEND -> {
            EventConstants.From.FROM_KOL_CREATOR_MORE_DISCOVERIES
        }

        else -> {
            throw IllegalArgumentException("page type not support: ${args.type}")
        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        TypeKolCreatorModelState::list,
        TypeKolCreatorModelState::loadMore
    ) { list, loadMore ->
        // 列表
        list.forEachIndexed { index, user ->
            kolMoreCreatorItem(
                args.type.toString(),
                user,
                args.type == TypeCreatorRequest.TYPE_RECOMMEND,
                listListener
            )
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore) {
                viewModel.loadMore()
            }
        }
    }

    override fun onDestroyView() {
        epoxyVisibilityTracker.detach(recyclerView)
        super.onDestroyView()
    }

    override fun getPageName(): String =
        PageNameConstants.FRAGMENT_NAME_KOL_MORE_CREATOR_TYPE_PREFIX + args.type
}