package com.socialplay.gpark.ui.notice.message

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.NavHostFragment
import com.bin.cpbus.CpEventBus
import com.ly123.tes.mgs.metacloud.ImMessageListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.helper.CommandMessageRegistry
import com.ly123.tes.mgs.metacloud.message.ImMessageEvent
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.origin.GroupAtInfo
import com.meta.box.biz.friend.FriendBiz
import com.socialplay.gpark.data.interactor.AccountInteractor

import com.socialplay.gpark.data.model.notification.ImPrivateMessage
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.model.friend.CmdSendFriendAskMessage
import com.socialplay.gpark.data.model.groupchat.CmdJoinGroupChatAskMessage
import com.socialplay.gpark.data.model.mgs.CmdMgsInviteDataMessage
import com.socialplay.gpark.data.model.notification.ImGroupMessage
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.im.ConversationListFragment
import com.socialplay.gpark.ui.im.conversation.BaseChatFragment
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.main.MainFragment
import com.socialplay.gpark.util.extension.findTyped
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2024/07/24
 * desc   : Im消息通知转发
 */
object ImNotificationHandler {
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }

    private var resumeActivityRef: WeakReference<Activity>? = null
    private var topActivityRef: WeakReference<Activity>? = null
    private var mainActivityRef: WeakReference<MainActivity>? = null
    private val activityLifecycleCallbacks = object : Application.ActivityLifecycleCallbacks{
        override fun onActivityCreated(
            activity: Activity,
            savedInstanceState: Bundle?
        ) {
        }

        override fun onActivityStarted(activity: Activity) {
        }

        override fun onActivityResumed(activity: Activity) {
            resumeActivityRef = WeakReference(activity)
            topActivityRef = WeakReference(activity)
            if(activity is MainActivity){
                mainActivityRef = WeakReference(activity)
            }
        }

        override fun onActivityPaused(activity: Activity) {
            resumeActivityRef = null
        }

        override fun onActivityStopped(activity: Activity) {
        }

        override fun onActivitySaveInstanceState(
            activity: Activity,
            outState: Bundle
        ) {
        }

        override fun onActivityDestroyed(activity: Activity) {
            if (topActivityRef?.get() === activity) {
                topActivityRef = null
            }
            if (resumeActivityRef?.get() === activity) {
                resumeActivityRef = null
            }
            if (mainActivityRef?.get() === activity) {
                mainActivityRef = null
            }
        }
    }

    fun registerCommand(application: Application) {
        application.unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks)
        application.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
        registerMgsInviteCommand()
    }

    fun unRegisterCommand() {
        unregisterMgsInvitationCommandListener()
    }

    private fun isFront(): Boolean {
        return resumeActivity != null
    }

    private val resumeActivity: Activity? get() = resumeActivityRef?.get()

    private val topActivity: Activity? get() = topActivityRef?.get()

    private val mainActivity: MainActivity? get() = mainActivityRef?.get()

    private fun findNavHostFragment(): NavHostFragment? {
        val resumeActivity = mainActivity ?: return null
        return resumeActivity.supportFragmentManager.fragments.findTyped<NavHostFragment>()
    }

    private fun findCurrentFragment(): Fragment? {
        val resumeActivity = mainActivity ?: return null
        val navHostFragment = resumeActivity.supportFragmentManager.fragments.findTyped<NavHostFragment>() ?: return null
        navHostFragment.childFragmentManager.fragments.forEach {
            if (it.isVisible) {
                if (it is MainFragment) {
                    it.childFragmentManager.fragments.forEach {
                        if (it.isVisible) {
                            return it
                        }
                    }
                }
                return it
            }
        }
        return null
    }

    private val mgsInviteCmdListener: suspend (CmdMgsInviteDataMessage) -> Unit = {
        Timber.tag("leownnnn").d("this is $this, isFront: ${isFront()}")
        // 当前Activity不在前台发送跨进程消息到游戏，展示邀请消息
        if (!isFront()) {
            CpEventBus.post(it.content)
        } else {
            showFloatNotice(FloatNoticeInteractor.TYPE_UNIT_INVITE, it.content)
        }
    }

    private val sendFriendAskCmdListener: suspend (CmdSendFriendAskMessage) -> Unit = {
        Timber.tag("leownnnn").d("this is $this, isFront: ${isFront()}")
        // 当前Activity不在前台发送跨进程消息到游戏，展示邀请消息
        if (!isFront()) {
            CpEventBus.post(it.content)
        } else {
            showFloatNotice(FloatNoticeInteractor.TYPE_SEND_FRIEND_ASK, it.content)
        }
    }

    private val joinGroupChatCmdListener: suspend (CmdJoinGroupChatAskMessage) -> Unit = {
        Timber.tag("leownnnn").d("this is $this, isFront: ${isFront()}")
        if (!isFront()) {
            CpEventBus.post(it.content)
        } else {
            showFloatNotice(FloatNoticeInteractor.TYPE_CHAT_GROUP_ASK, it.content)
        }
    }

    //所有的消息
    private val imMessageListener = object : ImMessageListener {
        override fun onReceived(messageEvent: ImMessageEvent) {
            if (!PandoraToggle.isIMEntrance) {
                return
            }
            if (messageEvent.message.conversationType == Conversation.ConversationType.PRIVATE) {
                val friend = FriendBiz.friendList.value.findLast { it.uuid == messageEvent.message.senderUserId } ?: return
                if (!ImPrivateMessage.isSupportMessage(messageEvent.message)) {
                    //不支持的消息类型
                    return
                }
                val content = topActivity?.let {
                    ImPrivateMessage.getSummerContent(messageEvent.message.content, it).toString()
                } ?: ""
                val message = ImPrivateMessage(
                    content = content,
                    portrait = friend.avatar ?: "",
                    nickname = friend.remark ?: friend.name ?: "",
                    sendUserId = messageEvent.message.senderUserId,
                    messageId = messageEvent.message.messageId,
                    receiveTime = System.currentTimeMillis()
                )
                if (!isFront()) {
                    CpEventBus.post(message)
                } else {
                    showFloatNotice(FloatNoticeInteractor.TYPE_IM_PRIVATE_MESSAGE, message)
                }
            } else if (messageEvent.message.conversationType == Conversation.ConversationType.CHATROOM) {
                val groupInfo = messageEvent.message.customData?.groupInfo
                // 群聊
                if (Conversation.ConversationType.GROUP.value != groupInfo?.type) {
                    return
                }
                if(!ImGroupMessage.isSupportMessage(messageEvent.message)){
                    return
                }
                val conversationId = MetaCloud.getConversationId(
                    Conversation.ConversationType.GROUP,
                    messageEvent.message.targetId
                )
                MetaCloud.getConversation(conversationId) { conversation ->
                    if (conversation?.messageRevOpt == Message.MessageRevOpt.RECEIVE) {
                        var atType = Message.ATType.AT_UNKNOWN
                        val atInfoList = messageEvent.message.customData?.groupAtInfoList
                        val currentUserInfo = accountInteractor.accountLiveData.value
                        val currentUserId = currentUserInfo?.uuid
                        if (!atInfoList.isNullOrEmpty() && !currentUserId.isNullOrEmpty()) {
                            for (atInfo in atInfoList) {
                                if (atInfo.atType == GroupAtInfo.AT_TYPE_MEMBER && atInfo.memberInfo?.uuid == currentUserId) {
                                    atType = Message.ATType.AT_ME
                                    break
                                } else if (atInfo.atType == GroupAtInfo.AT_TYPE_ALL) {
                                    atType = Message.ATType.AT_ALL
                                }
                            }
                        }

                        val content = topActivity
                            ?.let {
                                ImPrivateMessage.getSummerContent(
                                    messageEvent.message.content,
                                    it
                                ).toString()
                            } ?: ""
                        val senderName = messageEvent.message.customData?.groupSenderInfo?.nickname
                        val text = if (senderName.isNullOrEmpty()) {
                            content
                        } else {
                            "$senderName: $content"
                        }
                        val groupMessage = ImGroupMessage(
                            messageId = messageEvent.message.messageId,
                            content = text,
                            receiveTime = System.currentTimeMillis(),
                            customData = messageEvent.message.customData,
                            atType = atType,
                        )
                        if (!isFront()) {
                            CpEventBus.post(groupMessage)
                        } else {
                            showFloatNotice(
                                FloatNoticeInteractor.TYPE_IM_GROUP_MESSAGE,
                                groupMessage
                            )
                        }
                    }
                }
            }
        }
    }

    fun showFloatNotice(type: String, content: Any) {
        val fragment = kotlin.runCatching { findNavHostFragment() }.getOrNull() ?: return
        val nowFragment = kotlin.runCatching { findCurrentFragment() }.getOrNull()
        Timber.d("showFloatNotice %s", nowFragment)
        if (skipType(type) && skipFragment()) {
            //消息页面处理
        } else {
            GlobalContext.get().get<FloatNoticeInteractor>().showFloatNotice(
                fragment.requireContext(),
                resumeActivityRef?.get() ?: fragment.requireActivity(),
                fragment,
                type, content, null, null, false
            )
        }
    }

    private fun skipType(type: String): Boolean {
        return type == FloatNoticeInteractor.TYPE_IM_PRIVATE_MESSAGE
                || type == FloatNoticeInteractor.TYPE_IM_GROUP_MESSAGE
                || type == FloatNoticeInteractor.TYPE_SEND_FRIEND_ASK
    }

    private fun skipFragment(): Boolean {
        val nowFragment = kotlin.runCatching { findCurrentFragment() }.getOrNull()
        return nowFragment is BaseChatFragment || nowFragment is ConversationListFragment
    }

    private fun registerMgsInviteCommand() {
        CommandMessageRegistry.addMessageListener(mgsInviteCmdListener)
        CommandMessageRegistry.addMessageListener(sendFriendAskCmdListener)
        CommandMessageRegistry.addMessageListener(joinGroupChatCmdListener)
        if (PandoraToggle.isIMEntrance) {
            RongImHelper.registerInitCallback {
                MetaCloud.registerImMessageListener(imMessageListener)
            }
        }
    }

    /**
     * 解除注册的MGS邀请透传消息监听器
     */
    private fun unregisterMgsInvitationCommandListener() {
        try {
            CommandMessageRegistry.removeMessageListener(mgsInviteCmdListener)
            CommandMessageRegistry.removeMessageListener(sendFriendAskCmdListener)
            CommandMessageRegistry.removeMessageListener(joinGroupChatCmdListener)
            if (PandoraToggle.isIMEntrance) {
                RongImHelper.registerInitCallback {
                    MetaCloud.unRegisterImMessageListener(imMessageListener)
                }
            }
        } catch (e: Throwable) {
            // 因为没有同意隐私协议时，还未初始化，所以会抛出异常，不需要处理
        }
    }
}
