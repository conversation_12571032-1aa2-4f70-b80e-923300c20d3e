package com.socialplay.gpark.ui.developer.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.app.initialize.LibBuildConfigInit
import com.socialplay.gpark.data.local.AppDatabase
import kotlinx.coroutines.launch

class BuildConfigViewModel(private val db: AppDatabase) : ViewModel() {
    private val _buildConfigLivedata = MutableLiveData<List<String>>()
    val buildConfigLivedata: LiveData<List<String>> = _buildConfigLivedata

    fun requestBuildConfigData() {
        viewModelScope.launch {
            LibBuildConfigInit.initConfig()
            _buildConfigLivedata.postValue(LibBuildConfigInit.fields())
        }
    }
}