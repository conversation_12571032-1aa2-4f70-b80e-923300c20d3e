package com.socialplay.gpark.ui.qrcode

import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogCameraPermissionBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.AppSystemSettingUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding


class CameraPermissionDialog : BaseDialogFragment() {

    override val binding by viewBinding(DialogCameraPermissionBinding::inflate)

    private val args by navArgs<CameraPermissionDialogArgs>()

    private var isCanceled: Boolean? = null

    val analyticMap by lazy {
        mapOf(
            "gameid" to (args.gameId ?: ""),
            "gamepkg" to (args.packageName ?: "")
        )
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    companion object {
        const val KEY_PERMISSION_RESULT = "key_permission_result"
        const val REQUEST_PERMISSION_CAMERA = "request_permission_camera"
    }

    override fun init() {
        binding.tvPermissionDisAgree.setOnAntiViolenceClickListener {
            isCanceled = true
            Analytics.track(EventConstants.EVENT_CAMERA_DIALOG_CANCEL_CLICK) {
                putAll(analyticMap)
            }
            dismiss()
        }

        binding.tvPermissionAgree.setOnAntiViolenceClickListener {
            isCanceled = false
            Analytics.track(EventConstants.EVENT_CAMERA_DIALOG_CONFIRM_CLICK) {
                putAll(analyticMap)
            }
            if (args.goSettings) {
                context?.let { AppSystemSettingUtil.goAppSystemSetting(it) }
            }
            dismiss()
        }
    }

    override fun loadFirstData() {

    }

    override fun dismiss() {
        super.dismiss()
        if (isCanceled == null) {
            Analytics.track(EventConstants.EVENT_CAMERA_DIALOG_CANCEL_CLICK) {
                putAll(analyticMap)
            }
        }
    }

    override fun onDestroyView() {
        setFragmentResult(args.scanResultKey, Bundle().apply { putBoolean(KEY_PERMISSION_RESULT, isCanceled == false) })
        super.onDestroyView()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override fun getStyle() = R.style.DialogStyleNonFullScreen
}