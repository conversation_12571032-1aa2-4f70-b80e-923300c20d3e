package com.socialplay.gpark.ui.outfit

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.account.ClearRedDotEvent
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostCommentContent
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.outfit.UgcDesignDetailRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignGetRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignLikeRequest
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.editor.detail.commentlist.BaseCommentListViewModel
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/04
 *     desc   :
 * </pre>
 */
data class UgcDesignDetailState(
    val itemId: String,
    val categoryId: Int,
    val from: Int,
    val tabId: Int,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val showCommentPinRedDot: Boolean,
    val detail: Async<UgcDesignDetail> = Uninitialized,
    val commentList: Async<PagingApiResult<PostComment>> = Uninitialized,
    val commentListLoadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val addCommentResult: Async<PostComment> = Uninitialized,
    val addReplyResult: Async<PostReply> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
    val isFollow: Boolean? = null,
    val title: String? = null,
    val desc: String? = null,
    val isLike: Boolean? = null,
    val likeCount: Long? = null,
    val got: Boolean? = null,
    val getResult: Async<String> = Uninitialized,
    val gotCount: Long? = null,
    val isMe: Boolean? = null,
    val isRefresh: Async<Boolean> = Uninitialized,
    val uniqueTag: Int = 0,
    val guideInvoke: Pair<Int, Boolean>? = null,
) : MavericksState {

    val trackId get() = detail()?.trackId ?: itemId
    val feedType get() = detail()?.feedType ?: -1
}

class UgcDesignDetailViewModel(
    initialState: UgcDesignDetailState,
    context: Application,
    repo: IMetaRepository,
    accountInteractor: AccountInteractor,
    private val ugcRepo: UgcRepository
) : BaseCommentListViewModel<UgcDesignDetailState>(initialState, context, repo, accountInteractor) {

    val detail get() = oldState.detail()
    val trackId get() = oldState.trackId
    val feedType get() = oldState.feedType

    var ugcDesignDetailGuide: Boolean
        get() = accountInteractor.ugcDesignDetailGuide
        set(_) {
            accountInteractor.ugcDesignDetailGuide = true
        }

    var ugcModelDetailGuide: Boolean
        get() = accountInteractor.ugcModelDetailGuide
        set(_) {
            accountInteractor.ugcModelDetailGuide = true
        }
    var commentSuccessCount = 0

    init {
        registerHermes()
        initDetail()
        initCommentList()
    }

    fun initDetail() {
        getUgcDetailInfo()
    }

    fun initCommentList() = withState { s ->
        getCommentList(true, s.targetCommentId, s.targetReplyId)
    }

    fun getUgcDetailInfo() = withState { s ->
        if (!s.detail.shouldLoad) return@withState
        ugcRepo.getUgcDesignDetail(UgcDesignDetailRequest(s.itemId)).map {
            Analytics.track(
                EventConstants.LIBRARY_DETAIL_SHOW,
                "metrialidid" to it.trackId,
                "authorid" to it.uuid.orEmpty(),
                "type" to it.feedType,
                "show_categoryid" to s.categoryId,
                "from" to s.from,
                "tab_id" to s.tabId,
                "is_recreate" to it.trackIsRecreate
            )
            if (s.commentList.shouldLoad) {
                getCommentList(true, s.targetCommentId, s.targetReplyId)
            }
            it
        }.execute { result ->
            when (result) {
                is Success -> {
                    val detail = result()
                    if (detail.published) {
                        copy(
                            detail = result,
                            title = detail.title,
                            desc = detail.comment,
                            isLike = detail.isFavorite,
                            likeCount = detail.favorites,
                            got = detail.isOwned,
                            isFollow = detail.isFollow,
                            gotCount = detail.sales,
                            isMe = isMe(detail.uuid)
                        )
                    } else {
                        copy(detail = result)
                    }
                }

                else -> {
                    copy(detail = result)
                }
            }
        }
    }

    fun getCommentList(
        refresh: Boolean,
        targetCommentId: String? = null,
        targetReplyId: String? = null
    ) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        getCommentList(
            moduleContentId = s.itemId,
            sortType = PostCommentListRequestBody.QUERY_TYPE_LIKE,
            page = s.page,
            pageSize = 20,
            replySize = 0,
            replySortType = PostReplyListRequestBody.QUERY_LATEST,
            refresh = refresh,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
            commentCollapse = false,
            commentReplyStatus = PostComment.REPLY_STATUS_INIT,
            withAuthorReply = true,
            authorReplySize = 1
        ) { result, targetPage ->
            when (result) {
                is Success -> {
                    copy(
                        commentList = result,
                        commentListLoadMore = Success(LoadMoreState(result().end)),
                        page = targetPage,
                        isRefresh = if (commentList is Success) {
                            Success(refresh)
                        } else {
                            isRefresh
                        },
                        uniqueTag = if (refresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        commentList = if (commentList is Success) commentList else Fail(result.error),
                        commentListLoadMore = Fail(result.error),
                        isRefresh = if (commentList is Success) {
                            Fail(result.error, refresh)
                        } else {
                            isRefresh
                        }
                    )
                }

                else -> {
                    copy(
                        commentList = if (commentList is Success) commentList else Loading(),
                        commentListLoadMore = Loading(),
                        isRefresh = if (commentList is Success) {
                            Loading(refresh)
                        } else {
                            isRefresh
                        }
                    )
                }
            }
        }
    }

    fun addCommentViaNet(commentContent: PostCommentContent) = withState { s ->
        if (!commentContent.valid || s.addCommentResult is Loading) return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val requestBody = PostCommentRequestBody(
            commentContent.text,
            MODULE_MATERIAL,
            s.itemId,
            mediaList = commentContent.mediaList
        )
        val tempComment = requestBody.toPostComment(
            "",
            accountInteractor.accountLiveData.value,
            ts
        )
        repo.addPostComment(requestBody).map {
            val result = tempComment.copy(commentId = it.data.orEmpty())
            if (result.commentId.isNotEmpty()) {
                addComment(result)
            }
            if (PandoraToggle.enableModuleGuideFirstInteract && accountInteractor.assetFirstInteract) {
                val newGuideInvoke =
                    BaseAccountInteractor.MODULE_GUIDE_INVOKE_COMMENT to !(oldState.guideInvoke?.second
                        ?: false)
                setState { copy(guideInvoke = newGuideInvoke) }
            }
            result to it.toastMsg
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (data, toastMsg) = result()
                    copy(
                        addCommentResult = if (data.commentId.isNotEmpty()) {
                            Success(data)
                        } else {
                            Fail(ApiDataException(String::class))
                        },
                        toast = if (toastMsg.isNullOrEmpty()) {
                            toast
                        } else {
                            toast.toMsg(toastMsg)
                        }
                    )
                }

                is Fail -> {
                    copy(addCommentResult = Fail(result.error, tempComment))
                }

                else -> {
                    copy(addCommentResult = Loading())
                }
            }
        }
    }

    fun addReplyViaNet(replyContent: PostCommentContent) = withState { s ->
        if (!replyContent.valid || s.addCommentResult is Loading) return@withState
        val replyTarget = getReplyTarget() ?: return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val userInfo = accountInteractor.accountLiveData.value
        val requestBody = if (replyTarget.isTargetComment) {
            PostReplyRequestBody(
                replyContent.text,
                userInfo?.uuid.orEmpty(),
                replyTarget.asComment.commentId,
                mediaList = replyContent.mediaList
            )
        } else {
            val targetReply = replyTarget.asReply
            PostReplyRequestBody(
                content = replyContent.text,
                uid = userInfo?.uuid.orEmpty(),
                commentId = replyTarget.commentId.orEmpty(),
                replyUid = targetReply.uid,
                replyNickname = targetReply.nickname,
                replyContentId = targetReply.replyId,
                mediaList = replyContent.mediaList
            )
        }
        val tempReply = requestBody.toPostReply("", userInfo, ts)
        repo.addPostReply(requestBody).map {
            val result = tempReply.copy(replyId = it)
            addReply(result to replyTarget)
            result
        }.execute { result ->
            when (result) {
                is Fail -> {
                    copy(addReplyResult = result.copyEx(tempReply))
                }

                else -> {
                    copy(addReplyResult = result)
                }
            }
        }
    }

    fun like() = withState { s ->
        val isLike = s.isLike ?: return@withState
        val likeCount = s.likeCount ?: return@withState

        val newIsLike = !isLike
        if (newIsLike) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_DETAIL_LIKE_CLICK,
                "authorid" to s.detail()?.uuid.orEmpty(),
                "metrialidid" to s.trackId,
                "type" to s.feedType,
                "from" to 1
            )
        } else {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_DETAIL_CANCEL_LIKE_CLICK,
                "authorid" to s.detail()?.uuid.orEmpty(),
                "metrialidid" to s.trackId,
                "type" to s.feedType,
                "from" to 1
            )
        }
        val newLikeCount = if (newIsLike) likeCount + 1 else (likeCount - 1).coerceAtLeast(0)
        setState { copy(isLike = newIsLike, likeCount = newLikeCount) }
        ugcRepo.likeUgcDesign(UgcDesignLikeRequest(s.itemId, newIsLike)).map {
            if (PandoraToggle.enableModuleGuideFirstInteract && newIsLike && accountInteractor.assetFirstInteract) {
                val newGuideInvoke =
                    BaseAccountInteractor.MODULE_GUIDE_INVOKE_LIKE to !(oldState.guideInvoke?.second
                        ?: false)
                setState { copy(guideInvoke = newGuideInvoke) }
            }
        }.execute {
            this
        }
    }

    fun get() = withState { s ->
        if (s.got != false || s.getResult is Loading) return@withState
        ugcRepo.getUgcDesign(UgcDesignGetRequest.single(s.itemId)).map {
            if (PandoraToggle.enableModuleGuideFirstInteract && accountInteractor.assetFirstInteract) {
                val newGuideInvoke =
                    BaseAccountInteractor.MODULE_GUIDE_INVOKE_GET to !(oldState.guideInvoke?.second
                        ?: false)
                setState { copy(guideInvoke = newGuideInvoke) }
            }
            it
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(
                        got = true,
                        getResult = result,
                        gotCount = (gotCount ?: 0) + 1,
                    )
                }

                is Fail -> {
                    copy(getResult = result)
                }

                else -> {
                    copy(getResult = result)
                }
            }
        }
    }

    fun edit(title: String?, desc: String?) {
        setState { copy(title = title, desc = desc) }
    }

    fun follow() = withState { s ->
        s.isFollow ?: return@withState
        val detail = s.detail() ?: return@withState
        val uid = detail.uuid ?: return@withState
        val toFollow = !s.isFollow
        Analytics.track(
            EventConstants.LIBRARY_DETAIL_FOLLOW_CLICK,
            "metrialidid" to detail.trackId,
            "authorid" to uid,
            "type" to detail.feedType
        )
        if (toFollow) {
            repo.followUser(uid)
        } else {
            repo.unfollowUser(uid)
        }.execute { result ->
            when (result) {
                is Success -> {
                    EventBus.getDefault()
                        .post(UserFollowEvent(uid, toFollow, UserFollowEvent.FROM_GAME_DETAIL))

                    if (toFollow != isFollow) {
                        copy(isFollow = toFollow)
                    } else {
                        this
                    }
                }

                is Fail -> {
                    copy(toast = toast.toError(result))
                }

                else -> {
                    this
                }
            }
        }
    }

    fun follow(isFollow: Boolean) = withState { s ->
        if (s.isFollow != isFollow) {
            setState { copy(isFollow = isFollow) }
        }
    }

    override val oldListResult: PagingApiResult<PostComment>?
        get() = oldState.commentList()
    override val authorId: String?
        get() = oldState.detail()?.uuid
    override val moduleContentId: String
        get() = oldState.itemId
    override val pageType: Long
        get() = 0L
    override val contentType: Int
        get() = CONTENT_TYPE_UGC_DESIGN_COMMENT

    override val moduleContentType: Int = MODULE_MATERIAL

    override val UgcDesignDetailState.oldListResult: PagingApiResult<PostComment>?
        get() = commentList()

    override fun UgcDesignDetailState.updateCommentList(
        result: PagingApiResult<PostComment>?,
        msg: Any?
    ): UgcDesignDetailState {
        return if (result == null && msg == null) {
            this
        } else {
            copy(
                commentList = if (result != null) commentList.copyEx(result) else commentList,
                toast = if (msg != null) toast.tryToMsg(msg) else toast
            )
        }
    }

    override fun trackDelete(id: String, isMe: Boolean, type: Long) {
        Analytics.track(
            EventConstants.LIBRARY_REVIEW_DELETE_SUCCESS,
            "metrialidid" to trackId,
            "reviewtype" to type,
            "reviewid" to id,
            "deletetype" to if (isMe) 1L else 0L
        )
    }

    override fun clearCommentPinRedDot() = withState { s ->
        super.clearCommentPinRedDot()
        if (!s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun invokeToast(resId: Int) = withState {
        setState { copy(toast = toast.toResMsg(resId)) }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) = withState { s ->
        if (accountInteractor.isMe(s.detail()?.uuid)) return@withState
        if (s.detail()?.uuid == event.uuid && s.isFollow != event.followStatus) {
            setState { copy(isFollow = event.followStatus) }
        }
    }

    @Subscribe
    fun onClearRedDotEvent(event: ClearRedDotEvent) = withState { s ->
        if (!event.isCommentPin || !s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }

    companion object : KoinViewModelFactory<UgcDesignDetailViewModel, UgcDesignDetailState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcDesignDetailState
        ): UgcDesignDetailViewModel {
            return UgcDesignDetailViewModel(state, get(), get(), get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): UgcDesignDetailState? {
            val args = viewModelContext.args as UgcDesignDetailArgs
            val accountInteractor: AccountInteractor = get()

            return UgcDesignDetailState(
                args.itemId,
                args.categoryId,
                args.from,
                args.tabId,
                args.targetCommentId,
                args.targetReplyId,
                accountInteractor.showCommentPinRedDot
            )
        }
    }
}