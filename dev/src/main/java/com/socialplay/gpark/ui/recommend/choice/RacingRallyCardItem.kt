package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import com.airbnb.epoxy.EpoxyModel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemRacingRallyBinding
import com.socialplay.gpark.databinding.AdapterChoiceCardItemRacingRallyTemplateBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import android.graphics.Color
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import android.text.TextUtils
import android.view.Gravity
import com.airbnb.epoxy.Carousel

/**
 * Racing Rally Star Creations card implementation
 * First item is template, subsequent items are user creations
 */
fun MetaModelCollector.choiceRacingRallyCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList
    if (games.isNullOrEmpty()) return
    
    // Title
    textItem(
        text = card.cardName,
        textSize = 18.0f,
        fontFamily = R.font.poppins_bold_700,
        textColorRes = R.color.textColorPrimary,
        isSingleLine = true,
        ellipsize = TextUtils.TruncateAt.END,
        gravity = Gravity.START or Gravity.CENTER_VERTICAL,
        paddingLeftDp = 16.0f,
        paddingTopDp = 16.0f,
        paddingRightDp = 12.0f,
        paddingBottomDp = 8.0f,
        idStr = "ChoiceRacingRallyCardTitle-$cardPosition",
        spanSize = spanSize
    )
    
    // Horizontal scrolling list
    carouselNoSnapWrapBuilder {
        id("ChoiceRacingRallyCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        
        games.forEachIndexed { position, game ->
            if (position == 0) {
                // First item is template
                add(
                    ChoiceRacingRallyTemplateItem(
                        game,
                        position,
                        card,
                        cardPosition,
                        spanSize,
                        listener
                    ).id("ChoiceRacingRallyTemplate-$cardPosition-$position-${game.code}")
                )
            } else {
                // Subsequent items are user creations
                add(
                    ChoiceRacingRallyCreationItem(
                        game,
                        position,
                        card,
                        cardPosition,
                        spanSize,
                        listener
                    ).id("ChoiceRacingRallyCreation-$cardPosition-$position-${game.code}")
                )
            }
        }
    }
}

/**
 * Template item (first item) - larger with special styling
 */
data class ChoiceRacingRallyTemplateItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemRacingRallyTemplateBinding>(
    R.layout.adapter_choice_card_item_racing_rally_template,
    AdapterChoiceCardItemRacingRallyTemplateBinding::bind
) {

    override fun AdapterChoiceCardItemRacingRallyTemplateBinding.onBind() {
        root.setMargin(right = dp(12))
        
        // Load image
        listener.getGlideOrNull()?.let { glide ->
            glide.load(item.imageUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivGameIcon)
        }
        
        // Set title
        tvGameTitle.text = item.displayName
        
        // Set template label
        tvTemplateLabel.text = "Template"
        
        // Set theme color background if available
        item.themeColor?.let { color ->
            try {
                val parsedColor = Color.parseColor(color)
                viewThemeBackground.setBackgroundColor(parsedColor)
                viewThemeBackground.visibility = View.VISIBLE
            } catch (e: Exception) {
                viewThemeBackground.visibility = View.GONE
            }
        } ?: run {
            viewThemeBackground.visibility = View.GONE
        }

        // Handle Create button click
        btnCreate.setOnAntiViolenceClickListener {
            // Handle template creation - could open editor or template selection
            listener.onItemClick(cardPosition, card, position, item, false)
        }

        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}

/**
 * Creation item (subsequent items) - smaller with user info
 */
data class ChoiceRacingRallyCreationItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemRacingRallyBinding>(
    R.layout.adapter_choice_card_item_racing_rally,
    AdapterChoiceCardItemRacingRallyBinding::bind
) {

    override fun AdapterChoiceCardItemRacingRallyBinding.onBind() {
        root.setMargin(right = dp(10))
        
        // Load image
        listener.getGlideOrNull()?.let { glide ->
            glide.load(item.imageUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivGameIcon)
        }
        
        // Set title
        tvGameTitle.text = item.displayName
        
        // Set creator info
        if (!item.nickname.isNullOrEmpty()) {
            tvCreatorNickname.text = item.nickname
            tvCreatorNickname.visibility = View.VISIBLE
            
            // Load creator avatar
            listener.getGlideOrNull()?.let { glide ->
                glide.load(item.avatar)
                    .placeholder(R.drawable.icon_default_avatar)
                    .transform(CenterCrop(), RoundedCorners(12.dp))
                    .into(sivCreatorAvatar)
            }
            sivCreatorAvatar.visibility = View.VISIBLE
        } else {
            tvCreatorNickname.visibility = View.GONE
            sivCreatorAvatar.visibility = View.GONE
        }
        
        // Set like count
        item.likeCount?.let { count ->
            if (count > 0) {
                tvZan.text = count.toString()
                tvZan.visibility = View.VISIBLE
            } else {
                tvZan.visibility = View.GONE
            }
        } ?: run {
            tvZan.visibility = View.GONE
        }
        
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}
