package com.socialplay.gpark.ui.qrcode

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.camera.core.Camera
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.fragment.app.clearFragmentResultListener
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.model.qrcode.DecodeResult
import com.socialplay.gpark.databinding.FragmentQrCodeScanBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.qrcode.CameraPermissionDialog.Companion.REQUEST_PERMISSION_CAMERA
import com.socialplay.gpark.ui.view.QRCoverView
import com.socialplay.gpark.util.AppSystemSettingUtil
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber


/**
 *  <AUTHOR> wei.zhu
 *  time   : 2021/6/22
 *  desc   : 二维码扫描页面
 */
class QRCodeScanFragment : BaseFragment<FragmentQrCodeScanBinding>(), LifecycleObserver {

    companion object {
        const val KEY_SCAN_RESULT = "scan.result"
        const val KEY_SCAN_RESULT_SOURCE = "scan.result.source"

        const val KEY_SCAN_REQUEST_ENTRY = "scan.result.request.entry"
        const val KEY_SCAN_REQUEST_DATA = "scan.result.request.data"

        private const val CHOOSE_IMAGE_REQUEST_CODE = 1

        // 首页扫一扫的key
        const val KEY_HOME_REQUEST_SCAN_QRCODE = "key_request_scan_qrcode.from.home"

        // 个人主页扫一扫
        const val KEY_PROFILE_REQUEST_SCAN_QRCODE = "key_request_scan_qr_code.from.profile"

        // 加好友界面到扫一扫的key
        const val KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE = "key_request_scan_qrcode.from.add.friend"

        // mgs游戏到扫一扫的key
        const val KEY_REQUEST_KEY_GAME_TO_QR_CODE = "key_request_scan_qrcode.from.mgs.game"
    }

    private val args by navArgs<QRCodeScanFragmentArgs>()

    private val vm by viewModel<QRCodeScanViewModel>()

    private var camera: Camera? = null

    override var navColorRes = R.color.black

    private val analyticFrom: String by lazy {
        when (args.scanResultKey) {
            KEY_HOME_REQUEST_SCAN_QRCODE       -> {
                "home"
            }
            KEY_REQUEST_KEY_GAME_TO_QR_CODE    -> {
                "mgs_game"
            }
            KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE -> {
                "add_friend"
            }
            KEY_PROFILE_REQUEST_SCAN_QRCODE    -> {
                "profile"
            }
            else                               -> {
                "other"
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentQrCodeScanBinding? {
        return FragmentQrCodeScanBinding.inflate(inflater, container, false)
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_QR_CODE_SCAN

    override fun isStatusBarTextDark(): Boolean = false

    override fun init() {
        initListeners()
        initObservers()
        binding.lottieScan.repeatCount = -1
        binding.lottieScan.playAnimation()
        if (PermissionRequest.checkSelfPermission(requireActivity(), Manifest.permission.CAMERA)) {
            initPreview()
        } else {
            viewLifecycleOwner.lifecycleScope.launch {
                delay(1)
                requestPermission()
            }
        }
    }

    private fun initPreview() {
        vm.previewAndDecode(requireContext(), viewLifecycleOwner, binding.pvPreview.surfaceProvider, object : ViewportProvider {
            val _analyzeViewport = Rect(0, 0, 0, 0)
            val _previewViewport = Rect(0, 0, 0, 0)

            override fun getPreviewViewport(): Rect {
                _previewViewport.set(
                    binding.pvPreview.left,
                    binding.pvPreview.top,
                    binding.pvPreview.right,
                    binding.pvPreview.bottom
                )
                return _previewViewport
            }

            override fun getAnalyzeViewPort(): Rect {
                _analyzeViewport.set(
                    binding.lottieScan.left,
                    binding.lottieScan.top,
                    binding.lottieScan.right,
                    binding.lottieScan.bottom
                )
                return _analyzeViewport
            }
        }) {
            this.camera = it
        }
    }

    private fun requestPermission() {
        if (!checkFragmentAvailable()) return
        PermissionRequest.with(requireActivity())
            .permissions(Permission.CAMERA)
            .denied {
                if (!checkFragmentAvailable()) return@denied
                viewLifecycleOwner.lifecycleScope.launch {
                    delay(100)
                    if (!checkFragmentAvailable()) return@launch
                    MetaRouter.IM.goCameraPermission(this@QRCodeScanFragment, REQUEST_PERMISSION_CAMERA, false, args.packageName, args.gameId)
                }
            }
            .granted { if (checkFragmentAvailable()) initPreview() }
            .branch(PermissionRequest.SCENE_QR_CODE)
            .request()
    }

    private fun initListeners() {
        binding.btGallery.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_QR_SCAN_PICTURE_CLICK) {
                put("gameid", args.gameId.toString())
                put("gamepkg", args.packageName.toString())
                put("from", analyticFrom)
            }
            chooseQRCodeToDecode()
        }
        binding.tblTitleBar.setOnBackClickedListener {
            popup()
        }
        setFragmentResultListener(REQUEST_PERMISSION_CAMERA) { key, bundle ->
            if (key != REQUEST_PERMISSION_CAMERA) {
                return@setFragmentResultListener
            }
            if (bundle.getBoolean(CameraPermissionDialog.KEY_PERMISSION_RESULT, false)) {
                context?.let { AppSystemSettingUtil.goAppSystemSetting(it) }
            }
            popup()
        }

        binding.vQrCodeFinder.setGestureListener(object : QRCoverView.GestureListener {
            override fun onClick(x: Int, y: Int) {
                camera?.apply {
                    val focusPoint = SurfaceOrientedMeteringPointFactory(1f, 1f)
                        .createPoint(x / binding.vQrCodeFinder.width.toFloat(), y / binding.vQrCodeFinder.height.toFloat())

                    val focusAction = FocusMeteringAction.Builder(
                        focusPoint, FocusMeteringAction.FLAG_AF
                    ).disableAutoCancel().build()
                    cameraControl.startFocusAndMetering(focusAction)
                }
            }

            override fun onDoubleClick() {
                camera?.apply {
                    val zoomState = cameraInfo.zoomState.value ?: return@apply
                    if (zoomState.zoomRatio < zoomState.maxZoomRatio / 2) {
                        cameraControl.setZoomRatio((zoomState.maxZoomRatio / 2).coerceAtLeast(zoomState.minZoomRatio))
                    } else {
                        cameraControl.setZoomRatio(zoomState.minZoomRatio)
                    }
                }
            }

            override fun onZoomIn() {
            }

            override fun onZoomOut() {
            }
        })
    }

    private fun initObservers() {
        val callback: (DataResult<DecodeResult>) -> Unit = object : (DataResult<DecodeResult>) -> Unit {
            override fun invoke(it: DataResult<DecodeResult>) {
                Timber.i("QRCode decode result code %s data %s", it.message, it.data?.result)
                sendDecodeResultAnalytics(it)
                if (it is DataResult.Success) {
                    //仅监听一次结果，防止快速识别出结果 导致多次回调
                    vm.qrCodeScanResultCallback.removeCallback(this)

                    setFragmentResultByActivity(args.scanResultKey, Bundle().apply {
                        putString(KEY_SCAN_RESULT, it.data.result.text)
                        putSerializable(KEY_SCAN_RESULT_SOURCE, it.data.source)

                        putSerializable(KEY_SCAN_REQUEST_ENTRY, args.entry)
                        putBundle(KEY_SCAN_REQUEST_DATA, arguments)
                    })
                    popup()
                } else if (!it.message.isNullOrBlank()) {
                    it.message?.let { toast(it) }
                }
            }
        }
        vm.qrCodeScanResultCallback.observe(viewLifecycleOwner, callback)
    }

    private fun sendDecodeResultAnalytics(dataResult: DataResult<DecodeResult>) {
        var type = QrCodeConstant.SCAN_RESULT_NOT_FOUNT
        if (dataResult is DataResult.Success) {
            kotlin.runCatching {
                val text = dataResult.data.result.text
                val uri = Uri.parse(text)
                type = when {
                    uri.getQueryParameter("my_uniq_id") != null -> { //加好友
                        QrCodeConstant.SCAN_RESULT_HOME_PAGE
                    }
                    uri.getQueryParameter("shareId") != null    -> { //房间二维码
                        QrCodeConstant.SCAN_RESULT_JOIN_ROOM
                    }
                    uri.scheme?.startsWith("http") == true      -> { //其他Web地址
                        QrCodeConstant.SCAN_RESULT_WRB_URL
                    }
                    else                                        -> {
                        QrCodeConstant.SCAN_RESULT_NOT_GPARK
                    }
                }
            }
        }

        Analytics.track(EventConstants.EVENT_QR_SCAN_RESULT) {
            put("gameid", args.gameId.toString())
            put("gamepkg", args.packageName.toString())
            put("from", analyticFrom)
            put("result", type)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CHOOSE_IMAGE_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            val result = PictureSelector.obtainSelectorList(data)
            result.firstOrNull()?.let {
                vm.decodeFromLocalPath(
                    requireContext(),
                    it.compressPath ?: it.realPath ?: it.availablePath
                )
            }
        }
    }

    private fun chooseQRCodeToDecode() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine)
            .setSelectionMode(SelectModeConfig.SINGLE)
            .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(requireContext()))
            .setCompressEngine(LubanCompressEngine())
            .forResult(CHOOSE_IMAGE_REQUEST_CODE)
    }

    override fun loadFirstData() {
    }

    private fun checkFragmentAvailable() = !isDetached && activity != null && activity?.isFinishing != true && isBindingAvailable()

    override fun onDestroyView() {
        clearFragmentResultListener(REQUEST_PERMISSION_CAMERA)
        binding.lottieScan.cancelAnimation()
        super.onDestroyView()
    }

    private fun popup() {
        //从游戏内来，回到游戏
        val gameId = args.gameId
        if (!gameId.isNullOrEmpty()) {
            resumeGameById(gameId)
        }
        findNavController().popBackStack()
    }
}