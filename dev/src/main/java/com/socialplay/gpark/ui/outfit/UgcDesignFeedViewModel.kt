package com.socialplay.gpark.ui.outfit

import android.content.ComponentCallbacks
import android.os.SystemClock
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcDesignFeedState(
    val event: Triple<Int, Long, Any?>? = null
) : MavericksState

class UgcDesignFeedViewModel(
    initState: UgcDesignFeedState,
    private val metaKV: MetaKV,
) : BaseViewModel<UgcDesignFeedState>(initState) {

    private var showGuideInit = true
    var showGuide: Boolean = true
        get() {
            if (showGuideInit) {
                showGuideInit = false
                field = metaKV.account.showAssetRookieTabGuide
            }
            return field
        }
        set(value) {
            if (value != field) {
                field = value
                metaKV.account.showAssetRookieTabGuide = value
            }
        }

    fun invokeEvent(event: Int, data: Any? = null) {
        val curTs = SystemClock.elapsedRealtime()
        val newEvent = Triple(event, curTs, data)
        setState { copy(event = newEvent) }
    }

    companion object : KoinViewModelFactory<UgcDesignFeedViewModel, UgcDesignFeedState>() {

        const val EVENT_CLICK_SORT = 1
        const val EVENT_ASSET_DETAIL_LIKE = 2

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcDesignFeedState
        ): UgcDesignFeedViewModel {
            return UgcDesignFeedViewModel(state, get())
        }
    }
}