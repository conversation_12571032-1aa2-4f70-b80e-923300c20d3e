package com.socialplay.gpark.ui.editor

import android.content.Context
import androidx.annotation.CallSuper
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.UgcDraftInfo
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.MVCoreProxyInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.function.editor.IEditorLaunchCallback
import com.socialplay.gpark.function.editor.LaunchOverResult
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSEngineNotMatchDialog
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.backups.UgcBackupFragmentArgs
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2MineFragment
import com.socialplay.gpark.ui.editor.legecy.RenameLocalDialog
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/3/14
 * Desc: 移动编辑器类-
 */
abstract class BaseEditorFragment<T: ViewBinding>: BaseFragment<T>() {

    protected var editorGameLaunchHelper: EditorGameLaunchHelper? = null
    protected val mvCoreProxy by inject<MVCoreProxyInteractor>()

    protected val gameStartScenes by lazy { MWGameStartScenes(this) }
    private val editorInteractor: EditorInteractor by inject()

    @CallSuper
    override fun init() {
        val launchHelper = EditorGameLaunchHelper(editorDownloadCallback)
        launchHelper.init(this)
        gameStartScenes.register(launchHelper)
        editorGameLaunchHelper = launchHelper

        val invoke: (Pair<String, String>) -> Unit = { data: Pair<String,String> ->
            Timber.d("checkcheck onReceivedStartGame errorMsg:${data.first}")
            if (isBindingAvailable()) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    if (data.first.isNullOrEmpty()) {
                        gameStartScenes.show()
                    } else {
                        hideLoadingUI(false, null, false)
                    }
                }
            }
        }
        MWLifeCallback.startGame.observe(viewLifecycleOwner, false, observer = invoke)
        MWLifeCallback.startLocalGame.observe(viewLifecycleOwner, false, observer = invoke)
    }

    open val editorDownloadCallback: IEditorLaunchCallback = object : IEditorLaunchCallback {
        override fun onLaunchOver(result: LaunchOverResult) {
            Timber.d("checkcheck onLaunchOver, ${result.launchSuccess}, ${if (!result.launchSuccess) result.msg else null}")
            if (result.e is TSUserCancelledException) {
                hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
            } else if (isBindingAvailable()) {
                if (result.e is TSEngineVersionNotMatchException) {
                    TSEngineNotMatchDialog.show(this@BaseEditorFragment, result.gameInfo?.icon)
                    hideLoadingUI(result.launchSuccess, "", result.needGoMineLocal)
                } else {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                }
            }
        }

        override fun onChecking(gameInfo: GameDetailInfo?, id: String?, path: String?, type: String?) {
            Timber.d("checkcheck onLaunchingGame type:$type")
            if (isBindingAvailable() && type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
                showLoadingUI(type)
            }
        }

        override fun onUgcTemplateCreated() {
            gameStartScenes.switchToScene()
        }
    }

    /**
     * 隐藏加载界面
     */
    open fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            if (!launchSuccess) {
                toast(msg ?: getString(R.string.verse_download_failed))
                gameStartScenes.hide()
            }
            if (needGoMine && PandoraToggle.isUgcBackup) {
                (parentFragment as? EditorCreateV2Fragment)?.viewModel?.changeSelectTab(true, 1)
                    ?: MetaRouter.MobileEditor.creation(this@BaseEditorFragment, initTab = 1)
            }
        }
    }

//    protected fun startPlaza(fragment: Fragment, categoryId: Int, gameIdCallback: (id: String) -> Unit) {
//        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
//            var plazaGameId = editorInteractor.gameConfigLiveData.value?.data?.plazaGameId
//            if (plazaGameId.isNullOrEmpty()) {
//                plazaGameId = editorInteractor.fetchGameConfigSingle().data?.plazaGameId
//                if (plazaGameId.isNullOrEmpty()) {
//                    ToastUtil.showShort(requireContext(), R.string.fetch_game_detail_failed)
//                    return@launch
//                }
//            }
//            gameIdCallback.invoke(plazaGameId)
//            val gameInfo = withContext(Dispatchers.IO) {
//                editorInteractor.getPlazaInfo()
//            }
//            if (gameInfo == null) {
//                ToastUtil.showShort(requireContext(), R.string.fetch_game_detail_failed)
//                return@launch
//            }
//
//            editorGameLaunchHelper?.startNormalTsGame(
//                fragment , gameInfo,
//                ResIdBean().setCategoryID(categoryId).setGameVersionName(gameInfo.gameVersion())
//            )
//        }
//    }

    /**
     * 展示加载界面
     */
    private fun showLoadingUI(type: String) {
        gameStartScenes.show(type = type)
    }

    protected fun onPlayGame(item: EditorCreationShowInfo) {
        if (!item.hasAvailableUgcGame()) {
            return
        }
        val ugcId = item.getUgcId() ?: return
        Analytics.track(
            EventConstants.UGC_CREATE_DETAIL_CLICK, "ugcid" to ugcId
        )
        val resIdBean = ResIdBean().setGameId(ugcId)
            .setClickGameTime(System.currentTimeMillis())
            .setTsType(ResIdBean.TS_TYPE_UCG)
            .setCategoryID(CategoryId.MOBILE_EDITOR_PUBLISH)
            .setGameCode(item.getParentGameCode())
        if (PandoraToggle.enableUgcDetail) {
            MetaRouter.MobileEditor.ugcDetail(
                this,
                ugcId,
                item.getParentGameCode(),
                resIdBean
            )
        } else {
            editorGameLaunchHelper?.startUgcGame(this,
                ugcId,
                item.getUgcPackageName(),
                item.getGameName().orEmpty(),
                item.getParentGameCode(),
                resIdBean
            )
        }
    }

    /**
     * 复制本地工程
     */
    private fun onClickCopyGame(item: EditorCreationShowInfo) {
        Analytics.track(EventConstants.EVENT_UGC_CREATE_SET_COPY_CLICK, getCommonParams(item))
        val draftInfo = item.draftInfo ?: return
        onClickCopyGameHelper(draftInfo)
    }

    protected open fun onClickCopyGameHelper(draft: UgcDraftInfo) {}

    /**
     * 展示删除本地工程弹窗
     */
    private fun showDeleteSaveDialog(item: EditorCreationShowInfo) {
        Analytics.track(EventConstants.EVENT_UGC_CREATE_SET_DELETE_CLICK) {
            putAll(getCommonParams(item))
        }
        val confirm = SimpleListData(
            getString(R.string.text_confirm),
            bgResource = R.drawable.bg_common_dialog_confirm,
            textColor = R.color.white
        )
        val cancel = SimpleListData(getString(R.string.dialog_cancel))
        ListDialog()
            .list(mutableListOf(confirm, cancel))
            .content(getString(R.string.delete_creation))
            .image(R.drawable.icon_delete)
            .onViewCreateCallback {
                Analytics.track(EventConstants.EVENT_UGC_CREATE_DELETE_SHOW) {
                    putAll(getCommonParams(item))
                }
            }.clickCallback {
                when (it) {
                    confirm -> {
                        // 直接删除
                        Analytics.track(EventConstants.EVENT_UGC_CREATE_DELETE_CONFIRM_CLICK) {
                            putAll(getCommonParams(item))
                        }
                        if (isBindingAvailable()) {
                            context?.let {
                                showDeleteSaveDialogHelper(item, it)
                            }
                        }
                    }

                    else -> {
                        Analytics.track(EventConstants.EVENT_UGC_CREATE_DELETE_CANCEL_CLICK) {
                            putAll(getCommonParams(item))
                        }
                    }
                }
            }.show(childFragmentManager, "CreationDeleteDialog")
    }

    protected open fun showDeleteSaveDialogHelper(item: EditorCreationShowInfo, context: Context) {}

    protected fun showMoreDialog(item: EditorCreationShowInfo) {
        // 更多
        Analytics.track(
            EventConstants.EVENT_UGC_CREATE_SET_CLICK, getCommonParams(item)
        )
        if (item.draftInfo?.underReview() == true) {
            toast(R.string.editor_publishing)
        }
        ListDialog().list(mutableListOf<SimpleListData>().apply {
            if (item.hasAvailableUgcGame()) {
                add(
                    SimpleListData(
                        getString(R.string.build_play),
                        bgResource = R.drawable.bg_common_dialog_yellow
                    )
                )
            }
            if (item.draftInfo != null || item.isOnlyCloud()) {
                if (PandoraToggle.isUgcBackup) {
                    Analytics.track(EventConstants.UGC_BACKUP_EXPOSURE)
                    add(SimpleListData(getString(R.string.select_backup_all_caps)))
                }
            }
            if (item.draftInfo != null && item.ugcInfo == null) {
                add(SimpleListData(getString(R.string.rename_local_game)))
            }
            if (item.draftInfo != null && item.draftInfo?.canCopy() == true) {
                add(SimpleListData(getString(R.string.duplicate_cap)))
            }
            add(
                SimpleListData(
                    getString(R.string.delete_cap), textColor = R.color.color_FF5F42
                )
            )
        }).clickCallback {
            when (it?.text) {
                getString(R.string.build_play) -> {
                    onPlayGame(item)
                }

                getString(R.string.rename_local_game) -> {
                    item.draftInfo?.let { onClickRenameGame(it.jsonConfig.name ?: "", it.path) }
                }

                getString(R.string.duplicate_cap) -> {
                    item.draftInfo?.let { onClickCopyGame(item) }
                }

                getString(R.string.delete_cap) -> {
                    showDeleteSaveDialog(item)
                }

                getString(R.string.select_backup_all_caps) -> {
                    goUgcBackup(item)
                }
            }
        }.show(childFragmentManager, "CreationMoreOptionsDialog")
    }

    /**
     * 重命名本地工程
     */
    protected fun onClickRenameGame(currentName: String, path: String) {
        Analytics.track(EventConstants.EVENT_UGC_MY_BUILD_ITEM_NAME_CLICK)
        RenameLocalDialog.show(
            this,
            currentName,
            path,
            EditorCreateV2MineFragment.REQUEST_KEY_EDITOR_CREATION
        )
    }

    protected fun goUgcBackup(
        item: EditorCreationShowInfo?,
    ) {
        Analytics.track(EventConstants.UGC_BACKUP_CLICK)
        val jsonConfig = item?.draftInfo?.jsonConfig
        val fileId = jsonConfig?.fileId ?:item?.cloudProject?.projectId ?:return
        val gameIdentity = jsonConfig?.parentId ?:item?.cloudProject?.templateSceneId?: return
        viewLifecycleOwner.lifecycleScope.launch {
            val args = UgcBackupFragmentArgs(
                fileId,
                gameIdentity,
                item?.draftInfo?.path ?: EditorLocalHelper.getLocalUnzipFile(fileId).absolutePath,
                EditorConfigJsonEntity.TYPE_NORMAL,
                jsonConfig?.gid,
                jsonConfig?.parentPackageName
            )
            MetaRouter.MobileEditor.ugcBackup(this@BaseEditorFragment, args)
        }
    }

    protected fun getCommonParams(item: EditorCreationShowInfo): HashMap<String, Any> {
        val map = hashMapOf<String, Any>(
            "gameid" to (item.getParentGameCode().orEmpty()),
            "status" to (item.draftInfo?.auditStatus ?: "1"),
            "fileid" to (item.draftInfo?.jsonConfig?.fileId.orEmpty())
        )
        if (item.getUgcId() != null) {
            map["ugcid"] = item.getUgcId().orEmpty()
        }
        return map
    }

    override fun onDestroyView() {
        editorGameLaunchHelper?.let {
            gameStartScenes.unregister(it)
            it.onDestroyHelper()
            editorGameLaunchHelper = null
        }
        super.onDestroyView()
    }
}