package com.socialplay.gpark.ui.profile.outfit

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.RoleStyle
import com.socialplay.gpark.databinding.FragmentProfileTabClothesBinding
import com.socialplay.gpark.databinding.PopUpProfileOutfitBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.profile.home.ProfileViewModel
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.dp
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.sharedViewModel

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/04/02
 *     desc   :
 * </pre>
 */
@Parcelize
data class ProfileTabOutfitFragmentArgs(val isMe: Boolean, val otherUuid: String) : Parcelable

class ProfileTabOutfitFragment :
    BaseRecyclerViewFragment<FragmentProfileTabClothesBinding>(R.layout.fragment_profile_tab_clothes) {

    companion object {
        fun newInstance(isMe: Boolean, otherUuid: String) = ProfileTabOutfitFragment().apply {
            arguments = ProfileTabOutfitFragmentArgs(isMe, otherUuid).asMavericksArgs()
        }
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvClothes

    private val args: ProfileTabOutfitFragmentArgs by args()
    private val vm: ProfileTabOutfitViewModel by fragmentViewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private val profileViewModel: ProfileViewModel by parentFragmentViewModel()

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpProfileOutfitBinding.inflate(layoutInflater) }

    private var pendingDeleteOutfit: Pair<RoleStyle, Int>? = null

    private var otherUid: String = ""

    private val itemListener = object : IProfileTabClothesListener {
        override fun like(item: RoleStyle, position: Int) {
            Analytics.track(
                EventConstants.PROFILE_OUTFIT_LIKE_CLICK,
                "shareid" to item.style.shareId.orEmpty(),
                "uuid" to otherUid,
                "like" to (if (item.isLike) "1" else "0")
            )
            vm.likeStyle(item, position)
            DialogShowManager.triggerLike(this@ProfileTabOutfitFragment)
        }

        override fun more(view: View, item: RoleStyle, position: Int) {
            pendingDeleteOutfit = item to position
            popupBinding.cv.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val x = -popupBinding.cv.measuredWidth - 2.dp
            val y = -(4.dp)
            popupWindow.showAsDropDownByLocation(view, x, y)
        }

        override fun click(item: RoleStyle, position: Int) {
            Analytics.track(
                EventConstants.PROFILE_OUTFIT_CLICK,
                "shareid" to item.style.shareId.orEmpty(),
                "uuid" to otherUid
            )
            context?.let {
                MetaRouter.MobileEditor.fullScreenRole(
                    it,
                    FullScreenEditorActivityArgs(
                        categoryId = CategoryId.PROFILE_OUTFIT_TAB,
                        tryOn = RoleGameTryOn.create(
                            from = if (args.isMe) {
                                RoleGameTryOn.FROM_MY_PROFILE
                            } else {
                                RoleGameTryOn.FROM_OTHER_PROFILE
                            },
                            allowTryOn = true,
                            roleData = GsonUtil.safeToJson(item)
                        )
                    )
                )
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            ProfileTabOutfitState::loadMore
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentProfileTabClothesBinding? {
        return FragmentProfileTabClothesBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        otherUid = profileViewModel.oldState.uuid
        vm.updateUuid(otherUid)
        if (args.isMe) {
            vm.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
                val uuid = it?.uuid ?: return@observe
                if (otherUid != uuid) {
                    otherUid = uuid
                    vm.updateUuid(otherUid)
                }
            }
        }

        binding.lvClothes.setVerticalBias(0.08f)
        binding.rvClothes.layoutManager = GridLayoutManager(
            requireContext(),
            3,
            GridLayoutManager.VERTICAL,
            false
        )

        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupBinding.tvDeleteBtn.setOnClickListener {
            ConfirmDialog.Builder(this)
                .content(getString(R.string.delete_confirm, getString(R.string.outfit_no_cap)))
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .dismissCallback { action ->
                    pendingDeleteOutfit?.let { (outfit, position) ->
                        val result = if (action == ConfirmDialog.CONFIRM) {
                            vm.deleteStyle(outfit, position)
                            0
                        } else {
                            1
                        }
                        Analytics.track(
                            EventConstants.PROFILE_OUTFIT_DELETE_CLICK,
                            "shareid" to outfit.style.shareId.orEmpty(),
                            "result" to result.toString()
                        )
                    }
                    pendingDeleteOutfit = null
                }
                .show()
            popupWindow.dismiss()
        }

        vm.setupRefreshLoading(
            ProfileTabOutfitState::styles,
            binding.lvClothes,
            binding.rlClothes,
            emptyMsg = getString(R.string.no_outfits)
        ) {
            vm.getClothesList(true)
        }
        vm.registerToast(ProfileTabOutfitState::toast)
        vm.registerAsyncErrorToast(ProfileTabOutfitState::loadMore)
    }

    override fun epoxyController() = simpleController(
        vm,
        ProfileTabOutfitState::styles,
        ProfileTabOutfitState::loadMore
    ) { styles, loadMore ->
        val items = styles.invoke()
        if (!items.isNullOrEmpty()) {
            items.forEachIndexed { index, item ->
                profileTabOutfitItem(item, index, args.isMe, itemListener)
            }
            loadMoreFooter(
                loadMore,
                idStr = "LoadMoreFooter-ProfileTabClothes",
                spanSize = 3,
                showEnd = false
            ) {
                vm.getClothesList(false)
            }
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_PROFILE_TAB_CLOTHES

    override fun onDestroyView() {
        pendingDeleteOutfit = null
        popupWindow.dismiss()
        super.onDestroyView()
    }
}