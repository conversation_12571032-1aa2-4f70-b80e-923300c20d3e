package com.socialplay.gpark.ui.im.activities

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.databinding.AdapterSysActivitiesBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.util.DateUtil.formatAgoStyleForChat
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.orNull
import com.socialplay.gpark.util.extension.visible

class SysActivitiesAdapter(private val glide: GlideGetter) :
    BasePagingDataAdapter<SysActivitiesInfo, AdapterSysActivitiesBinding>(DIFF_ITEM_CALLBACK) {

    companion object {
        private const val PAYLOAD_NAME = 1
        private const val PAYLOAD_ICON = 2
        private const val PAYLOAD_TIME = 3
        private const val PAYLOAD_TITLE = 4
        private const val PAYLOAD_CONTENT = 5
        private const val PAYLOAD_IMAGE = 6
        private const val PAYLOAD_ADDITION = 7
        private const val PAYLOAD_SUB_ICON = 8
        private const val PAYLOAD_JUMP_ARROW = 9

        private val DIFF_ITEM_CALLBACK = object : DiffUtil.ItemCallback<SysActivitiesInfo>() {
            override fun areItemsTheSame(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Boolean {
                return oldItem.msgId == newItem.msgId && oldItem.contentType == newItem.contentType
            }

            override fun areContentsTheSame(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Boolean {
                return oldItem.content == newItem.content &&
                        oldItem.additionalValue == newItem.additionalValue &&
                        oldItem.fromIcon == newItem.fromIcon &&
                        oldItem.fromName == newItem.fromName &&
                        oldItem.sendTime == newItem.sendTime &&
                        oldItem.subGroup == newItem.subGroup &&
                        oldItem.linkValue == newItem.linkValue
            }

            override fun getChangePayload(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Any? {
                val payload = lazy { arrayListOf<Int>() }
                if (oldItem.fromName != newItem.fromName) {
                    payload.value.add(PAYLOAD_NAME)
                }
                if (oldItem.fromIcon != newItem.fromIcon) {
                    payload.value.add(PAYLOAD_ICON)
                }
                if (oldItem.sendTime != newItem.sendTime) {
                    payload.value.add(PAYLOAD_TIME)
                }
                if (oldItem.content.title != newItem.content.title) {
                    payload.value.add(PAYLOAD_TITLE)
                }
                if (oldItem.content.content != newItem.content.content) {
                    payload.value.add(PAYLOAD_CONTENT)
                }
                if (oldItem.content.image != newItem.content.image) {
                    payload.value.add(PAYLOAD_IMAGE)
                }
                if (oldItem.additionalValue != newItem.additionalValue || oldItem.additionalType != newItem.additionalType) {
                    payload.value.add(PAYLOAD_ADDITION)
                }
                if (oldItem.subGroup?.listIcon != newItem.subGroup?.listIcon) {
                    payload.value.add(PAYLOAD_SUB_ICON)
                }
                if (oldItem.linkType != newItem.linkType || oldItem.linkValue != newItem.linkValue) {
                    payload.value.add(PAYLOAD_JUMP_ARROW)
                }

                return payload.orNull()
            }
        }
    }

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): AdapterSysActivitiesBinding {
        return AdapterSysActivitiesBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BindingViewHolder<AdapterSysActivitiesBinding>,
        item: SysActivitiesInfo,
        position: Int
    ) {
        holder.binding.apply {
            glide()?.run {
                load(item.fromIcon).circleCrop()
                    .into(imgIcon)
            }
            tvTitle.text = item.fromName
            tvMessage.text = item.content.content
            tvTime.text = item.sendTime.formatAgoStyleForChat(tvTime.context)
            when (item.additionalType) {
                SysActivitiesInfo.ADD_TYPE_TEXT -> {
                    tvContentImage.goneIfValueEmpty(item.additionalValue)
                    ivContentImage.gone()
                }

                SysActivitiesInfo.ADD_TYPE_IMAGE -> {
                    tvContentImage.gone()
                    if (!item.additionalValue.isNullOrBlank()) {
                        ivContentImage.visible()
                        glide()?.run {
                            load(item.additionalValue).transform(CenterCrop(), RoundedCorners(8.dp))
                                .into(ivContentImage)
                        }
                    } else {
                        ivContentImage.gone()
                    }
                }

                else -> {
                    tvContentImage.gone()
                    ivContentImage.gone()
                }
            }
        }
    }

}