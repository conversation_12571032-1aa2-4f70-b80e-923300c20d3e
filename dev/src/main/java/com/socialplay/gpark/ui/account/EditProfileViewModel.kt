package com.socialplay.gpark.ui.account

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserCreateInfo
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.DateUtilWrapper
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date


class EditProfileViewModel(
    val accountInteractor: AccountInteractor,
    val tTaiInteractor :TTaiInteractor,
    val metaRepository: IMetaRepository
) : ViewModel() {

    private val _genderLiveData = SingleLiveData<Gender?>()
    val genderLiveData: LiveData<Gender?> = _genderLiveData

    private val _accountBanLiveData = SingleLiveData<Boolean>()
    val accountBanLiveData: LiveData<Boolean> = _accountBanLiveData

    private val _genderChangeLiveData = SingleLiveData<Boolean>()
    val genderChangeLiveData: LiveData<Boolean> = _genderChangeLiveData

    private val _userProfile = MutableLiveData<UserProfileInfo?>()
    val userProfile: LiveData<UserProfileInfo?> = _userProfile
    private val _isCreate = MutableLiveData<Boolean?>()
    val isCreate: LiveData<Boolean?> = _isCreate
    fun postGenderData(gender: Gender) {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return
        }
        _genderLiveData.postValue(gender)
    }

    fun updateUserGender(gender: Int = -1) = viewModelScope.launch {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return@launch
        }
        accountInteractor.updateUser(mGender = gender, mBirthday = null, reviewBirth = false)
            .collect {
                _genderChangeLiveData.postValue(it.data == true)
            }
    }
    fun updateUserBirthday(date: Date, gameId: String?)= viewModelScope.launch {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return@launch
        }
        val mBirthday = date.time
        accountInteractor.updateUser(mBirthday = mBirthday, reviewBirth = false)
            .collect {
                if (it.data == true ) {
                    if (gameId == null) {
                        Analytics.track(
                            EventConstants.EVENT_PROFILE_EDIT_AGE_COMFIRM,
                        )
                    } else {
                        Analytics.track(
                            EventConstants.EVENT_PROFILE_EDIT_AGE_COMFIRM,
                            mapOf("gameid" to gameId)
                        )
                    }
                }
                _genderChangeLiveData.postValue(it.data == true)
            }
    }

    fun getWheelDefaultDate(): Calendar {
        val birth = accountInteractor.accountLiveData.value?.birth
        return if (birth == null) {
            DateUtilWrapper.getDefaultBirthCalendar()
        } else {
            Calendar.getInstance().apply {
                time = Date(birth)
            }
        }
    }
    fun getUserProfile() = viewModelScope.launch {
        val data = tTaiInteractor.getConfig(TTaiKV.ID_LINK_PROFILE)
        val list = if (data != null) {
            GsonUtil.gsonSafeParseCollection<List<ProfileLinkInfo>>(data.value) ?: emptyList()
        } else {
            emptyList()
        }
        getUserCreate()
        metaRepository.queryUserProfile(accountInteractor.curUuid).collect {
            val linkList = it.data?.externalLinks?.map { item ->
                val linkInfo = list.find { it.type == item.type }
                item.copy(icon = linkInfo?.icon.orEmpty())
            }
            it.data?.externalLinks = linkList?: emptyList()
            _userProfile.value = it.data
        }
    }
    private fun getUserCreate() = viewModelScope.launch {
        metaRepository.getUserCreate().collect {
            _isCreate.value =
                (it.data?.ugcCreatorStatus == UserCreateInfo.IS_CREATE) || it.data?.postCreatorStatus == UserCreateInfo.IS_CREATE
        }
    }
}