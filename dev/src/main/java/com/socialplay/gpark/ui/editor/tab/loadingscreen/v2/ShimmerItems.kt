package com.socialplay.gpark.ui.editor.tab.loadingscreen.v2

import com.airbnb.epoxy.Carousel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewEditorLoadingShimmerIntroLine1Binding
import com.socialplay.gpark.databinding.ViewEditorLoadingShimmerIntroLine2BlockBinding
import com.socialplay.gpark.databinding.ViewEditorLoadingShimmerIntroLine3Binding
import com.socialplay.gpark.databinding.ViewEditorLoadingShimmerItemBinding
import com.socialplay.gpark.databinding.ViewEditorLoadingShimmerItemCardBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin


fun MetaModelCollector.shimmerIntroLine1(marginStart:Int) {
    add(ShimmerIntroLine1(marginStart).apply {
        id("shimmerIntroLine1-${this}")
    })
}

fun MetaModelCollector.shimmerIntroLine2Block(id: String? = null) {
    add(ShimmerIntroLine2Block().apply {
        id?.let { id(it) } ?: id("shimmerIntroLine2Block-${this}")
    })
}

fun MetaModelCollector.shimmerIntroLine3(marginStart:Int) {
    add(ShimmerIntroLine3(marginStart).apply { id("shimmerIntroLine3-${this}") })
}

fun MetaModelCollector.shimmerIem(
    marginStart: Int,
    marginEnd: Int,
    viewportWidth: Int,
    id: String? = null,
) {
    add(ShimmerIem(marginStart, marginEnd, viewportWidth).apply {
        id?.let { id(it) } ?: id("shimmerIem-${this}")
    })
}

fun MetaModelCollector.shimmerIemCard(id: String? = null) {
    add(ShimmerIemCard().apply {
        id?.let { id(it) } ?: id("shimmerIemCard-${this}")
    })
}



 class ShimmerIntroLine1(private val marginStart:Int) :
    ViewBindingItemModel<ViewEditorLoadingShimmerIntroLine1Binding>(
        R.layout.view_editor_loading_shimmer_intro_line1,
        ViewEditorLoadingShimmerIntroLine1Binding::bind
    ) {
    override fun ViewEditorLoadingShimmerIntroLine1Binding.onBind() {
        root.setMargin(left = marginStart)
    }
}

 class ShimmerIntroLine2Block :
    ViewBindingItemModel<ViewEditorLoadingShimmerIntroLine2BlockBinding>(
        R.layout.view_editor_loading_shimmer_intro_line2_block,
        ViewEditorLoadingShimmerIntroLine2BlockBinding::bind
    ) {
    override fun ViewEditorLoadingShimmerIntroLine2BlockBinding.onBind() {
    }
}

 class ShimmerIntroLine3(private val marginStart:Int) :
    ViewBindingItemModel<ViewEditorLoadingShimmerIntroLine3Binding>(
        R.layout.view_editor_loading_shimmer_intro_line3,
        ViewEditorLoadingShimmerIntroLine3Binding::bind
    ) {
    override fun ViewEditorLoadingShimmerIntroLine3Binding.onBind() {
        root.setMargin(left = marginStart)
    }
}

class ShimmerIem(
    private val marginStart: Int,
    private val marginEnd: Int,
    private val viewportWidth: Int
) :
    ViewBindingItemModel<ViewEditorLoadingShimmerItemBinding>(
        R.layout.view_editor_loading_shimmer_item,
        ViewEditorLoadingShimmerItemBinding::bind
    ) {
    override fun ViewEditorLoadingShimmerItemBinding.onBind() {
        root.setMargin(left = marginStart, right = marginEnd)
        carouselItems.setPadding(Carousel.Padding.dp(10, 0, 10, 0,8))
        carouselItems.setController(MetaEpoxyController {
            val cardCount = (viewportWidth / 80.dp + 0.5).toInt()
            repeat(cardCount) { shimmerIemCard() }
        })
        carouselItems.requestModelBuild()
    }
}

class ShimmerIemCard :
    ViewBindingItemModel<ViewEditorLoadingShimmerItemCardBinding>(
        R.layout.view_editor_loading_shimmer_item_card,
        ViewEditorLoadingShimmerItemCardBinding::bind
    ) {
    override fun ViewEditorLoadingShimmerItemCardBinding.onBind() {
    }
}