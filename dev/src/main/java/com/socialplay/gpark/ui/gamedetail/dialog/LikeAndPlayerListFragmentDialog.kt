package com.socialplay.gpark.ui.gamedetail.dialog

import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.databinding.FragmentLikeAndPlayerListBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.gamedetail.dialog.LikeAndPlayerListFragmentDialog.Companion.TYPE_LIKE
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize
import timber.log.Timber

/**
 * 点赞和游玩列表弹窗
 */
@Parcelize
data class LikeAndPlayerListFragmentDialogArgs(
    val gameId: String,
    val likeCount: Int,
    val playerCount: Int,
    val type: Int = TYPE_LIKE,
    val isOwner: Boolean = true,
    val gameType: Int
) : Parcelable

class LikeAndPlayerListFragmentDialog : BaseBottomSheetDialogFragment() {

    override val binding by viewBinding(FragmentLikeAndPlayerListBinding::inflate)

    private var tabLayoutMediator: TabLayoutMediator? = null
    private val viewModel: LikeAndPlayerListViewModel by fragmentViewModel()
    private val args: LikeAndPlayerListFragmentDialogArgs by args()

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            if (tab.tag == TYPE_LIKE.toString()) {
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_LIKE_LIST_SHOW,
                    "gameid" to args.gameId,
                    "user_type" to if (args.isOwner) "owner" else "visitor",
                    "creatortype" to args.gameType.toString(),
                    "page_type" to "like",
                )
            } else if (tab.tag == TYPE_PLAYER.toString()) {
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_LIKE_LIST_SHOW,
                    "gameid" to args.gameId,
                    "user_type" to if (args.isOwner) "owner" else "visitor",
                    "creatortype" to args.gameType.toString(),
                    "page_type" to "player",
                )
            }
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    companion object {
        const val TYPE_LIKE = 0
        const val TYPE_PLAYER = 1

        fun newInstance(
            gameId: String,
            likeCount: Int,
            playerCount: Int,
            type: Int = TYPE_LIKE,
            isOwner: Boolean = true,
            gameType: Int
        ): LikeAndPlayerListFragmentDialog {
            return LikeAndPlayerListFragmentDialog().apply {
                arguments = LikeAndPlayerListFragmentDialogArgs(
                    gameId,
                    likeCount,
                    playerCount,
                    type,
                    isOwner,
                    gameType,
                ).asMavericksArgs()
            }
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        tab.customView?.findViewById<TextView>(R.id.tv_selected)?.isInvisible = !select
        tab.customView?.findViewById<TextView>(R.id.tv_normal)?.isInvisible = select
    }

    override fun init() {
        skipCollapsed()

        // 设置圆角背景
        dialog?.window?.decorView?.background = null
        getBottomSheet()?.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_corner_top_20_white)

        // 设置关闭按钮点击事件
        binding.ivClose.setOnClickListener {
            dismiss()
        }

        withState(viewModel) {
            Timber.d("LikeAndPlayerList init, withState")
            initTab(it.tabItems, it.likeCount, it.playerCount)
            binding.vpLikeAndPlayer.setCurrentItem(it.type, false)

//            // 延迟检查所有列表是否为空，确保初始状态下的高度是正确的
//            binding.root.post {
//                checkAllListsEmpty()
//            }
        }
    }

    private fun initTab(
        tabItems: List<CommonTabItem>, likeCount: Int, playerCount: Int
    ) {
        binding.tabLikeAndPlayer.addOnTabSelectedListener(tabCallback)
        val titles = getTitle(tabItems, likeCount, playerCount)
        val adapter = CommonTabStateAdapter(
            tabItems.map { it.fragmentInvoke }, childFragmentManager, viewLifecycleOwner.lifecycle
        )
        binding.vpLikeAndPlayer.adapterAllowStateLoss = adapter
        binding.vpLikeAndPlayer.offscreenPageLimit = 2

        tabLayoutMediator = TabLayoutMediator(
            binding.tabLikeAndPlayer, binding.vpLikeAndPlayer
        ) { tab: TabLayout.Tab, position: Int ->
            val tabBinding = LayoutInflater.from(requireContext()).inflate(R.layout.tab_indicator_like_and_player, null)
            val tvNormal = tabBinding.findViewById<TextView>(R.id.tv_normal)
            val tvSelected = tabBinding.findViewById<TextView>(R.id.tv_selected)
            tvNormal.text = titles[position]
            tvSelected.text = titles[position]
            tab.customView = tabBinding
            tab.tag = tabItems[position].type
        }
        tabLayoutMediator?.attach()

        // 初始化选中状态
        val initialTab = binding.tabLikeAndPlayer.getTabAt(args.type)
        initialTab?.select()
        initialTab?.customView?.findViewById<TextView>(R.id.tv_normal)?.visibility = View.INVISIBLE
        initialTab?.customView?.findViewById<TextView>(R.id.tv_selected)?.visibility = View.VISIBLE
    }

    private fun getTitle(
        tabItems: List<CommonTabItem>, likeCount: Int, playerCount: Int
    ): List<String> {
        return tabItems.mapIndexed { index, item ->
            val count = if (index == TYPE_LIKE) likeCount else playerCount
            getString(item.title, UnitUtil.formatNumberWithUnit(count, 1, true, true, true))
        }
    }

    override fun onDestroyView() {
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.tabLikeAndPlayer.clearOnTabSelectedListeners()
        binding.vpLikeAndPlayer.adapterAllowStateLoss = null
        super.onDestroyView()
    }

    override fun getPageName(): String = PageNameConstants.DIALOG_LIKE_AND_PLAYER_LIST
    override fun needCountTime(): Boolean = true
}