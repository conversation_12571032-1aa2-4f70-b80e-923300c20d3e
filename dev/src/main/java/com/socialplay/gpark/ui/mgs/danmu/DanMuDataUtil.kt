package com.socialplay.gpark.ui.mgs.danmu


import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Typeface
import android.os.Build
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.bytedance.danmaku.render.engine.render.draw.bitmap.BitmapData
import com.bytedance.danmaku.render.engine.render.draw.text.TextData

import com.meta.biz.mgs.data.model.Member
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.mgs.danmu.advanced.AdvancedDanmakuData
import com.socialplay.gpark.ui.mgs.danmu.advanced.DIGG_STATE_SELF_DIGG
import com.socialplay.gpark.ui.mgs.danmu.advanced.DiggData
import com.socialplay.gpark.ui.mgs.danmu.advanced.GradientTextData
import com.socialplay.gpark.ui.mgs.danmu.advanced.LAYER_TYPE_SCROLL
import com.socialplay.gpark.ui.mgs.danmu.advanced.LevelData
import com.socialplay.gpark.ui.mgs.danmu.advanced.NineBitmapData
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import java.sql.Time


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/02/27
 *     desc   :
 *
 */
object DanMuDataUtil {
    fun getUserStroke(member: Member?): Int? {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return null
        }
        return if (duration < 5) {
            R.drawable.icon_user_s_one
        } else if (duration < 10) {
            R.drawable.icon_user_s_two
        } else if (duration < 15) {
            R.drawable.icon_user_s_three
        } else {
            R.drawable.icon_user_s_four
        }
    }

    fun getChatBg(member: Member?): Int {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return R.drawable.icon_danmu_normal
        }
        return if (duration < 5) {
            R.drawable.icon_danmu_one
        } else if (duration < 10) {
            R.drawable.icon_danmu_two
        } else if (duration < 15) {
            R.drawable.icon_danmu_three
        } else {
            R.drawable.icon_danmu_four
        }
    }
    fun getInputBg(member: Member?): Int {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return R.drawable.icon_danmu_normal_input
        }
        return if (duration < 5) {
            R.drawable.icon_danmu_one_input
        } else if (duration < 10) {
            R.drawable.icon_danmu_two_input
        } else if (duration < 15) {
            R.drawable.icon_danmu_three_input
        } else {
            R.drawable.icon_danmu_four_input
        }
    }

    fun getTextColor(member: Member?): Int {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return R.color.white
        }
        return if (duration < 5) {
            R.color.color_9FDDFF
        } else if (duration < 10) {
            R.color.color_B998FF
        } else if (duration < 15) {
            R.color.color_FFCE50
        } else {
            R.color.color_FFF54D
        }
    }

    fun getCountColor(member: Member?): Int {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return R.color.white
        }
        return if (duration < 5) {
            R.color.color_BCFFF8
        } else if (duration < 10) {
            R.color.color_E7A2FF
        } else if (duration < 15) {
            R.color.color_FFE350
        } else {
            R.color.color_FFF772
        }
    }

    fun getCountStrokeColor(member: Member?): Int {
        val duration = member?.let { getDanMuLevelTime(it) } ?: 0
        if (member == null || duration < 1) {
            return R.color.white
        }
        return if (duration < 5) {
            R.color.color_24A6FF
        } else if (duration < 10) {
            R.color.color_9932FF
        } else if (duration < 15) {
            R.color.color_E67D17
        } else {
            R.color.color_7D27F9
        }
    }

    fun getDanMuData(
        metaApp: Context,
        member: Member,
        bgHeight: Float,
        contentSize: Float,
        avatarHeight: Float,
        stokeHeight: Float,
        countSize: Float,
        content: String,
        callBack: (AdvancedDanmakuData) -> Unit
    ) {
        val inputColor = getTextColor(member)
        val chatBg = BitmapFactory.decodeResource(metaApp.resources, getChatBg(member))
        val userStrokeBitmap: Bitmap? = getUserStroke(member)?.let { BitmapFactory.decodeResource(metaApp.resources, it) }
        Glide.with(metaApp).asBitmap().centerCrop().load(member.avatar)
            .transform(RoundedCorners(360.dp)).into(object : SimpleTarget<Bitmap?>() {
            override fun onResourceReady(mDiggBitmap: Bitmap, transition: Transition<in Bitmap?>?) {
                val data = AdvancedDanmakuData().apply {
                    userId = member.uuid
                    textData = TextData().apply {
                        text = member.nickname + " : " + content
                        textColor = ContextCompat.getColor(metaApp, inputColor)
                        textSize = contentSize

                    }
                    nineBitmap = NineBitmapData().apply {
                        bitmap = chatBg
                        height = bgHeight
                    }
                    diggData = DiggData().apply {
                        diggState = DIGG_STATE_SELF_DIGG

                        diggIcon = BitmapData().apply {
                            width = avatarHeight
                            height = avatarHeight
                            bitmap = mDiggBitmap
                        }
                        diggBgIcon = BitmapData().apply {
                            width = stokeHeight
                            height = stokeHeight
                            bitmap = userStrokeBitmap
                        }
                        diggText = LevelData().apply {
                            LevelText = GradientTextData().apply {
                                text = if (getDanMuLevelTime(member) >= 1)  getDanMuLevelTime(member).toString() else ""
                                textColor = ContextCompat.getColor(metaApp, getCountColor(member))
                                textSize = countSize
                                textStrokeColor = ContextCompat.getColor(metaApp, getCountStrokeColor(member))
                                textStrokeWidth = ScreenUtil.dp2px(metaApp, 0.5f).toFloat()
                                isBold = true
                                typeface = getFont(metaApp)
                            }
                        }
                    }
                    layerType = LAYER_TYPE_SCROLL
                }
                callBack.invoke(data)
            }
        })

    }
    fun getDanMuLevelTime(member: Member): Long {
        return member.duration / 3600
    }

    private fun getFont(metaApp: Context): Typeface? {
        try {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                metaApp.resources.getFont(R.font.poppins_bold_700)
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }
}