package com.socialplay.gpark.ui.gamedetail.dialog

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import org.koin.android.ext.android.get

/**
 * 点赞和游玩列表ViewModel的State
 */
data class LikeAndPlayerListModelState(
    val gameId: String,
    val likeCount: Int,
    val playerCount: Int,
    val type: Int,
    val tabItems: List<CommonTabItem>
) : MavericksState {
    constructor(args: LikeAndPlayerListFragmentDialogArgs) : this(
        args.gameId,
        args.likeCount,
        args.playerCount,
        args.type,
        getTabItems(args.gameId, args.isOwner, args.gameType, args.likeCount, args.playerCount)
    )
}

/**
 * 获取Tab项
 */
private fun getTabItems(
    gameId: String,
    isOwner: Boolean = true,
    gameType: Int,
    likeCount: Int = 0,
    playerCount: Int = 0
): List<CommonTabItem> {
    return listOf(
        CommonTabItem(R.string.like_tab_title, LikeAndPlayerListFragmentDialog.TYPE_LIKE.toString()) {
            LikeAndPlayerItemFragment.newInstance(
                LikeAndPlayerItemFragment.TYPE_LIKE,
                gameId,
                isOwner,
                gameType,
                likeCount,
                playerCount
            )
        },
        CommonTabItem(R.string.player_tab_title, LikeAndPlayerListFragmentDialog.TYPE_PLAYER.toString()) {
            LikeAndPlayerItemFragment.newInstance(
                LikeAndPlayerItemFragment.TYPE_PLAYER,
                gameId,
                isOwner,
                gameType,
                likeCount,
                playerCount
            )
        }
    )
}

/**
 * 点赞和游玩列表ViewModel
 */
class LikeAndPlayerListViewModel(
    private val repository: IMetaRepository,
    initialState: LikeAndPlayerListModelState
) : BaseViewModel<LikeAndPlayerListModelState>(initialState) {

    companion object : KoinViewModelFactory<LikeAndPlayerListViewModel, LikeAndPlayerListModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LikeAndPlayerListModelState
        ): LikeAndPlayerListViewModel {
            return LikeAndPlayerListViewModel(get(), state)
        }
    }
}
