package com.socialplay.gpark.ui.editor.module

import android.graphics.Rect
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.databinding.FragmentUgcModuleHomeBinding
import com.socialplay.gpark.databinding.PopUpUgcModuleProjectBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.BaseEditorFragmentV2
import com.socialplay.gpark.ui.editor.backups.UgcBackupFragment
import com.socialplay.gpark.ui.editor.backups.UgcBackupFragmentArgs
import com.socialplay.gpark.ui.editor.legecy.RenameLocalDialog
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visibleList

class UgcModuleHomeFragment :
    BaseEditorFragmentV2<FragmentUgcModuleHomeBinding>(R.layout.fragment_ugc_module_home) {

    companion object {
        const val START_GAME_STATUS_INIT = 0
        const val START_GAME_STATUS_TEMPLATE = 1
        const val START_GAME_STATUS_LOCAL = 2
        const val START_GAME_STATUS_DONE = 3

        const val REQUEST_KEY_EDITOR_CREATION = "request_key_editor_creation"
    }

    private val vm: UgcModuleHomeViewModel by fragmentViewModel()
    private val tabVM: UgcModuleTabViewModel by parentFragmentViewModel()
    private val args by args<UgcModuleTabArgs>()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpUgcModuleProjectBinding.inflate(layoutInflater) }

    private var lastToastTs = 0L

    private var startGameStatus = START_GAME_STATUS_INIT

    private var loadingDialog: DialogFragment? = null

    private var needNotifyMaskEvent = false

    private val itemListener = object : IUgcModuleHomeListener {
        override fun clickTips(view: View) {
            val curTs = SystemClock.elapsedRealtime()
            if (curTs - lastToastTs >= 2000) {
                lastToastTs = curTs
                toast(
                    getString(
                        R.string.ugc_module_project_count_tips,
                        vm.oldState.maxProjectCount() ?: 50
                    )
                )
            }
        }

        override fun clickTemplate() {
            val s = vm.oldState
            if (s.reachLimit) {
                toast(R.string.ugc_work_amount_reach_limit)
                needNotifyMaskEvent = false
            } else {
                s.template()?.let {
                    startGameStatus = START_GAME_STATUS_TEMPLATE
                    editorGameLaunchHelper?.startTemplateGame(
                        this@UgcModuleHomeFragment,
                        it,
                        ResIdBean().setCategoryID(CategoryId.UGC_MODULE_TEMPLATE)
                    )
                }
            }
        }

        override fun clickMore(view: View, item: EditorCreationShowInfo, position: Int) {
            popupBinding.vCopyClick.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.MOD_DRAFT_COPY_CLICK
                )
                vm.copyProject(item, position)
                popupWindow.dismiss()
            }
            popupBinding.vSelectBackupClick.setOnAntiViolenceClickListener {
                handleBackup(item, position)
                popupWindow.dismiss()
            }
            popupBinding.vRenameClick.setOnAntiViolenceClickListener {
                handleRename(item, position)
                popupWindow.dismiss()
            }
            popupBinding.vDeleteClick.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.MOD_DRAFT_DELETE_CLICK
                )
                val confirm = SimpleListData(
                    getString(R.string.text_confirm),
                    bgResource = R.drawable.bg_common_dialog_confirm,
                    textColor = R.color.white
                )
                val cancel = SimpleListData(getString(R.string.dialog_cancel))
                ListDialog()
                    .list(mutableListOf(confirm, cancel))
                    .content(getString(R.string.delete_project_confirmation))
                    .image(R.drawable.icon_delete)
                    .clickCallback {
                        when (it) {
                            confirm -> {
                                // 直接删除
                                if (isBindingAvailable()) {
                                    context?.let {
                                        vm.deleteProject(item, position)
                                    }
                                }
                            }
                        }
                    }.show(childFragmentManager, "ProjectDeleteDialog")
                popupWindow.dismiss()
            }

            if (item.isOnlyCloud()) {
                visibleList(
                    popupBinding.mtvCopy,
                    popupBinding.vCopyClick,
                    popupBinding.vDivider1,
                    visible = false
                )
                visibleList(
                    popupBinding.mtvSelectBackup,
                    popupBinding.vSelectBackupClick,
                    popupBinding.vDivider2,
                    visible = true
                )
                visibleList(
                    popupBinding.mtvRename,
                    popupBinding.vRenameClick,
                    popupBinding.vDivider3,
                    visible = false
                )
            } else if (item.isClouded()) {
                visibleList(
                    popupBinding.mtvCopy,
                    popupBinding.vCopyClick,
                    popupBinding.vDivider1,
                    popupBinding.mtvSelectBackup,
                    popupBinding.vSelectBackupClick,
                    popupBinding.vDivider2,
                    popupBinding.mtvRename,
                    popupBinding.vRenameClick,
                    popupBinding.vDivider3,
                    visible = true
                )
            } else {
                visibleList(
                    popupBinding.mtvCopy,
                    popupBinding.vCopyClick,
                    popupBinding.vDivider1,
                    visible = true
                )
                visibleList(
                    popupBinding.mtvSelectBackup,
                    popupBinding.vSelectBackupClick,
                    popupBinding.vDivider2,
                    visible = false
                )
                visibleList(
                    popupBinding.mtvRename,
                    popupBinding.vRenameClick,
                    popupBinding.vDivider3,
                    visible = true
                )
            }

            var maxWidth = 0
            for (child in popupBinding.clContainer.children) {
                if (child.isVisible && child is MetaTextView) {
                    val width = child.measureString()
                    if (width > maxWidth) {
                        maxWidth = width
                    }
                }
            }

            popupBinding.cv.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val x = -maxWidth + view.measuredWidth - dp(78)
            val rawY = -dp(12)
            val rect = Rect()
            view.getGlobalVisibleRect(rect)
            val rawBottom = rect.bottom + popupBinding.cv.measuredHeight + rawY
            binding.root.getGlobalVisibleRect(rect)
            val y = if (rawBottom > rect.bottom) {
                -popupBinding.cv.measuredHeight - view.measuredHeight + dp(8)
            } else {
                rawY
            }
            popupWindow.showAsDropDownByLocation(view, x, y, autoHeight = false)
        }

        override fun clickContinueBtn(item: EditorCreationShowInfo, position: Int) {
            if (item.isOnlyCloud()) {
                handleBackup(item, position)
            } else {
                val draftInfo = item.draftInfo ?: return
                startGameStatus = START_GAME_STATUS_LOCAL
                editorGameLaunchHelper?.startLocalGame(
                    this@UgcModuleHomeFragment,
                    draftInfo.jsonConfig.gid,
                    draftInfo.path,
                    draftInfo.jsonConfig.parentPackageName.orEmpty(),
                    draftInfo.jsonConfig.fileId ?: "",
                    ResIdBean().setClickGameTime(System.currentTimeMillis())
                        .setGameCode(draftInfo.jsonConfig.gid)
                        .setGameId(draftInfo.jsonConfig.gid)
                        .setCategoryID(CategoryId.UGC_MODULE_TEMPLATE_CONTINUE)
                        .setTsType(ResIdBean.TS_TYPE_LOCAL)
                )
            }
        }

        override fun showTemplate(view: View) {
            if (!args.showNewbieGuide || vm.ugcModuleHomeGuide) return
            vm.ugcModuleHomeGuide = true
            view.doOnLayoutSized {
                val rect = Rect()
                view.getGlobalVisibleRect(rect)
                tabVM.invokeEvent(UgcModuleTabViewModel.EVENT_GUIDE, rect)
            }
        }

        override fun clickBanner(item: UniJumpConfig, position: Int) {
            Analytics.track(
                EventConstants.MOD_BANNER_CLICK,
                "bannerid" to item.id.orEmpty()
            )
            UniJumpUtil.jump(
                this@UgcModuleHomeFragment,
                item,
                LinkData.SOURCE_MODULE_CREATE,
                CategoryId.UGC_MODULE_BANNER,
                mapOf("videoPlayAnalyticsFrom" to "module_banner")
            )
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentUgcModuleHomeBinding? {
        return FragmentUgcModuleHomeBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initPopup()

        EpoxyVisibilityTracker().attachV2(viewLifecycleOwner, recyclerView)

        vm.setupRefreshLoading(
            UgcModuleHomeState::status,
            binding.lv,
            binding.rl,
        ) {
            vm.refresh()
        }
        vm.onEach(
            UgcModuleHomeState::copyResult,
            UgcModuleHomeState::deleteResult
        ) { copyResult, deleteResult ->
            if (copyResult is Loading || deleteResult is Loading) {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
            } else {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = null
            }
        }
        vm.onAsync(UgcModuleHomeState::copyResult, deliveryMode = uniqueOnly(), onFail = { _ ->
            toast(R.string.duplicated_failed)
        }) {
            toast(R.string.duplicated_successfully)
        }
        vm.onAsync(UgcModuleHomeState::deleteResult, deliveryMode = uniqueOnly(), onFail = { _ ->
            toast(R.string.delete_draft_failed)
        }) {
            if (!it) {
                toast(R.string.delete_draft_failed)
            }
        }
        vm.registerToast(UgcModuleHomeState::toast)
        tabVM.onEach(UgcModuleTabState::event, deliveryMode = uniqueOnly()) {
            when (it?.first) {
                UgcModuleTabViewModel.EVENT_START_TEMPLATE -> {
                    needNotifyMaskEvent = true
                    itemListener.clickTemplate()
                }
            }
        }

        setFragmentResultListenerByHostFragment(
            UgcBackupFragment.KEY,
            viewLifecycleOwner
        ) { _, bundle ->
            val ok = bundle.getBoolean(UgcBackupFragment.KEY_RESULT)
            if (ok) {
                vm.getProjects()
            }
        }
        setFragmentResultListenerByActivity(
            REQUEST_KEY_EDITOR_CREATION, viewLifecycleOwner
        ) { key, bundle ->
            if (key == REQUEST_KEY_EDITOR_CREATION && bundle.getString(
                    RenameLocalDialog.KEY_RESULT
                ) == RenameLocalDialog.RESULT_REFRESH_LOCAL
            ) {
                val path = bundle.getString(RenameLocalDialog.KEY_PATH)
                    ?: return@setFragmentResultListenerByActivity
                val newName = bundle.getString(RenameLocalDialog.KEY_NEW_NAME)
                    ?: return@setFragmentResultListenerByActivity
                vm.renameLocal(newName, path)
            }
        }
    }

    private fun initPopup() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationGameDetailCommon
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupWindow.setOnDismissListener {
            popupBinding.vCopyClick.unsetOnClick()
            popupBinding.vDeleteClick.unsetOnClick()
        }
    }

    private fun handleBackup(item: EditorCreationShowInfo, position: Int) {
        val jsonConfig = item.draftInfo?.jsonConfig
        val fileId = jsonConfig?.fileId ?: item.cloudProject?.projectId ?: return
        val gameIdentity = jsonConfig?.parentId ?: item.cloudProject?.templateSceneId ?: return
        MetaRouter.MobileEditor.ugcBackup(
            this,
            UgcBackupFragmentArgs(
                fileId,
                gameIdentity,
                item.draftInfo?.path ?: EditorLocalHelper.getLocalUnzipFile(fileId).absolutePath,
                EditorConfigJsonEntity.TYPE_MODULE,
                jsonConfig?.gid,
                jsonConfig?.parentPackageName,
                true
            )
        )
    }

    private fun handleRename(item: EditorCreationShowInfo, position: Int) {
        val draftInfo = item.draftInfo ?: return
        RenameLocalDialog.show(
            this,
            draftInfo.jsonConfig.name.orEmpty(),
            draftInfo.path,
            REQUEST_KEY_EDITOR_CREATION
        )
    }

    override fun showLoadingUI() {
        super.showLoadingUI()
        if (needNotifyMaskEvent) {
            needNotifyMaskEvent = false
            tabVM.invokeEvent(UgcModuleTabViewModel.EVENT_HIDE_MASK)
        }
    }

    override fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        super.hideLoadingUI(launchSuccess, msg, needGoMine)
        when (startGameStatus) {
            START_GAME_STATUS_TEMPLATE -> {
                Analytics.track(
                    EventConstants.MOD_MANAGEMENT_BUILD_ENTER_CLICK,
                    "result" to (if (launchSuccess) 0 else 1)
                )
                if (launchSuccess) {
                    startGameStatus = START_GAME_STATUS_DONE
                }
            }

            START_GAME_STATUS_LOCAL -> {
                Analytics.track(
                    EventConstants.MOD_MANAGEMENT_CONTINUE_BUILD_CLICK,
                    "result" to (if (launchSuccess) 0 else 1)
                )
                if (launchSuccess) {
                    startGameStatus = START_GAME_STATUS_DONE
                }
            }
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcModuleHomeState::uniqueTag,
        UgcModuleHomeState::wrapper
    ) { _, wrapper ->
        if (wrapper == null) return@simpleController
        if (wrapper.banners.banners.isNotEmpty()) {
            add(UgcModuleHomeBannerItem(wrapper.banners, itemListener).id("UgcModuleHomeBanner"))
        }
        add(UgcModuleHomeNewTemplate(itemListener).id("UgcModuleHomeTemplate"))
        add(
            UgcModuleHomeProjectTitle(
                wrapper.projects.size,
                wrapper.maxProjectCount,
                itemListener
            ).id("UgcModuleHomeProjectTitle")
        )
        if (wrapper.projects.isEmpty()) {
            empty(
                top = dp(60),
                descRes = R.string.no_creation,
                idStr = "UgcModuleHomeProjectEmpty"
            )
        } else {
            wrapper.projects.forEachIndexed { index, item ->
                add(
                    UgcModuleHomeProject(
                        item,
                        index,
                        itemListener
                    ).id("UgcModuleHomeProject-${item.moduleEpoxyId ?: index.toString()}")
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcModuleHomeState::status
        )
        epoxyController.setFilterDuplicates(true)
    }

    override fun onResume() {
        super.onResume()
        if (startGameStatus == START_GAME_STATUS_DONE) {
            startGameStatus = START_GAME_STATUS_INIT
            vm.getProjects()
        }
    }

    override fun onDestroyView() {
        loadingDialog = null
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        super.onDestroyView()
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_MODULE_HOME

    override fun enableGoMine() = false
}