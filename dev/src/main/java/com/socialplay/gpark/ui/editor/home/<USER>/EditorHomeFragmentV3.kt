package com.socialplay.gpark.ui.editor.home.v2

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.addListener
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.asFlow
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.RequestManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.meta.biz.ugc.model.GameTransform
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.editor.AvatarLoadingStatus
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.FragmentEditorHomeV3Binding
import com.socialplay.gpark.databinding.IncludeRoleBtnsBinding
import com.socialplay.gpark.databinding.PopUpProfileV2Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.editor.home.BaseEditorHomeFragment
import com.socialplay.gpark.ui.outfit.IProfileUgcClothListener
import com.socialplay.gpark.ui.outfit.ProfileUgcClothPreviewDialog
import com.socialplay.gpark.ui.outfit.profileUgcCloth
import com.socialplay.gpark.ui.profile.BaseProfileViewModel
import com.socialplay.gpark.ui.profile.HeProfileFragment
import com.socialplay.gpark.ui.profile.HeProfileFragment.Companion.TAG_BLOCK
import com.socialplay.gpark.ui.profile.MeProfileFragment
import com.socialplay.gpark.ui.profile.reportBlock.BaseProfileReportBlockObserver
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.reportBlock.BlockUserViewModel
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.addBottomSheetCallback
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.contentView
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.syncHeightWith
import com.socialplay.gpark.util.extension.measure
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber


class EditorHomeFragmentV3 : BaseEditorHomeFragment<FragmentEditorHomeV3Binding>() {

    companion object {
        const val PROFILE_TAG = "Profile"
    }

    override val ivFeedBack: ImageView? get() = null
    override val flAvatarContainer: FrameLayout get() = binding.flAvatarContainer
    override val includeBtns: IncludeRoleBtnsBinding? get() = null
    override val fcvLoadingLayout: ViewGroup get() = binding.fcvLoadingLayout
    override val tvBalance: TextView get() = binding.tvBalance
    override val rlRechargeEntrance: ViewGroup get() = binding.rlRechargeEntrance
    override val clRechargeFirstTip: ViewGroup get() = binding.clRechargeFirstTip

    override val showWallet: Boolean
        get() = args.isMe
    private val profileViewModel by viewModel<BaseProfileViewModel>()
    private val blockViewModel by viewModel<BlockUserViewModel>()
    private val args by navArgs<EditorHomeFragmentV3Args>()

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpProfileV2Binding.inflate(layoutInflater) }

    private var reportBlockObserver: BaseProfileReportBlockObserver? = null

    private var offset = 0.0f
    private var peekHeight = 0
    private var actualPeekHeight = 0
    private var sheetHeight = 0
    private var nonPeekHeight = 0
    private var threshold = 0

    private val behavior get() = BottomSheetBehavior.from(binding.fragmentDataList)

    private val ugcClothController by lazy { buildCurrentClothesController() }

    private val uuid: String get() = if (args.isMe) {
        accountInteractor.curUuid
    } else {
        args.uuid
    }

    private val ugcClothListener = object : IProfileUgcClothListener {
        override fun clickCloth(item: ProfileCurrentCloth, position: Int) {
            if (item.ugc) {
                Analytics.track(
                    EventConstants.CURRENTLY_CLOTHING_BAR_DESIGN_CLICK
                )
                MetaRouter.UgcDesign.detail(
                    this@EditorHomeFragmentV3,
                    item.itemId,
                    CategoryId.UGC_DESIGN_PROFILE_CLOTHES
                )
            } else {
                ProfileUgcClothPreviewDialog.show(
                    this@EditorHomeFragmentV3,
                    uuid,
                    args.isMe,
                    item
                )
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorHomeV3Binding? {
        return FragmentEditorHomeV3Binding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        childFragmentManager.beginTransaction().run {
            val cachedFragment = childFragmentManager.findFragmentByTag(PROFILE_TAG)
            if (cachedFragment != null) {
                show(cachedFragment)
                setMaxLifecycle(cachedFragment, Lifecycle.State.RESUMED)
            } else {
                if (args.isMe) {
                    replace(
                        R.id.fragment_data_list,
                        MeProfileFragment::class.java,
                        args.profileArgs,
                        PROFILE_TAG
                    )
                } else {
                    replace(
                        R.id.fragment_data_list,
                        HeProfileFragment::class.java,
                        args.profileArgs,
                        PROFILE_TAG
                    )
                }
            }
            commitAllowingStateLoss()
        }

        // 同步 CoordinatorLayout里面的UE视口占位View高度
        // 使其和真实的UE视口高度一致
        val contentView = requireActivity().window.contentView
        // 加速高度同步，减少闪烁
        if (contentView != null) {
            binding.root.measure(
                contentView.width,
                contentView.height,
                heightMeasureMode = MeasureSpec.EXACTLY
            )

            Timber.d("measureResult: contentView.height:${contentView.height} flViewModeViewport.measuredHeight:${binding.flViewModeViewport.measuredHeight}")

            binding.vRoleViewWidgetsPlaceholder.setHeight(binding.flViewModeViewport.measuredHeight)
        }
        binding.vRoleViewWidgetsPlaceholder.syncHeightWith(binding.flViewModeViewport)

        val contentHeight = contentView?.height ?: binding.root.height.coerceAtLeast(binding.root.measuredHeight)
        if (contentHeight > 0) {
            initSheet(contentHeight)
        } else {
            binding.root.doOnLayoutSized {
                initSheet(it.height)
            }
        }

        if (!args.isFromBottom) {
            binding.ivBackBtn.visible()
            binding.ivBackBtn.setOnAntiViolenceClickListener {
                navigateUp()
            }
        }
        if (args.isMe) {
            initPopup()
            binding.tvSparkCount.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.UGC_LIGHT_UP_USERPAGE
                )
                MetaRouter.Web.navigate(
                    this,
                    title = null,
                    url = profileViewModel.sparkH5Url,
                    showTitle = false
                )
            }
            binding.ivShareBtn.setOnAntiViolenceClickListener {
                val recentGames = profileViewModel.shareProfileData.value
                if (recentGames == null) {
                    toast(R.string.loading)
                    profileViewModel.getUserExtra4Share()
                } else {
                    share(recentGames)
                }
            }
            binding.ivMsgBtn.setOnAntiViolenceClickListener {
                MetaRouter.IM.goChatTabFragment(
                    this,
                    source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_AVATAR
                )
            }
            binding.ivSettingsBtn.setOnAntiViolenceClickListener {
                popupBinding.cv.measure(
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                )
                popupWindow.showAsDropDownByLocation(
                    it,
                    -popupBinding.cv.measuredWidth + 18.dp,
                    -(4).dp
                )
            }
            mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
                binding.vMsgRedDot.visible(it > 0)
            }
            profileViewModel.userProfile.observe(viewLifecycleOwner) {
                binding.ivShareBtn.visible(it != null)
            }
            profileViewModel.shareProfileData.observe(viewLifecycleOwner) {
                if (it != null) {
                    share(it)
                }
            }
            initBtn(binding.tvDress, binding.slDress, true) {
                onClickDressBtn()
            }
            accountInteractor.badgeLiveData.observe(viewLifecycleOwner) {
                val show = it?.dressResource?.hasNew ?: false
                binding.vRoleRedDot.visible(show)
                binding.llRoleUpdate.visible(show)
            }
            accountInteractor.sparkBalanceLiveData.observe(viewLifecycleOwner) {
                it ?: return@observe
                binding.tvSparkCount.text = (it.consumable + it.unusable).toString()
            }
            binding.rvCurrentClothes.gone()
            binding.tvSparkCount.visible(PandoraToggle.enableLightUp)
            threshold = 0
        } else {
            binding.tvDress.compoundDrawables(top = R.drawable.ic_tryon_btn)
            binding.tvDress.setText(R.string.head_bar_role_try_on)
            reportBlockObserver = BaseProfileReportBlockObserver(this)
            binding.ivShareBtn.setOnAntiViolenceClickListener {
                share(null)
            }
            binding.ivMsgBtn.gone()
            binding.ivSettingsBtn.setOnAntiViolenceClickListener {
                reportBlockObserver?.showMoreDialog(it)
            }
            reportBlockObserver?.doBlockListener { isBlock ->
                Timber.tag(TAG_BLOCK).d("doBlockListener-> $isBlock")
                profileViewModel.userProfile.value?.uid?.let { uid ->
                    Timber.tag(TAG_BLOCK).d("doBlockListener userProfile-> $isBlock $uid")
                    if (isBlock) {
                        blockViewModel.relationAdd(uid)
                    } else {
                        blockViewModel.relationDel(uid)
                    }
                }
            }
            profileViewModel.userProfile.observe(viewLifecycleOwner) {
                binding.ivShareBtn.visible(it != null)
                reportBlockObserver?.addUserProfileInfo(it)
                initBtn(
                    binding.tvDress,
                    binding.slDress,
                    it?.canTryOn() == true && PandoraToggle.enableTryOnShare
                ) {
                    Analytics.track(
                        EventConstants.PROFILE_TRY_ON_CLICK,
                        "uuid" to uuid
                    )
                    onClickDressBtn(
                        needTrack = false,
                        tryOnData = RoleGameTryOn.create(
                            tryOnUserId = uuid,
                            from = RoleGameTryOn.FROM_OTHER_PROFILE,
                            allowTryOn = true
                        )
                    )
                }
            }
            binding.rvCurrentClothes.layoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL,
                false
            )
            binding.rvCurrentClothes.setControllerAndBuildModels(ugcClothController)

            viewModel.loadingStatusFlow
                .distinctUntilChangedBy { it }
                .combine(profileViewModel.currentClothesLiveData.asFlow()) { status, clothes ->
                    if (status is AvatarLoadingStatus.Success) {
                        clothes
                    } else {
                        null
                    }
                }.distinctUntilChangedBy { it }.collectWithLifecycleOwner(viewLifecycleOwner) {
                    if (it.isNullOrEmpty()) {
                        binding.rvCurrentClothes.gone()
                        threshold = 0
                    } else {
                        binding.rvCurrentClothes.visible()
                        threshold = dp(80)
                        Analytics.track(
                            EventConstants.CURRENTLY_CLOTHING_BAR_SHOW,
                            "count" to it.size,
                            "count_design" to it.count { it.viewable }
                        )
                    }
                    ugcClothController.requestModelBuild()

                    val newPeekHeight = peekHeight - threshold
                    val newViewportHeight = nonPeekHeight + threshold
                    behavior.setPeekHeight(newPeekHeight, true)
                    binding.flViewModeViewport.setHeight(newViewportHeight)
                    actualPeekHeight = newPeekHeight
                    updateOffset()
                }
        }
        mainViewModel.needEnterFullAvatarCallback.observe(viewLifecycleOwner) {
            onClickDressBtn(needTrack = false, tryOnData = it)
        }
    }

    private fun initPopup() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationFromRight
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupBinding.vScanClick.setOnAntiViolenceClickListener {
            MetaRouter.IM.goQRCodeScan(
                requireActivity(),
                this,
                QRCodeScanFragment.KEY_PROFILE_REQUEST_SCAN_QRCODE,
                ScanEntry.Profile
            )
            popupWindow.dismiss()
        }
        popupBinding.vEditClick.setOnAntiViolenceClickListener {
            MetaRouter.Account.editProfile(this, null, null)
            Analytics.track(EventConstants.EVENT_PROFILE_EDIT_PROFILE_CLICK)
            popupWindow.dismiss()
        }
        if (args.isFromBottom) {
            popupBinding.vSettingsClick.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_PROFILE_SET_CLICK)
                Analytics.track(EventConstants.HOMEPAGE_SETTING_CLICK) {
                    put("type", 0)
                }
                MetaRouter.AccountSetting.setting(this, LoginPageSource.Profile, null)
                popupWindow.dismiss()
            }
        } else {
            visibleList(
                popupBinding.vDivider2,
                popupBinding.mtvSettings,
                popupBinding.vSettingsClick,
                visible = false
            )
        }
    }

    private fun initSheet(contentHeight: Int) {
        peekHeight = (contentHeight * 0.3f).toInt()
        nonPeekHeight = contentHeight - peekHeight
        sheetHeight = contentHeight - getDimensionPx(R.dimen.title_bar_height) - StatusBarUtil.getStatusBarHeight(requireContext()) - dp(100)
        val behavior = behavior
        behavior.peekHeight = peekHeight
        behavior.addBottomSheetCallback(
            viewLifecycleOwner,
            object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {}

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    offset = slideOffset
                    updateOffset()
                }
            }
        )
        binding.fragmentDataList.setHeight(sheetHeight)
        binding.flViewModeViewport.setHeight(nonPeekHeight)
        actualPeekHeight = peekHeight
        updateOffset()
    }

    private fun updateOffset() {
        val transY = (-(sheetHeight - actualPeekHeight) * offset + threshold).coerceAtMost(0.0f)
        binding.flDressContainer.translationY = transY
    }

    override fun getTabBarAnimationDelay(isHide: Boolean): Long {
        return if(isHide) 80 else 0
    }

    override fun setFullAvatarRelatedViewStatus(viewMode: Boolean, isAnim: Boolean) {

        val clLayout = binding.clLayout
        val llTitleBar = binding.llTitleBar
        val flDressContainer = binding.flDressContainer
        val currentClothes = binding.flCurrentClothesContainer

        val targetTranslationY = if (viewMode) 0F else {
            if(!clLayout.isLaidOut){
                val widthSpec = MeasureSpec.makeMeasureSpec(
                    requireActivity().resources.displayMetrics.widthPixels,
                    MeasureSpec.AT_MOST
                )

                val heightSpec = MeasureSpec.makeMeasureSpec(
                    requireActivity().resources.displayMetrics.heightPixels,
                    MeasureSpec.AT_MOST
                )
                clLayout.measure(widthSpec, heightSpec)
            }
            clLayout.measuredHeight.toFloat()
        }

        Timber.d("setFullAvatarRelatedViewStatus viewMode:$viewMode isAnim:$isAnim targetTranslationY:$targetTranslationY")

        if(!isAnim){
            llTitleBar.visible(viewMode)
            flDressContainer.visible(viewMode)
            currentClothes.visible(viewMode)
            clLayout.translationY  = targetTranslationY
            return
        }

        val animatorSet = AnimatorSet()

        val to = if(viewMode) 1F else 0F

        val statusBarAlphaAnimator = ObjectAnimator.ofFloat(
            llTitleBar, View.ALPHA,
            llTitleBar.alpha, to
        ).apply {
            addListener(onEnd = { llTitleBar.visible(viewMode) })
            duration = if (viewMode) 33 else 33
            startDelay = if (viewMode) 0 else 0
        }

        val listTranslationAnimator = ObjectAnimator.ofFloat(
            clLayout, View.TRANSLATION_Y,
            clLayout.translationY, targetTranslationY
        ).apply {
            addListener(
                onStart = { if(viewMode) clLayout.visible() },
                onEnd = { if(!viewMode) clLayout.gone() }
            )
            startDelay = if (viewMode) 0 else 0
            duration = if (viewMode) 0 else 0
        }

        val includeButtonsAlphaAnimator = ObjectAnimator.ofFloat(
            flDressContainer, View.ALPHA,
            flDressContainer.alpha, to
        ).apply {
            addListener(onEnd = { flDressContainer.visible(viewMode) })
            duration = if (viewMode) 33 else 33
            startDelay = if (viewMode) 0 else 0
        }

        val currentClothesAlphaAnimator = ObjectAnimator.ofFloat(
            currentClothes, View.ALPHA,
            currentClothes.alpha, to
        ).apply {
            addListener(onEnd = { currentClothes.visible(viewMode) })
            duration = if (viewMode) 33 else 33
            startDelay = if (viewMode) 0 else 0
        }

        animatorSet.playTogether(
            statusBarAlphaAnimator,
            includeButtonsAlphaAnimator,
            listTranslationAnimator,
            currentClothesAlphaAnimator
        )
        animatorSet.start()
    }

    private fun share(userExtra: ShareRawData.UserExtra?) {
        val user = profileViewModel.userProfile.value ?: return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.user(user, userExtra)
        )
    }

    override fun onDestroyView() {
        reportBlockObserver?.dismiss()
        reportBlockObserver = null
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        super.onDestroyView()
    }

    override fun getCustomData(status: String): String? {
        return when (status) {
            GameTransform.STATUS_ROLE_VIEW -> {
                JSONObject()
                    .put("customType", "gparkAvatarView")
                    .put("avatarPercent", 70)
                    .put("myUuid", accountInteractor.curUuid)
                    .put("otherUuid", uuid)
                    .toString()
            }

            else -> {
                null
            }
        }
    }

    override fun roleCategoryId(): Int {
        return if (args.isFromBottom) {
            super.roleCategoryId()
        } else if (args.isMe) {
            CategoryId.JUMP_ROLE_GAME_FROM_ME_PROFILE
        } else {
            CategoryId.JUMP_ROLE_GAME_FROM_OTHER_PROFILE
        }
    }

    private fun buildCurrentClothesController() = MetaEpoxyController {
        if (view == null || isRemoving) return@MetaEpoxyController
        val data = profileViewModel.currentClothesLiveData.value
        data?.forEachIndexed { index, item ->
            profileUgcCloth(item, index, ugcClothListener)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ugcClothController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        ugcClothController.onSaveInstanceState(outState)
    }
}