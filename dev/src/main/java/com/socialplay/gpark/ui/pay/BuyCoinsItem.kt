package com.socialplay.gpark.ui.pay

import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemBuyCoinsBinding
import com.socialplay.gpark.function.pay.RechargeProductCompat
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible

interface IBuyCoinsItemClickedListener {
    fun onClicked(product: RechargeProductCompat)
}

data class BuyCoinsItem(
    val product: RechargeProductCompat,
    val itemClickedListener: IBuyCoinsItemClickedListener,
) : ViewBindingItemModel<ItemBuyCoinsBinding>(
    R.layout.item_buy_coins,
    ItemBuyCoinsBinding::bind
) {
    override fun ItemBuyCoinsBinding.onBind() {
        root.setOnClickListener {
            // 当价格还没从google商店查询到时, currencyCodePrice 的值为空
            if (product.currencyCodePrice.isNotEmpty()) {
                itemClickedListener.onClicked(product)
            }
        }
        // 总赠送代币
        val awardCoins = product.awardCoinNum + product.memberRewardCoinNum
        // 总代币
        tvItemCoinsCount.text = UnitUtilWrapper.formatCoinCont(product.baseCoinNum + awardCoins)
        if (awardCoins > 0) {
            if (product.baseCoinNum > 0) {
                tvItemCoinsBaseCount.visible(true)
                ivItemCoinsAdd.visible(true)
                // 基础代币
                tvItemCoinsBaseCount.text = UnitUtilWrapper.formatCoinCont(product.baseCoinNum)
            } else {
                tvItemCoinsBaseCount.visible(false)
                ivItemCoinsAdd.visible(false)
            }
            tvItemCoinsAwardCount.visible(true)
            // 总赠送代币
            tvItemCoinsAwardCount.setTextWithArgs(
                R.string.buy_coins_page_award_coins,
                UnitUtilWrapper.formatCoinCont(awardCoins)
            )
        } else {
            tvItemCoinsBaseCount.visible(false)
            ivItemCoinsAdd.visible(false)
            tvItemCoinsAwardCount.visible(false)
        }
        // 当价格还没从google商店查询到时, currencyCodePrice 的值为空
        tvItemPrice.text = product.currencyCodePrice.ifEmpty {
            "--"
        }
    }
}