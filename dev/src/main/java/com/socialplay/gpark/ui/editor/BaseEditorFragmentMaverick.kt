package com.socialplay.gpark.ui.editor

import android.os.Bundle
import android.view.View
import androidx.annotation.LayoutRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.MVCoreProxyInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.function.editor.IEditorLaunchCallback
import com.socialplay.gpark.function.editor.LaunchOverResult
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSEngineNotMatchDialog
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/3/14
 * Desc: 移动编辑器类-
 */
abstract class BaseEditorFragmentMaverick<VB : ViewBinding>(
    @LayoutRes layoutRes: Int
): BaseFragment<VB>(layoutRes) {

    protected var editorGameLaunchHelper: EditorGameLaunchHelper? = null
    protected val mvCoreProxy by inject<MVCoreProxyInteractor>()

    private val gameStartScenes by lazy { MWGameStartScenes(this) }
    private val editorInteractor: EditorInteractor by inject()

    private var isBindingAvailable = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isBindingAvailable = true
        super.onViewCreated(view, savedInstanceState)
        val launchHelper = EditorGameLaunchHelper(editorDownloadCallback)
        launchHelper.init(this)
        gameStartScenes.register(launchHelper)
        editorGameLaunchHelper = launchHelper

        val invoke = { data: Pair<String,String> ->
            Timber.d("checkcheck onReceivedStartGame errorMsg:${data.first}")
            if (isBindingAvailable()) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    if (data.first.isNullOrEmpty()) {
                        gameStartScenes.show()
                    } else {
                        hideLoadingUI(false, null, false)
                    }
                }
            }
        }
        MWLifeCallback.startGame.observe(viewLifecycleOwner, false, observer = invoke)
        MWLifeCallback.startLocalGame.observe(viewLifecycleOwner, false, observer = invoke)
    }

    open val editorDownloadCallback: IEditorLaunchCallback = object : IEditorLaunchCallback {
        override fun onLaunchOver(result: LaunchOverResult) {
            Timber.d("checkcheck onLaunchOver, ${result.launchSuccess}, ${if (!result.launchSuccess) result.msg else null}")
            if (isBindingAvailable()) {
                if (result.e is TSUserCancelledException) {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                } else if (result.e is TSEngineVersionNotMatchException) {
                    TSEngineNotMatchDialog.show(this@BaseEditorFragmentMaverick, result.gameInfo?.icon)
                    hideLoadingUI(result.launchSuccess, "", result.needGoMineLocal)
                } else {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                }
            }
        }

        override fun onChecking(gameInfo: GameDetailInfo?, id: String?, path: String?, type: String?) {
            Timber.d("checkcheck onLaunchingGame type:$type")
            if (isBindingAvailable() && type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
                showLoadingUI(type)
            }
        }

        override fun onUgcTemplateCreated() {
            gameStartScenes.switchToScene()
        }
    }

    /**
     * 隐藏加载界面
     */
    open fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            if (!launchSuccess) {
                toast(msg ?: getString(R.string.verse_download_failed))
                gameStartScenes.hide()
            }
            if (needGoMine && PandoraToggle.isUgcBackup) {
                MetaRouter.MobileEditor.creation(this@BaseEditorFragmentMaverick, initTab = 1)
            }
        }
    }

//    protected fun startPlaza(fragment: Fragment, categoryId: Int, gameIdCallback: (id: String) -> Unit) {
//        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
//            var plazaGameId = editorInteractor.gameConfigLiveData.value?.data?.plazaGameId
//            if (plazaGameId.isNullOrEmpty()) {
//                plazaGameId = editorInteractor.fetchGameConfigSingle().data?.plazaGameId
//                if (plazaGameId.isNullOrEmpty()) {
//                    ToastUtil.showShort(requireContext(), R.string.fetch_game_detail_failed)
//                    return@launch
//                }
//            }
//            gameIdCallback.invoke(plazaGameId)
//            val gameInfo = withContext(Dispatchers.IO) {
//                editorInteractor.getPlazaInfo()
//            }
//            if (gameInfo == null) {
//                ToastUtil.showShort(requireContext(), R.string.fetch_game_detail_failed)
//                return@launch
//            }
//
//            editorGameLaunchHelper?.startNormalTsGame(
//                fragment , gameInfo,
//                ResIdBean().setCategoryID(categoryId).setGameVersionName(gameInfo.gameVersion())
//            )
//        }
//    }

    /**
     * 展示加载界面
     */
    private fun showLoadingUI(type: String) {
        gameStartScenes.show(type = type)
    }

    override fun onDestroyView() {
        editorGameLaunchHelper?.let {
            gameStartScenes.unregister(it)
            it.onDestroyHelper()
            editorGameLaunchHelper = null
        }
        super.onDestroyView()
        isBindingAvailable = false
    }

     override fun isBindingAvailable(): Boolean {
        return isBindingAvailable
    }
}