package com.socialplay.gpark.ui.kol.list.provider

import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.FollowedCreatorListInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.FollowedCreatorAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setPaddingEx
import kotlin.math.ceil

/**
 * Kol关注创作者列表
 */
class FollowedCreatorListProvider(
    glide: RequestManager,
    listener: IKolCreatorAdapterListener?
) :
    BaseListCreatorProvider(glide, listener) {

    override val itemViewType: Int = CreatorMultiType.TYPE_FOLLOWED_CREATOR

    companion object {
        const val SHOW_ITEM_COUNT = 4.6F
        const val RV_STATE_KEY = "FollowedCreator"
    }

    override fun buildRv(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val info = item.toFollowedCreatorListInfo()
        val adapter = initAdapter(helper, info)
        val rv = helper.getView<RecyclerView>(R.id.rvHor)
        rv.setPaddingEx(helper.dp(16))
        rv.adapter = adapter
        listener?.popRvStoredState(RV_STATE_KEY)?.let {
            rv.layoutManager?.onRestoreInstanceState(it)
        }
        adapter.setList(info.userList)
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerView>(R.id.rvHor)?.layoutManager?.onSaveInstanceState()?.let {
            listener?.saveRvState(RV_STATE_KEY, it)
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initAdapter(
        holder: BaseViewHolder,
        info: FollowedCreatorListInfo
    ): FollowedCreatorAdapter {
        val itemWidth = calculateItemWidth(holder)
        val adapter =
            FollowedCreatorAdapter(info.userList.toMutableList(), itemWidth.toInt(), glide)
        adapter.setOnItemShowListener { item, position ->
            Analytics.track(
                EventConstants.EVENT_FOLLOW_CREATOR_SHOW,
                EventParamConstants.KEY_USERID to item.uuid,
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_KOL_CREATOR_TAB_FOLLOWED
            )
        }
        adapter.setOnItemClickListener { _, view, position ->
            val item = adapter.getItem(position)
            listener?.goProfile(item.uuid, EventConstants.From.FROM_KOL_CREATOR_TAB_FOLLOWED)
        }
        return adapter
    }

    private fun calculateItemWidth(holder: BaseViewHolder): Int {
        return if (holder.isPad) {
            holder.dp(64)
        } else {
            val space = holder.dp(16)
            val allSpaceWidth = ceil(SHOW_ITEM_COUNT).toInt() * space
            val itemWidth = (holder.screenWidth - allSpaceWidth) / SHOW_ITEM_COUNT
            itemWidth.toInt()
        }
    }

    override fun onClickGoMore(helper: BaseViewHolder, item: CreatorMultiInfo) {
        listener?.goMoreFollowedCreator(item.rvType)
    }
}