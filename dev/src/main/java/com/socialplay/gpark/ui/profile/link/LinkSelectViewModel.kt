package com.socialplay.gpark.ui.profile.link

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.flow.combine
import org.koin.android.ext.android.get

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/16
 *     desc   :
 *
 */
data class LinkSelectState(
    val linkList: List<ProfileLinkInfo>,
    val uuid: String
) : MavericksState {
    constructor(args: LinkSelectArgs) : this(args.linkList, args.uuid)
}

class LinkSelectViewModel(
    initialState: LinkSelectState,
    private val repo: IMetaRepository,
    private val tTaiInteractor: TTaiInteractor,
    private val accountInteractor: AccountInteractor
) : BaseViewModel<LinkSelectState>(initialState) {

    init {
        if (initialState.linkList.isEmpty()) {
            getLinkList()
        }
    }

    private fun getLinkList() = withState { s ->
        combine(
            repo.queryUserProfile(s.uuid),
            tTaiInteractor.getTTaiWithTypeV3<List<ProfileLinkInfo>>(TTaiKV.ID_LINK_PROFILE)
        ) { userProfileResult, linkConfigRaw ->
            val userProfile = userProfileResult.data
            check(userProfile != null)
            val linkConfig = linkConfigRaw.orEmpty().associateBy { it.type }
            userProfile.externalLinks.map { item ->
                val linkInfo = linkConfig[item.type]
                item.copy(icon = linkInfo?.icon.orEmpty())
            }
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(linkList = result())
                }

                else -> {
                    this
                }
            }
        }
    }

    companion object :
        KoinViewModelFactory<LinkSelectViewModel, LinkSelectState>() {


        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LinkSelectState
        ): LinkSelectViewModel {
            return LinkSelectViewModel(state, get(), get(), get())
        }
    }
}