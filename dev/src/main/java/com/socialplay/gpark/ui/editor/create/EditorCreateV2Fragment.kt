package com.socialplay.gpark.ui.editor.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.navigation.fragment.navArgs
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.databinding.FragmentEditorCreateV2Binding
import com.socialplay.gpark.databinding.TabIndicatorCreateBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.editor.create.v3.EditorCreateV3FormworkFragment
import com.socialplay.gpark.ui.editor.create.v4.EditorCreateV4FormworkFragment
import com.socialplay.gpark.ui.main.MainAddDialog
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.addTabGap
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/06
 *     desc   :
 * </pre>
 */
class EditorCreateV2Fragment : BaseFragment<FragmentEditorCreateV2Binding>() {

    companion object {
        const val KEY_INIT_TAB = "init_tab"
        const val KEY_CATEGORY_ID = "categoryId"
        const val KEY_GUIDE_FLAG = "guide_flag"
    }

    val viewModel by viewModel<EditorCreateViewModel>()
    private val args by navArgs<EditorCreateV2FragmentArgs>()

    private val accountInteractor: AccountInteractor by inject()

    private var firstSelectTab = true

    private val tabListener by lazy {
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabPos = tab.position
                if (tabPos == 0 && !firstSelectTab) {
                    Analytics.track(EventConstants.UGC_TEMPLATE_TAB_PAGE_CLICK)
                }
                viewModel.changeSelectTab(false, tabPos)
                updateTab(tab, true)
                firstSelectTab = false
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTab(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        }
    }
    private var tabLayoutMediator: TabLayoutMediator? = null

    private var initTab = -1

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorCreateV2Binding? {
        return FragmentEditorCreateV2Binding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        initTab = savedInstanceState?.getInt(KEY_INIT_TAB, args.initTab) ?: args.initTab
        viewModel.initArgs(args.fromBottomTab)
        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        if (viewModel.needSwitchToMine) {
            viewModel.changeSelectTab(true, 1)
        }
        super.onResume()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(KEY_INIT_TAB, initTab)
        super.onSaveInstanceState(outState)
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initView() {
        (binding.vp.getChildAt(0) as? ViewGroup)?.overScrollMode = ViewGroup.OVER_SCROLL_NEVER
        if (args.fromBottomTab) {
            binding.tbl.setBackIconVisible(false)
            if (!MainBottomNavigationItem.hasProfileTab) {
                shrinkTabLayout()
            }
        } else {
            binding.root.setPaddingEx(bottom = 0)
            binding.tbl.setOnBackClickedListener {
                navigateUp()
            }
            binding.tvTitle.gone()
            shrinkTabLayout()
        }
        Analytics.track(EventConstants.UGC_TEMPLATE_PAGE_SHOW) {
            put("source", args.source.ifNullOrEmpty { if (args.fromBottomTab) "tab" else "home" })
        }
    }

    private fun shrinkTabLayout() {
        val tabLayoutMargin = 60.dp
        binding.tl.setMargin(tabLayoutMargin, 0, tabLayoutMargin, 0)
    }

    private fun initData() {
        viewModel.delayLiveData.observe(viewLifecycleOwner) {
            initViewLater()
            initDataLater()
        }
    }

    private fun initViewLater() {
        binding.lv.hide()
        binding.vp.adapterAllowStateLoss = EditorCreateV2Adapter(
            buildList(2) {
                add {
                    val fragment = if (PandoraToggle.enableUgcBuildVersion == "3") {
                        EditorCreateV4FormworkFragment()
                    } else if (PandoraToggle.enableUgcBuildVersion == "2") {
                        EditorCreateV3FormworkFragment()
                    } else {
                        EditorCreateV2FormworkFragment()
                    }
                    fragment.apply {
                        val bundle = Bundle(2)
                        bundle.putInt(KEY_CATEGORY_ID, args.categoryId)
                        if (!args.source.isNullOrEmpty() && args.source == MainAddDialog.GUIDE_SRC) {
                            bundle.putBoolean(EditorCreateV2Fragment.KEY_GUIDE_FLAG, true)
                        } else {
                            bundle.putBoolean(EditorCreateV2Fragment.KEY_GUIDE_FLAG, false)
                        }
                        arguments = bundle
                    }
                }
                add { EditorCreateV2MineFragment() }
            },
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        binding.tl.addOnTabSelectedListener(tabListener)
        val tabTitles = listOf(R.string.create_v2_template, R.string.create_v2_creation)
        tabLayoutMediator = TabLayoutMediator(binding.tl, binding.vp) { tab, position ->
            val tabBinding = TabIndicatorCreateBinding.inflate(layoutInflater)
            val title = getString(tabTitles[position])
            tabBinding.mtvNormal.text = title
            tabBinding.mtvSelected.text = title
            tab.customView = tabBinding.root
        }
        tabLayoutMediator?.attach()
        binding.tl.addTabGap(3.dp)
        if (initTab != -1) {
            binding.vp.setCurrentItem(initTab, false)
            initTab = -1
        }
        Analytics.track(EventConstants.UGC_TEMPLATE_TAB_PAGE_SHOW)
    }

    private fun initDataLater() {
        viewModel.tabPosLiveData.observe(viewLifecycleOwner) {
            if (it.first && binding.vp.currentItem != it.second) {
                binding.vp.currentItem = it.second
            }
        }
    }

    private fun updateTab(tab: TabLayout.Tab, isSelected: Boolean) {
        val cv = tab.customView ?: return
        TabIndicatorCreateBinding.bind(cv).apply {
            mtvNormal.invisible(isSelected)
            mtvSelected.invisible(!isSelected)
        }
    }

    override fun loadFirstData() {
        viewModel.delay4Loading()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NEW_CREATE

    override fun onDestroyView() {
        binding.vp.adapterAllowStateLoss = null
        binding.tl.removeOnTabSelectedListener(tabListener)
        tabLayoutMediator?.detach()
        tabLayoutMediator = null

        super.onDestroyView()
    }

}