package com.socialplay.gpark.ui.im.conversation

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.AbsListView
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.ly123.tes.mgs.im.ImMessageHelper
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.ChatGroupSystemMessage
import com.ly123.tes.mgs.metacloud.message.UnknownMessage
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.ui.view.TagTextView
import com.socialplay.gpark.util.IMDateUtils
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext
import timber.log.Timber

open class CustomMessageListAdapter(
    val ctx: Context,
    var clickListener: OnMessageClickListener? = null
) : MessageListAdapter(ctx, clickListener) {
    companion object {
        const val TAG = "MessageListAdapter"
        val PRE_LOAD_FLAG = R.id.base_adapter_pre_load_tag
        val PRE_LOAD_BIND_FLAG = R.id.base_adapter_pre_load_tag_bind
    }

    private var mPreLoadCache: MutableMap<String, View?>? = null

    suspend fun preLoad(messages: List<UIMessage>) {
        val screenWidth = ScreenUtil.getScreenWidth(ctx)
        val widthMeasureSpec = MeasureSpec.makeMeasureSpec(
            screenWidth,
            MeasureSpec.AT_MOST
        )
        val heightMeasureSpec = MeasureSpec.makeMeasureSpec(
            0,
            MeasureSpec.UNSPECIFIED
        )
        // 只预加载一屏的数据
        val cacheMap = mutableMapOf<String, View?>()
        val maxPreLoadHeight = ScreenUtil.getScreenWidth(ctx)
        // ListView 在显示完一屏后, 会额外预留一个itemView的做缓存, 所以这里也额外预留一个
        var extraPreLoad = 1
        var totalHeight = 0
        messages.forEach { uiMessage ->
            if (totalHeight < maxPreLoadHeight || extraPreLoad > 0) {
                if (totalHeight > maxPreLoadHeight) {
                    extraPreLoad--
                }
                val iteView = newView(ctx, -1, null)
                try {
                    // 绑定图片消息的时候, 会在子线程中使用Glide, 导致闪退
                    bindView(iteView, -1, uiMessage)
                    iteView.setTag(PRE_LOAD_BIND_FLAG, true)
                } catch (_: Throwable) {
                    // ignore
                }
                try {
                    // 这里 measure 偶现闪退
                    iteView.measure(widthMeasureSpec, heightMeasureSpec)
                    totalHeight += iteView.measuredHeight
                } catch (_: Throwable) {
                    totalHeight += maxPreLoadHeight / 8
                }
                iteView.setTag(PRE_LOAD_FLAG, uiMessage)
                cacheMap[uiMessage.messageId ?: ""] = iteView
            }
        }
        mPreLoadCache = cacheMap
    }

    private fun getPreLoadCache(messageId: String?): View? {
        messageId ?: return null
        val preLoadCache = mPreLoadCache ?: return null
        return preLoadCache[messageId]
    }

    private fun removePreLoadCache(messageId: String?) {
        messageId ?: return
        val preLoadCache = mPreLoadCache ?: return
        if (preLoadCache.contains(messageId)) {
            preLoadCache[messageId]?.setTag(PRE_LOAD_FLAG, null)
            preLoadCache[messageId]?.setTag(PRE_LOAD_BIND_FLAG, null)
            preLoadCache.remove(messageId)
        }
    }


    override fun newView(var1: Context?, position: Int, var3: ViewGroup?): View {
        // position 可能为-1, -1的时候表示是预加载调用的
        if (position >= 0) {
            val uiMessage = getItem(position)
            val preLoadView = getPreLoadCache(uiMessage?.messageId)
            if (preLoadView != null && uiMessage === preLoadView.getTag(PRE_LOAD_FLAG)) {
                Timber.tag(TAG).d("newView-withCache")
                return preLoadView
            }
            Timber.tag(TAG).d("newView-missCache")
        }

        val view = LayoutInflater.from(context).inflate(R.layout.item_rc_message, null, false)

        val holder = ViewHolder()
        holder.leftIconView = view.findViewById(R.id.rc_left)
        holder.rightIconView = view.findViewById(R.id.rc_right)
        holder.leftHeaddressView = view.findViewById(R.id.rc_left_headdress)
        holder.rightHeaddressView = view.findViewById(R.id.rc_right_headdress)
        holder.leftHeadView = view.findViewById(R.id.rc_rl_left_head)
        holder.rightHeadView = view.findViewById(R.id.rc_rl_right_head)
        holder.leftHeadViewDiver = view.findViewById(R.id.view_diver_left)
        holder.rightHeadViewDiver = view.findViewById(R.id.view_diver_right)
        holder.nameView = view.findViewById(R.id.rc_title)
        holder.contentView = view.findViewById(R.id.rc_content)
        holder.layout = view.findViewById(R.id.rc_layout)
        holder.progressBar = view.findViewById(R.id.rc_progress)
        holder.warning = view.findViewById(R.id.rc_warning)
        holder.time = view.findViewById(R.id.rc_time)
        holder.rcTitleLayout = view.findViewById(R.id.rc_title_layout)
        holder.flContentLayout = view.findViewById(R.id.fl_content)
        holder.rcTagOwner = view.findViewById(R.id.rc_tag_owner)
        holder.rcTagAdmin = view.findViewById(R.id.rc_tag_admin)
        holder.rootLayout = view.findViewById(R.id.rc_layout_item_message)
        view?.tag = holder
        timeGone = holder.time?.visibility == View.GONE
        return view
    }


    override fun bindView(view: View?, position: Int, data: UIMessage?) {
        // position 可能为-1, -1的时候表示是预加载调用的
        if (position >= 0 && !data?.messageId.isNullOrEmpty()) {
            val preLoadView = getPreLoadCache(data.messageId)
            if (preLoadView != null) {
                // 预加载缓存用一次就不能再用了, 因为ListView有View复用, 下一次就是绑定别的item, 需要重新走绑定逻辑
                removePreLoadCache(data.messageId)
                if (preLoadView === view && preLoadView.getTag(PRE_LOAD_BIND_FLAG) != null) {
                    Timber.tag(TAG).d("bindView-withCache")
                    return
                }
            }
            Timber.tag(TAG).d("bindView-missCache")
        }

        val holder: ViewHolder = view?.tag as ViewHolder
        if (data == null) {
            Timber.e("MessageListAdapter Message is null !")
            return
        }
        var tag: ProviderTag?
        var provider = ImMessageHelper.getInstance().getMessageTemplate(data.content::class.java)
        if (provider == null) {
            provider = ImMessageHelper.getInstance().getMessageTemplate(UnknownMessage::class.java)
            tag = ImMessageHelper.getInstance().getMessageProviderTag(UnknownMessage::class.java)
        } else {
            tag = ImMessageHelper.getInstance().getMessageProviderTag(data.content::class.java)
        }
        if (provider == null) {
            Timber.e("MessageListAdapter provider is null !")
            return
        }
        if (data.content::class.java == ChatGroupSystemMessage::class.java) {
            val s = data.content as ChatGroupSystemMessage
            val selfUUID = GlobalContext.get().get<AccountInteractor>().curUuid
            if (s.toAccount != null && s.toAccount!!.isNotEmpty() && !s.toAccount!!.contains(
                    selfUUID
                )
            ) {
                // 如果有指定人选，但是不是我，那这里就直接返回了
                // 设置 holder.rootLayout 的 visible 后, ListView 还是会让当前 Item 占用空间
                // 并且设置定高度为 0 也会让, ListView 还是会让当前 Item 占用空间
                // 所以将 holder.rootLayout 的高度设置为 1 像素
                // 如果偏移1像素不可接受的话, 可以考虑在 item 的布局外套个 FrameLayout
                holder.rootLayout?.setLayoutParams(
                    AbsListView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        1
                    )
                )
                return
            }
        }
        val oldLayoutParams = holder.rootLayout?.layoutParams
        if (oldLayoutParams?.height == 1) {
            oldLayoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        var messageView: View?
        try {
            messageView = holder.contentView?.inflate(provider)
        } catch (var14: Exception) {
            Timber.e("MessageListAdapter bindView contentView inflate error %s", var14)
            provider = ImMessageHelper.getInstance().getMessageTemplate(UnknownMessage::class.java)
            tag = ImMessageHelper.getInstance().getMessageProviderTag(UnknownMessage::class.java)
            messageView = holder.contentView?.inflate(provider)
        }
        provider.bindView(messageView, position, data, messageClickListener)
        if (tag == null) {
            return
        }
        updateView(view, tag, holder, data, position)

        onViewBounded(holder, position, data)
    }

    fun updateView(
        view: View, tag: ProviderTag, holder: ViewHolder, data: UIMessage?, position: Int
    ) {
        if (tag.hide) {
            holder.contentView?.visibility = View.GONE
            holder.time?.visibility = View.GONE
            holder.nameView?.visibility = View.GONE
            holder.leftHeadView?.visibility = View.GONE
            holder.rightHeadView?.visibility = View.GONE
        } else {
            holder.contentView?.visibility = View.VISIBLE
            holder.time?.visibility = View.VISIBLE
            holder.rcTitleLayout?.visibility = View.VISIBLE
        }
        if (data != null) {
            setLayoutStatus(holder, tag, data)
        }
        if (data != null) {
            ThreadHelper.runOnUiThreadCatching {
                setUserIcon(holder, data)
            }
        }
        bindViewClickEvent(view, tag, holder, position, data)
        val onAvatarLongClick = View.OnLongClickListener { onAvatarLongClick(data?.senderUserId) }
        holder.rightHeadView?.setOnLongClickListener(onAvatarLongClick)
        holder.leftHeadView?.setOnLongClickListener(onAvatarLongClick)
        if (data != null) {
            holder.time?.text =
                IMDateUtils.getConversationFormatDate(data.message.sentTime, view.context)
            if (data.messageDirection === Message.MessageDirection.SEND) {
                holder.time?.gravity = Gravity.RIGHT
            } else {
                holder.time?.gravity = Gravity.LEFT
            }
        }
        if (data != null && data.messageDirection === Message.MessageDirection.SEND) {
            holder.rcTitleLayout?.gravity = Gravity.RIGHT
        } else {
            holder.rcTitleLayout?.gravity = Gravity.LEFT
        }
        if (data != null && data.conversationType != Conversation.ConversationType.PRIVATE && tag.showSenderName) {
            setNickNameAndAdminTag(holder, data)
        }
        if (tag.messageContent.simpleName == UnknownMessage::class.java.simpleName || tag.centerInHorizontal) {
            holder.time?.visibility = View.GONE
            holder.nameView?.visibility = View.GONE
            holder.rcTitleLayout?.visibility = View.GONE
            val frameLayoutParam =
                holder.flContentLayout?.layoutParams as ViewGroup.MarginLayoutParams
            frameLayoutParam.topMargin = 0
            holder.flContentLayout?.layoutParams = frameLayoutParam
        } else {
            val frameLayoutParam =
                holder.flContentLayout?.layoutParams as ViewGroup.MarginLayoutParams
            frameLayoutParam.topMargin = 24.dp
            holder.flContentLayout?.layoutParams = frameLayoutParam
        }
    }

    private fun setNickNameAndAdminTag(holder: ViewHolder, data: UIMessage) {
        holder.nameView?.visibility = View.VISIBLE
        // 优先从本地读取名字, 因为本地的数据更准确
        holder.nameView?.text = getNickname(data.senderUserId ?: "")
            ?: data.messageSenderName() ?: "unknown"

        val role = getRole(data.senderUserId ?: "")
        when (role) {
            Role.GROUP_OWNER -> {
                holder.rcTagOwner?.visible(true)
                holder.rcTagAdmin?.visible(false)
            }

            Role.GROUP_ADMIN -> {
                holder.rcTagOwner?.visible(false)
                holder.rcTagAdmin?.visible(true)
            }

            else -> {
                holder.rcTagOwner?.visible(false)
                holder.rcTagAdmin?.visible(false)
            }
        }
    }

    enum class Role {
        /**
         * 群主
         */
        GROUP_OWNER,

        /**
         * 群管理员
         */
        GROUP_ADMIN,

        /**
         * 群成员
         */
        GROUP_MEMBER,

        /**
         * 私聊
         */
        PRIVATE_CHAT,
    }

    open fun getRole(userId: String): Role {
        return Role.PRIVATE_CHAT
    }

    open fun getNickname(userId: String): String? {
        return null
    }

    open fun onAvatarLongClick(senderId: String?): Boolean {
        return false
    }


    class ViewHolder : MessageListAdapter.ViewHolder() {

        var flContentLayout: FrameLayout? = null
        var rcTitleLayout: LinearLayout? = null
        var rcTagOwner: TagTextView? = null
        var rcTagAdmin: TagTextView? = null
        var rootLayout: View? = null
    }
}