package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.ColorInt
import androidx.core.graphics.toRectF

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/10/11
 * desc   :
 * </pre>
 */


class MaskingLayout : FrameLayout {

    constructor(context: Context, @ColorInt backgroundColor: Int) : super(context) {
        setBackgroundColor(backgroundColor)
        initView()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initView()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes) {
        initView()
    }

    private lateinit var background: MaskingDrawable

    private fun initView() {
        background = MaskingDrawable(getBackground())
        setBackground(background)
    }

    fun setClipArea(view: View, rx: Float? = null, ry: Float? = null) {
        setClipArea(view.left, view.top, view.right, view.bottom, rx, ry)
    }

    fun setClipArea(left: Int, top: Int, right: Int, bottom: Int, rx: Float? = null, ry: Float? = null) {
        val path = Path()
        if (rx != null || ry != null) {
            path.addRoundRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), rx ?: 0F, ry ?: 0F, Path.Direction.CW)
        } else {
            path.addRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), Path.Direction.CW)
        }
        background.applyPath(path)
        invalidate()
    }

    fun setClipArea(rect: Rect, rx: Float? = null, ry: Float? = null) {
        val path = Path()
        if (rx != null || ry != null) {
            path.addRoundRect(rect.toRectF(), rx ?: 0F, ry ?: 0F, Path.Direction.CW)
        } else {
            path.addRect(rect.toRectF(), Path.Direction.CW)
        }
    }
}