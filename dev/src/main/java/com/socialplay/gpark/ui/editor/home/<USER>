package com.socialplay.gpark.ui.editor.home

import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import com.socialplay.gpark.util.extension.dp

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/03/08
 *     desc   :
 */
class FullscreenAvatarLoadingLayout @JvmOverloads constructor(
    context: Context,
    attrSet: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AvatarLoadingLayout(context, attrSet, defStyleAttr) {

    init {
        adjustAvatar()
    }

    private fun adjustAvatar() {
        val isPortrait = resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT

        if (isPortrait) {
            binding.ivAvatar.updateLayoutParams<ViewGroup.LayoutParams> {
                height = 442.dp
            }

            binding.pbLoadingProgress.updateLayoutParams<MarginLayoutParams> {
                bottomMargin = 56.dp
            }
        }else{
            binding.ivAvatar.updateLayoutParams<ViewGroup.LayoutParams> {
                height = 0
            }

            binding.pbLoadingProgress.updateLayoutParams<MarginLayoutParams> {
                bottomMargin = 10.dp
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        adjustAvatar()
    }
}