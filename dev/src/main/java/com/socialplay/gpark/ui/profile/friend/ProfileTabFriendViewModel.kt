package com.socialplay.gpark.ui.profile.friend

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.profile.friend.FriendEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flatMapLatest

/**
 * created by liyanfeng on 2022/7/25 4:32 下午
 * @describe:
 */
class ProfileTabFriendViewModel(private val repository: IMetaRepository) : ViewModel() {



}