package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.widget.ScrollView
import com.socialplay.gpark.R

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/11
 *     desc   : 可指定滚动Target焦点的ScrollView,用于实现点击EditText时ScrollView自动滚动到最底部
 */
class FocusableScrollView : ScrollView {

    private val focusViewId: Int

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.FocusableScrollView)
        focusViewId = typedArray.getResourceId(R.styleable.FocusableScrollView_focusAt, NO_ID)
        typedArray.recycle()
    }

    override fun computeScrollDeltaToGetChildRectOnScreen(rect: Rect): Int {
        if (focusViewId != NO_ID && childCount > 0) {
            val focusView = findViewById<View>(focusViewId)
            val contentContainerView = getChildAt(0)

            if (focusView != null) {
                if (focusView != contentContainerView) {
                    focusView.getDrawingRect(rect)
                    offsetDescendantRectToMyCoords(focusView, rect)
                } else {
                    return contentContainerView.bottom
                }
            }
        }
        return super.computeScrollDeltaToGetChildRectOnScreen(rect)
    }
}