package com.socialplay.gpark.ui.view;

/**
 * Created by <PERSON><PERSON><PERSON>Wang on 2016/7/16.
 * website: https://github.com/Carbs0126/
 * <p>
 * Thanks to :
 * 1.ReadMoreTextView
 * https://github.com/borjabravo10/ReadMoreTextView
 * 2.TouchableSpan
 * http://stackoverflow.com/questions
 * /20856105/change-the-text-color-of-a-single-clickablespan-when-pressed-without-affecting-o
 * 3.FlatUI
 * http://www.bootcss.com/p/flat-ui/
 */

import static com.socialplay.gpark.ui.post.feed.CommunityFeedFragment.POST_TAG;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Build;
import android.text.DynamicLayout;
import android.text.Layout;
import android.text.Selection;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.socialplay.gpark.R;

import java.lang.reflect.Field;
import java.text.BreakIterator;

import timber.log.Timber;


public class ExpandableTextView extends MetaTextView {
    public static final String TAG = "ExpandableTextView";
    public static final int STATE_SHRINK = 0;
    public static final int STATE_EXPAND = 1;

    private static final String CLASS_NAME_VIEW = "android.view.View";
    private static final String CLASS_NAME_LISTENER_INFO = "android.view.View$ListenerInfo";
    private static final String ELLIPSIS_HINT = "...";
    private static final String GAP_TO_EXPAND_HINT = " ";
    private static final String GAP_TO_SHRINK_HINT = " ";
    private static final String SPACE_STRING = " ";
    private static final int MAX_LINES_ON_SHRINK = 2;
    private static final int TO_EXPAND_HINT_COLOR = 0xFF0D1824;
    private static final int TO_SHRINK_HINT_COLOR = 0xFFE74C3C;
    private static final int TO_EXPAND_HINT_COLOR_BG_PRESSED = 0x55999999;
    private static final int TO_SHRINK_HINT_COLOR_BG_PRESSED = 0x55999999;
    private static final boolean WHOLE_TOGGLE_ENABLE = false;
    private static final boolean TOGGLE_CLICK_ENABLE = false;
    private static final boolean SHOW_TO_EXPAND_HINT = true;
    private static final boolean SHOW_TO_SHRINK_HINT = true;

    private String mEllipsisHint;
    private String mToExpandHint = "";
    private String mToShrinkHint = "";
    private float mToExpandHintOffset = 0F;
    private Boolean enableHintExtendClick = false;
    private Boolean mToShrinkHintBold = false;
    private Boolean mToExpandHintBold = false;
    private String mGapToExpandHint = GAP_TO_EXPAND_HINT;
    private String mGapToShrinkHint = GAP_TO_SHRINK_HINT;
    private boolean mWholeToggle = WHOLE_TOGGLE_ENABLE;
    private boolean mEnableToggleClick = TOGGLE_CLICK_ENABLE;
    private boolean mShowToExpandHint = SHOW_TO_EXPAND_HINT;
    private boolean mShowToShrinkHint = SHOW_TO_SHRINK_HINT;
    private int mMaxLinesOnShrink = MAX_LINES_ON_SHRINK;
    private int mToExpandHintColor = TO_EXPAND_HINT_COLOR;
    private int mToShrinkHintColor = TO_SHRINK_HINT_COLOR;
    private int mToExpandHintColorBgPressed = TO_EXPAND_HINT_COLOR_BG_PRESSED;
    private int mToShrinkHintColorBgPressed = TO_SHRINK_HINT_COLOR_BG_PRESSED;
    private int mCurrState = STATE_SHRINK;

    //  used to add to the tail of modified text, the "shrink" and "expand" text
    private TouchableSpan mTouchableSpan;
    private BufferType mBufferType = BufferType.NORMAL;
    private TextPaint mTextPaint;
    private Layout mLayout;
    private int mTextLineCount = -1;
    private int mLayoutWidth = 0;
    private int mFutureTextViewWidth = 0;

    //  the original text of this view
    private CharSequence mOrigText;

    //  used to judge if the listener of corresponding to the onclick event of ExpandableTextView
    //  is specifically for inner toggle
    private ExpandableClickListener mExpandableClickListener;
    @Nullable
    private OnExpandListener mOnExpandListener;
    //是否可以收起, default is true
    private boolean enableShrink = true;
    //是否点击Span特殊文字，true：不回调onClickListener
//    @Setter
    private boolean linkClickSpan;

    @Nullable
    private OnShrinkingCallback onShrinkingCallback;

    public ExpandableTextView(Context context) {
        super(context);
        init();
    }

    public ExpandableTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttr(context, attrs);
        init();
    }

    public ExpandableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttr(context, attrs);
        init();
    }

    private void initAttr(Context context, AttributeSet attrs) {
        mToExpandHint = context.getString(R.string.more_cap);
        mToShrinkHint = context.getString(R.string.fold_cap);
        if (attrs == null) {
            return;
        }
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ExpandableTextView);
        if (a == null) {
            return;
        }
        int n = a.getIndexCount();
        for (int i = 0; i < n; i++) {
            int attr = a.getIndex(i);
            if (attr == R.styleable.ExpandableTextView_etv_MaxLinesOnShrink) {
                mMaxLinesOnShrink = a.getInteger(attr, MAX_LINES_ON_SHRINK);
            } else if (attr == R.styleable.ExpandableTextView_etv_EllipsisHint) {
                mEllipsisHint = a.getString(attr);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHint) {
                mToExpandHint = a.getString(attr);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToShrinkHint) {
                mToShrinkHint = a.getString(attr);
            }  else if (attr == R.styleable.ExpandableTextView_etv_ToShrinkHintBold) {
                mToShrinkHintBold = a.getBoolean(attr, false);
            }  else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHintBold) {
                mToExpandHintBold = a.getBoolean(attr, false);
            } else if (attr == R.styleable.ExpandableTextView_etv_WholeToggle) {
                mWholeToggle = a.getBoolean(attr, WHOLE_TOGGLE_ENABLE);
            }  else if (attr == R.styleable.ExpandableTextView_etv_EnableToggleClick) {
                mEnableToggleClick = a.getBoolean(attr, TOGGLE_CLICK_ENABLE);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHintShow) {
                mShowToExpandHint = a.getBoolean(attr, SHOW_TO_EXPAND_HINT);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToShrinkHintShow) {
                mShowToShrinkHint = a.getBoolean(attr, SHOW_TO_SHRINK_HINT);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHintColor) {
                mToExpandHintColor = a.getInteger(attr, TO_EXPAND_HINT_COLOR);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToShrinkHintColor) {
                mToShrinkHintColor = a.getInteger(attr, TO_SHRINK_HINT_COLOR);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHintColorBgPressed) {
                mToExpandHintColorBgPressed = a.getInteger(attr, TO_EXPAND_HINT_COLOR_BG_PRESSED);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToShrinkHintColorBgPressed) {
                mToShrinkHintColorBgPressed = a.getInteger(attr, TO_SHRINK_HINT_COLOR_BG_PRESSED);
            } else if (attr == R.styleable.ExpandableTextView_etv_InitState) {
                mCurrState = a.getInteger(attr, STATE_SHRINK);
            } else if (attr == R.styleable.ExpandableTextView_etv_GapToExpandHint) {
                mGapToExpandHint = a.getString(attr);
            } else if (attr == R.styleable.ExpandableTextView_etv_GapToShrinkHint) {
                mGapToShrinkHint = a.getString(attr);
            } else if (attr == R.styleable.ExpandableTextView_etv_ToExpandHintOffset) {
                mToExpandHintOffset = a.getDimension(attr, 0F);
            } else if (attr == R.styleable.ExpandableTextView_etv_ExtendClickScope) {
                enableHintExtendClick = a.getBoolean(attr, false);
            }
        }
        a.recycle();
    }

    private void init() {
        mTouchableSpan = new TouchableSpan();
        setMovementMethod(new LinkTouchMovementMethod());
        if (TextUtils.isEmpty(mEllipsisHint)) {
            mEllipsisHint = ELLIPSIS_HINT;
        }
        if (mWholeToggle) {
            mExpandableClickListener = new ExpandableClickListener();
            setOnClickListener(mExpandableClickListener);
        }
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                ViewTreeObserver obs = getViewTreeObserver();
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    obs.removeOnGlobalLayoutListener(this);
                } else {
                    obs.removeGlobalOnLayoutListener(this);
                }
                setTextInternal(getNewTextByConfig(), mBufferType);
            }
        });
    }

    public void setCurrState(int state) {
        if(this.mCurrState == state){
            return;
        }

        this.mCurrState = state;

        if (mOnExpandListener != null) {
            if (state == STATE_EXPAND) {
                mOnExpandListener.onExpand(this);
            } else {
                mOnExpandListener.onShrink(this);
            }
        }

        if(isLaidOut()){
            setTextInternal(getNewTextByConfig(), mBufferType);
        }else{
            getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    ViewTreeObserver obs = getViewTreeObserver();
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                        obs.removeOnGlobalLayoutListener(this);
                    } else {
                        obs.removeGlobalOnLayoutListener(this);
                    }
                    setTextInternal(getNewTextByConfig(), mBufferType);
                }
            });
        }
    }

    /**
     * used in ListView or RecyclerView to update ExpandableTextView
     * @param text
     *          original text
     * @param futureTextViewWidth
     *          the width of ExpandableTextView in px unit,
     *          used to get max line number of original text by given the width
     * @param expandState
     *          expand or shrink
     */
    public void updateForRecyclerView(CharSequence text, int futureTextViewWidth, int expandState) {
        mFutureTextViewWidth = futureTextViewWidth;
        mCurrState = expandState;
        setText(text);
    }

    public void updateForRecyclerView(CharSequence text, BufferType type, int futureTextViewWidth) {
        mFutureTextViewWidth = futureTextViewWidth;
        setText(text, type);
    }

    public void updateForRecyclerView(CharSequence text, int futureTextViewWidth) {
        mFutureTextViewWidth = futureTextViewWidth;
        setText(text);
    }

    /**
     * get the current state of ExpandableTextView
     * @return
     *      STATE_SHRINK if in shrink state
     *      STATE_EXPAND if in expand state
     */
    public int getExpandState() {
        return mCurrState;
    }

    /**
     * refresh and get a will-be-displayed text by current configuration
     * @return
     *      get a will-be-displayed text
     */
    private CharSequence getNewTextByConfig() {
        if (TextUtils.isEmpty(mOrigText)) {
            return mOrigText;
        }

        mLayout = getLayout();
        if (mLayout != null) {
            mLayoutWidth = mLayout.getWidth();
        }

        if (mLayoutWidth <= 0) {
            if (getWidth() == 0) {
                if (mFutureTextViewWidth == 0) {
                    return mOrigText;
                } else {
                    mLayoutWidth = mFutureTextViewWidth - getPaddingLeft() - getPaddingRight();
                }
            } else {
                mLayoutWidth = getWidth() - getPaddingLeft() - getPaddingRight();
            }
        }

        mTextPaint = getPaint();

        mTextLineCount = -1;
        switch (mCurrState) {
            case STATE_SHRINK: {
                mLayout = new DynamicLayout(mOrigText, mTextPaint, Math.max(mLayoutWidth, 0), Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, false);
                mTextLineCount = mLayout.getLineCount();

                if (mTextLineCount <= mMaxLinesOnShrink) {
                    // 防止点击spannable后面空白区域也会触发link click，文本最后加了一个空格
                    if (mLayout.getLineWidth(mTextLineCount - 1) + mTextPaint.measureText(SPACE_STRING) + 0.5 < Math.max(mLayoutWidth, 0)) {
                        return new SpannableStringBuilder(mOrigText).append(SPACE_STRING);
                    }
                    return mOrigText;
                }
                int indexEnd = getValidLayout().getLineEnd(mMaxLinesOnShrink - 1);
                int indexStart = getValidLayout().getLineStart(mMaxLinesOnShrink - 1);
                // 没搞懂为什么后面要减去hint的长度
                int indexEndTrimmed = indexEnd
                        - getLengthOfString(mEllipsisHint)
                        - (mShowToExpandHint ? getLengthOfString(mToExpandHint) + getLengthOfString(mGapToExpandHint) : 0);

                if (indexEndTrimmed <= indexStart) {
                    indexEndTrimmed = indexEnd;
                }

                int remainWidth = getValidLayout().getWidth() -
                        (int) (mTextPaint.measureText(mOrigText.subSequence(indexStart, indexEndTrimmed).toString()) + 0.5);
                float widthTailReplaced = mTextPaint.measureText(getContentOfString(mEllipsisHint)
                        + (mShowToExpandHint ? (getContentOfString(mToExpandHint) + getContentOfString(mGapToExpandHint)) : "")) + mToExpandHintOffset;

                int indexEndTrimmedRevised = indexEndTrimmed;
                if (remainWidth > widthTailReplaced) {
                    // LIBO 此处代码是为了让hint顶到一排的最后，并且会忽略换行符（导致收缩行数多于设置的值），感觉目前需求中不需要顶到最后，先注释掉
                    // 要解决的话思路应该是遇到换行符就break吧
                    int extraOffset = 0;
                    int extraWidth = 0;
                    while (remainWidth > widthTailReplaced + extraWidth) {
                        extraOffset++;
                        if (indexEndTrimmed + extraOffset <= mOrigText.length()) {
                            CharSequence nextWord = mOrigText.subSequence(indexEndTrimmed, indexEndTrimmed + extraOffset);
                            if (nextWord.toString().contains("\n")) {
                                extraOffset--;
                                break;
                            } else {
                                extraWidth = (int) (mTextPaint.measureText(nextWord.toString()) + 0.5);
                            }
                        } else {
                            break;
                        }
                    }
                    indexEndTrimmedRevised += extraOffset - 1;
                } else {
                    int extraOffset = 0;
                    int extraWidth = 0;
                    while (remainWidth + extraWidth < widthTailReplaced) {
                        extraOffset--;
                        if (indexEndTrimmed + extraOffset > indexStart) {
                            extraWidth = (int) (mTextPaint.measureText(mOrigText.subSequence(indexEndTrimmed + extraOffset, indexEndTrimmed).toString()) + 0.5);
                        } else {
                            break;
                        }
                    }
                    indexEndTrimmedRevised += extraOffset;
                }

                // emoji 表情占两个字节, 如果简单的做字符串切片, 可能会截断 emoji 表情, 导致显示乱码
                // 所以需要先对截断的 index 做修正
                BreakIterator iterator = BreakIterator.getCharacterInstance();
                iterator.setText(mOrigText.toString());
                indexEndTrimmedRevised = iterator.preceding(indexEndTrimmedRevised);

                CharSequence fixText = removeEndLineBreak(mOrigText.subSequence(0, indexEndTrimmedRevised));
                SpannableStringBuilder ssbShrink = new SpannableStringBuilder(fixText)
                        .append(mEllipsisHint);
                if (mShowToExpandHint) {
                    String contentHint = getContentOfString(mToExpandHint);
                    String contentGap = getContentOfString(mGapToExpandHint);
                    ssbShrink.append(contentHint).append(contentGap);
                    int spanStart = ssbShrink.length() - contentHint.length() - contentGap.length();
                    int spanEnd = spanStart + getAfterTrimString(contentHint).length() + getAfterTrimString(contentGap).length();
                    ssbShrink.setSpan(mTouchableSpan, spanStart, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                if (onShrinkingCallback != null) {
                    CharSequence extraContent = onShrinkingCallback.onShrinking(indexEndTrimmedRevised);
                    if (TextUtils.isEmpty(extraContent)) {
                        return ssbShrink;
                    } else {
                        return ssbShrink.append(extraContent);
                    }
                }
                return ssbShrink;
            }
            case STATE_EXPAND: {
                if (!mShowToShrinkHint) {
                    return mOrigText;
                }
                mLayout = new DynamicLayout(mOrigText, mTextPaint, mLayoutWidth, Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, false);
                mTextLineCount = mLayout.getLineCount();

                if (mTextLineCount <= mMaxLinesOnShrink) {
                    return mOrigText;
                }

                SpannableStringBuilder ssbExpand = new SpannableStringBuilder(mOrigText);
                if (enableShrink) {
                    ssbExpand.append(mToShrinkHint).append(mGapToShrinkHint);
                    ssbExpand.setSpan(mTouchableSpan, ssbExpand.length() - getLengthOfString(mToShrinkHint) - 1, ssbExpand.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
                return ssbExpand;

            }
            default:
        }
        return mOrigText;
    }

    private String getAfterTrimString(String originText) {
        return originText.trim().replaceAll("[\\s\\u00A0]+$", "");
    }

    private CharSequence removeEndLineBreak(CharSequence text) {
        int count = 0;
        String str = text.toString();
        while (str.endsWith("\n")) {
            str = str.substring(0, str.length() - 1);
            count++;
        }
        return text.subSequence(0, text.length() - count);
    }

    public void setExpandListener(@Nullable OnExpandListener listener) {
        mOnExpandListener = listener;
    }

    private Layout getValidLayout() {
        return mLayout != null ? mLayout : getLayout();
    }

    public void toggle() {
        switch (mCurrState) {
            case STATE_SHRINK:
                mCurrState = STATE_EXPAND;
                if (mOnExpandListener != null) {
                    mOnExpandListener.onExpand(this);
                }
                break;
            case STATE_EXPAND:
                mCurrState = STATE_SHRINK;
                if (mOnExpandListener != null) {
                    mOnExpandListener.onShrink(this);
                }
                break;
        }
        linkClickSpan = true;
        setTextInternal(getNewTextByConfig(), mBufferType);
    }

    @Override
    public boolean performClick() {
        Timber.tag(POST_TAG).d("performClick %s", linkClickSpan);
        if (linkClickSpan) {
            return true;
        }
        return super.performClick();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        linkClickSpan = false;
        return super.onTouchEvent(event);
    }


    @Override
    public void setText(CharSequence text, BufferType type) {
        mOrigText = text;
        mBufferType = type;
        setTextInternal(getNewTextByConfig(), type);
    }

    private void setTextInternal(CharSequence text, BufferType type) {
        super.setText(text, type);
    }

    private int getLengthOfString(String string) {
        if (string == null)
            return 0;
        return string.length();
    }

    private String getContentOfString(String string) {
        if (string == null)
            return "";
        return string;
    }

    public interface OnExpandListener {
        void onExpand(@NonNull ExpandableTextView view);

        void onShrink(@NonNull ExpandableTextView view);
    }

    public interface OnShrinkingCallback {
        @Nullable
        CharSequence onShrinking(int remainStart);
    }

    private class ExpandableClickListener implements OnClickListener {
        @Override
        public void onClick(View view) {
            toggle();
        }
    }

    public OnClickListener getOnClickListener(View view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            return getOnClickListenerV14(view);
        } else {
            return getOnClickListenerV(view);
        }
    }

    private OnClickListener getOnClickListenerV(View view) {
        OnClickListener retrievedListener = null;
        try {
            Field field = Class.forName(CLASS_NAME_VIEW).getDeclaredField("mOnClickListener");
            field.setAccessible(true);
            retrievedListener = (OnClickListener) field.get(view);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return retrievedListener;
    }

    private OnClickListener getOnClickListenerV14(View view) {
        OnClickListener retrievedListener = null;
        try {
            Field listenerField = Class.forName(CLASS_NAME_VIEW).getDeclaredField("mListenerInfo");
            Object listenerInfo = null;

            if (listenerField != null) {
                listenerField.setAccessible(true);
                listenerInfo = listenerField.get(view);
            }

            Field clickListenerField = Class.forName(CLASS_NAME_LISTENER_INFO).getDeclaredField("mOnClickListener");

            if (clickListenerField != null && listenerInfo != null) {
                clickListenerField.setAccessible(true);
                retrievedListener = (OnClickListener) clickListenerField.get(listenerInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return retrievedListener;
    }


    /**
     * Copy from:
     *  http://stackoverflow.com/questions/20856105/change-the-text-color-of-a-single-clickablespan-when-pressed-without-affecting-o
     * By:
     *  Steven Meliopoulos
     */
    private class TouchableSpan extends ClickableSpan {
        private boolean mIsPressed;

        public void setPressed(boolean isSelected) {
            mIsPressed = isSelected;
        }

        @Override
        public void onClick(View widget) {
            if (hasOnClickListeners()
                    && (getOnClickListener(ExpandableTextView.this) instanceof ExpandableClickListener)) {
                Log.d(ExpandableTextView.TAG, "onClick empty");
            } else if (!mEnableToggleClick) {
                Log.d(ExpandableTextView.TAG, "onClick !mEnableToggleClick");
            } else {
                toggle();
                Log.d(ExpandableTextView.TAG, "toggle");
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            switch (mCurrState) {
                case STATE_SHRINK:
                    ds.setColor(mToExpandHintColor);
                    ds.bgColor = mIsPressed ? mToExpandHintColorBgPressed : 0;
                    ds.setFakeBoldText(mToExpandHintBold);
                    break;
                case STATE_EXPAND:
                    ds.setColor(mToShrinkHintColor);
                    ds.bgColor = mIsPressed ? mToShrinkHintColorBgPressed : 0;
                    ds.setFakeBoldText(mToShrinkHintBold);
                    break;
            }
            ds.setUnderlineText(false);
        }
    }

    /**
     * Copy from:
     *  http://stackoverflow.com/questions
     *  /20856105/change-the-text-color-of-a-single-clickablespan-when-pressed-without-affecting-o
     * By:
     *  Steven Meliopoulos
     */
    public class LinkTouchMovementMethod extends LinkMovementMethod {
        private ClickableSpan mPressedSpan;
        // 是否点击了hint区域后面的区域
        private boolean isOverClick;

        @Override
        public boolean onTouchEvent(TextView textView, Spannable spannable, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                PressSpanResult pairResult = getPressedSpan(textView, spannable, event);
                isOverClick = pairResult.isOverLength;
                mPressedSpan = pairResult.touchedSpan;
                if (mPressedSpan instanceof TouchableSpan) {
                    ((TouchableSpan) mPressedSpan).setPressed(true);
                    Selection.setSelection(spannable, spannable.getSpanStart(mPressedSpan), spannable.getSpanEnd(mPressedSpan));
                }
            } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                PressSpanResult pairResult = getPressedSpan(textView, spannable, event);
                isOverClick = pairResult.isOverLength;
                ClickableSpan touchedSpan = pairResult.touchedSpan;
                if (mPressedSpan != null && touchedSpan != mPressedSpan) {
                    if (mPressedSpan instanceof TouchableSpan) {
                        ((TouchableSpan) mPressedSpan).setPressed(false);
                    }
                    mPressedSpan = null;
                    Selection.removeSelection(spannable);
                }
            } else if (event.getAction() == MotionEvent.ACTION_UP) {
                if (!enableHintExtendClick && isOverClick) {
                    linkClickSpan = true;
                }
                if (mPressedSpan != null) {
                    linkClickSpan = true;
                    if (mPressedSpan instanceof TouchableSpan) {
                        ((TouchableSpan) mPressedSpan).setPressed(false);
                    }
                    super.onTouchEvent(textView, spannable, event);
                }
                mPressedSpan = null;
                isOverClick = false;
                Selection.removeSelection(spannable);
            } else {
                mPressedSpan = null;
                isOverClick = false;
                Selection.removeSelection(spannable);
            }
            return true;
        }

        private class PressSpanResult {
            ClickableSpan touchedSpan;
            boolean isOverLength;

            public PressSpanResult(ClickableSpan touchedSpan, boolean isOverLength) {
                this.touchedSpan = touchedSpan;
                this.isOverLength = isOverLength;
            }
        }

        private PressSpanResult getPressedSpan(TextView textView, Spannable spannable, MotionEvent event) {

            int x = (int) event.getX();
            int y = (int) event.getY();

            x -= textView.getTotalPaddingLeft();
            y -= textView.getTotalPaddingTop();

            x += textView.getScrollX();
            y += textView.getScrollY();

            Layout layout = textView.getLayout();
            int line = layout.getLineForVertical(y);
            int off = layout.getOffsetForHorizontal(line, x);

            ClickableSpan[] link = spannable.getSpans(off, off, ClickableSpan.class);
            ClickableSpan touchedSpan = null;
            // 是否点击到可点击区域之后的区域
            boolean isOverLength = off >= layout.getText().length();
            // off < layout.getText().length() 不加的话，点击hint后的区域也可以相应 展开/收起事件
            if (link.length > 0 && (enableHintExtendClick || !isOverLength)) {
                touchedSpan = link[0];
            }
            if (touchedSpan instanceof TouchableSpan && !mEnableToggleClick) {
                touchedSpan = null;
            }
            return new PressSpanResult(touchedSpan, isOverLength);
        }
    }


    public void setShrinkEnabled(boolean enable) {
        enableShrink = enable;
    }

    public boolean hasExpandText() {
        return mTextLineCount > mMaxLinesOnShrink;
    }

    public void setOnShrinkingCallback(@Nullable OnShrinkingCallback onShrinkingCallback) {
        this.onShrinkingCallback = onShrinkingCallback;
    }
}