package com.socialplay.gpark.ui.videofeed.comment

import android.animation.Animator
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.StringRes
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.videofeed.common.Comment
import com.socialplay.gpark.data.model.videofeed.common.CommentArgs
import com.socialplay.gpark.data.model.videofeed.common.Reply
import com.socialplay.gpark.databinding.DialogVideoFeedCommentBinding
import com.socialplay.gpark.databinding.ViewVideoFeedCommentPopupMoreBinding
import com.socialplay.gpark.databinding.ViewVideoFeedCommentPopupSortBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.videofeed.VideoFeedViewModel
import com.socialplay.gpark.ui.videofeed.common.CommentListAdapter
import com.socialplay.gpark.ui.videofeed.common.CommentViewModel
import com.socialplay.gpark.ui.videofeed.common.CommentViewModelState
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.SpaceItemDecoration
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.OverscrollLinearLayoutManager
import com.socialplay.gpark.util.ReverseableSmoothScroller
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addBottomSheetCallback
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.reflect.KProperty1

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/12/28
 *     desc   :
 */
class VideoFeedCommentDialogFragment : BaseBottomSheetDialogFragment() {

    companion object {
        fun show(
            fragmentManager: FragmentManager,
            reqId: String,
            videoId: String,
            videoCommentCount: Long,
            resIdBean: ResIdBean
        ) {
            val fragment = VideoFeedCommentDialogFragment().apply {
                arguments = CommentArgs(reqId, videoId, videoCommentCount, resIdBean).asMavericksArgs()
            }
            fragment.show(fragmentManager, "VideoFeedComment#${videoId}")
        }
    }

    override fun isStatusBarTextDark() = false

    override fun getStyle(): Int = R.style.VideoFeedCommentBottomSheetDialogStyle

    private val args: CommentArgs by args()

    private val commentViewModel: CommentViewModel by fragmentViewModel()

    override val binding by viewBinding(DialogVideoFeedCommentBinding::inflate)

    private val videoViewModel by parentFragmentViewModel(VideoFeedViewModel::class)

    private var positionSmoothScroller: ReverseableSmoothScroller? = null

    private val adapter by lazy {
        CommentListAdapter(
            glide = Glide.with(this),
            expandMoreRepliesListener = ::expandReplies,
            collapseMoreRepliesListener = ::collapseReplies,
            commentAuthorClickListener = ::onCommentAuthorClicked,
            commentLikeListener = ::onCommentLikeClicked,
            commentClickListener = ::onCommentClicked,
            commentTextExpandClickListener = ::onCommentExpandClicked,
            commentMoreClickListener = ::onCommentMoreClicked,
            commentLongClickListener = ::onCommentLongClicked,
            replyAuthorClickListener = ::onReplyAuthorClicked,
            replyLikeClickListener = ::onReplyLikeClicked,
            replyClickListener = ::onReplyClicked,
            replyMoreClickListener = ::onReplyMoreClicked,
            replyTextExpandClickListener = ::onReplyExpandClicked,
            replyLongClickListener = ::onReplyLongClicked
        )
    }

    override fun getPageName() = PageNameConstants.DIALOG_VIDEO_FEED_COMMENT

    override fun dimAmount() = 0.01F

    override var heightPercent: Float = 0.67F

    override fun needCountTime(): Boolean = true


    override fun init() {}


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.VideoFeedCommentBottomSheetDialogStyle)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): BottomSheetDialog {
        return super.onCreateDialog(savedInstanceState).apply {
            // 默认展开，防止有些手机内容显示不下导致的输入框被挡住了
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
            dismissWithAnimation = true
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.root.setBackgroundResource(R.drawable.bg_corner_top_20_white)

        dialog?.behavior?.addBottomSheetCallback(viewLifecycleOwner, object : BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {}

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                refreshVideoViewport()
            }
        })

        binding.rvComment.layoutManager = OverscrollLinearLayoutManager(requireContext())
        positionSmoothScroller = ReverseableSmoothScroller(binding.rvComment)
        binding.rvComment.adapter = adapter

        binding.rvComment.addItemDecoration(
            SpaceItemDecoration(
                0,
                14.dp,
                showFirst = false,
                showLast = true
            )
        )

        setupLoadMoreView()
        setupLoadMore()

        // 修复加载更多状态不对的问题，直接禁用动画也可以解决
        // 测试下来应该是动画导致创建了多个LoadMoreViewHolder
        // 但是BaseQuickAdapter的LoadMoreModule处理不了这种情况
        binding.rvComment.itemAnimator = object : DefaultItemAnimator() {
            override fun canReuseUpdatedViewHolder(viewHolder: RecyclerView.ViewHolder): Boolean {
                if (viewHolder.itemViewType == BaseQuickAdapter.LOAD_MORE_VIEW) {
                    return true
                }
                return super.canReuseUpdatedViewHolder(viewHolder)
            }

            override fun canReuseUpdatedViewHolder(
                viewHolder: RecyclerView.ViewHolder,
                payloads: MutableList<Any>
            ): Boolean {
                if (viewHolder.itemViewType == BaseQuickAdapter.LOAD_MORE_VIEW) {
                    return true
                }
                return super.canReuseUpdatedViewHolder(viewHolder, payloads)
            }
        }

        commentViewModel.registerToast(CommentViewModelState::toastMsg)
        commentViewModel.registerAsyncErrorToast(CommentViewModelState::refresh)

        commentViewModel.setupRefreshLoading(CommentViewModelState::refresh, binding.lv, emptyMsgId = R.string.no_comment) {
            viewLifecycleOwner.lifecycleScope.launch {
                commentViewModel.refreshComment(overrideCommentCnt = true)
            }
        }

        commentViewModel.onEach(CommentViewModelState::scrollToTop, deliveryMode = uniqueOnly()) {
            if (it) {
                viewLifecycleOwner.lifecycleScope.launch {
                    delay(100)
                    binding.rvComment.scrollToPosition(0)
                    commentViewModel.setScrollToTop(false)
                }
            }
        }

        commentViewModel.onEach(CommentViewModelState::items) {
            adapter.submitData(it.toMutableList())
        }

        commentViewModel.onAsync(CommentViewModelState::refresh, onFail = { _ ->
            binding.lv.showError()
        }, onSuccess = {
            binding.lv.hide()
        })

        commentViewModel.onEach(CommentViewModelState::articleDetail) {
            val commentCount = it?.commentCount ?: 0
            val commentCountStr = UnitUtil.formatKMCount(commentCount)
            binding.tvCommentCountHang.setTextWithArgs(R.string.comment_count, commentCountStr)

            if (it != null) {
                videoViewModel.setCommentCountLocally(it.postId, it.commentCount)
            }
        }

        commentViewModel.onEach(CommentViewModelState::queryType) {
            val resId = when (it) {
                PostCommentListRequestBody.QUERY_TYPE_LIKE -> R.string.likes_sort
                else -> R.string.recent_reply
            }
            binding.tvCommentSortHang.setText(resId)
        }

        binding.lv.setRetry {
            commentViewModel.refreshComment()
        }

        binding.tvComment.setOnAntiViolenceClickListener {
            showCommentInputDialog()
        }

        binding.tvCommentSortHang.setOnAntiViolenceClickListener {
            showSortPopup(it)
        }
    }

    override fun loadFirstData() {
        super.loadFirstData()
        // 首次加载数据放在进入动画播放完毕后
        playInAnimation { commentViewModel.refreshComment() }
    }

    override fun invalidate() {}


    // 播放进入动画，使用自定义的动画而不是自带的BottomSheetWindowAnimation
    // 为了能够根据动画进度设置视频播放器盒子的位置
    private fun playInAnimation(onEnd: (animator: Animator) -> Unit = {}) {
        val root = binding.root
        root.doOnLayoutSized {
            if (!isBindingAvailable()) return@doOnLayoutSized

            Timber.d("playInAnimation from:${root.measuredHeight} to:${0F}")

            val bottomSheetAnimation = ObjectAnimator.ofFloat(
                root,
                View.TRANSLATION_Y,
                root.measuredHeight.toFloat(),
                0F
            )

            bottomSheetAnimation.addUpdateListener(viewLifecycleOwner) {
                // 延迟到下一动画帧，否则这里动画产生的位移可能还没生效，此时拿到的位置会是错误
                root.postOnAnimation {
                    if (!isBindingAvailable()) return@postOnAnimation
                    refreshVideoViewport()
                }
            }
            bottomSheetAnimation.addListener(viewLifecycleOwner, onEnd = onEnd)

            bottomSheetAnimation.duration = 250
            bottomSheetAnimation.start()
        }
    }

    private fun refreshVideoViewport(default: Int = -1) {
        var bottom = default
        if (isBindingAvailable()) {
            val location = intArrayOf(0, 0)
            binding.root.getLocationOnScreen(location)
            bottom = location[1]
        }
        videoViewModel.positionVideoAtTop(bottom)
    }


    private fun onCommentMoreClicked(
        view: View,
        bindingAdapterPosition: Int,
        comment: Comment
    ) {
        showMenuDialog(view, comment.isSelf, onDelete = {
            ConfirmDialog.Builder(this)
                .content(getString(R.string.delete_confirm, getString(R.string.comment)))
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .confirmCallback {
                    commentViewModel.deleteComment(comment.playerComment.commentId)
                }
                .show()
        }, onReport = {
            MetaRouter.Report.postReport(this, comment.playerComment.commentId, ReportType.PostComment)
        })
    }

    private fun onReplyMoreClicked(
        view: View,
        bindingAdapterPosition: Int,
        reply: Reply
    ) {
        showMenuDialog(view, reply.isSelf, onDelete = {
            ConfirmDialog.Builder(this)
                .content(getString(R.string.delete_confirm, getString(R.string.reply)))
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .confirmCallback {
                    commentViewModel.deleteReply(
                        reply.owner.playerComment.commentId,
                        reply.playerReply.replyId
                    )
                }
                .show()
        }, onReport = {
            MetaRouter.Report.postReport(this, reply.playerReply.replyId, ReportType.PostReply)
        })
    }

    private fun onCommentExpandClicked(bindingAdapterPosition: Int, comment: Comment, isExpand: Boolean) {
        commentViewModel.setCommentTextExpandStatus(comment.playerComment.commentId, isExpand)
    }

    private fun onCommentLongClicked(bindingAdapterPosition: Int, comment: Comment): Boolean {
        return false
    }

    private fun onCommentClicked(bindingAdapterPosition: Int, comment: Comment) {
        showReplyCommentInputDialog(bindingAdapterPosition, comment)
    }

    private fun onCommentLikeClicked(bindingAdapterPosition: Int, comment: Comment) {
        val isLike = !comment.isLiked
        commentViewModel.setCommentLikeStatus(comment.playerComment.commentId, isLike)
        if (isLike) {
            DialogShowManager.triggerLike(this@VideoFeedCommentDialogFragment)
        }
        withState(commentViewModel) {
            Analytics.track(
                EventConstants.EVENT_POST_REPLY_LIKE_CLICK,
                "type" to (if (isLike) 1 else 2),
                "location" to 2,
                "postId" to it.args.postId,
                "show_categoryId" to it.args.resId.getCategoryID(),
            )
        }
    }

    private fun onCommentAuthorClicked(bindingAdapterPosition: Int, comment: Comment) {
        val uid = comment.playerComment.uid
        MetaRouter.Profile.other(this@VideoFeedCommentDialogFragment, uid, "video_feed_comment")
    }

    private fun expandReplies(bindingAdapterPosition: Int, comment: Comment) {
        commentViewModel.expandOrLoadMoreReplies(comment.playerComment.commentId)
    }

    private fun collapseReplies(bindingAdapterPosition: Int, comment: Comment) {
        commentViewModel.collapseReplies(comment.playerComment.commentId)
    }

    private fun showCommentInputDialog() {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
            if (it) {
                viewLifecycleOwner.lifecycleScope.launch {
                    val state = commentViewModel.awaitState()
                    // 必须等到游戏详情页获取成功后才能评论
                    if (state.articleDetail != null) {
                        BasicInputDialog.show(
                            fragment = this@VideoFeedCommentDialogFragment,
                            hint = getString(R.string.post_reply),
                            sendText = getString(R.string.video_feed_comment_send),
                            dimAmount = 0.2f,
                        ) inputShow@{
                            if (it.isNullOrBlank()) return@inputShow

                            Analytics.track(
                                EventConstants.EVENT_POST_REPLY_SEND,
                                EventParamConstants.KEY_TYPE to EventParamConstants.TYPE_COMMENT_COMMENT,
                                EventParamConstants.KEY_LOCATION to EventParamConstants.LOCATION_COMMENT_VIDEO_FEED,
                                "postId" to state.args.postId,
                                "show_categoryId" to state.args.resId.getCategoryID(),
                            )

                            commentViewModel.commentArticle(it)
                        }
                    }
                }
            }
        }
    }

    private fun showReplyCommentInputDialog(bindingAdapterPosition: Int, comment: Comment) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
            if (it) {
                positionSmoothScroller?.start(bindingAdapterPosition, offset = 0, delay = 50)

                BasicInputDialog.show(
                    fragment = this,
                    hint = getString(R.string.replying_at, comment.playerComment.nickname),
                    dimAmount = 0.2f,
                    sendText = getString(R.string.video_feed_comment_send)
                ) inputShow@{

                    positionSmoothScroller?.reverse()

                    if (it.isNullOrBlank()) return@inputShow


                    withState(commentViewModel) {
                        Analytics.track(
                            EventConstants.EVENT_POST_REPLY_SEND,
                            EventParamConstants.KEY_TYPE to EventParamConstants.TYPE_COMMENT_REPLY,
                            EventParamConstants.KEY_LOCATION to EventParamConstants.LOCATION_COMMENT_VIDEO_FEED,
                            "postId" to (it.args.postId ?: ""),
                            "show_categoryId" to it.args.resId.getCategoryID(),
                        )
                    }
                    commentViewModel.replyComment(comment.playerComment.commentId, it)
                }
            }
        }
    }

    private fun onReplyExpandClicked(bindingAdapterPosition: Int, reply: Reply, isExpand: Boolean) {
        commentViewModel.setReplyTextExpandStatus(
            reply.owner.playerComment.commentId,
            reply.playerReply.replyId,
            isExpand
        )
    }

    private fun onReplyLongClicked(bindingAdapterPosition: Int, reply: Reply): Boolean {
        return false
    }

    private fun onReplyAuthorClicked(bindingAdapterPosition: Int, reply: Reply) {
        val uid = reply.playerReply.uid
        MetaRouter.Profile.other(this@VideoFeedCommentDialogFragment, uid, "video_feed_comment")
    }

    private fun onReplyLikeClicked(bindingAdapterPosition: Int, reply: Reply) {
        val isLiked = !reply.isLiked

        commentViewModel.setReplyLikeStatus(
            reply.owner.playerComment.commentId,
            reply.playerReply.replyId,
            isLiked
        )
    }

    private fun onReplyClicked(bindingAdapterPosition: Int, reply: Reply) {
        showReplyReplyInputDialog(bindingAdapterPosition, reply)
    }

    private fun showMenuDialog(
        view: View,
        isSelf: Boolean,
        onDelete: () -> Unit,
        onReport: () -> Unit
    ) {

        val binding = ViewVideoFeedCommentPopupMoreBinding.inflate(layoutInflater)

        binding.tvDelete.visible(isSelf)
        binding.tvReport.visible(!isSelf)

        binding.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )

        val x = -binding.cv.measuredWidth + 10.dp
        val y = -(16).dp

        val popupWindow = PopupWindowCompat(
            binding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }

        popupWindow.showAsDropDownByLocation(view, x, y)

        binding.root.setOnClickListener {
            popupWindow.dismiss()
        }

        binding.tvDelete.setOnClickListener {
            popupWindow.dismiss()
            onDelete()
        }

        binding.tvReport.setOnClickListener {
            popupWindow.dismiss()
            onReport()
        }
    }

    private fun showSortPopup(view: View) {

        val binding = ViewVideoFeedCommentPopupSortBinding.inflate(layoutInflater)

        binding.root.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )

        val x = view.measuredWidth - binding.root.measuredWidth
        val y = -(14).dp

        val popupWindow = PopupWindowCompat(
            binding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }

        popupWindow.showAsDropDownByLocation(view, x, y)

        binding.root.setOnClickListener {
            popupWindow.dismiss()
        }

        binding.tvLikeSort.setOnClickListener {
            commentViewModel.setQueryType(CommentViewModel.QUERY_TYPE_LIKE)
            popupWindow.dismiss()
        }

        binding.tvRecentReply.setOnClickListener {
            commentViewModel.setQueryType(CommentViewModel.QUERY_TYPE_RECENT)
            popupWindow.dismiss()
        }

    }

    private fun showReplyReplyInputDialog(
        bindingAdapterPosition: Int,
        reply: Reply
    ) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
            if (it) {
                positionSmoothScroller?.start(bindingAdapterPosition, offset = 0, delay = 50)

                BasicInputDialog.show(
                    fragment = this,
                    hint = getString(R.string.replying_at, reply.playerReply.nickname),
                    dimAmount = 0.2f,
                    sendText = getString(R.string.video_feed_comment_send)
                ) inputShow@{
                    positionSmoothScroller?.reverse()

                    if (it.isNullOrBlank()) return@inputShow

                    val commentId = reply.owner.playerComment.commentId
                    val item = reply.playerReply

                    commentViewModel.replyReply(
                        commentId = commentId,
                        content = it,
                        repliedId = item.replyId,
                        repliedName = item.nickname,
                        repliedUuid = item.uid
                    )

                    withState(commentViewModel) {
                        Analytics.track(
                            EventConstants.EVENT_POST_REPLY_SEND,
                            EventParamConstants.KEY_TYPE to EventParamConstants.TYPE_COMMENT_REPLY,
                            EventParamConstants.KEY_LOCATION to EventParamConstants.LOCATION_COMMENT_VIDEO_FEED,
                            "postId" to it.args.postId,
                            "show_categoryId" to it.args.resId.getCategoryID(),
                        )
                    }
                }
            }
        }
    }

    private fun CommentViewModel.setupRefreshLoading(
        asyncProp: KProperty1<CommentViewModelState, Async<*>>,
        loadingView: LoadingView,
        @StringRes emptyMsgId: Int = R.string.no_data,
        onRefresh: () -> Unit
    ) {
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            deliveryMode = uniqueOnly(),
            onLoading = {
                val state = this.awaitState()
                if (state.items.isEmpty()) {
                    loadingView.showLoading()
                }
            },
            onFail = { _, _ ->
                val state = this.awaitState()
                if (state.items.isEmpty()) {
                    loadingView.showError()
                }
            },
            onSuccess = {
                adapter.setNewInstance(mutableListOf())

                val state = this.awaitState()
                if (state.items.isEmpty()) {
                    loadingView.showEmpty(getString(emptyMsgId))
                } else {
                    loadingView.hide()
                }
            }
        )
    }

    private fun setupLoadMore() {
        commentViewModel.onAsync(
            CommentViewModelState::loadMore,
            onLoading = {
                adapter.loadMoreModule.loadMoreToLoading()
            }, onFail = { _, _ ->
                adapter.loadMoreModule.loadMoreFail()
            }, onSuccess = {
                if (it.isEnd) {
                    adapter.loadMoreModule.loadMoreEnd()
                } else {
                    adapter.loadMoreModule.loadMoreComplete()
                }
            })
    }

    private fun setupLoadMoreView() {
        adapter.loadMoreModule.apply {
            preLoadNumber = 1
            isEnableLoadMoreIfNotFullPage = false
            loadMoreView = EmptyLoadMoreView()
            setOnLoadMoreListener {
                viewLifecycleOwner.lifecycleScope.launch {
                    if (!NetUtil.isNetworkAvailable()) {
                        adapter.loadMoreModule.loadMoreFail()
                    } else {
                        commentViewModel.loadMoreComment()
                    }
                }
            }
        }
    }
}