package com.socialplay.gpark.ui.base

import android.animation.ValueAnimator
import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AccelerateInterpolator
import androidx.annotation.StyleRes
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.observer.LifecycleObserver
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.onPageCreate
import com.socialplay.gpark.function.apm.onPageDestroyView
import com.socialplay.gpark.function.apm.onPageResume
import com.socialplay.gpark.function.apm.onPageStart
import com.socialplay.gpark.function.apm.onPageViewCreated
import com.socialplay.gpark.function.apm.page.IPageMonitor
import com.socialplay.gpark.function.apm.page.view.ISpeedLayout
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwner
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwnerProvider
import timber.log.Timber
import java.lang.Integer.min

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/11
 * desc   :
 * </pre>
 */


abstract class BaseDialogFragment : AppCompatDialogFragment(), ViewBindingLifecycleOwnerProvider,
    IPageMonitor {

    protected abstract val binding: ViewBinding
    private var viewBindingLifecycleOwner: ViewBindingLifecycleOwner? = null
    private var isLoadFirstData = false
    protected open val destId: Int = 0
    private var hasNavUp = false

    protected open var navColorRes: Int = R.color.transparent
    private var nextNavColor: Int = 0

    protected var glide: RequestManager? = null
        get() {
            if (field == null && enableGlide) {
                field = Glide.with(this)
            }
            return field
        }
    private var enableGlide: Boolean = false

    private var nestFadeStatus: Int = 0
    protected open var willNestDialog: Boolean = false
    protected val nestFading: Boolean get() = nestFadeStatus > 0

    protected var shouldDismiss = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        viewBindingLifecycleOwner = ViewBindingLifecycleOwner()
        return binding.root.apply {
            if (this is ISpeedLayout) {
                setPageConfig(
                    this@BaseDialogFragment::class.java.simpleName,
                    <EMAIL>(),
                    <EMAIL>()
                )
            } else {
                if (PageMonitor.getConfig(this@BaseDialogFragment::class.java.simpleName) != null) {
                    Timber.tag("PageMonitor")
                        .e("${this@BaseDialogFragment::class.java.simpleName} not set page monitor layout!")
                }
            }
        }
    }


    override fun onResume() {
        onPageResume()
        super.onResume()
        setStatusBarTextColor(isStatusBarTextDark())
        dialog?.window?.let {
            it.navColor = nextNavColor
        }
    }

    override fun onStart() {
        onPageStart()
        kotlin.runCatching {
            super.onStart()
            if (shouldDismiss) {
                dismissAllowingStateLoss()
            }
        }.getOrElse {
            dismissAllowingStateLoss()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        onPageCreate()
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_TITLE, getStyle())
        if (tackUsingTime()) {
            LifecycleObserver(this, getFragmentName())
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        onPageViewCreated()
        super.onViewCreated(view, savedInstanceState)
        enableGlide = true
        dialog?.run {
            window?.run {
                setLayout(getWidth(view.context), windowHeight())
                val attr = attributes
                if (isTransparent()) {
                    attr.flags = attr.flags and WindowManager.LayoutParams.FLAG_DIM_BEHIND.inv()
                } else {
                    attr.flags = attr.flags or WindowManager.LayoutParams.FLAG_DIM_BEHIND
                    attr.dimAmount = dimAmount()
                }
                if (isFullScreen() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    attr.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                    decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                }
                attributes = attr
            }
            hideNavigationBar(this)
            setGravity(this)
            val clickOutsideDismiss = isClickOutsideDismiss()
            val backPressedDismiss = isBackPressedDismiss()
            if (backPressedDismiss) {
                setCancelable(true)
            }
            setCanceledOnTouchOutside(clickOutsideDismiss)

            setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0 && event.action == KeyEvent.ACTION_UP) {
                    return@setOnKeyListener <EMAIL>() || !backPressedDismiss
                }
                return@setOnKeyListener false
            }
        }
        init()
        if (!isLoadFirstData) {
            isLoadFirstData = true
            loadFirstData()
        }
        if (nextNavColor == 0) {
            nextNavColor = getColorByRes(navColorRes)
        }
    }

    override fun dismiss() {
        dismissAllowingStateLoss()
    }

    override fun dismissAllowingStateLoss() {
        if (parentFragment?.canShowDialog != true || !willNestDialog || nestFadeStatus > 0) {
            nestFadeStatus = 0
            dismissIfPossible()
        } else if (nestFadeStatus == 0) {
            ValueAnimator.ofFloat(1.0f, 0.0f).apply {
                addUpdateListener(viewLifecycleOwner) {
                    val alpha = it.animatedValue as Float
                    dialog?.window?.attributes?.let {
                        it.alpha = alpha
                        dialog?.window?.attributes = it
                    }
                }
                addListener(viewLifecycleOwner, onEnd = {
                    nestFadeStatus = 0
                    dismissIfPossible()
                    afterNestFaded()
                })
                duration = 200
                interpolator = AccelerateInterpolator()
                start()
            }
            nestFadeStatus = 1
        } else {
            nestFadeStatus = 0
            dismissIfPossible()
        }
    }

    private fun dismissIfPossible() {
        if (isAdded) {
            super.dismissAllowingStateLoss()
        } else {
            kotlin.runCatching {
                super.dismissAllowingStateLoss()
            }.getOrElse {
                shouldDismiss = true
            }
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (!manager.isStateSaved && !isStateSaved) {
            super.show(manager, tag)
        }
    }

    private fun getWidth(context: Context): Int {
        val marginHorizontal = marginHorizontal(context)
        val widthRatio = widthRatio()
        var windowWidth = windowWidth(context)
        if (widthRatio > 0) {
            windowWidth = (widthRatio * ScreenUtil.getScreenWidth(context)).toInt()
        }
        if (marginHorizontal > 0 && (windowWidth > 0 || windowWidth == WindowManager.LayoutParams.MATCH_PARENT)) {
            if (windowWidth == WindowManager.LayoutParams.MATCH_PARENT) {
                windowWidth = ScreenUtil.getScreenWidth(context)
            }
            windowWidth -= marginHorizontal * 2
        }

        val maxWidth = maxWidth()
        if (maxWidth > 0) {
            if (windowWidth == WindowManager.LayoutParams.MATCH_PARENT) {
                windowWidth = min(ScreenUtil.getScreenWidth(context), maxWidth)
            } else if (windowWidth != WindowManager.LayoutParams.WRAP_CONTENT) {
                windowWidth = min(windowWidth, maxWidth)
            }
        }

        return windowWidth
    }

    private fun setGravity(dialog: Dialog) {
        dialog.window?.run {
            val gravity = gravity()
            if (gravity == Gravity.CENTER || gravity == Gravity.BOTTOM || gravity == Gravity.TOP) {
                val att = attributes
                att.gravity = gravity
                this.attributes = att
            }
        }
    }

    private fun hideNavigationBar(dialog: Dialog) {
        dialog.window?.run {
            if (isHideNavigation()) {
                var uiOptions =
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                decorView.systemUiVisibility = uiOptions
            }
        }
    }

    @StyleRes
    open fun getStyle() = R.style.DialogStyle

    open fun isClickOutsideDismiss() = true

    open fun isFullScreen() = false

    open fun isBackPressedDismiss() = true

    open fun isHideNavigation() = false

    open fun isTransparent() = false

    open fun isStatusBarTextDark(): Boolean {
        return true
    }

    open fun setStatusBarTextColor(isDark: Boolean) {
        dialog?.window?.let {
            if (isDark) {
                StatusBarUtil.setLightMode(it)
            } else {
                StatusBarUtil.setDarkMode(it)
            }
        }
    }

    open fun dimAmount() = 0.7F

    open fun widthRatio() = 0.0F

    open fun marginHorizontal(context: Context) = 0

    open fun windowWidth(context: Context) = WindowManager.LayoutParams.MATCH_PARENT

    open fun maxWidth() = 0

    open fun windowHeight() = WindowManager.LayoutParams.WRAP_CONTENT

    open fun gravity() = Gravity.BOTTOM

    open fun afterNestFaded() {}

    abstract fun init()

    abstract fun loadFirstData()
    open fun getFragmentName(): String = ""
    open fun tackUsingTime() = false

    override fun onDetach() {
        super.onDetach()
        isLoadFirstData = false
    }

    override fun onDestroyView() {
        nestFadeStatus = 0
        onPageDestroyView()
        enableGlide = false
        glide = null
        super.onDestroyView()
        viewBindingLifecycleOwner?.onDestroyView()
        viewBindingLifecycleOwner = null
    }

    override fun viewBindingLayoutInflater(): LayoutInflater {
        return layoutInflater
    }

    override fun viewBindingLifecycleOwner(): ViewBindingLifecycleOwner {
        return viewBindingLifecycleOwner ?: error("view not create or destroy")
    }

    fun isBindingAvailable(): Boolean {
        return viewBindingLifecycleOwner != null
    }

    protected fun navigateUp() {
        if (hasNavUp) return
        if (destId != 0) {
            hasNavUp = navigateUp(destId)
            if (hasNavUp) return
        }
        hasNavUp = findNavController().navigateUp()
    }

    open fun onBackPressed(): Boolean = false
}