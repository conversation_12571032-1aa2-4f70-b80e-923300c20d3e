package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.meta.biz.mgs.data.model.MGSMessage
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemMgsTabMessageBinding
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.visible

class MgsTabMessageAdapter : BaseMgsMessageAdapter<ItemMgsTabMessageBinding>(false) {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemMgsTabMessageBinding {
        return ItemMgsTabMessageBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<ItemMgsTabMessageBinding>, item: MGSMessage, position: Int) {
        holder.binding.apply {
            val nickName =
                if (checkInformationMessage(item)) holder.itemView.context.getString(R.string.app_name) else item.mgsMessageExtra?.imUser?.name
            val avatar = if (checkInformationMessage(item))
                ContextCompat.getDrawable(holder.itemView.context, R.mipmap.ic_launcher)
            else item.mgsMessageExtra?.imUser?.portrait
            Glide.with(holder.itemView.context).load(avatar)
                .error(R.drawable.icon_default_avatar).into(ivAvatar)
            tvNickName.text = nickName
            tvContent.text = getContent(holder.itemView.context, item)
            if(checkInformationMessage(item)) {
                root.setBackgroundResource(R.drawable.shape_black_32_corner_12)
            }
        }
    }

}