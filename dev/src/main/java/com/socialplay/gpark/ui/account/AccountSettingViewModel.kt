package com.socialplay.gpark.ui.account

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.base.*
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 * @author: ning.wang
 * @date: 2021-09-28 3:15 下午
 * @desc:
 */
class AccountSettingViewModel(val accountInteractor: AccountInteractor) : ViewModel(){

    private var accountObserver: Observer<MetaUserInfo?>

    private val _accountLiveData: MutableLiveData<MetaUserInfo?> = MutableLiveData()
    val accountLivedata get() = _accountLiveData

    private val _isU13LiveData: MutableLiveData<Boolean> = MutableLiveData()
    val isU13LiveData get() = _isU13LiveData

    val logoutStateCallback: LifecycleCallback<(DataResult<Boolean>) -> Unit> = LifecycleCallback()


    init {
        accountObserver = Observer<MetaUserInfo?> { _accountLiveData.postValue(it) }
        accountInteractor.accountLiveData.observeForever(accountObserver)
    }

    override fun onCleared() {
        super.onCleared()
        accountInteractor.accountLiveData.removeObserver(accountObserver)
    }

    fun logout() = viewModelScope.launch {
        accountInteractor.logout(true).collect {
            logoutStateCallback.dispatchOnMainThread { invoke((it)) }
        }
    }

    fun ditout(code: String) = viewModelScope.launch {
        accountInteractor.ditout(code).collect {
            if (it.succeeded && it.data == true) {
                logout()
            } else {
                logoutStateCallback.dispatchOnMainThread { invoke(it) }
            }
        }
    }

    fun checkU13() = viewModelScope.launch {
        val result = false
        _isU13LiveData.postValue(result)
    }
}