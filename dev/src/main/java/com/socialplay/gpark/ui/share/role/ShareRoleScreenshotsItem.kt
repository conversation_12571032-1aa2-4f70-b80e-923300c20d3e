package com.socialplay.gpark.ui.share.role

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.RoleMyInfo
import com.socialplay.gpark.data.model.share.RoleScreenshot
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.databinding.AdapterShareRoleMyInfoV2Binding
import com.socialplay.gpark.databinding.AdapterShareRoleScreenshotV2Binding
import com.socialplay.gpark.databinding.ItemRoleScreenshotSharePlatformBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/28
 *     desc   :
 * </pre>
 */
interface IShareRoleScreenshotsListener {
    fun clickScreenshot(position: Int)
    fun clickMyInfo()
    fun clickPlatform(item: SharePlatform)
}

fun MetaModelCollector.shareRoleScreenshotItem(
    item: RoleScreenshot,
    position: Int,
    listener: IShareRoleScreenshotsListener
) {
    add {
        ShareRoleScreenshotItem(item, position, listener).id("ShareRoleScreenshot-$position")
    }
}

data class ShareRoleScreenshotItem(
    val item: RoleScreenshot,
    val position: Int,
    val listener: IShareRoleScreenshotsListener
) : ViewBindingItemModel<AdapterShareRoleScreenshotV2Binding>(
    R.layout.adapter_share_role_screenshot_v2,
    AdapterShareRoleScreenshotV2Binding::bind
) {
    override fun AdapterShareRoleScreenshotV2Binding.onBind() {
        ivScreenshot.setImageURI(item.uri)
        ivCheck.setImageResource(
            if (item.isChecked) R.drawable.ic_share_check_checked
            else R.drawable.ic_share_check
        )
        ivCheck.setOnClickListener {
            listener.clickScreenshot(position)
        }
    }

    override fun AdapterShareRoleScreenshotV2Binding.onUnbind() {
        ivCheck.unsetOnClick()
    }
}

fun MetaModelCollector.shareRoleMyInfoItem(
    item: RoleMyInfo,
    listener: IShareRoleScreenshotsListener
) {
    add {
        ShareRoleMyInfoItem(item, listener).id("ShareRoleMyInfo")
    }
}

data class ShareRoleMyInfoItem(
    val item: RoleMyInfo,
    val listener: IShareRoleScreenshotsListener
) : ViewBindingItemModel<AdapterShareRoleMyInfoV2Binding>(
    R.layout.adapter_share_role_my_info_v2,
    AdapterShareRoleMyInfoV2Binding::bind
) {
    override fun AdapterShareRoleMyInfoV2Binding.onBind() {
        ShareRoleScreenshotsUtil.bindMyInfoV2(includeAdapterMyInfo, item.info)
        ivCheck.setImageResource(
            if (item.isChecked) R.drawable.ic_share_check_checked
            else R.drawable.ic_share_check
        )
        ivCheck.setOnClickListener {
            listener.clickMyInfo()
        }
    }

    override fun AdapterShareRoleMyInfoV2Binding.onUnbind() {
        ivCheck.unsetOnClick()
    }
}

fun MetaEpoxyController.shareRolePlatformItem(
    item: SharePlatform,
    listener: IShareRoleScreenshotsListener
) {
    add {
        ShareRolePlatformItem(item, listener).id("ShareRolePlatform-${item.code}")
    }
}

data class ShareRolePlatformItem(
    val item: SharePlatform,
    val listener: IShareRoleScreenshotsListener
):ViewBindingItemModel<ItemRoleScreenshotSharePlatformBinding>(
    R.layout.item_role_screenshot_share_platform,
    ItemRoleScreenshotSharePlatformBinding::bind
) {

    override fun ItemRoleScreenshotSharePlatformBinding.onBind() {
        ShareRoleScreenshotsUtil.bindPlatform(item, tvTitle, ivIcon)

        root.setOnAntiViolenceClickListener {
            listener.clickPlatform(item)
        }
    }

    override fun ItemRoleScreenshotSharePlatformBinding.onUnbind() {
        root.unsetOnClick()
    }
}