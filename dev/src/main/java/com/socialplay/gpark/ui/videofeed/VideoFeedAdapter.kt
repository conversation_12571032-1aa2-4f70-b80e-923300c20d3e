package com.socialplay.gpark.ui.videofeed

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.videofeed.GameStatus
import com.socialplay.gpark.data.model.videofeed.VideoFeedArgs
import com.socialplay.gpark.data.model.videofeed.WrappedVideoFeedItem
import com.socialplay.gpark.data.model.videofeed.VideoPlayStatus
import com.socialplay.gpark.databinding.AdapterVideoFeedBinding
import com.socialplay.gpark.databinding.IncludeVideoFeedWidgetsBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.ExpandableTextView.OnExpandListener
import com.socialplay.gpark.util.QuickDoubleTapDetector
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/11/13
 *     desc   :
 */

typealias ItemExpandListener = (bindingAdapterPosition: Int, isExpanded: Boolean) -> Unit

typealias ItemAttachListener = (bindingAdapterPosition: Int) -> Unit

typealias ItemCommentClickListener = (bindingAdapterPosition: Int) -> Unit
typealias ItemAvatarClickListener = (bindingAdapterPosition: Int) -> Unit
typealias ItemFollowClickListener = (bindingAdapterPosition: Int) -> Unit
typealias ItemShareClickListener = (bindingAdapterPosition: Int) -> Unit


interface ItemLikeListener{
    fun onLikeClicked(bindingAdapterPosition: Int, isLiked: Boolean)
    fun onLikeAnimationComplete(bindingAdapterPosition: Int, isLiked: Boolean)
}


interface ItemClickListener{
    fun onSingleTapConfirmed(bindingAdapterPosition: Int,event: MotionEvent)
    fun onDoubleTapConfirmed(bindingAdapterPosition: Int,event: MotionEvent)
}


class VideoFeedAdapter(
    val glide: RequestManager,
    val uiStyle: Int
) : BaseDifferAdapter<WrappedVideoFeedItem, AdapterVideoFeedBinding>(DIFF_CALLBACK) {

    companion object {

        // 视频封面可见状态变化
        private const val PAYLOAD_COVER_VISIBILITY_CHANGED = 1

        // 视频内容展开状态变化
        private const val PAYLOAD_EXPAND_STATE_CHANGED = 2

        // 点赞状态变化
        private const val PAYLOAD_LIKE_STATE_CHANGED = 3

        private const val PAYLOAD_DOWNLOAD_BUTTON_STATE_CHANGED = 4
        private const val PAYLOAD_UPDATE_BUTTON_STATE_CHANGED = 5

        // 播放状态变化
        private const val PAYLOAD_PLAY_STATE_CHANGED = 6

        // 点赞数量变化
        private const val PAYLOAD_LIKE_CNT_CHANGED = 7

        // 评论变化
        private const val PAYLOAD_COMMENT_CNT_CHANGED = 8

        // 视频小组件
        private const val PAYLOAD_DECORATIONS_VISIBILITY_CHANGED = 9

        // 关注状态变化
        private const val PAYLOAD_USER_FOLLOW_CHANGED = 10

        // 分享数量变化
        private const val PAYLOAD_SHARE_COUNT_CHANGED = 11

        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<WrappedVideoFeedItem>() {
            override fun areItemsTheSame(
                oldItem: WrappedVideoFeedItem,
                newItem: WrappedVideoFeedItem
            ): Boolean {
                return oldItem.videoFeedItem.videoId == newItem.videoFeedItem.videoId
            }

            override fun areContentsTheSame(
                oldItem: WrappedVideoFeedItem,
                newItem: WrappedVideoFeedItem
            ): Boolean {
                return oldItem == newItem
            }


            override fun getChangePayload(
                oldItem: WrappedVideoFeedItem,
                newItem: WrappedVideoFeedItem
            ): Any? {

                val payloads = ArrayList<Int>()

                if (oldItem.isFirstFrameRendered != newItem.isFirstFrameRendered || oldItem.isDataReady != newItem.isDataReady) {
                    payloads.add(PAYLOAD_COVER_VISIBILITY_CHANGED)
                }

                if (oldItem.isExpanded != newItem.isExpanded) {
                    payloads.add(PAYLOAD_EXPAND_STATE_CHANGED)
                }

                if (oldItem.videoFeedItem.isLike != newItem.videoFeedItem.isLike) {
                    payloads.add(PAYLOAD_LIKE_STATE_CHANGED)
                }

                if (oldItem.gameStatus != newItem.gameStatus) {
                    payloads.add( PAYLOAD_DOWNLOAD_BUTTON_STATE_CHANGED)
                }

                if (oldItem.videoPlayStatus != newItem.videoPlayStatus) {
                    payloads.add( PAYLOAD_PLAY_STATE_CHANGED)
                }

                if (oldItem.videoFeedItem.videoLikeCount != newItem.videoFeedItem.videoLikeCount) {
                    payloads.add( PAYLOAD_LIKE_CNT_CHANGED)
                }

                if (oldItem.videoFeedItem.videoCommentCount != newItem.videoFeedItem.videoCommentCount) {
                    payloads.add(PAYLOAD_COMMENT_CNT_CHANGED)
                }

                if(oldItem.decorationsVisible != newItem.decorationsVisible){
                    payloads.add(PAYLOAD_DECORATIONS_VISIBILITY_CHANGED)
                }

                if(oldItem.videoFeedItem.author.isFollow != newItem.videoFeedItem.author.isFollow){
                    payloads.add(PAYLOAD_USER_FOLLOW_CHANGED)
                }

                if (oldItem.videoFeedItem.shareCount != newItem.videoFeedItem.shareCount) {
                    payloads.add(PAYLOAD_SHARE_COUNT_CHANGED)
                }

                return payloads.ifEmpty { null }
            }
        }
    }

    private var _itemExpandListener: ItemExpandListener? = null
    private var _itemLikeListener: ItemLikeListener? = null
    private var _itemClickListener: ItemClickListener? = null
    private var _itemAttachListener: ItemAttachListener? = null
    private var _itemCommentClickListener: ItemCommentClickListener? = null
    private var _itemAvatarClickListener: ItemAvatarClickListener? = null
    private var _itemFollowClickListener: ItemFollowClickListener? = null
    private var _itemShareClickListener: ItemFollowClickListener? = null

    fun setItemExpandListener(itemExpandListener: ItemExpandListener?) {
        this._itemExpandListener = itemExpandListener
    }

    fun setItemLikeListener(itemLikeListener: ItemLikeListener?) {
        this._itemLikeListener = itemLikeListener
    }

    fun setItemClickListener(itemClickListener: ItemClickListener?) {
        this._itemClickListener = itemClickListener
    }

    fun setItemAttachListener(itemAttachListener: ItemAttachListener?) {
        this._itemAttachListener = itemAttachListener
    }

    fun setItemCommentClickListener(itemCommentClickListener: ItemCommentClickListener?) {
        this._itemCommentClickListener = itemCommentClickListener
    }


    fun setItemAvatarClickListener(itemAvatarClickListener: ItemAvatarClickListener?) {
        this._itemAvatarClickListener = itemAvatarClickListener
    }

    fun setItemFollowClickListener(itemFollowClickListener: ItemFollowClickListener?) {
        this._itemFollowClickListener = itemFollowClickListener
    }

    fun setItemShareClickListener(itemShareClickListener: ItemShareClickListener?) {
        this._itemShareClickListener = itemShareClickListener
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterVideoFeedBinding {
        val inflater = LayoutInflater.from(parent.context)
        return AdapterVideoFeedBinding.inflate(inflater, parent, false).apply {
            root.setTag(R.id.tag_binding_data, IncludeVideoFeedWidgetsBinding.bind(root))
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
        item: WrappedVideoFeedItem,
        payloads: List<Any>
    ) {

        Timber.d("CoverDbg Convert partial pos:${holder.bindingAdapterPosition} vid:${item.videoFeedItem.videoId} coverVisible:${item.coverVisible} payloads:${payloads}")
        Timber.d("RefreshDebug convert payloads:${payloads}")
        payloads.filterIsInstance<List<*>>().flatten().forEach {
            when (it) {
                PAYLOAD_COVER_VISIBILITY_CHANGED -> {
                    setCoverStatus(item, holder)
                }

                PAYLOAD_EXPAND_STATE_CHANGED -> {
                    setExpandStatus(item, holder)
                }

                PAYLOAD_LIKE_STATE_CHANGED -> {
                    setLikeStatus(item, holder)
                }

                PAYLOAD_LIKE_CNT_CHANGED -> {
                    setLikeCntStatus(item, holder)
                }

                PAYLOAD_DOWNLOAD_BUTTON_STATE_CHANGED -> {
                    setDownloadButtonStatus(item, holder)
                }

                PAYLOAD_PLAY_STATE_CHANGED -> {
                    setVideoPlayStatus(item, holder)
                }

                PAYLOAD_COMMENT_CNT_CHANGED -> {
                    setCommentCntStatus(item, holder)
                }

                PAYLOAD_DECORATIONS_VISIBILITY_CHANGED -> {
                    setDecorationsStatus(item, holder)
                }

                PAYLOAD_USER_FOLLOW_CHANGED -> {
                    setFollowStatus(item, holder, true)
                }

                PAYLOAD_SHARE_COUNT_CHANGED -> {
                    setShareCntStatus(item, holder)
                }
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
        item: WrappedVideoFeedItem
    ) {

        Timber.d("CoverDbg Convert fully pos:${holder.bindingAdapterPosition} vid:${item.videoFeedItem.videoId} coverVisible:${item.coverVisible}")

        glide.load(item.videoFeedItem.videoCover).into(holder.binding.ivCover)

        val widgetsBinding = holder.widgetsBinding

        when (uiStyle) {
            VideoFeedArgs.STYLE_RECOMMEND -> {
                widgetsBinding.clContent.setPaddingEx(bottom = 54.dp)
            }
            else -> {
                widgetsBinding.clContent.setPaddingEx(bottom = 20.dp)
            }
        }

        glide.load(item.videoFeedItem.author.avatar)
            .placeholder(R.drawable.placeholder_corner)
            .error(R.drawable.icon_default_avatar)
            .into(widgetsBinding.sivAuthorAvatar)

        widgetsBinding.clGameInfo.visible(item.videoFeedItem.game != null)
        item.videoFeedItem.game?.let {
            widgetsBinding.tvGameName.text = it.displayName

            glide.load(it.iconUrl)
                .placeholder(R.drawable.placeholder_corner)
                .into(widgetsBinding.ivGameIcon)
        }


        widgetsBinding.tvContent.text = item.videoFeedItem.videoContent
        widgetsBinding.tvAuthor.text = "@${item.videoFeedItem.author.name}"

        widgetsBinding.ivAuthorOfficial.visible(item.videoFeedItem.author.isOfficial)

        widgetsBinding.tvContent.setExpandListener(object : OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onExpand")
                _itemExpandListener?.invoke(holder.bindingAdapterPosition, true)
            }

            override fun onShrink(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onShrink")
                _itemExpandListener?.invoke(holder.bindingAdapterPosition, false)
            }
        })

        widgetsBinding.llLikeContainer.setOnClickListener {
            val latestItem = getItem(holder.bindingAdapterPosition)
            Timber.d("Like clicked pos:${holder.bindingAdapterPosition} likeStatus:${latestItem.videoFeedItem.isLike}")

            if (widgetsBinding.lavLikeAnim.isAnimating) return@setOnClickListener

            if (latestItem.videoFeedItem.isLike) {
                //Revert my like
                _itemLikeListener?.onLikeClicked(holder.bindingAdapterPosition, false)

                widgetsBinding.lavLikeAnim.visible(true)
                widgetsBinding.lavLikeAnim.setAnimation(R.raw.anim_video_feed_like_cancel)
                widgetsBinding.lavLikeAnim.progress = 0F

                widgetsBinding.lavLikeAnim.playAnimationWithCallback{
                    _itemLikeListener?.onLikeAnimationComplete(holder.bindingAdapterPosition, false)
                }
            } else {

                _itemLikeListener?.onLikeClicked(holder.bindingAdapterPosition, true)

                widgetsBinding.ivLike.visible(false)
                widgetsBinding.lavLikeAnim.visible(true)
                widgetsBinding.lavLikeAnim.setAnimation(R.raw.anim_video_feed_like)
                widgetsBinding.lavLikeAnim.progress = 0F

                widgetsBinding.lavLikeAnim.playAnimationWithCallback{
                    _itemLikeListener?.onLikeAnimationComplete(holder.bindingAdapterPosition, true)
                }
            }
        }

        widgetsBinding.llCommentContainer.setOnClickListener {
            _itemCommentClickListener?.invoke(holder.bindingAdapterPosition)
        }

        widgetsBinding.sivAuthorAvatar.setOnClickListener {
            _itemAvatarClickListener?.invoke(holder.bindingAdapterPosition)
        }

        widgetsBinding.lavFollowAnim.setOnAntiViolenceClickListener {
            Timber.d("Follow clicked pos:${holder.bindingAdapterPosition}")
            _itemFollowClickListener?.invoke(holder.bindingAdapterPosition)
        }

        widgetsBinding.llShareContainer.setOnAntiViolenceClickListener {
            _itemShareClickListener?.invoke(holder.bindingAdapterPosition)
        }

        setRootViewTapListener(item, holder)

        setExpandStatus(item, holder)
        setLikeStatus(item, holder)
        setLikeCntStatus(item, holder)

        setDownloadButtonStatus(item, holder)

        setVideoPlayStatus(item, holder)

        setCoverStatus(item, holder)

        setCommentCntStatus(item, holder)

        setDecorationsStatus(item, holder)

        setFollowStatus(item, holder, false)

        setShareCntStatus(item, holder)
    }

    private fun setFollowStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
        shouldPlayAnim: Boolean
    ) {
        Timber.d("setFollowStatus pos:${holder.bindingAdapterPosition} ${item.videoPlayStatus}")
        val widgetsBinding = holder.widgetsBinding
        val isFollowed = item.videoFeedItem.author.isFollow

        if(isFollowed){
            if (!shouldPlayAnim) {
                widgetsBinding.lavFollowAnim.invisible(true)
                widgetsBinding.lavFollowAnim.progress = 1F

            } else {
                widgetsBinding.lavFollowAnim.playAnimationWithCallback {
                    widgetsBinding.lavFollowAnim.postDelayed({
                        widgetsBinding.lavFollowAnim.invisible(true)
                        widgetsBinding.lavFollowAnim.progress = 1F
                    }, 700)
                }
            }
        }else{
            widgetsBinding.lavFollowAnim.visible(true)
            widgetsBinding.lavFollowAnim.progress = 0F
        }
    }

    private fun setShareCntStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,){

        val widgetsBinding = holder.widgetsBinding

        if (PandoraToggle.enableShareVideoFeed) {
            widgetsBinding.llShareContainer.visible()

            val videoShareCount = item.videoFeedItem.shareCount

            Timber.d("setShareCntStatus pos:${holder.bindingAdapterPosition} shareCount:$videoShareCount")

            if (videoShareCount == 0L) {
                widgetsBinding.tvShareCnt.setText(R.string.share)
            } else {
                widgetsBinding.tvShareCnt.text = UnitUtil.formatKMCount(videoShareCount)
            }
        } else {
            widgetsBinding.llShareContainer.gone()
        }
    }

    private fun setVideoPlayStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {
        Timber.d("setVideoPlayStatus pos:${holder.bindingAdapterPosition} ${item.videoPlayStatus}")
        val widgetsBinding = holder.widgetsBinding
        widgetsBinding.ivPlay.visible(item.videoPlayStatus == VideoPlayStatus.Paused)
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun setRootViewTapListener(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {

        val widgetsBinding = holder.widgetsBinding

        val gestureDetector = QuickDoubleTapDetector(holder.view.context,object : QuickDoubleTapDetector.OnTappingListener{
            override fun onSingleTapConfirmed(event: MotionEvent) {
                _itemClickListener?.onSingleTapConfirmed(holder.bindingAdapterPosition, event)
            }

            override fun onDoubleTapConfirmed(event: MotionEvent) {
                _itemClickListener?.onDoubleTapConfirmed(holder.bindingAdapterPosition, event)
            }
        })

        widgetsBinding.root.setOnTouchListener { _, event ->
            return@setOnTouchListener gestureDetector.onTouchEvent(event)
        }
    }

    private fun setDownloadButtonStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {

        Timber.d("setDownloadButtonStatus bindingAdapterPosition:${holder.bindingAdapterPosition} videoId:${item.videoFeedItem.videoId}")

        val widgetsBinding = holder.widgetsBinding

        val dptPlay = widgetsBinding.dpnPlayGame
        when (item.gameStatus) {
            is GameStatus.Launching -> {
                dptPlay.setTextColor(widgetsBinding.root.context.getColor(R.color.black_50))
                dptPlay.setText(R.string.video_feed_game_launching)
            }

            else -> {
                dptPlay.setTextColor(widgetsBinding.root.context.getColor(R.color.black))
                dptPlay.setText(R.string.video_feed_enter_game)
            }
        }

    }

    private fun setCommentCntStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,){

        val videoCommentCount = item.videoFeedItem.videoCommentCount

        Timber.d("setCommentCntStatus pos:${holder.bindingAdapterPosition} commentCnt:$videoCommentCount")

        val widgetsBinding = holder.widgetsBinding

        if (videoCommentCount == 0L) {
            widgetsBinding.tvCommentCnt.setText(R.string.comment)
        } else {
            widgetsBinding.tvCommentCnt.text = UnitUtil.formatKMCount(videoCommentCount)
        }
    }

    private fun setCoverStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {
        Timber.d("setCoverStatus pos:${holder.bindingAdapterPosition} ${item.coverVisible}")
        val ivCover = holder.binding.ivCover
        ivCover.visible(item.coverVisible, false)
    }

    private fun setLikeCntStatus(item: WrappedVideoFeedItem,
                                 holder: BaseVBViewHolder<AdapterVideoFeedBinding>,){

        val widgetsBinding = holder.widgetsBinding

        val videoLikeCount = item.videoFeedItem.videoLikeCount

        Timber.d("setLikeCntStatus pos:${holder.bindingAdapterPosition} videoLikeCount:$videoLikeCount")

        if (videoLikeCount == 0L) {
            widgetsBinding.tvLikeCnt.setText(R.string.video_feed_like_cnt_zero_text)
        } else {
            widgetsBinding.tvLikeCnt.text = UnitUtil.formatKMCount(videoLikeCount)
        }
    }


    private fun setDecorationsStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {
        holder.binding.includeWidgets.clContent.visible(item.decorationsVisible)
    }


    private fun setLikeStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {

        val widgetsBinding = holder.widgetsBinding

        Timber.d("setLikeStatus pos:${holder.bindingAdapterPosition} likeStatus:${item.videoFeedItem.isLike}")

        widgetsBinding.lavLikeAnim.invisible(true)
        widgetsBinding.ivLike.visible(true)

        if (item.videoFeedItem.isLike) {
            widgetsBinding.ivLike.setImageResource(R.drawable.ic_video_feed_liked)
            Timber.d("LavLikeAnim status pos:${holder.bindingAdapterPosition} isAnimating:${widgetsBinding.lavLikeAnim.isAnimating} composition:${widgetsBinding.lavLikeAnim.composition}")
        } else {
            widgetsBinding.ivLike.setImageResource(R.drawable.ic_video_feed_like)
        }
    }

    private fun setExpandStatus(
        item: WrappedVideoFeedItem,
        holder: BaseVBViewHolder<AdapterVideoFeedBinding>,
    ) {

        Timber.d("setExpandStatus ${item.videoFeedItem.videoId}:${item.isExpanded}")

        val expandState = if (item.isExpanded) {
            ExpandableTextView.STATE_EXPAND
        } else {
            ExpandableTextView.STATE_SHRINK
        }

        val widgetsBinding = holder.widgetsBinding
        widgetsBinding.tvContent.setCurrState(expandState)
    }

    private val RecyclerView.ViewHolder.widgetsBinding: IncludeVideoFeedWidgetsBinding
        get() {
            return itemView.getTag(R.id.tag_binding_data) as IncludeVideoFeedWidgetsBinding
        }

    private fun LottieAnimationView.playAnimationWithCallback(completionCallback: (() -> Unit)? = null) {
        this.progress = 0F

        val lav = this

        val listener = object : FakeAnimatorListenerAdapter() {

            override fun onAnimationEnd(animation: Animator) {
                Timber.d("playAnimation end")
                lav.removeAnimatorListener(this)
                lav.removeAnimatorListenerByTag(this)
                completionCallback?.invoke()
            }

            override fun onAnimationCancel(animation: Animator) {
                Timber.d("playAnimation cancel")
                lav.removeAnimatorListener(this)
                lav.removeAnimatorListenerByTag(this)
                completionCallback?.invoke()
            }

            override fun onFakeAnimationCancel() {
                Timber.d("playAnimation fake cancel")
                lav.removeAnimatorListener(this)
                lav.removeAnimatorListenerByTag(this)
                lav.pauseAnimation()
                completionCallback?.invoke()
            }
        }
        this.addAnimatorListener(listener)
        this.addAnimatorListenerByTag(listener)
        this.playAnimation()
    }


    override fun onViewAttachedToWindow(holder: BaseVBViewHolder<AdapterVideoFeedBinding>) {
        super.onViewAttachedToWindow(holder)
        _itemAttachListener?.invoke(holder.bindingAdapterPosition)
    }

    override fun onViewDetachedFromWindow(holder: BaseVBViewHolder<AdapterVideoFeedBinding>) {
        super.onViewDetachedFromWindow(holder)
        val widgetsBinding = holder.widgetsBinding
        Timber.d("onViewDetachedFromWindow pos:${holder.bindingAdapterPosition} ${widgetsBinding.lavLikeAnim.isAnimating}")

        // 处理View被移除后AnimationListener没有回调取消/完成的问题。
        widgetsBinding.lavLikeAnim.let { likeAnim ->
            likeAnim.getAnimatorListenersByTag()?.toMutableList()
                ?.forEach { it.onFakeAnimationCancel() }
        }
    }

    private fun View.addAnimatorListenerByTag(listener: FakeAnimatorListenerAdapter) {
        var listeners = this.getTag(R.id.tag_animation_listener) as? MutableList<FakeAnimatorListenerAdapter>
        if (listeners == null) {
            listeners = mutableListOf()
            this.setTag(R.id.tag_animation_listener, listeners)
        }
        listeners.add(listener)
    }

    private fun View.removeAnimatorListenerByTag(listener: FakeAnimatorListenerAdapter) {
        val listeners = this.getTag(R.id.tag_animation_listener) as? MutableList<FakeAnimatorListenerAdapter>
        listeners?.remove(listener)
    }

    private fun View.getAnimatorListenersByTag(): List<FakeAnimatorListenerAdapter>? {
        return this.getTag(R.id.tag_animation_listener) as? List<FakeAnimatorListenerAdapter>
    }


    internal open class FakeAnimatorListenerAdapter : AnimatorListenerAdapter() {
        open fun onFakeAnimationCancel() {}
    }
}