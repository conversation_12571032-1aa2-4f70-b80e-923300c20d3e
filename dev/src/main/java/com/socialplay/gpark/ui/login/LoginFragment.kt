package com.socialplay.gpark.ui.login

import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.TextPaint
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.activity.addCallback
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.google.android.material.tabs.TabLayout
import com.google.android.material.textfield.TextInputEditText
import com.meta.pandora.Pandora
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LOGIN_TYPE_GPARK_ID
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginTypes
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.event.AuthorizeResultEvent
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentLoginBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.LoginCompleteRouter
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SoftKeyboardUtil
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setFontFamily
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-24 12:04 下午
 * @desc:
 */
class LoginFragment : BaseFragment<FragmentLoginBinding>() {

    var isAgreeLogin = false

    private var continueAccountInfo: ContinueAccountInfo? = null
    private val viewModel by viewModel<LoginViewModel>()
    private val args by navArgs<LoginFragmentArgs>()
    private val h5PageConfig by inject<H5PageConfigInteractor>()

    private var loadingDialogFragment: LoadingDialogFragment? = null
    private var isThirdAuthorizeLoggedIn = false
    private var isSmallScreen = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLoginBinding? {
        return FragmentLoginBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        continueAccountInfo = args.continueAccountInfo
        viewModel.init(
            args.source ?: LoginSource.Unknown.source,
            args.onlyLogin,
            LoginViewModel.LOCATION_LOGIN
        )
        Analytics.track(EventConstants.EVENT_LOGIN_PAGE_SHOW) {
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginSource.Unknown.source)
        }

        if (args.source != LoginSource.ThirdAppAuthorize.source && !EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun init() {
        continueAccountInfo = args.continueAccountInfo
        initView()
        initData()
        adjustUIForScreenDensity()
    }

    /**
     * 根据屏幕密度动态调整UI，解决在低分辨率设备上的UI重叠问题
     */
    private fun adjustUIForScreenDensity() {
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.densityDpi

        // 针对xhdpi(320dpi)及以下设备进行特殊处理
        if (density <= 320) {
            isSmallScreen = true
            // 调整文字大小
            binding.ivLoginAccountTip.textSize = resources.getDimension(R.dimen.login_title_text_size) / displayMetrics.density
            binding.tvAgreement.textSize = resources.getDimension(R.dimen.login_agreement_text_size) / displayMetrics.density

            // 调整间距和高度
            val loginButtonParams = binding.tvLogin.layoutParams
            loginButtonParams.height = resources.getDimensionPixelSize(R.dimen.login_button_height)
            binding.tvLogin.layoutParams = loginButtonParams

            val inputAccountParams = binding.inputAccount.layoutParams as ViewGroup.MarginLayoutParams
            inputAccountParams.height = resources.getDimensionPixelSize(R.dimen.login_input_height)
            inputAccountParams.topMargin = resources.getDimensionPixelSize(R.dimen.login_vertical_spacing)
            binding.inputAccount.layoutParams = inputAccountParams

            val inputPasswordParams = binding.inputPassword.layoutParams as ViewGroup.MarginLayoutParams
            inputPasswordParams.height = resources.getDimensionPixelSize(R.dimen.login_input_height)
            inputPasswordParams.topMargin = resources.getDimensionPixelSize(R.dimen.login_vertical_spacing)
            binding.inputPassword.layoutParams = inputPasswordParams

            // 调整底部协议区域的边距
            val agreementParams = binding.clAgreement.layoutParams as ViewGroup.MarginLayoutParams
            agreementParams.bottomMargin = resources.getDimensionPixelSize(R.dimen.dp_24)
            binding.clAgreement.layoutParams = agreementParams

            // 确保内容可滚动
            binding.rlContentArea.isFillViewport = true
        }
    }

    override fun loadFirstData() {
        if (!EnvConfig.isParty()) {
            args.continueAccountInfo?.let {
                when (it.loginType) {
                    LoginWay.Account.way -> {
                        viewModel.updateMode(LoginViewModel.MODE_ACCOUNT_EMAIL)
                        binding.etAccount.setText(it.loginKey)
                        binding.tvSwitchLoginWay.setText(R.string.login_by_gpark_id)
                    }

                    LOGIN_TYPE_GPARK_ID -> {
                        viewModel.updateMode(LoginViewModel.MODE_GPARK_ID)
                        binding.etAccount.setText(it.loginKey)
                        binding.tvSwitchLoginWay.setText(R.string.login_by_account_email)
                    }

                    else -> {
                        return@let
                    }
                }
                viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                    delay(100)
                    InputUtil.showSoftBoard(binding.etPassword)
                }
            } ?: args.lastLoginType?.let {
                when (it) {
                    LoginWay.Account.way -> {
                        viewModel.updateMode(LoginViewModel.MODE_ACCOUNT_EMAIL)
                        binding.tvSwitchLoginWay.setText(R.string.login_by_gpark_id)
                    }

                    LOGIN_TYPE_GPARK_ID -> {
                        viewModel.updateMode(LoginViewModel.MODE_GPARK_ID)
                        binding.tvSwitchLoginWay.setText(R.string.login_by_account_email)
                    }

                    else -> {
                        return@let
                    }
                }
                viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                    delay(100)
                    InputUtil.showSoftBoard(binding.etAccount)
                }
            }
        }
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_NAME_LOGIN

    private fun initView() {

        binding.apply {
            viewLifecycleOwner.lifecycleScope.launch {
                delay(10)
                rlContentArea.fullScroll(View.FOCUS_DOWN)
            }

            val agreementContent = getAgreementStringBuilder()
            binding.tvAgreement.text = agreementContent
            binding.tvAgreement.movementMethod = InterceptClickEventLinkMovementMethod(binding.tvAgreement)
            binding.tvAgreement.isClickable = false
            binding.tvAgreement.isContextClickable = false

            if (!EnvConfig.isParty()) {
                // GPark 默认同意check框
                isAgreeLogin = true
            }
            cbAgree.isChecked = isAgreeLogin
            cbAgree.setOnCheckedChangeListener { buttonView, isChecked ->
                isAgreeLogin = isChecked
            }

            vHotCheck.setOnClickListener {
                cbAgree.isChecked = !cbAgree.isChecked
            }


            requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
                goBack(false)
            }

            val items: List<LoginItemData> = viewModel.fetchLoginOthers()
            addLoginItems(items)

            tvLogin.setOnAntiViolenceClickListener {
                InputUtil.hideKeyboard(it)

                Analytics.track(EventConstants.EVENT_LOGIN_PAGE_LOGIN_CLICK) {
                    put(EventConstants.KEY_LOGIN_WAY, LoginWay.Account.way)
                }
                checkCanLogin {
                    viewModel.authAccount(
                        requireContext(),
                        if (isContinueLogin()) LoginType.LastAccount else LoginType.ExistAccount
                    )
                }
            }
            ivClearName.setOnAntiViolenceClickListener {
                binding.etAccount.setText("")
                binding.vAccountBlock.visible(false)
            }
            ivClearPassword.setOnAntiViolenceClickListener {
                binding.etPassword.setText("")
            }

            ivPasswordVisibility.setOnClickListener {
                viewModel.togglePasswordVisibility()
            }

            etAccount.addTextChangedListener {
                viewModel.postAccountValueChanged(it?.toString())
                setFont(etAccount)
            }

            etPassword.addTextChangedListener {
                viewModel.postPasswordValueChanged(it?.toString())
                setFont(etPassword)
            }
            etAccount.onFocusChangeListener = OnFocusChangeListener { p0, focus ->
                updateAccountUI(focus, viewModel.mode)
            }
            etPassword.onFocusChangeListener = OnFocusChangeListener { p0, focus ->
                updatePasswordUI(focus)
            }
            tbl.setOnBackAntiViolenceClickedListener {
                goBack(false)
            }

            vPhoneLoginView.setOnAntiViolenceClickListener {
                MetaRouter.Login.loginByPhone(this@LoginFragment, loginSource = args.source ?: "")
            }

            tvForgotPassword.setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_LOGIN_CLICK_FORGOT)
                MetaRouter.Account.passwordForget(this@LoginFragment, LoginPageSource.Login, args.gid)
            }
            // continue login
            val continueLogin = isContinueLogin()
            updateContinueLoginUI(continueLogin)
            // continue login end

            Timber.i("source====${args.source}")

            if (EnvConfig.isParty()) {
                ivAgeRestriction.visible(true)
                ivAgeRestriction.setImageResource(LoginSpecialWrapper.getAgeRestrictionIconRes())
                ivAgeRestriction.setOnAntiViolenceClickListener {
                    LoginSpecialWrapper.showAgeRestrictionDialog(requireActivity(), this@LoginFragment)
                }
            } else {
                ivAgeRestriction.visible(false)
            }
        }
        // 默认来这里都先隐藏键盘
        SoftKeyboardUtil.hideSoftKeyboard(this@LoginFragment)

        if (!EnvConfig.isParty()) {
            if (args.continueAccountInfo == null) {
                binding.tvSwitchLoginWay.visible()
                binding.tvSwitchLoginWay.setOnClickListener {
                    viewModel.switchMode()
                }
            }

            viewModel.modeFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
                when (it) {
                    LoginViewModel.MODE_ACCOUNT_EMAIL -> {
                        updateAccountUI(
                            binding.etAccount.hasFocus(),
                            LoginViewModel.MODE_ACCOUNT_EMAIL
                        )
                        binding.tvSwitchLoginWay.setText(R.string.login_by_gpark_id)
                    }

                    LoginViewModel.MODE_GPARK_ID -> {
                        updateAccountUI(
                            binding.etAccount.hasFocus(),
                            LoginViewModel.MODE_GPARK_ID
                        )
                        binding.tvSwitchLoginWay.setText(R.string.login_by_account_email)
                    }

                    else -> {
                        return@collectWithLifecycleOwner
                    }
                }

                val currentAccount = viewModel.currentAccount.orEmpty()
                if (binding.etAccount.text?.toString() != currentAccount) {
                    binding.etAccount.setText(currentAccount)
                }
            }
        }
    }

    private fun updateContinueLoginUI(continueLogin: Boolean) {
        if (!continueLogin) {
            // 继续登录不展示删除账户按钮
            viewLifecycleOwner.lifecycleScope.launch {
                viewModel.currentAccountFlow.map { it.isNullOrEmpty() }.collect {
                    binding.ivClearName.visible(!it, true)
                }
            }
        }

        visibleList(binding.tvContinueLogin, binding.ivContinueLogin, binding.vAccountBlock, visible = continueLogin)
        visibleList(
            binding.vPhoneLoginView,
            binding.tvPhoneLogin,
            binding.vLineOrL,
            binding.tvOr,
            binding.vLineOrR,
            binding.ivLoginAccountTip,
            visible = !continueLogin
        )
        if (continueLogin) {
            binding.tvContinueLogin.text = continueAccountInfo?.nickname
            glide?.run {
                load(continueAccountInfo?.portrait).placeholder(R.drawable.placeholder_corner_360)
                    .into(binding.ivContinueLogin)
            }
            binding.etAccount.setText(continueAccountInfo?.loginKey)
            updateAccountUI(true, viewModel.mode)
            viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                delay(100)
                InputUtil.showSoftBoard(binding.etPassword)
            }
        }

        // 手机号登录，目前只在233派对才支持
        visibleList(
            binding.vPhoneLoginView,
            binding.tvPhoneLogin,
            binding.vLineOrL,
            binding.tvOr,
            binding.vLineOrR,
            visible = EnvConfig.isParty()
        )
    }

    private fun checkCanLogin(cont: () -> Unit) {
        if (!EnvConfig.isParty()) {
            // GPark 直接通过
            cont()
            return
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        if (binding.cbAgree.isChecked) {
            cont()
            return
        }
        LoginSpecialWrapper.showProtocolDialogBottomFragment(requireActivity(), this) {
            if (!isAdded || isDetached) return@showProtocolDialogBottomFragment // 检查 Fragment 是否已附加
            isAgreeLogin = true
            binding.cbAgree.isChecked = true
            Pandora.send(EventConstants.EVENT_CONTRACT_ACCESS) {
                put("state", 0)
            }
            cont()
        }
    }

    private fun addLoginItems(items: List<LoginItemData>) {
        val iconSize = 44.dp
//        val iconSize = if (isSmallScreen) 44.dp else 40.dp
        items.forEachIndexed { index, loginItemData ->
            val imageView = ImageView(requireContext())
            val layoutParams = LinearLayout.LayoutParams(
                iconSize,
                iconSize
//                ViewGroup.LayoutParams.WRAP_CONTENT,
//                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            layoutParams.leftMargin = if (index == 0) 0 else 20.dp
            imageView.layoutParams = layoutParams
            imageView.setImageResource(loginItemData.loginIcon)
            imageView.setOnAntiViolenceClickListener { it2 ->
                InputUtil.hideKeyboard(it2)
                Analytics.track(EventConstants.EVENT_LOGIN_PAGE_LOGIN_CLICK) {
                    put(EventConstants.KEY_LOGIN_WAY, loginItemData.loginType.way)
                }
                checkCanLogin {
                    viewModel.auth(
                        loginItemData.loginType,
                        requireActivity(),
                        args.source ?: LoginSource.Unknown.source,
                        if (args.onlyLogin) LoginType.ExistAccount else LoginType.Unknown
                    )
                }
            }
            binding.llLoginByOtherSdk.addView(imageView)
        }
    }

    private fun isContinueLogin(): Boolean {
        return LoginTypes.checkIsAccountLogin(continueAccountInfo?.loginType, continueAccountInfo?.loginKey)
    }

    private fun updatePasswordUI(focus: Boolean) {
        if (focus) {
            binding.inputPassword.setBackgroundResource(R.drawable.bg_gray_login_fouces)
            binding.inputPassword.hint = getString(R.string.intl_password)
        } else {
            binding.inputPassword.setBackgroundResource(R.drawable.bg_f6_corner_12)
            if (binding.etPassword.text.toString().isBlank()) {
                binding.inputPassword.hint = getString(R.string.intl_enter_password)
            } else {
                binding.inputPassword.hint = getString(R.string.intl_password)
            }
        }
    }

    private fun updateAccountUI(focus: Boolean, mode: Int) {
        if (focus) {
            binding.inputAccount.setBackgroundResource(if (isContinueLogin()) R.drawable.bg_f6_corner_12 else R.drawable.bg_gray_login_fouces)
            binding.inputAccount.setHint(
                if (mode == LoginViewModel.MODE_GPARK_ID) {
                    R.string.gpark_id
                } else {
                    R.string.intl_account_or_email
                }
            )
        } else {
            binding.inputAccount.setBackgroundResource(R.drawable.bg_f6_corner_12)
            if (binding.etAccount.text.toString().isBlank()) {
                binding.inputAccount.setHint(
                    if (mode == LoginViewModel.MODE_GPARK_ID) {
                        R.string.intl_enter_gpark_id
                    } else {
                        R.string.intl_enter_account_or_email
                    }
                )
            } else {
                binding.inputAccount.setHint(
                    if (mode == LoginViewModel.MODE_GPARK_ID) {
                        R.string.gpark_id
                    } else {
                        R.string.intl_account_or_email
                    }
                )
            }
        }
    }

    private fun setFont(editText: TextInputEditText) {
        if (editText.text?.toString()?.isBlank() == true) {
            editText.setFontFamily(R.font.poppins_regular_400)
        } else {
            editText.setFontFamily(R.font.poppins_semi_bold_600)
        }
    }

    override fun onResume() {
        super.onResume()
        if (isThirdAuthorizeLoggedIn) {
            isThirdAuthorizeLoggedIn = false
            navigateUpToTarget(true)
        }
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.root)
        super.onPause()
    }

    private fun isThirdAppAuthorize(): Boolean {
        return args.source == LoginSource.ThirdAppAuthorize.source
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onThirdAuthoredLoginEvent(event: AuthorizeResultEvent) {
        //第三方授权登录成功后通知，解决p12登录成功后，返回app还停留在登录页面问题
        isThirdAuthorizeLoggedIn = event.status == AuthorizeResultEvent.SUCCESS
    }

    private fun navigateUpToTarget(
        isLoginSucceed: Boolean = false,
        isFirstBinding: Boolean = false,
        userInfo: MetaUserInfo? = null
    ) {
        if (isThirdAppAuthorize()) {
            backToAuthorizeApp(isLoginSucceed)
        } else {
            backToTargetPage(isLoginSucceed, isFirstBinding, userInfo)
        }
    }

    private fun backToTargetPage(isLoginSucceed: Boolean, isFirstBinding: Boolean, userInfo: MetaUserInfo? = null) {
        if (requireActivity() is LoginActivity) {
            //如果是LoginActivity就直接干掉
            requireActivity().finish()
        } else if (isLoginSucceed && args.successToMain) {
//            if (isFirstBinding) {
//                // Gpark逻辑，保持原样
//                MetaRouter.Startup.createAvatar(this, true)
//            } else if (PandoraToggle.hasSelectMode) {
//                MetaRouter.Startup.selectMode(this)
//            } else {
            LoginCompleteRouter.router(requireActivity(), this, userInfo)
//            }
        } else {
            // 上一页
            navigateUp()
        }
    }

    private fun backToAuthorizeApp(isLoginSucceed: Boolean = false) {
        EventBus.getDefault().post(
            AuthorizeResultEvent(
                if (isLoginSucceed) {
                    AuthorizeResultEvent.SUCCESS
                } else {
                    AuthorizeResultEvent.CANCEL
                }
            )
        )
    }

    private fun goBack(isLoginSucceed: Boolean, isFirstBinding: Boolean = false, userInfo: MetaUserInfo? = null) {
        val gameId = args.gid
        if (!gameId.isNullOrEmpty()) {
            resumeGameById(gameId)
        }
        navigateUpToTarget(isLoginSucceed, isFirstBinding = isFirstBinding, userInfo = userInfo)
    }

    private fun initData() {
        viewModel.setOAuthManagerCallback()
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.passwordVisibilityFlow.distinctUntilChanged().collect {
                if (it) {
                    binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_visible_password)
                    binding.etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                } else {
                    binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_hiden_password)
                    binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                }
                binding.etPassword.setSelection(binding.etPassword.length())
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.passwordFlow.map { it.isNullOrEmpty() }.collect {
                binding.ivPasswordVisibility.visible(!it, true)
                binding.ivClearPassword.visible(!it, true)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.accountAndPasswordValidFlow.collect {
                binding.tvLogin.enableWithAlpha(it)
            }
        }

        if (args.continueAccountInfo == null) {
            viewLifecycleOwner.lifecycleScope.launch {
                viewModel.continueAccountInfo.collect {
                    // 获取到继续登录信息
                    <EMAIL> = it
                    updateContinueLoginUI(isContinueLogin())
                }
            }
            if (!args.onlyLogin) {
                viewModel.getContinueAccount()
            }
        } else {
            continueAccountInfo = args.continueAccountInfo
            updateContinueLoginUI(isContinueLogin())
        }
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.signInStatusFlow.collect {
                when (it) {
                    is LoginState.Loading -> {
                        showLoading()
                    }

                    is LoginState.Succeeded -> {
                        dismissLoading()
                        goBack(true, it.userInfo?.firstBind == true, it.userInfo)
                    }

                    is LoginState.Failed -> {
                        dismissLoading()
                        val msg = it.message
                        if (it.violateMessage != null) {
                            showViolateDialogByInfo(it.violateMessage)
                        } else if (msg.isNotBlank()) {
                            toast(msg)
                        }
                    }

                    is LoginState.UserCanceled -> {
                        dismissLoading()
                    }
                }
            }
        }
    }

    private fun showViolateDialogByInfo(violateMessage: ViolateMessage) {
        violateMessage.toArgs()?.let { MetaRouter.Report.violateRulesDialog(this, it) }
    }

    private fun getAgreementStringBuilder(): Spannable {
        return LoginSpecialWrapper.getAgreementStringBuilder(this, h5PageConfig)
    }

    inner class SpanClick(val call: () -> Unit) : ClickableSpan() {
        override fun onClick(widget: View) {
            call.invoke()
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.isUnderlineText = false
            ds.color = Color.parseColor("#53535E")
        }

    }

    private fun showLoading() {
        loadingDialogFragment = MetaRouter.Dialog.loading(childFragmentManager, msg = getString(R.string.logging))
    }

    private fun dismissLoading() {
        loadingDialogFragment?.dismissAllowingStateLoss()
        loadingDialogFragment = null
    }

    override fun onDestroyView() {
        viewModel.removeOAuthManagerCallback()
        dismissLoading()
        super.onDestroyView()
    }

    override fun onDestroy() {
        if (args.source != LoginSource.ThirdAppAuthorize.source && EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }

        super.onDestroy()
    }

}