package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemRacingRallyBinding
import com.socialplay.gpark.databinding.AdapterChoiceCardItemRacingRallyTemplateBinding
import com.socialplay.gpark.ui.editorschoice.adapter.BaseEditorsChoiceItemAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import android.graphics.Color
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import android.view.View

/**
 * Racing Rally item adapter for template items (first item)
 */
class RacingRallyItemAdapter(
    data: MutableList<ChoiceGameInfo>?,
    private val glide: RequestManager
) : BaseEditorsChoiceItemAdapter<AdapterChoiceCardItemRacingRallyTemplateBinding>(data) {

    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterChoiceCardItemRacingRallyTemplateBinding {
        return AdapterChoiceCardItemRacingRallyTemplateBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterChoiceCardItemRacingRallyTemplateBinding>,
        item: ChoiceGameInfo
    ) {
        holder.binding.apply {
            // Load image
            GlideUtil.loadImageWithCorner(glide, item.imageUrl, ivGameIcon, 12)

            // Set title
            tvGameTitle.text = item.displayName

            // Set template label
            tvTemplateLabel.text = "Template"

            // Set theme color background if available
            item.themeColor?.let { color ->
                try {
                    val parsedColor = Color.parseColor(color)
                    viewThemeBackground.setBackgroundColor(parsedColor)
                    viewThemeBackground.visibility = View.VISIBLE
                } catch (e: Exception) {
                    viewThemeBackground.visibility = View.GONE
                }
            } ?: run {
                viewThemeBackground.visibility = View.GONE
            }
        }
    }
}


