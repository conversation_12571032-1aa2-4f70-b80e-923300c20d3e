package com.socialplay.gpark.ui.im.groupchat

import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.databinding.ItemGroupChatBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible

interface IItemClickedListener : IBaseEpoxyItemListener {
    fun onMoreClicked(groupChatInfo: GroupChatInfo)
    fun onItemClicked(groupChatInfo: GroupChatInfo)
}

data class GroupChatItem(
    private val info: GroupChatInfo,
    private val isMyGroup: Boolean = false,
    private val itemClickedListener: IItemClickedListener
) : ViewBindingItemModel<ItemGroupChatBinding>(
    R.layout.item_group_chat,
    ItemGroupChatBinding::bind
) {
    override fun ItemGroupChatBinding.onBind() {
        root.setOnClickListener {
            itemClickedListener.onItemClicked(info)
        }
        itemClickedListener.getGlideOrNull()?.run {
            load(info.icon).placeholder(R.drawable.icon_item_group_chat_avatar)
                .into(ivGroupAvatar)
        }
        ivGroupName.text = info.name
        ivGroupMembers.setTextWithArgs(
            R.string.group_chat_item_members_count,
            (info.memberCount ?: 0).toString()
        )
        tvMoreBtn.setOnClickListener {
            itemClickedListener.onMoreClicked(info)
        }
        if (isMyGroup) {
            tvMoreBtn.visible(true)
            tvMoreBtn.setText(R.string.group_item_go_chat)
            tvMoreBtn.setBackgroundResource(R.drawable.bg_ffef30_round_40)
            tvMoreBtn.setTextColorByRes(R.color.color_212121)
        } else {
            tvMoreBtn.visible(true)
            when (info.joinStatus) {
                GroupChatInfo.JOIN_STATUS_CAN_APPLY_JOIN, GroupChatInfo.JOIN_STATUS_CAN_JOIN -> {
                    tvMoreBtn.setText(R.string.group_item_join)
                    tvMoreBtn.setBackgroundResource(R.drawable.bg_ffef30_round_40)
                    tvMoreBtn.setTextColorByRes(R.color.color_212121)
                }

                GroupChatInfo.JOIN_STATUS_JOINED -> {
                    tvMoreBtn.setText(R.string.group_item_joined)
                    tvMoreBtn.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
                    tvMoreBtn.setTextColorByRes(R.color.color_CCCCCC)
                }

                GroupChatInfo.JOIN_STATUS_REQUESTED -> {
                    tvMoreBtn.setText(R.string.group_item_requested)
                    tvMoreBtn.setBackgroundResource(R.drawable.bg_f6f6f6_round_40)
                    tvMoreBtn.setTextColorByRes(R.color.color_CCCCCC)
                }

                else -> {
                    tvMoreBtn.visible(false)
                }
            }
            if (info.joinStatus == GroupChatInfo.JOIN_STATUS_CANT_JOIN) {
                ivGroupAvatar.alpha = 0.5f
                ivGroupName.alpha = 0.5f
                ivGroupMembers.alpha = 0.5f
                tvMoreBtn.alpha = 0.5f
            } else {
                ivGroupAvatar.alpha = 1f
                ivGroupName.alpha = 1f
                ivGroupMembers.alpha = 1f
                tvMoreBtn.alpha = 1f
            }
        }
    }
}