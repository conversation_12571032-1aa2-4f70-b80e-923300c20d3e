package com.socialplay.gpark.ui.account.setting.privacysetting

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.PrivacySetting
import com.socialplay.gpark.databinding.FragmentPrivacySettingBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.share.screenshot.ScreenshotMonitor
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.extension.navigateUp

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 * </pre>
 */
class PrivacySettingFragment :
    BaseRecyclerViewFragment<FragmentPrivacySettingBinding>(R.layout.fragment_privacy_setting) {

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val vm: PrivacySettingViewModel by fragmentViewModel()

    private val itemListener = object : PrivacySettingListener {
        override fun switch(item: PrivacySetting, position: Int) {
            when (item.id) {
                PrivacySetting.SWITCH_TRY_ON -> {
                    if (item.enable) {
                        ConfirmDialog.Builder(this@PrivacySettingFragment)
                            .image(R.drawable.dialog_icon_cry)
                            .content(getString(R.string.disable_try_on_tips))
                            .cancelBtnTxt(getString(R.string.dialog_cancel))
                            .confirmBtnTxt(getString(R.string.text_confirm_uppercase))
                            .confirmCallback {
                                vm.switchSettingStatus(item, position)
                            }
                            .show()
                    } else {
                        vm.switchSettingStatus(item, position)
                    }
                }
                PrivacySetting.SWITCH_MESSAGE_ON -> {
                    Analytics.track(EventConstants.EVENT_SET_IM_PRIVATE_CLOSE_CLICK , mapOf("type" to if(item.enable) 0 else 1))
                    vm.switchSettingStatus(item, position)
                }

                PrivacySetting.SWITCH_SCREENSHOT_SHARE -> {
                    vm.metaKV.appKV.iShowScreenshotPrivacyRedHot = false
                    vm.metaKV.appKV.iShowScreenshotPrivacyRedHotClicked = true
                    if (!item.enable) {
                        val permission =
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
                                && !PermissionRequest.checkSelfPermission(
                                    requireContext(),
                                    *Permission.LOCAL_IMAGE.permissions
                                )
                            ) {
                                Permission.LOCAL_MEDIA
                            } else if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU
                                && !PermissionRequest.checkSelfPermission(
                                    requireContext(),
                                    *Permission.READ_STORAGE.permissions
                                )
                            ) {
                                Permission.READ_STORAGE
                            } else {
                                null
                            }
                        if (permission != null) {
                            PermissionRequest.with(requireActivity())
                                .permissions(permission)
                                .enableGoSettingDialog()
                                .granted {
                                    vm.switchSettingStatus(item, position)
                                    ScreenshotMonitor.instance
                                        .update(enable = true, hasPermission = true)
                                }
                                .denied {
                                    vm.clearRedDot(item, position)
                                }
                                .branch(PermissionRequest.SCENE_SCREENSHOT)
                                .request()
                        } else {
                            vm.metaKV.appKV.userCloseScreenshotShare = true
                            vm.switchSettingStatus(item, position)
                            ScreenshotMonitor.instance
                                .update(enable = true, hasPermission = true)
                        }
                    } else {
                        vm.switchSettingStatus(item, position)
                        ScreenshotMonitor.instance
                            .update(enable = false)
                    }
                }

                PrivacySetting.SWITCH_RELATION_LIST -> {
                    if (item.enable) {
                        vm.switchSettingStatus(item, position)
                    } else {
                        vm.switchSettingStatus(item, position)
                    }
                    Analytics.track(EventConstants.SWITCH_EXPOSE_SOCIAL_LIST) {
                        put("type", if (item.enable) 1 else 0)
                    }
                }

                else -> {
                    vm.switchSettingStatus(item, position)
                }
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPrivacySettingBinding? {
        return FragmentPrivacySettingBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tbl.setOnBackClickedListener {
            navigateUp()
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        PrivacySettingState::settings
    ) { settings ->
        settings.forEachIndexed { index, item ->
            privacySettingItem(item, index, itemListener)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_PRIVACY_SETTINGS
}