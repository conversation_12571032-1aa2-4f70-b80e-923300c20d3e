package com.socialplay.gpark.ui.developer.viewmodel

import androidx.core.text.isDigitsOnly
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meta.pandora.Pandora
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle
import com.socialplay.gpark.function.pandora.DevPandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.ui.developer.bean.PandoraToggleBean
import kotlinx.coroutines.launch
import kotlin.reflect.KClass

/**
 * xingxiu.hou
 * 2021/6/7
 */
class DevPandoraToggleViewModel : ViewModel() {

    private val _pandoraToggleLivedata = MutableLiveData<List<PandoraToggleBean>>()
    val pandoraToggleLivedata: LiveData<List<PandoraToggleBean>> = _pandoraToggleLivedata

    private val _pandoraToggleStatusLivedata = MutableLiveData<Boolean>()
    val pandoraToggleStatusLivedata: LiveData<Boolean> = _pandoraToggleStatusLivedata

    fun getPandoraToggleStatus() {
        _pandoraToggleStatusLivedata.postValue(DeveloperPandoraToggle.isEnable())
    }

    fun requestPandoraToggleList() {
        viewModelScope.launch {
            val list = PandoraToggle.javaClass.declaredFields + PandoraToggleWrapper.javaClass.declaredFields
            val result = list.flatMap { field ->
                field.declaredAnnotations.mapNotNull {
                    if (it is DevPandoraToggle) {
                        val targetObject = if (field.declaringClass == PandoraToggle.javaClass) {
                            PandoraToggle
                        } else {
                            PandoraToggleWrapper
                        }
                        val key = kotlin.runCatching {
                            field.isAccessible = true
                            field.get(targetObject).toString()
                        }.getOrElse { "" }
                        val value = DeveloperPandoraToggle.getValue(key, it.defValue) ?: it.defValue
                        val onlineValue =
                            kotlin.runCatching { Pandora.getAbConfig(key, "") }
                                .getOrElse { ex -> ex.message ?: "Exception" }
                        PandoraToggleBean(
                            key,
                            it.name,
                            it.desc,
                            value,
                            onlineValue,
                            it.selectArray
                        ).apply {
                            valueType = getType(it.defValue)
                        }
                    } else {
                        null
                    }
                }
            }
            _pandoraToggleLivedata.postValue(result)
        }
    }

    private fun getType(value: String): KClass<*> {
        if (arrayOf("TRUE", "FALSE").any { it.equals(value, true) }) {
            return Boolean::class
        }
        if (value.isDigitsOnly()) {
            return Number::class
        }
        if (value.matches(Regex("^[-\\+]?[.\\d]*$"))) {
            return Float::class
        }
        return String::class
    }

}