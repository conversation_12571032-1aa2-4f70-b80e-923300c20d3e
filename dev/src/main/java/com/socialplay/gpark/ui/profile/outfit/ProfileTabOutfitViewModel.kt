package com.socialplay.gpark.ui.profile.outfit

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.RoleStyle
import com.socialplay.gpark.data.model.editor.RoleStyleRefreshEvent
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.addAllImmutable
import com.socialplay.gpark.util.extension.dropAtWithResult
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.replaceAt
import com.socialplay.gpark.util.extension.unregisterHermes
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/04/02
 *     desc   :
 * </pre>
 */
data class ProfileTabOutfitState(
    val isMe: Boolean,
    val otherUuid: String,
    val styles: Async<List<RoleStyle>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val beginIndex: Int = 0,
    val toast: ToastData = ToastData.EMPTY,
) : MavericksState

class ProfileTabOutfitViewModel(
    initialSate: ProfileTabOutfitState,
    private val repo: IMetaRepository,
    val accountInteractor: AccountInteractor
) : BaseViewModel<ProfileTabOutfitState>(initialSate) {

    private val styleSet = HashSet<String>()

    init {
        registerHermes()
        getClothesList(true)
    }

    fun getClothesList(isRefresh: Boolean, force: Boolean = false) = withState { s ->
        if (!force && (s.styles is Loading || s.loadMore is Loading)) return@withState
        val beginIndex = if (isRefresh) {
            0
        } else {
            s.beginIndex
        }
        repo.getRoleStyleList(s.isMe, s.otherUuid, beginIndex, PAGE_SIZE).map {
            val rawList = it.styleViewList
            if (isRefresh) {
                styleSet.clear()
            }
            val filteredList = rawList?.filter { item -> styleSet.add(item.style.styleId) }
            val newList = if (isRefresh) {
                filteredList
            } else {
                val oldList = oldState.styles.invoke()
                oldList.addAllImmutable(filteredList)
            }.orEmpty()
            val newBeginIndex = beginIndex + PAGE_SIZE
            val isEnd = (rawList?.size ?: 0) < PAGE_SIZE
            Triple(newList, newBeginIndex, isEnd)
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (newStyles, newBeginIndex, newIsEnd) = result.invoke()
                    copy(
                        styles = Success(newStyles),
                        loadMore = Success(LoadMoreState(isEnd = newIsEnd)),
                        beginIndex = newBeginIndex
                    )
                }

                is Fail -> {
                    copy(
                        styles = if (isRefresh) Fail(result.error, styles.invoke()) else styles,
                        loadMore = Fail(result.error)
                    )
                }

                else -> {
                    copy(
                        styles = if (isRefresh) Loading(styles.invoke()) else styles,
                        loadMore = Loading()
                    )
                }
            }

        }
    }

    fun likeStyle(item: RoleStyle, position: Int) {
        val oldList = oldState.styles.invoke() ?: return

        val newItem = item.switchLike()
        val newList = oldList.replaceAt(position, newItem)
        setState { copy(styles = styles.copyEx(newList)) }

        val newIsLike = newItem.isLike
        repo.likeRoleStyle(item.style.styleId, newIsLike).execute { this }
    }

    fun deleteStyle(item: RoleStyle, position: Int) {
        repo.deleteRoleStyle(item.style.styleId).map {
            assert(it.status)
            val oldList = oldState.styles.invoke() ?: return@map null
            val (ok, _, newList) = oldList.dropAtWithResult(position, item)
            if (!ok) return@map null
            styleSet.remove(item.style.styleId)
            newList
        }.execute { result ->
            when (result) {
                is Success -> {
                    val data = result.invoke()
                    if (data != null) {
                        copy(styles = styles.copyEx(data))
                    } else {
                        this
                    }
                }

                is Fail -> {
                    copy(toast = toast.toError(result))
                }

                else -> this
            }
        }
    }

    fun updateUuid(newUuid: String) = withState { s ->
        if (s.otherUuid == newUuid) return@withState
        setState { copy(otherUuid = newUuid) }
        getClothesList(true, force = true)
    }


    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }

    @Subscribe
    fun onEvent(refreshEvent: RoleStyleRefreshEvent) = withState { s ->
        if (s.isMe) {
            getClothesList(true)
        }
    }

    companion object : KoinViewModelFactory<ProfileTabOutfitViewModel, ProfileTabOutfitState>() {

        const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ProfileTabOutfitState
        ): ProfileTabOutfitViewModel {
            return ProfileTabOutfitViewModel(state, get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): ProfileTabOutfitState {
            val args = viewModelContext.args as ProfileTabOutfitFragmentArgs
            return ProfileTabOutfitState(
                args.isMe,
                args.otherUuid
            )
        }
    }
}