package com.socialplay.gpark.ui.post.v2

import android.os.Bundle
import android.os.Parcelable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.RequestManager
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.google.android.material.appbar.CustomAppBarBehavior
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.MomentLocalTSStartUp
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.video.PlayerVideoResource
import com.socialplay.gpark.data.model.video.VideoFeedShowInfo
import com.socialplay.gpark.databinding.FragmentPostDetailV2Binding
import com.socialplay.gpark.databinding.PopUpPostDetailBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.EventParamConstants.IMG_PRE_FROM_POST_DETAIL
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.community.FeedImageVideoUtil
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.exoplayer.VideoPlayerCacheInteractor
import com.socialplay.gpark.function.mw.TSLaunchWrapper
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchOptionAppendTsStartUp
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.ui.post.PostHelper
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.DateUtil.formatCreateDate
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.doAfterTextChanged
import com.socialplay.gpark.util.extension.doOnFocusChange
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.registerAdapterDataObserver
import com.socialplay.gpark.util.extension.setHintWithArgs
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.sp
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.extension.visiblePercent
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.parcelize.Parcelize
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.math.max

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/26
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostDetailFragmentArgs(
    val postId: String,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val videoProgress: Long,
    val source: String?,
    val fromTagDetailInfo: PostTag?
) : Parcelable

class PostDetailFragment :
    BaseFragment<FragmentPostDetailV2Binding>(R.layout.fragment_post_detail_v2), Player.Listener,
    IGlobalShareCallback {

    companion object {
        const val KEY_ACTION_POST = "key_action_post"
        const val KEY_PARAM_POST_ID = "key_post_id"
        const val KEY_PARAM_IS_DELETE = "key_is_delete" // 是否已删除
        const val KEY_PARAM_OPINION = "key_opinion" // 观点
        const val KEY_PARAM_LIKE_COUNT = "key_like_count" // 点赞数
        const val KEY_PARAM_COMMENT_COUNT = "key_comment_count" // 评论数
        const val KEY_PARAM_SHARE_COUNT = "key_share_count" // 分享数

        const val FEAT_DELETE = 1
        const val FEAT_REPORT = 2
        const val FEAT_EDIT = 3
    }

    private val vm: PostDetailViewModel by fragmentViewModel()
    private val args: PostDetailFragmentArgs by args()

    private val videoCacheInteractor: VideoPlayerCacheInteractor by inject()

    private var commentAuthorHeight = 0

    private var blueColor = 0

    private val dp10 = 10.dp
    private val dp14 = 14.dp
    private val dp16 = 16.dp
    private val dp26 = 26.dp
    private val dp28 = 28.dp

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpPostDetailBinding.inflate(layoutInflater) }

    private val mediaController by lazy { buildMediaController() }
    private val gameController by lazy { buildGameController() }

    //    private val tagController by lazy { buildTagController() }
    private val commentController by lazy { buildCommentController() }
    private val tsLaunchWrapper by lazy { TSLaunchWrapper(this) }

    private val friendInteractor by inject<FriendInteractor>()

    private var isDelete = false

    private var mark = 0L
    private var inputFocus = false
    private var sending = false

    private var videoProgress = -1L
    private var autoPlayed = false
    private var videoTime = 0L

    override val destId: Int = R.id.post_detail

    private val itemListener = object : IPostDetailListener {
        override fun openMedia(media: PostMediaResource, position: Int) {
            if (media.isImage) {
                val imageUrls = withState(vm) { it.detail.invoke()?.imageUrls }
                if (!imageUrls.isNullOrEmpty()) {
                    Analytics.track(EventConstants.EVENT_POST_VIEW_PICTURE)
                    ImgPreDialogFragment.show(
                        requireActivity(),
                        imageUrls,
                        IMG_PRE_FROM_POST_DETAIL,
                        position,
                        true
                    )
                }
            } else if (media.isVideo) {
                val playerController by inject<SharedVideoPlayerControllerInteractor>()
                val toRestoreUri = MediaItem.fromUri(media.resourceValue)
                playerController.setMediaItem(toRestoreUri)
                playerController.changeMuteState(false)
                MetaRouter.Video.fullScreenPlayer(
                    this@PostDetailFragment,
                    "post_detail",
                    bundleOf("postId" to args.postId)
                )
            }
        }

        override fun openGame(game: PostCardInfo) {
            CommunityUtil.goGameDetail(
                this@PostDetailFragment,
                game,
                CategoryId.COMMUNITY_GAME_CARD
            )
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@PostDetailFragment, uid, "postDetail")
            }
        }

        override fun operateComment(view: View, comment: PostComment, commentPosition: Int) {
            vm.setMoreId(commentId = comment.commentId)
            bindPopUpClick(getString(R.string.delete_confirm, getString(R.string.comment)), {
                vm.deleteComment(comment, commentPosition)
            }) {
                vm.reportComment(comment)
            }
            showPopUp(sort = false, showEdit = false, uid = comment.uid, view = view) {
                -popupBinding.cv.measuredWidth to -(8).dp
            }
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            vm.likeComment(comment, commentPosition)
            val isLike = !comment.isLike
            if (isLike) {
                DialogShowManager.triggerLike(this@PostDetailFragment)
            }
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyInput(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int
        ) {
            vm.setMoreId(replyId = reply.replyId)
            bindPopUpClick(getString(R.string.delete_confirm, getString(R.string.reply)), {
                vm.deleteReply(reply, replyPosition, reply.commentId, commentPosition)
            }) {
                vm.reportReply(reply)
            }
            showPopUp(sort = false, showEdit = false, uid = reply.uid, view = view) {
                -popupBinding.cv.measuredWidth to -(8).dp
            }
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            showReplyInput(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(replyId: String, commentId: String, commentPosition: Int) {
            vm.loadMoreReplies(replyId, commentId, commentPosition)
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@PostDetailFragment, data)
        }

        override fun isMe(uuid: String?): Boolean {
            return vm.isMe(uuid)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPostDetailV2Binding? {
        return FragmentPostDetailV2Binding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            PostDetailState::detail
        )
        mediaController.onRestoreInstanceState(savedInstanceState)
        gameController.onRestoreInstanceState(savedInstanceState)
//        tagController.onRestoreInstanceState(savedInstanceState)
        commentController.onRestoreInstanceState(savedInstanceState)
        commentController.setFilterDuplicates(true)
    }

    override fun onResume() {
        super.onResume()
        if (inputFocus) {
            binding.etComment.requestFocusFromTouch()
            InputUtil.showSoftBoard(binding.etComment)
        }
        playVideo()
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.etComment)
        pauseVideo()
        super.onPause()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mediaController.onSaveInstanceState(outState)
        gameController.onSaveInstanceState(outState)
//        tagController.onSaveInstanceState(outState)
        commentController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (!PandoraToggle.enablePublishSetupAccount
            || GlobalContext.get().get<AccountInteractor>().hasBindAccPwd
        ) {
            binding.vEtCommentCover.gone()
        } else {
            binding.vEtCommentCover.visible()
            binding.vEtCommentCover.setOnAntiViolenceClickListener {
                AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
                    if (it && isBindingAvailable()) {
                        binding.vEtCommentCover.gone()
                        vm.delayTask(PostDetailViewModel.TASK_INVOKE_COMMENT_INPUT, 300L)
                    }
                }
            }
        }

        commentAuthorHeight = maxOf(36.dp, 39.sp)
        blueColor = view.getColorByRes(R.color.color_4AB4FF)

        initPopUp()

        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            if (inputFocus) {
                hideReplyInput()
            } else {
                navigateUp()
            }
        }
        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, _ ->
            if (!autoPlayed) {
                playVideo()
            }
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.lv.setRetry {
            vm.initData()
        }
        binding.ivMore.setOnAntiViolenceClickListener {
            showMoreDialog(true)
        }
        binding.vAuthorClick.setOnAntiViolenceClickListener {
            withState(vm) { s ->
                s.detail.invoke()?.also { post ->
                    if (post.canGoRoom) {
                        CommunityUtil.showRoomDialog(
                            this,
                            post.userStatus!!,
                            post.uid.orEmpty(),
                            post.nickname,
                            CategoryId.COMMUNITY_GAME_CARD
                        )
                    } else {
                        itemListener.goUserPage(post.uid)
                    }
                }
            }
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.followUser()
        }
        binding.tvCommentSortHang.setOnAntiViolenceClickListener {
            withState(vm) { s ->
                if (s.loadMore !is Loading) {
                    showPopUp(sort = true, showEdit = false, uid = null, view = it) {
                        val dx = popupBinding.cv.measuredWidth + 32.dp - it.measuredWidth
                        -dx to -dp14
                    }
                }
            }
        }
        binding.vLikeClick.setOnAntiViolenceClickListener {
            vm.likePost()
            val isLike = !OpinionRequestBody.isLike(vm.oldState.opinion)
            if (isLike) {
                DialogShowManager.triggerLike(this@PostDetailFragment)
            }
        }
        binding.vShareClick.setOnAntiViolenceClickListener {
            showMoreDialog(false)
        }
        binding.etComment.doOnFocusChange(viewLifecycleOwner) { _, hasFocus ->
            if (hasFocus && !inputFocus) {
                inputFocus = true
                binding.vCommentCover.visible()
                val oldState = vm.oldState
                if (oldState.replyTarget == null) {
                    val target = AddPostCommentReplyTarget(args.postId, oldState.authorName)
                    vm.updateReplyTarget(target) {
                        mark = target.mark
                    }
                }
            } else if (!hasFocus && inputFocus) {
                inputFocus = false
                sending = false
                binding.vCommentCover.gone()
                binding.etComment.text = null
                binding.etComment.setHint(R.string.post_reply)
            }
        }
        binding.etComment.doAfterTextChanged(viewLifecycleOwner) {
            vm.updateReplyContent(it?.toString())
        }
        binding.tvSendBtn.setOnAntiViolenceClickListener {
            AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
                if (it && isBindingAvailable()) {
                    sending = true
                    binding.tvSendBtn.enableWithAlpha(false)
                    vm.checkUserMuteStatus()
                }
            }
        }
        binding.vCommentCover.setOnClickListener {
            hideReplyInput()
        }
        binding.includeOutfit.root.setOnAntiViolenceClickListener {
            vm.visitOutfitCard()?.let { outfit ->
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(),
                    FullScreenEditorActivityArgs(
                        categoryId = CategoryId.OUTFIT_SHARE_POST_DETAIL,
                        tryOn = RoleGameTryOn.create(
                            tryOnUserId = vm.detail?.uid.orEmpty(),
                            from = RoleGameTryOn.FROM_COMMUNITY_POST,
                            roleId = outfit.roleId,
                            allowTryOn = true
                        )
                    )
                )
            }
        }
        binding.includeUgcDesign.root.setOnAntiViolenceClickListener {
            vm.ugcDesign?.let {
                MetaRouter.UgcDesign.detail(this, it.itemId, CategoryId.UGC_DESIGN_POST_DETAIL)
            }
        }
        binding.ivOfficial.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }

        binding.rvImg.layoutManager = GridLayoutManager(requireContext(), 6)
        binding.rvImg.setController(mediaController)
        binding.rvGame.layoutManager = LinearLayoutManager(requireContext())
        binding.rvGame.setController(gameController)
//        binding.rvTopic.layoutManager = FlexboxLayoutManager(requireContext())
//        binding.rvTopic.setController(tagController)
        binding.rvComment.layoutManager = LinearLayoutManager(requireContext())
        binding.rvComment.setController(commentController)
        commentController.adapter.registerAdapterDataObserver(
            viewLifecycleOwner,
            object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    if (positionStart == 0) {
                        binding.rvComment.scrollToPosition(0)
                    }
                }
            }
        )
        //接口请求在ViewMode的Init方法调用，这个认为首次调用ViewModel的地方是接口请求的开始
        vm.registerToast(PostDetailState::errorToast)
        var trackShow = false
        vm.onAsync(PostDetailState::detail) {
            val isMe = vm.isMe(it.uid)
            glide?.run {
                load(it.avatar).error(R.drawable.placeholder)
                    .into(binding.ivAuthorAvatar)
            }
            binding.tvAuthorName.text = it.nickname
            binding.tvTime.text = it.createTime.formatCreateDate(requireContext())
            binding.tvContent.movementMethod =
                InterceptClickEventLinkMovementMethod(binding.tvContent)
            binding.tvContent.text = CommunityUtil.parsePostContent(
                requireContext(),
                it.content,
                it.tagList,
                it.communityTagList,
            ) {
                goTopicDetail(it, args.postId)
            }
            binding.ivMore.visible()
            if (it.mediaList.isNullOrEmpty()) {
                binding.vplv.gone()
                binding.rvImg.gone()
            } else {
                initMedia(it.mediaList)
            }
            binding.rvGame.gone(it.gameCardList.isNullOrEmpty())

            val moment = it.plotCardList?.firstOrNull()
            if (moment != null) {
                glide?.run {
                    load(moment.materialUrl).centerCrop()
                        .placeholder(R.drawable.placeholder_corner_8)
                        .into(binding.includeMomentTake.ivOutfitThumbnail)
                }
                binding.includeMomentTake.tvOutfitOwner.text = moment.templateName
                binding.includeMomentTake.root.visible()
                binding.includeMomentTake.tvOutfitTryOn.setOnClickListener {
                    tsLaunchWrapper.callLaunch {
                        val info = createTSGameDetailInfo(
                            moment.gameId ?: "",
                            "",
                            moment.templateName ?: ""
                        )
                        val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.PLOT_PAGE)
                            .setGameId(moment.gameId).setTypeID("${moment.templateId}")
                        val tsLaunchParams = TSLaunchParams(
                            info,
                            resIdBean,
                            option = TSLaunchOptionAppendTsStartUp()
                        )
                        tsLaunchParams.tsStartUp = moment.extendParams ?: ""
                        tsLaunchParams.localTsStartUp =
                            MomentLocalTSStartUp("3", "${moment.templateId}").toMap()
                        tsLaunchParams.setGameUniqueKey("${moment.gameId}_${moment.templateId}")
                        launchPlot(requireContext(), tsLaunchParams)
                    }
                }
            } else {
                binding.includeMomentTake.root.gone()
            }

            val outfit = it.outfit
            if (outfit != null) {
                binding.includeOutfit.root.visible()
                glide?.run {
                    load(outfit.wholeBodyImage).placeholder(R.drawable.avatar_friend_placeholder)
                        .into(binding.includeOutfit.ivOutfitThumbnail)
                }
                binding.includeOutfit.tvOutfitOwner.setTextWithArgs(
                    R.string.s_outfit,
                    it.nickname.orEmpty()
                )
                binding.includeOutfit.tvOutfitPv.setTextWithArgs(
                    R.string.tried_on,
                    outfit.pvStr
                )
            } else {
                binding.includeOutfit.root.gone()
            }

            val ugcDesign = it.ugcDesign
            if (ugcDesign != null) {
                binding.includeUgcDesign.root.visible()
                glide?.run {
                    load(ugcDesign.cover).into(binding.includeUgcDesign.ivOutfitThumbnail)
                }
                binding.includeUgcDesign.tvOutfitDesc.text =
                    ugcDesign.title.ifNullOrEmpty { getString(R.string.fashion_design) }
            } else {
                binding.includeUgcDesign.root.gone()
            }

            binding.ivOfficial.show(
                it.tagIds,
                it.labelInfo ?: it.user?.labelInfo,
                isOfficial = it.user?.isOfficial == true,
                glide = glide
            )
            val gameStatus = it.userStatus.toLocalStatus()
            binding.ivState.visible((it.canGoRoom && it.userStatus != null) || (gameStatus == FriendStatus.ONLINE || gameStatus == FriendStatus.PLAYING_GAME))
            visibleList(binding.spaceFollow, binding.tvFollowBtn, visible = !isMe)
            PostHelper.updateReviewStatus(binding.tvReviewStatusLabel, it.status)
            binding.vRedDotMore.visible(isMe && it.reviewFail && vm.showEditRedDot)
            binding.lv.hide()
            if (!trackShow) {
                trackShow = true
                trackShow(null)
                if (outfit != null) {
                    Analytics.track(
                        EventConstants.POST_LIST_OUTFIT_SHOW,
                        "resid" to it.postId,
                        "shareid" to outfit.roleId,
                        "tag" to it.tagList?.map { tag -> tag.tagId }?.joinToString(",").orEmpty(),
                        "source" to "1"
                    )
                }
            }
            vm.initData()
        }
        vm.onEach(PostDetailState::followStatus) {
            if (it) {
                binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_f0f0f0_corner_24)
                binding.tvFollowBtn.setText(R.string.following_cap)
            } else {
                binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_ffef30_round_24)
                binding.tvFollowBtn.setText(R.string.follow)
            }
        }
        vm.onEach(PostDetailState::opinion, PostDetailState::likeCount) { opinion, likeCount ->
            if (OpinionRequestBody.isLike(opinion)) {
                binding.ivLikeCount.setImageResource(R.drawable.icon_post_like_selected)
                binding.tvLikeCount.setTextColorByRes(R.color.textColorPrimary)
            } else {
                binding.ivLikeCount.setImageResource(R.drawable.icon_post_like_unselected)
                binding.tvLikeCount.setTextColorByRes(R.color.textColorSecondary)
            }
            binding.tvLikeCount.text = UnitUtil.formatKMCount(likeCount)
        }
        vm.onEach(PostDetailState::commentCount) {
            val commentCount = UnitUtil.formatKMCount(it)
            binding.tvCommentCount.text = commentCount
            binding.tvCommentCountHang.setTextWithArgs(R.string.comment_count, commentCount)
        }
        vm.onEach(PostDetailState::shareCount) {
            binding.tvShareCount.text = UnitUtil.formatKMCount(it)
        }
        vm.onEach(PostDetailState::queryType) {
            val resId = when (it) {
                PostCommentListRequestBody.QUERY_TYPE_LIKE -> R.string.likes_sort
                else -> R.string.recent_reply
            }
            binding.tvCommentSortHang.setText(resId)
        }
        vm.onEach(PostDetailState::deletePostResult) {
            it ?: return@onEach
            isDelete = true
            navigateUp()
        }
        vm.onEach(PostDetailState::reportData, uniqueOnly()) {
            it ?: return@onEach
            MetaRouter.Report.postReport(this, it.second, it.first) { submit ->
                if (submit) {
                    val analyticsParams = if (it.first == ReportType.Post) {
                        ReportSuccessDialogAnalyticsParams.Post(
                            postId = it.second,
                        )
                    } else if (it.first == ReportType.PostComment) {
                        ReportSuccessDialogAnalyticsParams.PostComment(
                            postId = args.postId,
                            commentId = it.second,
                        )
                    } else if (it.first == ReportType.PostReply) {
                        ReportSuccessDialogAnalyticsParams.PostCommentReply(
                            postId = args.postId,
                            replyId = it.second,
                        )
                    } else {
                        null
                    }
                    ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
                }
            }
        }
        vm.onEach(PostDetailState::detail, PostDetailState::replyTarget) { post, target ->
            if (target == null) return@onEach
            val nickname = when {
                target.isTargetPost -> {
                    post.invoke()?.nickname
                }

                target.isTargetComment -> {
                    target.asComment.nickname
                }

                else -> {
                    target.asReply.nickname
                }
            }
            binding.etComment.setHintWithArgs(R.string.replying_at, nickname.orEmpty())
        }
        vm.onEach(PostDetailState::addReplyResult, uniqueOnly()) {
            if (it == null || it.mark != mark) return@onEach
            if (it.result) {
                hideReplyInput()
            } else {
                binding.tvSendBtn.enableWithAlpha(true)
            }
            sending = false
        }
        vm.onEach(PostDetailState::replyContent) {
            if (it.isNullOrEmpty()) {
                binding.tvSendBtn.invisible()
                binding.etComment.setPaddingEx(right = dp16)
            } else {
                binding.tvSendBtn.visible()
                binding.tvSendBtn.enableWithAlpha(!sending)
                binding.etComment.setPaddingEx(
                    right = max(
                        binding.tvSendBtn.measuredWidth,
                        binding.tvSendBtn.width
                    )
                )
            }
        }
        vm.onEach(PostDetailState::muteStatus, uniqueOnly()) { it ->
            if (!it.complete) return@onEach
            if (it.invoke()?.isMuted == true) {
                sending = false
                toast(getString(R.string.community_ban, getString(R.string.commenting)))
                binding.tvSendBtn.enableWithAlpha(true)
            } else {
                if (vm.oldState.needPermissionDialog) {
                    Timber.d("needPermissionDialog")
                    binding.tvSendBtn.enableWithAlpha(true)
                    vm.updatePermissionDialog()
                    showPermissionDialog()
                } else {
                    vm.addCommentReply()
                }
            }
        }
        vm.onEach(PostDetailState::delayTask, deliveryMode = uniqueOnly()) {
            when (it?.first) {
                PostDetailViewModel.TASK_INVOKE_COMMENT_INPUT -> {
                    binding.etComment.requestFocusFromTouch()
                    InputUtil.showSoftBoard(binding.etComment)
                }
            }
        }
        vm.setupRefreshLoadingCustomFail(
            PostDetailState::detail,
            binding.lv,
            binding.refreshLayout,
            onFail = { e, _ ->
                if ((e as? ApiResultCodeException)?.code == 1016) {
                    binding.lv.showEmpty(
                        msg = getString(R.string.post_not_exist),
                        resId = R.drawable.icon_no_recent_activity
                    )
                } else {
                    binding.lv.showError()
                }
                binding.ivMore.gone()
            }
        ) {
            vm.initData(isRefresh = true)
        }
        vm.checkPermission(requireContext())

        friendInteractor.friendList.observe(viewLifecycleOwner) { friendList ->
            if (friendList.isNullOrEmpty()) {
                return@observe
            }
            vm.updateFriendState(friendList)
        }
    }

    private fun showPermissionDialog() {
        Analytics.track(EventConstants.EVENT_INTERACT_PUS_POST_SHOW)
        NotificationPermissionManager.showPermissionDialog(
            this,
            title = getString(R.string.notification_im_title),
            content = getString(R.string.notification_im_content),
            cancelText = getString(R.string.notification_app_cancel),
            confirmText = getString(R.string.notification_app_sure),
            dismissCallBack = {

            }, confirmCallBack = { resut ->
                if (resut) {
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        "result" to "0"
                    )
                } else {
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        "result" to "1"
                    )
                }
            }
        )


    }

    private fun goTopicDetail(tagInfo: PostTag, postId: String) {
        MetaRouter.Post.topicDetail(
            this,
            tagInfo,
            EventParamConstants.SOURCE_TOPIC_POST_DETAIL,
            postId
        )
    }

    private fun initMedia(medias: List<PostMediaResource>) {
        val video = medias.firstOrNull { media -> media.isVideo }
        if (video == null) {
            binding.rvImg.visible()
            binding.vplv.gone()
        } else {
            binding.rvImg.gone()
            binding.vplv.apply {
                visible()
                val videoPath = video.resPath
                currentFragment = this@PostDetailFragment
                FeedImageVideoUtil.setVideoSize(
                    null,
                    this,
                    video,
                    videoPath,
                    ScreenUtil.screenWidth - 32.dp,
                    glide
                )
                setDataResource(PlayerVideoResource(videoPath, videoPath))
                setOnClickFullScreen {
                    itemListener.openMedia(video, 0)
                }
                setOnVideoResume {
                    onVideoStateChange(true)
                }
                setOnVideoPause {
                    onVideoStateChange(false)
                }
                val newPlayer = SimpleExoPlayer.Builder(requireContext())
                    .setMediaSourceFactory(videoCacheInteractor.mediaSourceFactory)
                    .setLoadControl(FeedVideoHelper.fetchLodeControl(requireContext()))
                    .build()
                    .apply {
                        repeatMode = Player.REPEAT_MODE_OFF
                        volume = if (FeedVideoHelper.feedMuted) 0f else 1f
                        addListener(viewLifecycleOwner, this@PostDetailFragment)
                    }
                changeMuteView(FeedVideoHelper.feedMuted)
                addPlayer(newPlayer)
                if (videoProgress == -1L) {
                    videoProgress = args.videoProgress
                }
                setProgress(videoProgress)
                onActive(autoPlay = false)
            }
            initScrollCallback {
                playVideo()
            }
        }
    }

    private fun onVideoStateChange(resume: Boolean) {
        if (resume) {
            videoTime = System.currentTimeMillis()
        } else {
            val duration = System.currentTimeMillis() - videoTime
            if (duration > 0) {
                Analytics.track(
                    EventConstants.EVENT_POST_VIEW_VIDEO,
                    "playtime" to duration,
                    "postid" to args.postId
                )
            }
        }
    }

    private fun initPopUp() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupWindow.setOnDismissListener {
            popupBinding.tvDelete.unsetOnClick()
            popupBinding.tvReport.unsetOnClick()
        }
        popupBinding.tvRecentReply.setOnAntiViolenceClickListener {
            vm.setQueryType(PostCommentListRequestBody.QUERY_TYPE_TOP)
            popupWindow.dismiss()
        }
        popupBinding.tvLikeSort.setOnAntiViolenceClickListener {
            vm.setQueryType(PostCommentListRequestBody.QUERY_TYPE_LIKE)
            popupWindow.dismiss()
        }
        popupBinding.vEditClick.setOnAntiViolenceClickListener {
            popupWindow.dismiss()
            vm.showEditRedDot = false
            vm.detail?.let {
                Analytics.track(
                    EventConstants.POST_EDIT_CLICK,
                    EventParamConstants.KEY_POSTID to it.postId.orEmpty(),
                    EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                )
                MetaRouter.Post.goPublishPost(
                    this,
                    ResIdBean().setCategoryID(CategoryId.OUTFIT_SHARE_POST_DETAIL),
                    showRule = false,
                    postPublish = it.toPostPublish()
                )
            }
        }
    }

    private fun bindPopUpClick(deleteTitle: String, onDelete: () -> Unit, onReport: () -> Unit) {
        popupBinding.tvDelete.setOnAntiViolenceClickListener {
            vm.detail?.let {
                Analytics.track(
                    EventConstants.POST_DELETE_CLICK,
                    EventParamConstants.KEY_POSTID to it.postId,
                    EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                )
            }
            ConfirmDialog.Builder(this)
                .content(deleteTitle)
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .confirmCallback {
                    vm.detail?.let {
                        Analytics.track(
                            EventConstants.POST_DELETE_CONFIRM,
                            EventParamConstants.KEY_POSTID to it.postId,
                            EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                        )
                    }
                    onDelete()
                }
                .show()
            popupWindow.dismiss()
        }
        popupBinding.tvReport.setOnAntiViolenceClickListener {
            onReport()
            popupWindow.dismiss()
        }
    }

    private fun showPopUp(
        sort: Boolean,
        showEdit: Boolean,
        uid: String?,
        view: View,
        coordinate: () -> Pair<Int, Int>
    ) {
        if (sort) {
            popupBinding.llSort.visible()
            popupBinding.llSelf.gone()
            popupBinding.tvReport.gone()
        } else if (vm.isMe(uid)) {
            popupBinding.llSort.gone()
            popupBinding.llSelf.visible()
            popupBinding.vDivider2.visible(showEdit)
            popupBinding.tvEdit.visible(showEdit)
            popupBinding.vEditClick.visible(showEdit)
            popupBinding.vRedDotEdit.visible(showEdit && vm.showEditRedDot)
            popupBinding.tvReport.gone()
        } else {
            popupBinding.llSort.gone()
            popupBinding.llSelf.gone()
            popupBinding.tvReport.visible()
        }
        popupBinding.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val (x, y) = coordinate()
        popupWindow.showAsDropDownByLocation(view, x, y, autoHeight = false)
    }

    private fun showReplyInput(target: AddPostCommentReplyTarget) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
            if (it && isBindingAvailable()) {
                vm.updateReplyTarget(target) {
                    mark = target.mark
                }
                binding.etComment.requestFocusFromTouch()
                InputUtil.showSoftBoard(binding.etComment)
            }
        }
    }

    private fun hideReplyInput() {
        vm.clearReplyTarget()
        InputUtil.hideKeyboard(binding.etComment)
        binding.etComment.clearFocus()
    }

    private fun playVideo() {
        binding.vplv.getPlayer()?.run {
            if (binding.vplv.visiblePercent() > VideoFeedShowInfo.VIDEO_CAN_PLAY_SHOW_PERCENT) {
                autoPlayed = true
                play()
            } else {
                pause()
            }
        }

    }

    private fun pauseVideo() {
        binding.vplv.getPlayer()?.run {
            pause()
            videoProgress = currentPosition
        }
    }

    private fun clearVideo() {
        autoPlayed = false
        binding.vplv.getPlayer()?.run {
            removeListener(this@PostDetailFragment)
            binding.vplv.inActive()
            binding.vplv.removePlayer()
            release()
        }
    }

    private fun initScrollCallback(listener: (() -> Unit)?) {
        binding.cdl.stopScrollCallback = listener
        ((binding.abl.layoutParams as CoordinatorLayout.LayoutParams).behavior as? CustomAppBarBehavior)?.onFlingFinishedCallback =
            listener
    }

    override fun onDeviceVolumeChanged(volume: Int, muted: Boolean) {
        super.onDeviceVolumeChanged(volume, muted)
        binding.vplv.getPlayer()?.run {
            if (isPlaying) {
                FeedVideoHelper.feedMuted = false
                this.volume = 1F
                binding.vplv.changeMuteView(false)
            }
        }
    }

    private fun showMoreDialog(needFeature: Boolean) {
        val detail = vm.detail ?: return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.post(detail),
            features = if (needFeature) {
                buildList {
                    if (vm.isMe(detail.uid)) {
                        add(
                            ShareFeature(
                                FEAT_DELETE,
                                R.drawable.ic_share_feat_delete,
                                titleRes = R.string.delete_cap
                            )
                        )
                        if (detail.reviewFail) {
                            add(
                                ShareFeature(
                                    FEAT_EDIT,
                                    R.drawable.ic_share_feat_edit,
                                    titleRes = R.string.edit,
                                    showRedDot = vm.showEditRedDot
                                )

                            )
                        }
                    } else {
                        add(
                            ShareFeature(
                                FEAT_REPORT,
                                R.drawable.ic_share_feat_report,
                                titleRes = R.string.report
                            )
                        )
                    }
                }
            } else {
                null
            }
        )
    }

    private fun buildMediaController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        val medias = it.invoke()?.mediaList ?: return@simpleController
        if (medias.any { media -> media.isVideo }) return@simpleController
        val size = medias.size
        medias.forEachIndexed { index, media ->
            postMediaItem(media, index, size, itemListener)
        }
    }

    private fun buildGameController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        it.invoke()?.gameCardList?.forEach { game ->
            postGameItem(game, dp10, true, itemListener)
        }
    }

    private fun buildTagController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        it.invoke()?.tagList?.forEach { tag ->
            postTagItem(tag, dp14, false, blueColor, itemListener)
        }
    }

    private fun buildCommentController() = simpleController(
        vm,
        PostDetailState::commentList,
        PostDetailState::commentListRefreshTime,
        PostDetailState::loadMore,
        PostDetailState::moreCommentId,
        PostDetailState::moreReplyId
    ) { commentList, commentListRefreshTime, loadMore, moreCommentId, moreReplyId ->
        if (commentList is Success) {
            val commentListData = commentList.invoke()
            val commentListSize = commentListData.size
            val lastCommentPosition = commentListSize - 1
            commentListData.forEachIndexed { commentPosition, comment ->
                postCommentItem(
                    comment,
                    commentListRefreshTime = commentListRefreshTime,
                    commentPosition,
                    commentAuthorHeight,
                    comment.commentId == moreCommentId,
                    itemListener
                )
                comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                    postReplyItem(
                        reply,
                        replyPosition,
                        commentPosition,
                        blueColor,
                        reply.replyId == moreReplyId,
                        itemListener
                    )
                }
                if (!comment.isEnd) {
                    postReplyMoreItem(
                        comment.lastReplyId.orEmpty(),
                        comment.commentId,
                        commentPosition,
                        itemListener
                    )
                }
                if (commentPosition in 0 until lastCommentPosition) {
                    spacer(height = dp28, idStr = "Spacer-${comment.commentId}")
                }
            }
        }

        if (loadMore !is Uninitialized) {
            if (commentList is Success && loadMore is Success && commentList.invoke().isEmpty()) {
                empty(
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.let_comm_begin_with_your_comment,
                    desc = null,
                    top = dp26,
                    height = ViewGroup.LayoutParams.WRAP_CONTENT,
                    gravity = Gravity.CENTER_HORIZONTAL
                )
            } else {
                loadMoreFooter(loadMore, idStr = "LoadMoreFooter", endText = "") {
                    vm.getCommentList(false)
                }
            }
        }
    }

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_POST_DETAIL && data.postDetail != null) {
            vm.addShareCount(data.postDetail.postId)
        }
    }

    override fun invokeShareFeature(feature: ShareFeature) {
        when (feature.featureId) {
            FEAT_DELETE -> {
                vm.detail?.let {
                    Analytics.track(
                        EventConstants.POST_DELETE_CLICK,
                        EventParamConstants.KEY_POSTID to it.postId,
                        EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                    )
                }
                ConfirmDialog.Builder(this)
                    .content(getString(R.string.feed_delete_post_confirm))
                    .cancelBtnTxt(getString(R.string.dialog_cancel))
                    .confirmBtnTxt(getString(R.string.delete_cap))
                    .isRed(true)
                    .confirmCallback {
                        vm.detail?.let {
                            Analytics.track(
                                EventConstants.POST_DELETE_CONFIRM,
                                EventParamConstants.KEY_POSTID to it.postId,
                                EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                            )
                        }
                        vm.deletePost()
                    }
                    .show()
            }

            FEAT_REPORT -> {
                vm.reportPost()
            }

            FEAT_EDIT -> {
                vm.showEditRedDot = false
                vm.detail?.let {
                    Analytics.track(
                        EventConstants.POST_EDIT_CLICK,
                        EventParamConstants.KEY_POSTID to it.postId.orEmpty(),
                        EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                    )
                    MetaRouter.Post.goPublishPost(
                        this,
                        ResIdBean().setCategoryID(CategoryId.OUTFIT_SHARE_POST_DETAIL),
                        showRule = false,
                        postPublish = it.toPostPublish()
                    )
                }
            }
        }
    }

    override fun onDestroyView() {
        clearVideo()
        initScrollCallback(null)
        withState(vm) { s ->
            if (s.detail is Success) {
                setFragmentResult(
                    KEY_ACTION_POST,
                    bundleOf(
                        KEY_PARAM_POST_ID to args.postId,
                        KEY_PARAM_IS_DELETE to isDelete,
                        KEY_PARAM_OPINION to s.opinion,
                        KEY_PARAM_LIKE_COUNT to s.likeCount,
                        KEY_PARAM_COMMENT_COUNT to s.commentCount,
                        KEY_PARAM_SHARE_COUNT to s.shareCount
                    )
                )
            }
        }
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        super.onDestroyView()
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        trackShow(duration)
    }

    private fun trackShow(time: Long?) {
        val detail = vm.detail
        Analytics.track(EventConstants.EVENT_POST_DETAIL_SHOW) {
            put(EventParamConstants.KEY_POSTID, args.postId)
            time?.let { it1 -> put(EventParamConstants.KEY_PLAYTIME, it1) }
            put(EventParamConstants.KEY_SOURCE, args.source.toString())
            if (detail != null) {
                detail.tagList?.map { it.tagId }?.let {
                    put(EventParamConstants.KEY_TAG_LIST, it)
                }
                put(EventParamConstants.KEY_REVIEW_TAG, detail.reviewStatus2TrackParam)
            }
        }
    }

    override fun onDestroy() {
        vm.clearReplyTarget()
        super.onDestroy()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_POST_DETAIL
}