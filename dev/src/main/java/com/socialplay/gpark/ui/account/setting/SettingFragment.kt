package com.socialplay.gpark.ui.account.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.MineActionJump
import com.socialplay.gpark.databinding.FragmentSettingBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.main.UpdateDialog
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.collectLatest
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext

/**
 * created by liyanfeng on 2022/7/28 6:30 下午
 * @describe:
 */
class SettingFragment : BaseFragment<FragmentSettingBinding>() {

    private val accountInteractor by lazy {
        GlobalContext.get().get<AccountInteractor>()
    }
    private val viewModel by viewModel<SettingViewModel>()
    private val args by navArgs<SettingFragmentArgs>()
    private var isGoSys = false
    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_SETTING

    private val adapter = MineItemAdapter()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSettingBinding? {
        return FragmentSettingBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.tblTitleBar.setOnBackClickedListener {
            findNavController().popBackStack()
        }
        adapter.setIsSet()
        binding.rv.adapter = adapter
        adapter.setOnItemClickListener { _, position ->
            adapter.peek(position)?.let {
                when (val type = it.jump) {
                    is MineActionJump.GraphNav -> {
                        if (type.event != null) {
                            Analytics.track(type.event)
                        }
                        if (type.graphDestId == R.id.notification_setting) {
                            isGoSys = true
                            PermissionRequest.goNotification(requireActivity())
                        } else {
                            if (type.graphDestId == R.id.privacySetting) {
                                //去除小红点
                                viewModel.metaKV.appKV.iShowScreenshotSettingRedHotClicked = true
                                viewModel.metaKV.appKV.iShowScreenshotSettingRedHot = false
                                it.showLabelRedDot = false
                                adapter.notifyItemChanged(position)
                            }
                            MetaRouter.Control.navigate(this, type.graphDestId, type.navExtra)
                        }
                    }

                    is MineActionJump.Url -> {
                        Analytics.track(type.event) {
                            put("source", type.source)
                        }
                        val h5PageConfig = GlobalContext.get().get<H5PageConfigInteractor>()
                        if (type.url == h5PageConfig.getH5PageUrl(H5PageConfigInteractor.RECHARGE)) {
                            val url = h5PageConfig.getH5PageUrl(H5PageConfigInteractor.RECHARGE)
                                .toHttpUrl()
                            val newUrl = url.newBuilder()
                                .addQueryParameter("startTime", System.currentTimeMillis().toString())
                                .build().toString()
                            MetaRouter.Web.navigate(
                                this,
                                getString(it.displayNameResId),
                                newUrl,
                                type.isShow,
                                showStatusBar = type.showStatusBar,
                                useBabel = type.useBabel,
                                useCompatParams = type.useCompatParams
                            )
                        } else {
                            MetaRouter.Web.navigate(
                                this,
                                getString(it.displayNameResId),
                                type.url,
                                type.isShow,
                                showStatusBar = type.showStatusBar,
                                useBabel = type.useBabel,
                                useCompatParams = type.useCompatParams
                            )
                        }

                    }
//                    is MineActionJump.CompleteAccountItem -> {
//                        Analytics.track(type.event){
//                            put("source", type.source)
//                        }
//                        MetaRouter.Account.bindAccountAndPassword(this, isVisitor, "2")
//                    }
                    is MineActionJump.AccountSettingActionItem -> {
                        Analytics.track(type.event)
                        MetaRouter.AccountSetting.navigate(this, LoginPageSource.Setting, null)
                    }

                    is MineActionJump.LogoutActionItem -> {
                        Analytics.track(type.event)
                        showLogoutConfirmDialog()
                    }

                    is MineActionJump.RealName -> {
                        Analytics.track(type.event)
                        MetaRouterWrapper.RealName.showRealName(this)
                    }

                    is MineActionJump.Update -> {
                        UpdateDialog.showFromSetting(this, viewModel)
                    }

                    else -> {}
                }
            }
        }


        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            viewModel.items.collectLatest {
                it?.let { it1 -> adapter.submitData(it1) }
            }
        }

        viewModel.logoutStateCallback.observe(viewLifecycleOwner) {
            binding.vLoading.gone()
            if (it.succeeded) {
                MetaRouter.Startup.guideLogin(
                    this,
                    loginSource = LoginSource.Logout,
                    navOptions = MetaRouter.Control.getCommonNavOptionsBuilder()
                        .apply { setPopUpTo(R.id.main, false) }.build()
                )
            }
        }
    }

    private fun toEmoji(original: String): String {
        return kotlin.runCatching { String(Character.toChars(original.toInt(16))) }.getOrDefault("")
    }

    private fun showLogoutConfirmDialog() {
        ConfirmDialog.Builder(this)
            .title(getString(R.string.logout_confirm_title))
            .content(getString(R.string.logout_confirm_content))
            .cancelBtnTxt(getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.text_confirm), lightBackground = true, isBold = true)
            .isRed(true)
            .tips(getString(R.string.logout_uuid_tips, accountInteractor.getUserInfoFromCache()?.userNumber ?: ""))
            .image(R.drawable.dialog_icon_cry)
            .cancelCallback {
                Analytics.track(EventConstants.EVENT_LOGOUT_CANCEL_CLICK)
            }
            .confirmCallback {
                Analytics.track(EventConstants.EVENT_LOGOUT_CONFIRM_CLICK)
                binding.vLoading.visible()
                //退出登录
                viewModel.logout()
            }
            .navigate()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_LOGIN_SHOW_PAGE) {
            put(EventConstants.KEY_LOGIN_PAGE_NAME, getFragmentName())
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginPageSource.Unknown.source)
            args.gameId?.let {
                put(EventConstants.KEY_LOGIN_GAME_CODE, it)
            }
        }
        if (isGoSys) {
            viewModel.checkNotification(requireContext())
            isGoSys = false
        }
    }


    override fun loadFirstData() {

    }


}