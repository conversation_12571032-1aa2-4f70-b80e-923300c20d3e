package com.socialplay.gpark.ui.mgs.danmu.advanced

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.NinePatch
import android.graphics.Paint
import android.graphics.RectF
import com.bytedance.danmaku.render.engine.control.DanmakuConfig
import com.bytedance.danmaku.render.engine.data.DanmakuData
import com.bytedance.danmaku.render.engine.render.draw.DrawItem
import com.bytedance.danmaku.render.engine.render.draw.IDrawItemFactory
import com.bytedance.danmaku.render.engine.render.draw.text.TextData
import com.bytedance.danmaku.render.engine.render.draw.text.TextDrawItem


/**
 * Created by dss886 on 2021/07/07.
 */

class AdvancedDanmakuData : DanmakuData() {

    var textData: TextData? = null
    var diggData: DiggData? = null
    var userId: String? = null
    var nineBitmap: NineBitmapData? = null

    override var drawType: Int = DRAW_TYPE_ADVANCED
}

class AdvancedDanmakuDrawItem : DrawItem<AdvancedDanmakuData>() {
    private val mRectF = RectF()
    private var mPaddingLeft = 10f
    private var mPaddingRight = 10f
    override var x: Float = 0F
        set(value) {
            field = value
            updateXY()
        }
    override var y: Float = 0F
        set(value) {
            field = value
            updateXY()
        }

    private val mTextItem: TextDrawItem = TextDrawItem()
    private val mDiggItem: DiggDrawItem = DiggDrawItem()
    private val nineBitmapDrawItem = NineBitmapDrawItem()


    private val mDiggMargin: Float = 12f
    private var mHasDigg = false

    override fun getDrawType(): Int {
        return DRAW_TYPE_ADVANCED
    }

    override fun onBindData(data: AdvancedDanmakuData) {
        mHasDigg = data.diggData != null
        data.nineBitmap?.let {
            nineBitmapDrawItem.height = it.height
            nineBitmapDrawItem.bindData(it)
        }
        data.textData?.let {
            mTextItem.bindData(it)
        }
        data.diggData?.let {
            mDiggItem.bindData(it)
        }
    }

    override fun onMeasure(config: DanmakuConfig) {
        mTextItem.measure(config)
        mDiggItem.measure(config)
        height = mDiggItem.height
        width = mTextItem.width + mPaddingLeft + mPaddingRight
        mHasDigg = data?.diggData != null
        if (mHasDigg) {
            width += mDiggMargin + mDiggItem.width
        }
        updateXY()
    }

    override fun onDraw(canvas: Canvas, config: DanmakuConfig) {
        nineBitmapDrawItem.data?.bitmap?.let { drawDiggBg(canvas, it, nineBitmapDrawItem.data?.height ?: height)
        }
        if (mHasDigg) {
            mDiggItem.draw(canvas, config)
        }
        mTextItem.draw(canvas, config)
    }

    override fun recycle() {
        super.recycle()
        mTextItem.recycle()
        mDiggItem.recycle()
        nineBitmapDrawItem.recycle()
        mRectF.set(0f,0f,0f,0f)
    }

    private fun updateXY() {

        mDiggItem.x = x
        mDiggItem.y = y + (height - mDiggItem.height) / 2

        mTextItem.x = mDiggItem.x + mDiggItem.width
        mTextItem.y = y + (height - mTextItem.height) / 2


        nineBitmapDrawItem.x = x + mDiggItem.width / 2
        nineBitmapDrawItem.y = y + (height - nineBitmapDrawItem.height) / 2

    }

    private fun drawDiggBg(canvas: Canvas, bitmap: Bitmap, height: Float) {
        val np = NinePatch(bitmap, bitmap.ninePatchChunk, null)
        val y = y + (this.height - nineBitmapDrawItem.height) / 2
        val x = x + mPaddingLeft
        mRectF.set(x, y, x + width, y + height)
        np.draw(canvas, mRectF)
    }


}

class AdvancedDanmakuFactory : IDrawItemFactory {

    override fun getDrawType(): Int {
        return DRAW_TYPE_ADVANCED
    }

    override fun generateDrawItem(): DrawItem<out DanmakuData> {
        return AdvancedDanmakuDrawItem()
    }

}