package com.socialplay.gpark.ui.mgs.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.databinding.DialogMgsGameQrCodeBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.quitgame.GameQuitObserver
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Created by bo.li
 * Date: 2022/2/18
 * Desc: MGS二维码弹窗
 */
class MgsQrCodeDialog(
    private val metaApp: Context,
    private val activity: Activity,
    private val gameInfo: GameDetailInfo?,
    private val url: String,
    private val roomShowNum: String
) : Dialog(activity, android.R.style.Theme_Dialog) {

    private lateinit var binding: DialogMgsGameQrCodeBinding

    private val analyticMap by lazy {
        mapOf(
            "gameid" to gameInfo?.id.toString(),
            "gamepkg" to gameInfo?.packageName.toString()
        )
    }

    companion object {
        private val qrCodeSize = 146.dp
    }

    init {
        initView()
    }

    private fun initDialogParams() {
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        binding = DialogMgsGameQrCodeBinding.inflate(LayoutInflater.from(metaApp))
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            0.75F,
            gravity = Gravity.CENTER,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
        binding.clQrCard.setOnAntiViolenceClickListener { }
        binding.root.setOnAntiViolenceClickListener {
            dismiss()
        }
//        binding.ivCloseDialog.setOnAntiViolenceClickListener {
//            Analytics.track(EventConstants.EVENT_INVITE_FRIEND_QR_CLOSE_CLICK) {
//                putAll(analyticMap)
//            }
//            dismiss()
//        }
        binding.rlInviteScan.setOnAntiViolenceClickListener {
            gameInfo?.packageName?.let { GameQuitObserver.delayExitCheckUntilGameResumedAgain(it) }
            // 扫一扫
            Analytics.track(EventConstants.EVENT_INVITE_FRIEND_QR_PHOTO_CLICK) {
                putAll(analyticMap)
            }
            MetaRouter.Main.gameToQrCode(metaApp, gameInfo?.packageName.toString(), gameInfo?.id.toString())
            dismiss()
        }
        val build = QRCode.newQRCodeUtil().content(url).width(qrCodeSize).height(qrCodeSize).build()
        binding.ivInviteQrCode.setImageBitmap(build)
        Analytics.track(EventConstants.EVENT_INVITE_FRIEND_QR_SHOW) {
            putAll(analyticMap)
        }
    }
}