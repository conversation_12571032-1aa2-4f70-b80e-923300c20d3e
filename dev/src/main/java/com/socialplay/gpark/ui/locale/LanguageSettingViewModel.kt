package com.socialplay.gpark.ui.locale

import android.content.ComponentCallbacks
import android.content.Context
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.locale.LanguageOption
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import org.koin.android.ext.android.get

data class LanguageSettingItemModelState(
    val selectedOption: LanguageOption? = null,
    val optionList: List<LanguageOption> = listOf(),
    val restart: Async<Long?> = Uninitialized
) : MavericksState

class LanguageSettingViewModel(
    private val repository: IMetaRepository,
    private val appContext: Context,
    initialState: LanguageSettingItemModelState
) : BaseViewModel<LanguageSettingItemModelState>(initialState) {

    init {
        getLanguageSetting()
    }

    private fun getLanguageSetting() {
        val list = MetaLanguages.getSupportLanguageOptions(appContext)
        val currentLanguageCode = MetaLanguages.getAppCurrentLanguage(appContext)
        val option =
            if (MetaLanguages.isFollowSystemSetting(appContext)) {
                list.find { it.isSystem }
            } else {
                list.find { !it.isSystem && it.language == currentLanguageCode }
            } ?: list.first()
        setState {
            copy(
                selectedOption = option,
                optionList = list
            )
        }
    }

    fun changeAppLanguage(context: Context, languageOption: LanguageOption) {
        if (oldState.selectedOption == languageOption) return
        Analytics.track(EventConstants.EVENT_SETTINGS_LANGUAGE_CHOOSE) {
            put("settings_language", languageOption.language.allPlatformsLangCode)
        }
        suspend {
            val needRestart = MetaLanguages.setAppLanguageSettings(
                context,
                languageOption.language,
                languageOption.isSystem
            )
            if (needRestart) System.currentTimeMillis() else null
        }.execute {
            copy(
                selectedOption = languageOption,
                restart = it
            )
        }
    }

    companion object :
        KoinViewModelFactory<LanguageSettingViewModel, LanguageSettingItemModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LanguageSettingItemModelState
        ): LanguageSettingViewModel {
            return LanguageSettingViewModel(
                get(),
                get(),
                state
            )
        }
    }
}