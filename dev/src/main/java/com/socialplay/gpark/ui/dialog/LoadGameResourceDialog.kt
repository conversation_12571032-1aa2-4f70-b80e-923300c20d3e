package com.socialplay.gpark.ui.dialog

import android.content.DialogInterface
import android.graphics.Typeface
import android.view.Gravity
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogLoadGameResourcesBinding
import com.socialplay.gpark.function.mw.LoadGameResourceEventCallback
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

class LoadGameResourceDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "LoadGameResourceDialog"

        private const val KEY_LOAD_GAME_RESOURCE = "key_load_game_resource"

        private const val ARG_EVENT = "arg_event"

        private const val EVENT_NOTHING = 0
        private const val EVENT_CANCEL = 1

        fun show(
            fragment: Fragment,
            callback: LoadGameResourceEventCallback
        ): LoadGameResourceDialog {
            val dialog = LoadGameResourceDialog()
            fragment.childFragmentManager.setFragmentResultListener(
                KEY_LOAD_GAME_RESOURCE,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                val event = bundle.getInt(ARG_EVENT)
                when (event) {
                    EVENT_CANCEL -> {
                        callback.onCancel()
                    }
                }
                callback.onDismiss()
                fragment.childFragmentManager.clearFragmentResultListener(KEY_LOAD_GAME_RESOURCE)
            }
            dialog.show(fragment.childFragmentManager, TAG)
            return dialog
        }
    }

    override val binding by viewBinding(DialogLoadGameResourcesBinding::inflate)

    private var event = EVENT_NOTHING

    override fun init() {
        binding.ivCloseBtn.setOnAntiViolenceClickListener {
            event = EVENT_CANCEL
            dismissAllowingStateLoss()
        }
        if (EnvConfig.isParty()) {
            binding.tvTitle.typeface = Typeface.DEFAULT_BOLD
            binding.tvContent.typeface = Typeface.DEFAULT_BOLD
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResult(KEY_LOAD_GAME_RESOURCE, bundleOf(ARG_EVENT to event))
    }

    override fun getStyle() = R.style.DialogStyleNonFullScreen

    override fun windowHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun isCancelable(): Boolean {
        return false
    }

    override fun isClickOutsideDismiss(): Boolean {
        return false
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }
}