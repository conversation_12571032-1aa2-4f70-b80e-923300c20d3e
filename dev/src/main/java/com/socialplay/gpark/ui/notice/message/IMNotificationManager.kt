package com.socialplay.gpark.ui.notice.message

import android.app.Activity
import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import com.bin.cpbus.CpEventBus
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.UserInfo
import com.ly123.tes.mgs.metacloud.origin.GroupAtInfo
import com.ly123.tes.mgs.metacloud.origin.GroupMemberInfo
import com.ly123.tes.mgs.metacloud.origin.MessageCustomData
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.MgsSendGroupTxtEvent
import com.socialplay.gpark.data.model.event.MgsSendTxtEvent
import com.socialplay.gpark.data.model.groupchat.GroupChatMemberInfo
import com.socialplay.gpark.data.model.im.IMSettingInfo
import com.socialplay.gpark.data.model.notification.ImGroupMessage
import com.socialplay.gpark.data.model.notification.ImPrivateMessage
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.im.GameImHelper
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.ui.im.conversation.MentionSpan
import com.socialplay.gpark.ui.mgs.dialog.CustomInputDialog
import com.socialplay.gpark.ui.mgs.dialog.OnInputListener
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SoundPlayer
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.getColorByRes
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2024/08/06
 * desc   : IM私信消息处理
 */
 class IMNotificationManager(val friendInteractor: FriendInteractor){
    private var play: SoundPlayer? = null
    private var dialog: CustomInputDialog? = null
    private var oldData: ImPrivateMessage? = null
    private var oldGroupData: ImGroupMessage? = null
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    fun playMedia(context: Context) {
        play?.release()
        play = SoundPlayer()
        play?.play(context, R.raw.click, 0)
    }

    fun release() {
        play?.release()
    }
    fun dialogDismiss() {
        if ((dialog?.activity?.isFinishing)==false && (dialog?.activity?.isDestroyed)==false) {
            dialog?.dismiss()
        }
        dialog = null
    }

    fun handlerPrivateMessage(
        activity: Activity,
        context: Context,
        curGameId: String?,
        data: ImPrivateMessage,
        height: Float,
        left: Float,
        right: Float,
        disMissCallBack: (Boolean) -> Unit
    ) {
        Analytics.track(EventConstants.EVENT_IM_UPS_REPLAY_CLICK, mapOf("gameid" to (curGameId?:"")))
        if (dialog == null) {
            dialog = CustomInputDialog(activity, context, top = height, left, right)
        }
        if (oldData?.receiveTime == data.receiveTime) {
            //当前展示的就是点击的消息，不处理
            return
        }
        Timber.d("handlerPrivateMessage")
        oldData = data
        if (!curGameId.isNullOrEmpty()) {
            MgsDialogManager.showCustomInputDialog(true)
        }
        if ((dialog?.isShowing) == false) {
            dialog!!.show()
        }
        dialog!!.sendEmptyMessageDelayed(
            SpannableString(""),
            object : OnInputListener {
                override fun sendMessage(str: Spannable) {
                    Timber.d("CustomInputDialog_sendMessage")
                    Analytics.track(
                        EventConstants.EVENT_IM_SEND_BUTTON_CLICK,
                        mapOf("gameid" to (curGameId ?: ""))
                    )
                   GlobalScope.launch {
                        friendInteractor.reviewPrivateMessageRisk(str.toString(), null).collect{
                            if (it.succeeded && it.data?.checkPass() == true) {
                                CpEventBus.post(MgsSendTxtEvent(oldData!!.sendUserId, str.toString())
                                )
                                ToastUtil.showShort(context.getString(R.string.send_success))
                            } else {
                                ToastUtil.showShort(context.getString(R.string.send_filed))
                            }
                        }
                    }
                }

                override fun onTextChange(str: String) {

                }

                override fun onInputDialogDismiss() {
                    disMissCallBack.invoke(true)
                    if (!curGameId.isNullOrEmpty()) {
                        MgsDialogManager.showCustomInputDialog(false)
                    }
                    dialog = null
                }

            })
    }

    fun handlerGroupMessage(
        activity: Activity,
        context: Context,
        curGameId: String?,
        data: ImGroupMessage,
        height: Float,
        left: Float,
        right: Float,
        disMissCallBack: (Boolean) -> Unit
    ) {
        Analytics.track(EventConstants.EVENT_IM_UPS_REPLAY_CLICK, mapOf("gameid" to (curGameId?:"")))

        val messageSenderInfo = data.customData?.groupSenderInfo
        val nickname = messageSenderInfo?.nickname
        var initText: Spannable? = null
        if (messageSenderInfo != null && !nickname.isNullOrEmpty()) {
            val highlight = "@$nickname"
            val builder = SpannableStringBuilder("$highlight ")
            builder.setSpan(
                MentionSpan(
                    color = activity.getColorByRes(R.color.color_4AB4FF),
                    memberInfo = GroupChatMemberInfo(
                        avatar = messageSenderInfo.avatar,
                        modifyTime = messageSenderInfo.modifyTime,
                        nickname = messageSenderInfo.nickname,
                        power = messageSenderInfo.power,
                        uuid = messageSenderInfo.uuid
                    )
                ).apply {
                    start = 0
                    end = highlight.length
                },
                0,
                highlight.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            initText = builder
        }
        val atMemberInfo = if (messageSenderInfo == null) {
            null
        } else {
            GroupChatMemberInfo(
                avatar = messageSenderInfo.avatar,
                modifyTime = messageSenderInfo.modifyTime,
                nickname = messageSenderInfo.nickname,
                power = messageSenderInfo.power,
                uuid = messageSenderInfo.uuid
            )
        }
        if (dialog == null) {
            dialog = CustomInputDialog(
                activity,
                context,
                top = height,
                left,
                right,
            )
        }
        if (oldGroupData?.receiveTime == data.receiveTime) {
            //当前展示的就是点击的消息，不处理
            return
        }
        oldGroupData = data
        if (!curGameId.isNullOrEmpty()) {
            MgsDialogManager.showCustomInputDialog(true)
        }
        if ((dialog?.isShowing) == false) {
            dialog!!.show()
        }
        dialog!!.sendAtMessageDelayed(
            atMemberInfo,
            object : OnInputListener {
                override fun sendMessage(str: Spannable) {
                    Timber.d("CustomInputDialog_sendMessage")
                    Analytics.track(
                        EventConstants.EVENT_IM_SEND_BUTTON_CLICK,
                        mapOf("gameid" to (curGameId ?: ""))
                    )
                    // 提前获取 groupAtInfoList, 切协程后, 就获取不到了
                    val groupAtInfoList: MutableList<GroupAtInfo> = mutableListOf()
                    val spans = str.getSpans(
                        0,
                        str.length,
                        MentionSpan::class.java
                    )?.filter { span -> !span?.memberInfo?.uuid.isNullOrEmpty() }
                        ?.take(GroupAtInfo.MAX_AT_MEMBER_COUNT)
                        ?: emptyList()
                    spans.forEach { span ->
                        val memberInfo: GroupMemberInfo = span.memberInfo.toGroupMemberInfo()
                        groupAtInfoList.add(
                            GroupAtInfo(
                                if (memberInfo.uuid == Message.ATType.AT_ALL_ID) {
                                    GroupAtInfo.AT_TYPE_ALL
                                } else {
                                    GroupAtInfo.AT_TYPE_MEMBER
                                },
                                span.start,
                                span.end,
                                memberInfo,
                            )
                        )
                    }
                    GlobalScope.launch {
                        // TODO 群聊目前用的私聊的文本合法检查的业务检查码
                        friendInteractor.reviewPrivateMessageRisk(str.toString(), null).collect {
                            val groupInfo = data.customData?.groupInfo
                            if (it.succeeded && it.data?.checkPass() == true && groupInfo != null) {
                                val currentUserInfo = accountInteractor.accountLiveData.value
                                val currentUserId = currentUserInfo?.uuid
                                val user = UserInfo(
                                    currentUserInfo?.uuid,
                                    currentUserInfo?.nickname,
                                    currentUserInfo?.portrait
                                )
                                user.avatar = currentUserInfo?.portrait

                                val power = if (groupInfo.creator == currentUserId) {
                                    GroupMemberInfo.POWER_OWNER
                                } else if (groupInfo.managerIds?.contains(currentUserId) == true) {
                                    GroupMemberInfo.POWER_MANAGER
                                } else {
                                    GroupMemberInfo.POWER_MEMBER
                                }

                                val groupSenderInfo = GroupMemberInfo(
                                    avatar = user.avatar,
                                    nickname = user.name,
                                    power = power,
                                    uuid = user.userId,
                                )

                                CpEventBus.post(
                                    MgsSendGroupTxtEvent(
                                        str.toString(),
                                        "",
                                        MessageCustomData(
                                            groupSenderInfo = groupSenderInfo,
                                            groupInfo = groupInfo,
                                            groupAtInfoList = groupAtInfoList
                                        )
                                    )
                                )
                                ToastUtil.showShort(context.getString(R.string.send_success))
                            } else {
                                ToastUtil.showShort(context.getString(R.string.send_filed))
                            }
                        }
                    }
                }

                override fun onTextChange(str: String) {

                }

                override fun onInputDialogDismiss() {
                    disMissCallBack.invoke(true)
                    if (!curGameId.isNullOrEmpty()) {
                        MgsDialogManager.showCustomInputDialog(false)
                    }
                    dialog = null
                }

            })
    }
    //设置免打扰
    fun saveIMTipLastTime(context: Context, isInGame: Boolean?) {
        Analytics.track(
            EventConstants.EVENT_IM_PRIVATE_CLOSE_CLICK,
            mapOf("source" to if (isInGame==false) "1" else "0")
        )
        metaKV.account.saveIMTipLastTime(System.currentTimeMillis())
        showCloseMessageTips(context)
    }

    /**
     * 免打扰时间是否已经过了
     */
    private fun isShowIMTips():Boolean{
        val lastTime = metaKV.account.getIMTipLastTime()
        val nowTime = System.currentTimeMillis()
        val space =  metaKV.tTaiKV.imTips
        val data= GsonUtil.gsonSafeParse<IMSettingInfo>(space)
        Timber.d("isShowIMTips %s  %s  %s", lastTime, nowTime,GsonUtil.safeToJson(data?:""))
        // 免打扰时间已过
        return(nowTime- lastTime)> (data?.time ?: 0) * 60_000
    }

    /**
     * im提示
     */
    private fun showCloseMessageTips(context: Context): Boolean {
        val space =  metaKV.tTaiKV.imTips
        val data = GsonUtil.gsonSafeParse<IMSettingInfo>(space)
        val lastCount = metaKV.account.getIMTipCount()
        if ((data?.toastCount ?: 0) > lastCount) {
            metaKV.account.saveIMTipCount(lastCount + 1)
            ToastUtil.showShort(context.getString(R.string.im_tip_close_toast,(data?.time?:45).toString()))
            Analytics.track(EventConstants.EVENT_IM_PRIVATE_CLOSE_TIPS)
            return true
        }
        return false
    }

    /**
     * 是否展示消息提示
     */
    fun showMessageView():Boolean{
        return isOpenImPush() && isShowIMTips()
    }

    /**
     * 后端的开关状态
     */
    private fun isOpenImPush(): Boolean {
        //查询用户开关
        return accountInteractor.accountLiveData.value?.chatMessageSwitch ?: true
    }
}
