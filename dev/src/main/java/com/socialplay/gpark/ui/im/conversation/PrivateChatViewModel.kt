package com.socialplay.gpark.ui.im.conversation

import android.content.ComponentCallbacks
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.ly123.tes.mgs.metacloud.ITypingStatusListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.TypingStatus
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.NetUtil
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext

data class PrivateChatState(
    val test: String = "",
) : MavericksState

class PrivateChatViewModel(
    initialState: PrivateChatState,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
) : BaseViewModel<PrivateChatState>(initialState) {
    val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    private val _typingStatusInfo by lazy { MutableLiveData<Message.MessageType?>() }
    val typingStatusLiveData = _typingStatusInfo
    private val _friendStatusLiveData by lazy { MutableLiveData<FriendStatus?>() }
    val friendStatusLiveData = _friendStatusLiveData

    //好友信息
    private val _friendInfoLiveData: MutableLiveData<FriendInfo?> = MutableLiveData()
    val friendInfoLiveData: LiveData<FriendInfo?> = _friendInfoLiveData

    private lateinit var targetUUID: String

    private val typingStatusListener = object : ITypingStatusListener {
        override fun onTypingStatusChanged(
            conversationType: Conversation.ConversationType?,
            targetId: String?,
            typingStatusSet: MutableCollection<TypingStatus>?
        ) {
            if (typingStatusSet.isNullOrEmpty()) {
                _typingStatusInfo.postValue(null)
                return
            }
            typingStatusSet.forEach { status ->
                _typingStatusInfo.postValue(status.messageType)
            }
        }
    }

    private val friendInfoUpdateObserver: suspend (friendInfo: FriendInfo) -> Unit = {
        _friendInfoLiveData.value = it
        _friendStatusLiveData.postValue(_friendInfoLiveData.value?.status)
    }

    private suspend fun queryUserInfo(uuid: String) = metaRepository.queryFriendInfo(uuid)

    fun currentUserInfo(): MetaUserInfo? {
        return accountInteractor.accountLiveData.value
    }

    /**
     * 好友的资料
     */
    fun getFriendInfo(uuid: String, title: String?) = viewModelScope.launch {
        if (NetUtil.isNetworkAvailable()) {
            _friendInfoLiveData.value = queryUserInfo(uuid).data
            _friendStatusLiveData.postValue(_friendInfoLiveData.value?.status)
        }
    }

    /**
     * 输入状态监听
     */
    fun getTypingStatus() {
        MetaCloud.registerTypingStatusListener(typingStatusListener)
    }

    fun addMessageListener(targetId: String) = viewModelScope.launch {
        targetUUID = targetId
        FriendBiz.observeFriend(targetId, null, friendInfoUpdateObserver)
    }

    fun checkFriendStatus(
        otherUid: String
    ): Boolean {
        val friend = _friendInfoLiveData.value
        val isFriend = friend?.bothFriend == true
        val isOfficial = accountInteractor.isOfficial || friend?.isOfficial() == true
        return isFriend || isOfficial
    }

    override fun onCleared() {
        FriendBiz.removeFriendObserver(targetUUID, friendInfoUpdateObserver)
        MetaCloud.unRegisterTypingStatusListener(typingStatusListener)
        super.onCleared()
    }

    companion object : KoinViewModelFactory<PrivateChatViewModel, PrivateChatState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext, state: PrivateChatState
        ): PrivateChatViewModel {
            return PrivateChatViewModel(state, get())
        }
    }
}