package com.socialplay.gpark.ui.aibot.ugc.clip

import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.databinding.FragmentAiBotImageClipBinding
import com.socialplay.gpark.databinding.FragmentPlotClipImageBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.plot.chooseimage.PlotChooseImageActivity
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageFragmentArgs
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageModelState
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageViewModel
import com.socialplay.gpark.ui.view.ucrop.GestureCropImageView
import com.socialplay.gpark.ui.view.ucrop.OverlayView
import com.socialplay.gpark.ui.view.ucrop.TransformImageView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.addBackPressedDispatcherCallback
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visibleList
import com.yalantis.ucrop.callback.BitmapCropCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/27
 *     desc   :
 *
 */


class AIBotUGCImageClipFragment :
    BaseFragment<FragmentAiBotImageClipBinding>(R.layout.fragment_ai_bot_image_clip) {
    private val args by args<PlotClipImageFragmentArgs>()
    private val viewModel: PlotClipImageViewModel by fragmentViewModel()
    private lateinit var gestureCropImageView: GestureCropImageView
    private lateinit var overlayView: OverlayView

    companion object {
        const val CLIP_RESULT = "clip_result"
    }

    private val backPressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            withState(viewModel) {
                if (it.result is Loading) {
                    viewModel.stopProcess()
                } else {
                    onUserCancel()
                }
            }
        }
    }
    private val imageListener: TransformImageView.TransformImageListener = object : TransformImageView.TransformImageListener {
        override fun onRotate(currentAngle: Float) {
            //            setAngleText(currentAngle)
        }

        override fun onScale(currentScale: Float) {
            //            setScaleText(currentScale)
        }

        override fun onLoadComplete() {
            binding.ucrop.animate().alpha(1f).setDuration(300).setInterpolator(
                AccelerateInterpolator()
            )

        }

        override fun onLoadFailure(e: Exception) {

        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotImageClipBinding? {
        return FragmentAiBotImageClipBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addBackPressedDispatcherCallback(viewLifecycleOwner, backPressedCallback)
        initView()
        initData()
    }
    private fun initData(){
        viewModel.onEach(PlotClipImageModelState::cachePath) {
            if (it != null) {
                val uri = Uri.parse(args.inputPath)
                gestureCropImageView.setImageUri(uri, it, true)
            }
        }
        viewModel.registerAsyncErrorToast(PlotClipImageModelState::result)
        viewModel.onAsync(PlotClipImageModelState::result, onLoading = {

        }, onSuccess = {
            viewModel.oldState.cachePath?.let { viewModel.deleteCache(it) }
            setFragmentResult(CLIP_RESULT, Bundle().apply { putString(CLIP_RESULT, it) })
            navigateUp()
        }, onFail = { _, _ ->
            ToastUtil.showShort(R.string.failed_to_upload)
        })
    }

    private fun initView() {
        binding.userCancel.setOnAntiViolenceClickListener {
            onUserCancel()
        }
        binding.userSure.setOnAntiViolenceClickListener {
            cropImage()
        }
        gestureCropImageView = binding.ucrop.cropImageView
        overlayView = binding.ucrop.overlayView
        overlayView.setCircleDimmedLayer(true)
        overlayView.setShowCropGrid(false)
        overlayView.setShowCropFrame(false)
        overlayView.setDimmedColor(ContextCompat.getColor(requireContext(),R.color.black_80))
        overlayView.setCircleStrokeColor(ContextCompat.getColor(requireContext(),R.color.white))
        overlayView.setCropGridColor(ContextCompat.getColor(requireContext(),R.color.black_80))
        gestureCropImageView.targetAspectRatio = 1.0f
        gestureCropImageView.setTransformImageListener(imageListener)
    }
    private fun cropImage() {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO){
            gestureCropImageView.cropAndSaveImage(
                Bitmap.CompressFormat.JPEG,
                90,
                object : BitmapCropCallback {
                    @RequiresApi(Build.VERSION_CODES.R)
                    override fun onBitmapCropped(resultUri: Uri, offsetX: Int, offsetY: Int, imageWidth: Int, imageHeight: Int) {
                        resultUri.path?.let {
                            viewModel.processClip(
                                it,
                                UploadFileInteractor.BIZ_CODE_AI_BOT,
                                false
                            )
                        }
                    }

                    override fun onCropFailure(t: Throwable) {
                    }
                })
        }
    }

    private fun onUserCancel() {
        navigateUp()
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun invalidate() {

    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_AI_BOT_IMAGE_CLIP
}