package com.socialplay.gpark.app.initialize

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.util.extension.insertAt
import org.koin.core.context.GlobalContext

object PlayedGameInit {

    const val PLAYED_GAME_PAGE_SIZE = 100

    suspend fun refreshPlayedGame() {
        val myPlayedGameDao = GlobalContext.get().get<AppDatabase>().myPlayedGameDao
        val metaApi = GlobalContext.get().get<MetaApi>()
        val localList = myPlayedGameDao.playedGamesV2(0, PLAYED_GAME_PAGE_SIZE)
        if (localList.isNotEmpty()) {
            val onlineList = if (localList.size > 50) {
                DataSource.getDataResultForApi {
                    metaApi.getGameListByIds(localList.take(50).map { it.id })
                }.data.insertAt(-1, DataSource.getDataResultForApi {
                    metaApi.getGameListByIds(localList.subList(50, localList.size).map { it.id })
                }.data)
            } else {
                DataSource.getDataResultForApi {
                    metaApi.getGameListByIds(localList.map { it.id })
                }.data
            }
            if (!onlineList.isNullOrEmpty()) {
                localList.forEach { local ->
                    val online = onlineList.find {
                        if (local.isUgcType()) {
                            it.gameId == local.id
                        } else {
                            it.code == local.id
                        }
                    }
                    // 对比线上数据, 更新数据库
                    if (online != null && (local.name != online.name || local.icon != online.icon)) {
                        myPlayedGameDao.updateInfo(
                            local.copy(
                                icon = online.icon ?: "",
                                name = online.name ?: ""
                            )
                        )
                    }
                }
            }
        }
    }
}