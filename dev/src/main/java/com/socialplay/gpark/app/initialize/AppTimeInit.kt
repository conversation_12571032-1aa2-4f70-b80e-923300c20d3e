package com.socialplay.gpark.app.initialize

import android.app.Activity
import android.app.Application
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.ActivityLifecycleCallbacksAdapter

/**
 * 统计 app_time (主进程在前台时长, 剔除广告与其他)
 */
object AppTimeInit {
    private const val DELAY_TIME = 5 * 60 * 1000L
    private var resumeTime = System.currentTimeMillis()
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            analytics()
            sendMessageDelayed(Message.obtain(), DELAY_TIME)
        }
    }

    fun init(app: Application) {
        app.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacksAdapter() {
            override fun onActivityResumed(activity: Activity) {
                super.onActivityResumed(activity)
                if (activity is BaseActivity && activity.isNeedAnalytics()) {
                    resumeTime = System.currentTimeMillis()
                    handler.sendMessageDelayed(Message.obtain(), DELAY_TIME)
                }
            }

            override fun onActivityPaused(activity: Activity) {
                super.onActivityPaused(activity)
                if (activity is BaseActivity && activity.isNeedAnalytics()) {
                    analytics()
                    handler.removeCallbacksAndMessages(null)
                }
            }
        })
    }

    private fun analytics() {
        val duration = System.currentTimeMillis() - resumeTime
        if (duration <= 0) {
            return
        }
        Analytics.track(EventConstants.EVENT_APP_TIME, "playtime" to duration)
        resumeTime = System.currentTimeMillis()
    }

    fun onCrash() {
        analytics()
        handler.removeCallbacksAndMessages(null)
    }

}