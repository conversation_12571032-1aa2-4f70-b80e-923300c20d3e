package com.socialplay.gpark.app.initialize

import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.util.DateUtil
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     <AUTHOR> hailong.dong
 *     e-mail : <EMAIL>
 *     time   : 2021/06/08
 *     desc   :
 * </pre>
 */
object AppUseDataInit {
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }
    private val metaRepository: IMetaRepository by lazy { GlobalContext.get().get() }
    fun init() {
        val appKV = GlobalContext.get().get<MetaKV>().appKV
        appKV.appOpenTimes++

        val logInDays = appKV.logInDays
        val dayOfYear = DateUtil.dayOfYear()

        if (logInDays != dayOfYear) {
            appKV.isNewDay = true
            appKV.appUseDays += 1
            appKV.logInDays = dayOfYear
            updateRecentLogin()
        } else {
            appKV.isNewDay = false
        }
        //上报设备国家地区代码
        GlobalScope.launch { metaRepository.userAgentUpload().collect {} }
    }

    /**
     * 是全新的一天才调用
     */
    private fun updateRecentLogin() {
        val recentList = metaKV.appKV.recentUseDaysDate.toMutableList()
        metaKV.appKV.removeOutRangeRecentUseDays(recentList)
        recentList.add(DateUtil.getToday())
        metaKV.appKV.recentUseDaysDate = recentList
    }
}