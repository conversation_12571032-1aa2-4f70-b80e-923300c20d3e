package com.socialplay.gpark.di

import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.usecase.FinishDailyTaskUseCase
import com.socialplay.gpark.usecase.FinishShareOCVideoTaskUseCase
import com.socialplay.gpark.usecase.GetDailyTaskRewardStatusUseCase
import org.koin.dsl.module


val useCaseModule = module {
    factory { GetDailyTaskRewardStatusUseCase(get()) }
    factory { FinishDailyTaskUseCase(get()) }
    factory { FinishShareOCVideoTaskUseCase(get(), get<MetaKV>().dailyTaskKV) }
}