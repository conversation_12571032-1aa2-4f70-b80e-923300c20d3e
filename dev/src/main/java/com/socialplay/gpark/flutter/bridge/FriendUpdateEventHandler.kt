package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.data.interactor.FriendInteractor
import io.flutter.plugin.common.EventChannel
import timber.log.Timber

class FriendUpdateEventHandler(
    private val friendInteractor: FriendInteractor
) : EventChannel.StreamHandler {
    
    private var eventSink: EventChannel.EventSink? = null
    
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        
        // TODO: 监听好友状态变化
        // 这里需要监听friendInteractor的状态变化，并通过eventSink发送事件
        
        Timber.d("Friend update event handler started listening")
    }
    
    override fun onCancel(arguments: Any?) {
        eventSink = null
        Timber.d("Friend update event handler stopped listening")
    }
    
    private fun sendFriendUpdateEvent(update: Map<String, Any>) {
        eventSink?.success(update)
    }
}
