package com.socialplay.gpark.flutter.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngineCache
import com.socialplay.gpark.flutter.FlutterBridgeManager
import timber.log.Timber

class FlutterSettingsActivity : FlutterActivity() {
    
    companion object {
        private const val ENGINE_ID = "gpark_flutter_engine"
        
        fun createIntent(context: Context, initialRoute: String = "/settings"): Intent {
            return FlutterActivity
                .withCachedEngine(ENGINE_ID)
                .build(context)
                .apply {
                    putExtra("route", initialRoute)
                }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // 确保Flutter引擎已初始化
        if (!FlutterBridgeManager.getInstance().isInitialized()) {
            FlutterBridgeManager.getInstance().initialize(this)
        }
        
        super.onCreate(savedInstanceState)
        
        Timber.d("FlutterSettingsActivity created with route: ${getInitialRoute()}")
    }
    
    override fun getInitialRoute(): String {
        return intent.getStringExtra("route") ?: "/settings"
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Timber.d("FlutterSettingsActivity destroyed")
    }
}
