package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.data.interactor.AccountInteractor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class AccountBridge(
    private val accountInteractor: AccountInteractor
) : MethodChannel.MethodCallHandler {
    
    private val scope = CoroutineScope(Dispatchers.Main)
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getUserProfile" -> getUserProfile(result)
            "updateUserProfile" -> updateUserProfile(call, result)
            "isLoggedIn" -> isLoggedIn(result)
            "logout" -> logout(result)
            else -> result.notImplemented()
        }
    }
    
    private fun getUserProfile(result: MethodChannel.Result) {
        scope.launch {
            try {
                val account = accountInteractor.accountLiveData.value
                if (account != null) {
                    val profileData = mapOf(
                        "uuid" to account.uuid,
                        "nickname" to account.nickname,
                        "portrait" to account.portrait,
                        "signature" to account.signature,
                        "userNumber" to account.userNumber,
                        "email" to account.email
                    )
                    result.success(profileData)
                } else {
                    result.error("NO_USER", "User not logged in", null)
                }
            } catch (e: Exception) {
                Timber.e(e, "Error getting user profile")
                result.error("ERROR", e.message, null)
            }
        }
    }
    
    private fun updateUserProfile(call: MethodCall, result: MethodChannel.Result) {
        scope.launch {
            try {
                val nickname = call.argument<String>("nickname")
                val signature = call.argument<String>("signature")
                
                // TODO: 实现具体的更新逻辑
                // 这里需要调用实际的用户信息更新接口
                
                result.success(true)
            } catch (e: Exception) {
                Timber.e(e, "Error updating user profile")
                result.error("UPDATE_ERROR", e.message, null)
            }
        }
    }
    
    private fun isLoggedIn(result: MethodChannel.Result) {
        try {
            val isLoggedIn = accountInteractor.accountLiveData.value != null
            result.success(isLoggedIn)
        } catch (e: Exception) {
            Timber.e(e, "Error checking login status")
            result.error("ERROR", e.message, null)
        }
    }
    
    private fun logout(result: MethodChannel.Result) {
        scope.launch {
            try {
                // 调用原生登出逻辑
                accountInteractor.logout()
                result.success(true)
            } catch (e: Exception) {
                Timber.e(e, "Error logging out")
                result.error("LOGOUT_ERROR", e.message, null)
            }
        }
    }
}
