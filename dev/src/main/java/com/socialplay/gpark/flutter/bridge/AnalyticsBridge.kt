package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.function.analytics.Analytics
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import timber.log.Timber

class AnalyticsBridge : MethodChannel.MethodCallHandler {
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "trackEvent" -> trackEvent(call, result)
            else -> result.notImplemented()
        }
    }
    
    private fun trackEvent(call: MethodCall, result: MethodChannel.Result) {
        try {
            val eventName = call.argument<String>("eventName")
            val properties = call.argument<Map<String, Any>>("properties")
            
            if (eventName != null) {
                Analytics.track(eventName) {
                    properties?.forEach { (key, value) ->
                        put(key, value)
                    }
                }
                result.success(true)
            } else {
                result.error("INVALID_PARAMS", "Event name is required", null)
            }
        } catch (e: Exception) {
            Timber.e(e, "Error tracking event")
            result.error("ANALYTICS_ERROR", e.message, null)
        }
    }
}
