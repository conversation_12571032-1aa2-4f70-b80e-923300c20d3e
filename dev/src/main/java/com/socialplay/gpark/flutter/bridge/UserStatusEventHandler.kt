package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.data.interactor.AccountInteractor
import io.flutter.plugin.common.EventChannel
import timber.log.Timber

class UserStatusEventHandler(
    private val accountInteractor: AccountInteractor
) : EventChannel.StreamHandler {
    
    private var eventSink: EventChannel.EventSink? = null
    
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        
        // TODO: 监听用户状态变化
        // 这里需要监听accountInteractor的状态变化，并通过eventSink发送事件
        
        Timber.d("User status event handler started listening")
    }
    
    override fun onCancel(arguments: Any?) {
        eventSink = null
        Timber.d("User status event handler stopped listening")
    }
    
    private fun sendUserStatusEvent(status: Map<String, Any>) {
        eventSink?.success(status)
    }
}
