package com.socialplay.gpark.flutter.bridge

import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import android.os.Build

class TestBridge : MethodChannel.MethodCallHandler {
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getPlatformVersion" -> getPlatformVersion(result)
            else -> result.notImplemented()
        }
    }
    
    private fun getPlatformVersion(result: MethodChannel.Result) {
        try {
            val version = "Android ${Build.VERSION.RELEASE}"
            result.success(version)
        } catch (e: Exception) {
            result.error("ERROR", e.message, null)
        }
    }
}
