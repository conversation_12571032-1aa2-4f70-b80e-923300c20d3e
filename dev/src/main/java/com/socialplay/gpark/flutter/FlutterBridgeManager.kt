package com.socialplay.gpark.flutter

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import org.koin.core.context.GlobalContext
import com.socialplay.gpark.flutter.bridge.*
import timber.log.Timber

class FlutterBridgeManager private constructor() {
    
    companion object {
        private const val ENGINE_ID = "gpark_flutter_engine"
        
        // Method Channels
        const val CHANNEL_ACCOUNT = "com.socialplay.gpark/account"
        const val CHANNEL_NAVIGATION = "com.socialplay.gpark/navigation"
        const val CHANNEL_DATA = "com.socialplay.gpark/data"
        const val CHANNEL_ANALYTICS = "com.socialplay.gpark/analytics"
        const val CHANNEL_TEST = "com.socialplay.gpark/test"
        
        // Event Channels
        const val EVENT_USER_STATUS = "com.socialplay.gpark/user_status"
        const val EVENT_FRIEND_UPDATE = "com.socialplay.gpark/friend_update"
        
        @Volatile
        private var INSTANCE: FlutterBridgeManager? = null
        
        fun getInstance(): FlutterBridgeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FlutterBridgeManager().also { INSTANCE = it }
            }
        }
    }
    
    private var flutterEngine: FlutterEngine? = null
    private var isInitialized = false
    
    // 桥接处理器
    private lateinit var accountBridge: AccountBridge
    private lateinit var navigationBridge: NavigationBridge
    private lateinit var settingsBridge: SettingsBridge
    private lateinit var analyticsBridge: AnalyticsBridge
    private lateinit var testBridge: TestBridge
    
    fun initialize(context: Context) {
        if (isInitialized) return
        
        try {
            // 创建Flutter引擎
            flutterEngine = FlutterEngine(context).apply {
                // 执行Dart入口点
                dartExecutor.executeDartEntrypoint(
                    DartExecutor.DartEntrypoint.createDefault()
                )
            }
            
            // 缓存引擎
            FlutterEngineCache.getInstance().put(ENGINE_ID, flutterEngine!!)
            
            // 初始化桥接器
            setupBridges()
            
            isInitialized = true
            Timber.d("Flutter engine initialized successfully")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize Flutter engine")
        }
    }
    
    private fun setupBridges() {
        val messenger = flutterEngine!!.dartExecutor.binaryMessenger
        
        try {
            // 测试桥接
            testBridge = TestBridge()
            MethodChannel(messenger, CHANNEL_TEST).setMethodCallHandler(testBridge)
            
            // 账户桥接
            accountBridge = AccountBridge(GlobalContext.get().get())
            MethodChannel(messenger, CHANNEL_ACCOUNT).setMethodCallHandler(accountBridge)
            
            // 导航桥接
            navigationBridge = NavigationBridge()
            MethodChannel(messenger, CHANNEL_NAVIGATION).setMethodCallHandler(navigationBridge)
            
            // 设置桥接
            settingsBridge = SettingsBridge()
            MethodChannel(messenger, CHANNEL_DATA).setMethodCallHandler(settingsBridge)
            
            // 分析桥接
            analyticsBridge = AnalyticsBridge()
            MethodChannel(messenger, CHANNEL_ANALYTICS).setMethodCallHandler(analyticsBridge)
            
            // 事件通道
            setupEventChannels(messenger)
            
            Timber.d("Flutter bridges setup successfully")
        } catch (e: Exception) {
            Timber.e(e, "Failed to setup Flutter bridges")
        }
    }
    
    private fun setupEventChannels(messenger: io.flutter.plugin.common.BinaryMessenger) {
        try {
            // 用户状态事件
            EventChannel(messenger, EVENT_USER_STATUS).setStreamHandler(
                UserStatusEventHandler(GlobalContext.get().get())
            )
            
            // 好友更新事件
            EventChannel(messenger, EVENT_FRIEND_UPDATE).setStreamHandler(
                FriendUpdateEventHandler(GlobalContext.get().get())
            )
        } catch (e: Exception) {
            Timber.e(e, "Failed to setup event channels")
        }
    }
    
    fun getFlutterEngine(): FlutterEngine? = flutterEngine
    
    fun isInitialized(): Boolean = isInitialized
}
