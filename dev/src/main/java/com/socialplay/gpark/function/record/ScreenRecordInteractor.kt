package com.socialplay.gpark.function.record

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Point
import android.media.AudioManager
import android.media.MediaScannerConnection
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.ScreenRecordUserActionEvent
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.*
import kotlinx.coroutines.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

/**
 * @des:游戏录屏
 * @author: lijunjia
 * @date: 2021/12/30 15:18
 */
@SuppressLint("StaticFieldLeak")
object ScreenRecordInteractor {

    private var mediaProjectionManager: MediaProjectionManager? = null
    private var width = MetaScreenRecorder.defaultWidth
    private var height = MetaScreenRecorder.defaultHeight
    private var densityDpi = MetaScreenRecorder.defaultDpi
    private val mPathRecord: String
    private val mPathSave: String
    private val metaApp: Context = GlobalContext.get().get()
    private val metaRepository = GlobalContext.get().get<IMetaRepository>()
    private val metaKv = GlobalContext.get().get<MetaKV>()
    private var mediaScannerConnection: MediaScannerConnection? = null
    private var intentData: Intent? = null
    private var resultCode: Int? = null
    private var mGameId: String? = null
    private var mPackageName: String? = null
    private var mAppName: String? = null

    private val onVideoRecordListener = object : OnVideoRecordListener {
        override fun onBeforeStartRecord() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_BEFORE_START)
        }

        override fun onStartRecord() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_STARTED)
        }

        override fun onStartRecordFailed() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_START_FAILED)
        }

        override fun onEndRecord() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_END_RECORD)
        }

        override fun onAfterSaveRecord(saveFilePath: String, saveFileUri: Uri, showResult: Boolean) {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_AFTER_SAVE, saveFilePath, saveFileUri, showResult)
        }

        override fun onPauseRecord() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_PAUSE)
        }

        override fun onResumeRecord() {
            notifyRecordStatusToGame(ScreenRecordReceiver.ACTION_TYPE_RESUME)
        }
    }

    private fun notifyRecordStatusToGame(actionType: Int, saveFilePath: String? = null, saveFileUri: Uri? = null, showResult: Boolean = true) {
        ScreenRecordReceiver.sendRecordReceiver(metaApp, actionType, mPackageName ?: "", saveFilePath, saveFileUri, showResult)
    }

    init {
        Timber.e("ScreenRecordInteractor init")
        mPathRecord = ScreenRecordUtil.getRecordPath(metaApp)
        mPathSave = ScreenRecordUtil.getSavePath(metaApp)
        FileUtil.hasFileDir(mPathSave)
        FileUtil.hasFileDir(mPathRecord)
        MetaScreenRecorder.setRecordSaveDir(mPathRecord)
        registerHermesEventBus()
    }

    private fun registerHermesEventBus() {
        CpEventBus.register(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: ScreenRecordUserActionEvent) {
        if (!ProcessUtil.isMainProcess(metaApp)) {
            Timber.e("not main process")
            return
        }
        when (event.action) {
            ScreenRecordUserActionEvent.ACTION_START_RECORD -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    ScreenRecorderService.startService(metaApp)
                } else {
                    startRecord()
                }
            }
            ScreenRecordUserActionEvent.ACTION_STOP_RECORD -> {
                checkStopAndSaveScreenRecord(event.showEndDialog)
            }
            ScreenRecordUserActionEvent.ACTION_OPEN_AUDIO -> {
                switchAudioRecord(false)
            }
            ScreenRecordUserActionEvent.ACTION_CLOSE_AUDIO -> {
                switchAudioRecord(true)
            }
        }
    }


    fun init(gameId: String, packageName: String, appName: String) {
        this.mGameId = gameId
        this.mPackageName = packageName
        this.mAppName = appName
        initPath()
    }

    fun hasRequestScreenRecordUserPermission(): Boolean {
        return MetaScreenRecorder.isConfig()
    }


    private fun checkRecordAudio(): Boolean {
        val hasPermission = PermissionRequest.checkSelfPermission(metaApp, Manifest.permission.RECORD_AUDIO)
        if (metaKv.screenRecordKV.isRecordAudio && !hasPermission) {
            metaKv.screenRecordKV.isRecordAudio = false
        }
        if (hasPermission) {
            switchAudioRecord(!metaKv.screenRecordKV.isRecordAudio)
        }
        return hasPermission
    }


    /**
     *   开始录屏
     *   from  233进程或者游戏进程
     *   packageName 游戏进程的包名
     */
    fun startRecord() {
        if (MetaScreenRecorder.isRunning()) {
            Timber.d("my_record recording, save first")
            ScreenRecordAnalytics.eventStartRecordFailed("record is running")
            return
        }
        try {
            Timber.d("startRecord")
            setRecordMetric(metaApp)
            if (mediaProjectionManager == null) {
                Timber.d("my_record startRecord mediaProjectionManager is null")
                mediaProjectionManager = metaApp.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            }
            if (!MetaScreenRecorder.isConfig()) {
                setMediaProjection()
            } else {
                Timber.d("is config not set projection ")
            }
            onVideoRecordListener.onBeforeStartRecord()
            if (MetaScreenRecorder.startRecord(checkRecordAudio())) {
                onVideoRecordListener.onStartRecord()
            } else {
                ToastUtil.showShort(metaApp, "failed")
                onVideoRecordListener.onStartRecordFailed()
            }
        } catch (e: Exception) {
            MetaScreenRecorder.release()
            ScreenRecordAnalytics.eventStartRecordFailed("start record exception")
            Timber.d("record error, start error =%s", e)
            ToastUtil.showShort(metaApp, "failed")
            onVideoRecordListener.onStartRecordFailed()
        }

    }

    private fun switchAudioRecord(micMute: Boolean) {
        val audioManager = metaApp.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.isMicrophoneMute = micMute
    }

    /**
     * 停止此次录制
     */
    private fun stopRecord(): Boolean {
        onVideoRecordListener.onEndRecord()
        return MetaScreenRecorder.stopRecord()
    }

    /**
     * 暂停当前录制
     */
    fun pauseRecorder() {
        if (MetaScreenRecorder.isRunning()) {
            onVideoRecordListener.onPauseRecord()
            MetaScreenRecorder.pauseRecorder()
        }
    }

    /**
     * 恢复录制
     */
    fun resumeRecorder(recordAudio: Boolean) {
        if (MetaScreenRecorder.isPause()) {
            onVideoRecordListener.onResumeRecord()
            MetaScreenRecorder.resumeRecorder(recordAudio) //恢复
        }

    }

    /**
     * 是否正在录屏过程
     */
    fun isCurrentRunning(): Boolean {
        return MetaScreenRecorder.isRunning()
    }

    /**
     *
     */

    fun isRecordConfig(): Boolean {
        return MetaScreenRecorder.isConfig()
    }


    /**
     * 获取权限成功后，设置录屏参数，开启录屏
     */
    private fun setMediaProjection() {
        Timber.d("my_record setMediaProjection,resultCode:${resultCode} data:${intentData}")
        val resultCode = this.resultCode
        val intentData = this.intentData
        if (resultCode != null && intentData != null) {
            val mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, intentData)
            MetaScreenRecorder.setMediaProject(mediaProjection)
        }

    }

    /**
     * 初始化屏幕参数
     */
    private fun setRecordMetric(context: Context) {
        //系统两种方式获取宽高
        getScreenSize(context)
        MetaScreenRecorder.setConfig(width, height, densityDpi)
    }

    /**
     * 获取屏幕真实宽高
     */
    private fun getScreenSize(context: Context) {
        val metrics = DisplayMetrics()
        val windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(metrics)

        val display = windowManager.defaultDisplay
        val point = Point()
        display.getRealSize(point)
        //设置录制屏幕宽高
        this.densityDpi = metrics.densityDpi
        width = metrics.widthPixels.coerceAtLeast(point.x)
        height = metrics.heightPixels.coerceAtLeast(point.y)
        Timber.d("initRecorder,point.X:${point.x} y:${point.y}")
    }


    private fun initPath() {
        GlobalScope.launch {
            val gameName = mGameId?.let {
                val gameResult = metaRepository.getGameInfoCache(it)
                if (gameResult.succeeded) gameResult.data?.name ?: mAppName else mAppName
            } ?: mAppName
            MetaScreenRecorder.setRecordSaveFilePreFix(gameName)
        }
    }


    /**
     *  开启录屏请求intent
     */
    fun getScreenRecordPermission(context: Context): Intent? {
        kotlin.runCatching {
            if (mediaProjectionManager == null) {
                mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            }
            return if (mediaProjectionManager != null) {
                mediaProjectionManager?.createScreenCaptureIntent()
            } else {
                ScreenRecordAnalytics.eventStartRecordFailed("MediaProjectionManager is null")
                null
            }
        }.getOrElse {
            ScreenRecordAnalytics.eventStartRecordFailed("create apply user record permission intent exception")
            ToastUtil.showShort(context, "failed")
            return null
        }
    }


    /**
     * 结束录屏
     */
    private fun finishRecord(): Boolean {
        return MetaScreenRecorder.finishRecord()

    }

    private fun checkStopAndSaveScreenRecord(showResultDialog: Boolean = true) {
        if (MetaScreenRecorder.isRunning()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ScreenRecorderService.stopService(metaApp)
            }
            stopAndSaveScreenRecord(showResultDialog)
        }
    }

    private fun stopAndSaveScreenRecord(showResultDialog: Boolean = true) {
        val success = stopRecord()
        if (success) {
            saveScreenRecord(showResultDialog)
        }
    }

    private fun scanVideoFile(filePath: String, context: Context) {
        mediaScannerConnection = MediaScannerConnection(context, object : MediaScannerConnection.MediaScannerConnectionClient {
            override fun onScanCompleted(path: String?, uri: Uri?) {
                Timber.d("onScanCompleted: $path, uri:$uri")
                mediaScannerConnection?.disconnect()
            }

            override fun onMediaScannerConnected() {
                if (mediaScannerConnection?.isConnected == true) {
                    Timber.d("onMediaScannerConnected: The connection was successful ${File(filePath).exists()}")
                    runCatching {
                        mediaScannerConnection?.scanFile(filePath, "video/*")
                    }
                }
            }

        })
        mediaScannerConnection?.connect()
    }

    /**
     * 存储回录视频
     */
    private fun saveScreenRecord(showResultDialog: Boolean = true) {
        val item = File(MetaScreenRecorder.videoPath)
        if (!item.exists() || !item.isFile) {
            Timber.e("my_record，file not exist! current recording path:${item.absolutePath} isFile:${item.isFile}")
            ScreenRecordAnalytics.eventSaveRecordFailed("recorded file does not exist")
            return
        }
        //自由录制直接保存
        GlobalScope.launch(Dispatchers.IO) {
            val saveResult = kotlin.runCatching {
                VideoUtils.saveVideoToAlbumForPath(
                    metaApp,
                    MetaScreenRecorder.videoPath,
                    randomFileName = true
                )
            }.getOrDefault(Triple(false, null, null))
            withContext(Dispatchers.Main) {
                if (saveResult.first || (saveResult.second != null && saveResult.third != null)) {
                    if (saveResult.first) {
                        kotlin.runCatching { FileUtil.deleteFile(File(MetaScreenRecorder.videoPath)) }
                    }
                    ScreenRecordAnalytics.eventSaveRecordSuccess()
                    if (saveResult.second != null && saveResult.third != null) {
                        onVideoRecordListener.onAfterSaveRecord(
                            saveResult.second!!,
                            saveResult.third!!,
                            showResultDialog
                        )
                    }
                } else {
                    ToastUtil.showShort(metaApp, R.string.prompt_insufficient_storage)
                    Timber.d("save record video file to save path failed")
                    ScreenRecordAnalytics.eventSaveRecordFailed("record save file failed")
                }

            }
        }
    }


    /**
     * 允许录屏后开始录屏功能
     */
    fun onRecordUserPermit(resultCode: Int?, data: Intent?) {
        Timber.d("my_record onRecordUserPermit,resultCode:$resultCode,data:$data")
        if (resultCode != null && data != null) {
            this.resultCode = resultCode
            this.intentData = data
        }
    }

    /**
     * 因为暂时不需要“我的录屏”功能，为了避免以后需要添加此功能和减少视频存储的占用，保留“我的录屏”功能逻辑代码，但是删除我的录屏目录下录制的文件
     */
    private fun deleteMyRecordFiles(path: String) {
        Timber.i("file path: $path")
        val modifiedPath = path.replace(Regex("(/[^/]+)$"), "")
        Timber.i("dir path: $modifiedPath")
        val dir = File(modifiedPath)
        dir.listFiles()?.forEach {
            if (it.isFile) {
                it.delete()
            }
        }
        Timber.i("dir size: ${dir.listFiles()?.size}")
    }
}