package com.socialplay.gpark.function.mw.launch.setp

import android.content.Context
import com.meta.biz.mgs.MgsBiz
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.StartupInfo
import com.socialplay.gpark.function.mw.MWDeveloper
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2022/10/25
 */
class TSGameProcessPlotStep : ITSGameProcessStep {

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    override suspend fun process(context: Context, params: TSLaunchParams) {
        val new = StartupInfo(params.gameId, params.packageName, params.gameInfo.startupExtension)
        metaKV.playGame.saveCurStartupInfo(params.packageName, new)
        //添加自定义参数
        params.custom.putAll(MWDeveloper(metaKV).invokeParams())
        val roomCache = MgsBiz.getMgsGameRoom(params.packageName)
        //启动游戏参数构建
        params.buildLaunchJSON(roomCache?.inviteOpenId, roomCache?.roomInfo?.roomIdFromCp)
        // 房间信息在进入TS游戏后清理掉，只能单次使用
        MgsBiz.saveMgsGameRoom(null, params.packageName)
        params.setCoreConfig {
            forceKillGameProcess =
                metaKV.tsKV.lastPlayPlotGameUniqueKey != params.getGameUniqueKey()
        }
        metaKV.tsKV.lastPlayPlotGameUniqueKey = params.getGameUniqueKey()
    }
}