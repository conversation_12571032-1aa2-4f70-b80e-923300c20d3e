package com.socialplay.gpark.function.member

import com.socialplay.gpark.data.interactor.UserMemberInteractor
import com.socialplay.gpark.function.startup.core.ProcessType
import org.koin.core.context.GlobalContext

object UserMemberInit {
    private val userMemberInteractor: UserMemberInteractor by lazy { GlobalContext.get().get()}

    fun init(processType: ProcessType) {
        userMemberInteractor.init(processType)
    }
}