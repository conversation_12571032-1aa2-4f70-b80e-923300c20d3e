package com.socialplay.gpark.function.ipc.provider.ts

import com.socialplay.gpark.util.extension.ProviderKey
import com.meta.ipc.provider.FunctionProvider

/**
 * 该接口仅在TS游戏进程中注册，用于平台调用TS的方法
 */
interface ITSFunctionEntry : FunctionProvider {

    companion object : ProviderKey<ITSFunctionEntry>("com.socialplay.gpark.function.ipc.provider.ts.ITSFunctionEntry");

    /**
     * 调用TS提供的函数
     */
    fun call(action: String, data: Map<String, Any>?)

}