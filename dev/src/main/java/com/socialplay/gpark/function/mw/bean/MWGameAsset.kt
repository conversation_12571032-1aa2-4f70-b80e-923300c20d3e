package com.socialplay.gpark.function.mw.bean

import androidx.annotation.Keep
import org.json.JSONObject

/**
 * 2024/7/5
 */
@Keep
data class MWGameResourceRequest(
    val commitId: String,
    val platform: String,
    val gameList: List<MWGameResourceGameRequest>,
)

@Keep
data class MWGameResourceGameRequest(
    val gameId: String,
    val gameVersion: String,
)

@Keep
data class MWGameResourceResponse(
    val gameId: String,
    val gameVersion: String,
    val url: String,
)


@Keep
data class MWGameAsset(
    val guid: Int,
    val name: String,
    val path: String,
    val sha1: String,
    val size: Long,
//    val subDependencies: List<Any>,
    val type: String,
    val url: String,
) {
    companion object {
        fun fromJsonObj(obj: JSONObject): MWGameAsset {
            val guid = obj.optInt("guid", 0)
            val name = obj.optString("name", "")
            val path = obj.optString("path", "")
            val sha1 = obj.optString("sha1", "")
            val size = obj.optLong("size", 0L)
            val type = obj.optString("type", "")
            val url = obj.optString("url", "")
            return MWGameAsset(guid, name, path, sha1, size, type, url)
        }
    }
}