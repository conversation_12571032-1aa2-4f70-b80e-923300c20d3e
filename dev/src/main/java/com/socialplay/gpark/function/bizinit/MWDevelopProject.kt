package com.socialplay.gpark.function.bizinit

import androidx.fragment.app.Fragment
import com.meta.lib.mwbiz.MWLifeCallback
import com.meta.verse.lib.MetaVerseCore
import com.mw.develop.MWDevelopBiz
import com.mw.develop.model.ReviewGameInfo
import com.mw.develop.model.ReviewGameLanguage
import com.mw.develop.model.VersionInfo
import com.mw.develop.provider.IMWDevelopGameLaunchProvider
import com.mw.develop.provider.IMWDevelopHostProvider
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.TSLaunchWrapper
import com.socialplay.gpark.function.mw.bean.MWStartDeveloper
import com.socialplay.gpark.function.mw.launch.MWStatus
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope

object MWDevelopProject {

    fun initialize() {
        MWDevelopBiz.setHostProvider(object : IMWDevelopHostProvider {
            override fun createLaunchProvider(fragment: Fragment): IMWDevelopGameLaunchProvider {
                return MWDevelopTSLaunch(fragment)
            }

            override fun setOnEngineInfoCallback(callback: (Float, String) -> Unit) {
                MWDevelopEngineCallback(callback)
            }
        })
    }
}

class MWDevelopEngineCallback(private val callback: (Float, String) -> Unit) :
    CoroutineScope by MainScope() {
    init {
        if (MetaVerseCore.get().available()) {
            callback.invoke(1F,MetaVerseCore.get().engineVersion())
        } else {
            MWLifeCallback.downloadProgress.addCallback("MWDevelopEngineCallback") {
                callback.invoke(it, "")
            }
            MWStatus.engineAvailableFlow().collectIn(this) {
                MWLifeCallback.downloadProgress.removeCallback("MWDevelopEngineCallback")
                callback.invoke(1F,MetaVerseCore.get().engineVersion())
            }
        }
    }
}

class MWDevelopTSLaunch(private val fragment: Fragment) : IMWDevelopGameLaunchProvider {

    private val tsLaunchWrapper by lazy { TSLaunchWrapper(fragment) }

    override fun launchGame(
        gameInfo: ReviewGameInfo,
        versionInfo: VersionInfo,
        language: ReviewGameLanguage?
    ) {
        tsLaunchWrapper.listenLaunchFailed { info, throwable ->
            TSLaunchFailedWrapper.show(fragment, info, throwable)
        }
        tsLaunchWrapper.callLaunch {
            when {
                gameInfo.code.isNullOrEmpty() -> {
                    fragment.toast("code empty")

                }

                versionInfo.type != 4 -> {
                    fragment.toast(R.string.debug_apk_not_support)
                }

                else -> {
                    fragment.toast(R.string.game_start_launching)
                    val info = createTSGameDetailInfo(gameInfo.code, gameInfo.packageName?:"", gameInfo.name?:"")
                    val resIdBean = ResIdBean().setGameId(gameInfo.code).setCategoryID(CategoryId.GAME_REVIEW)
                    val params = TSLaunchParams(info, resIdBean).apply {
                        developer = MWStartDeveloper(versionInfo.viewerId?:"",language?.key ?: "")
                    }
                    params.fromDev = true
                    launch(fragment.requireContext(), params)
                }
            }
        }
    }

}