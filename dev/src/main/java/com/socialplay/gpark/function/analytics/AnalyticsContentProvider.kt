package com.socialplay.gpark.function.analytics

import android.content.ContentProvider
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import com.socialplay.gpark.util.GsonUtil

class AnalyticsContentProvider : ContentProvider() {
    companion object {
        private const val URI_SCHEME = "content://"

        const val METHOD_EVENT = "method_event"
        const val EVENT_VALUE = "event_value"


        fun getEventUri(context: Context): Uri {
            return Uri.parse("$URI_SCHEME${context.packageName}.analytics")
        }

    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        if (extras == null) {
            return null
        }
        when (method) {
            METHOD_EVENT -> {
                val json = extras.getString(EVENT_VALUE) ?: return null
                kotlin.runCatching {
                    val eventData = GsonUtil.gson.fromJson(json, MultiProcessEvent::class.java)
                    eventData.event?.let {
                        Analytics.track(it) {
                            eventData.params?.let { params ->
                                putAll(params)
                            }
                        }
                    }
                }
            }
        }


        return null
    }

    override fun onCreate(): Boolean {
        return true
    }


    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }


    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {

        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }


    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }
}