package com.socialplay.gpark.function.umw.impl.avatar

import com.meta.biz.ugc.UGCProtocolBiz
import com.meta.biz.ugc.listener.IMWFunction
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.lib.mwbiz.bean.bridge.SendMsg
import com.socialplay.gpark.function.umw.GameType
import com.socialplay.gpark.function.umw.UMW
import com.socialplay.gpark.function.umw.impl.AbsUMWImpl
import timber.log.Timber

/**
 * 角色游戏的UMW实现,用于和角色游戏交互
 * 角色游戏虽然运行在R进程中，但是游戏给客户端的回调都会发生在主进程中
 * 所以这里的实现和TS游戏的实现有所不同，需要用MWBizProxy通过IPC进行操作
 */
abstract class UMWAvatarImpl : AbsUMWImpl(), UMW.IAvatarInterface {

    init {
        defineProtocolBasicFunc()
    }

    override val ts: UMW.ITsInterface? get() = null

    override val avatar: UMW.IAvatarInterface get() = this

    private fun defineProtocolBasicFunc() {
        UGCProtocolBiz.setFuncListener(object : IMWFunction {
            override fun callUE(json: String): String? {
                if (MWBizProxy.isAvailable()) {
                    MWBizProxy.callUE(json)
                } else {
                    Timber.w("checkcheck_ugc_protocol, onCallUE MWBizProxy.available() == false")
                }
                return null
            }

            override fun registerAction(action: String) {
                MWBizProxy.registerMWMsgAction(action)
            }
        })
    }

    override fun getGameType(): GameType {
        return GameType.Avatar
    }

    override fun callUE(msg: SendMsg) {
        MWBizProxy.callUE(msg)
    }

    override fun currentGameId(): String {
        return MWBizProxy.currentGameId()
    }

    override fun currentGameName(): String {
        return MWBizProxy.currentGameName()
    }

    override fun currentGamePkg(): String {
        return MWBizProxy.currentGamePkg()
    }
}