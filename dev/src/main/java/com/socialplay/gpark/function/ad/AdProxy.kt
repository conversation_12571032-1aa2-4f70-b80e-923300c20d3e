package com.socialplay.gpark.function.ad

import android.content.Context
import android.content.Intent
import com.bin.cpbus.CpEventBus
import com.meta.ipc.IPC
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.ad.AdPlacement
import com.socialplay.gpark.data.model.ad.TSGamePreloadAdTTaiConfig
import com.socialplay.gpark.data.model.editor.GameLoadState
import com.socialplay.gpark.function.ad.event.ProcessSwitchEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.ipc.provider.host.HostFunctionEntry
import com.socialplay.gpark.function.ipc.provider.host.HostIntentStarter
import com.socialplay.gpark.function.ipc.provider.host.IPlatformFunctionEntry
import com.socialplay.gpark.function.ipc.provider.ts.IInGameIntentStarter
import com.socialplay.gpark.function.ipc.provider.ts.ITSFunctionEntry
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.ui.ad.AdActivity
import com.socialplay.gpark.ui.ad.AdShowStatus
import com.socialplay.gpark.ui.ad.AdType
import com.socialplay.gpark.ui.ad.TestRewardedAdActivity
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.get
import com.socialplay.gpark.util.extension.isRegistered
import com.socialplay.gpark.util.extension.runSafety
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

object AdProxy {

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val metaRepository: IMetaRepository by lazy { GlobalContext.get().get() }
    private val scope = MainScope()
    private val ipc by lazy { IPC.getInstance() }
    val gameLaunchParamMap = mutableMapOf<String, Boolean>()
    private val editorGameLoadInteractor: EditorGameLoadInteractor by lazy {
        GlobalContext.get().get()
    }

    init {
        // 处理切换横竖屏状态后的假进度
        EditorGameInteractHelper.avatarStatus.collectIn(scope) {
            val loadState = editorGameLoadInteractor.gameLoadState.value
            Timber.d("AvatarStatus changed status:%s loadState:%s", it, loadState)
            if (loadState is GameLoadState.LoadSuccess && it.isLandscape()) {
                val gameId = metaKV.tsKV.roleGameId
                checkIsPreloadAd(gameId, "")
            }
        }
    }

    fun callAction(processName: String, action: String?, data: Map<String, Any>?) {
        Timber.d("callAction: $processName, $action, $data")
        if (action == null) {
            return
        }
        when (processName) {
            StartupProcessType.H.desc -> {
                HostFunctionEntry.call(action, data)
            }

            StartupProcessType.M.desc -> {
                ipc.runSafety(ITSFunctionEntry) { call(action, data) }
            }
        }
    }

    fun checkIsPreloadAd(gameId: String, gamePkg: String) {
        if (PandoraToggle.isOpenAdFake) {
            Timber.e("checkIsPreloadAd return! isOpenAdFake false gameId: $gameId, gamePkg:$gamePkg")
            return
        }
        if (!ipc.get(IPlatformFunctionEntry).canPreloadAd(gameId, gamePkg)) {
            Timber.e("checkIsPreloadAd return! gameId: $gameId, gamePkg:$gamePkg")
            return
        }
        scope.launch {
            val itemText = metaKV.tTaiKV.preloadAdGameItems
            val jsonText = itemText.ifEmpty {
                val config = metaRepository.getTTaiConfigById(TTaiKV.ID_TS_GAME_PRELOAD_AD_CONFIG)
                    .singleOrNull()?.data
                config?.let {
                    metaKV.tTaiKV.saveConfig(it)
                }
                config?.value
            }
            val gameIdConfig = GsonUtil.gsonSafeParse<TSGamePreloadAdTTaiConfig>(jsonText)
            val canPreloadAd = gameIdConfig?.autoLoad?.firstOrNull { it == gameId } != null
            Timber.d("checkIsPreloadAd jsonText: $jsonText, gameId: $gameId, gamePkg:$gamePkg canPreloadAd: $canPreloadAd")
            if (canPreloadAd) {
                CpEventBus.post(ProcessSwitchEvent(gameId, gamePkg))
            }
        }

    }

    fun backToTsGame(processName: String, gamePkg: String?) {
        Timber.d("backToTsGame:$processName,  $gamePkg")
        when (processName) {
            StartupProcessType.H.desc -> {
                HostIntentStarter.backToGame()
            }

            StartupProcessType.M.desc -> {
                ipc.runSafety(IInGameIntentStarter) {
                    backToGame()
                }
            }
        }

    }


    fun isRewardedAdReady(gameId: String, gamePkg: String): Boolean {
        if (PandoraToggle.isOpenAdFake) {
            return true
        }
        if (!AdToggleCtrl.isActivatedRewardedAd(gameId, gamePkg)) {
            return false
        }
        if (PandoraToggle.adShowAmount < 1) {
            return false
        }
        if (isLaunchFromDev(gameId)) {
            //开发者模式显示测试广告
            return true
        }
        return AdToggleCtrl.isWithinAdCountLimit
    }

    fun isLaunchFromDev(gameId: String?): Boolean {
        return gameLaunchParamMap[gameId] ?: false
    }

    fun startShowRewardedAdResult(
        context: Context,
        processName: String,
        callbackAction: String,
        rewardedAction: String,
        gameId: String?,
        gamePackage: String?,
        data: Map<String, Any>
    ): Boolean {
        if (PandoraToggle.isOpenAdFake) {
            doAdSuccessFake(context, processName, rewardedAction, callbackAction)
            Analytics.track(EventConstants.EVENT_AD_SHOW_SUCCESS_FAKE) {
                put("type", "rewarded")
                put("gamecode", gameId ?: "")
                put("gamepkg", gamePackage ?: "")
            }
            return true
        }
        if (!AdToggleCtrl.isActivatedRewardedAd(gameId, gamePackage)) {
            Timber.d("showRewardedAd not activated")
            return false
        }
        var intent: Intent? = null
        if (isLaunchFromDev(gameId)) {
            //开发者模式显示测试广告
            intent = TestRewardedAdActivity.createIntent(
                context,
                processName,
                AdType.REWARDED,
                gameId = gameId,
                gamePkg = gamePackage,
                data
            )

        } else {
            if (AdToggleCtrl.isWithinAdCountLimit && AdToggleCtrl.isEnoughAdShowInterval) {
                intent = AdActivity.createLaunchIntent(
                    context,
                    processName,
                    AdType.REWARDED,
                    AdPlacement.InGameDefault.value,
                    gameId = gameId,
                    gamePkg = gamePackage,
                    data
                )
            } else {
                Timber.d("showRewardedAd not Allow Show Ad")
                ToastUtil.gameShowShort(context.getString(R.string.not_within_ad_count_limit_tips))
            }
        }
        if (null == intent) {
            Timber.d("showRewardedAd error intent == null")
            return false
        }
        //启动广告
        when (processName) {
            StartupProcessType.H.desc -> {
                HostIntentStarter.startActivity(intent)
                return true
            }

            StartupProcessType.M.desc -> {
                if (ipc.isRegistered(IInGameIntentStarter)) {
                    ipc.get(IInGameIntentStarter).startActivity(intent)
                    return true
                }
            }
        }
        return false
    }

    fun doAdFailFake(
        processName: String,
        callbackAction: String
    ) {
        callAction(
            processName,
            callbackAction,
            linkedMapOf(
                AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_SHOW_ERROR.value,
                AdActivity.KEY_AD_SHOW_RESULT to false
            )
        )
    }

    private fun doAdSuccessFake(
        context: Context,
        processName: String,
        rewardedAction: String,
        callbackAction: String
    ) {
        callAction(processName, rewardedAction, emptyMap())
        callAction(
            processName, callbackAction, linkedMapOf(
                AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_SHOW.value,
                AdActivity.KEY_AD_SHOW_RESULT to true
            )
        )
        callAction(
            processName,
            callbackAction,
            linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_REWARDED.value)
        )
        callAction(
            processName,
            callbackAction,
            linkedMapOf(AdActivity.KEY_AD_SHOW_STATUS to AdShowStatus.AD_CLOSED.value)
        )
        ToastUtil.gameShowShort(context.getString(R.string.toast_ad_free_tips_text))
    }
}