package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink.PARAM_OTHER_USER_NAME
import com.socialplay.gpark.function.deeplink.MetaDeepLink.PARAM_OTHER_UUID
import com.socialplay.gpark.function.router.MetaRouter

/**
 * 跳转聊天界面
 */
class ConversationLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val uuid = data.uri.getQueryParameter(PARAM_OTHER_UUID)?:return LinkHandleResult.Failed("no other uuid")
        val userName = data.uri.getQueryParameter(PARAM_OTHER_USER_NAME)

        MetaRouter.IM.gotoConversation(
            fragment = data.navHost,
            otherUid = uuid,
            title = userName,
            source = data.source
        )
        return LinkHandleResult.Success
    }
}