package com.socialplay.gpark.function.ipc

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/04/27
 *     desc   : 申明需要接收的Action，申明后会被自动注册到MW
 */
enum class ReceivedActionRegistry(val value: String, val desc:String) {
    AD_IS_ACTIVATED("ts.ad.is.activated","Check if certain type of ad is enabled or not"), //判断某种广告类型是否打开
    AD_GET_META_APP_INFO("ts.ad.meta.app.info","Get application info"), //获取app版本信息

    AD_IS_REWARDED_AD_READY("ts.ad.rewarded.isReady","Check if rewarded ad is ready or not"), //判断是否有激励广告
    AD_SHOW_REWARDED_AD("ts.ad.rewarded.show","Display rewarded ad"); //显示激励视频广告

    companion object {
        fun create(value: String): ReceivedActionRegistry? {
            return entries.find { it.value == value }
        }
    }
}