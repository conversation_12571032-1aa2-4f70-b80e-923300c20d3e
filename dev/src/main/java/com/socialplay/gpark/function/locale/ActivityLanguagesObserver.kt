package com.socialplay.gpark.function.locale

import android.app.Activity
import android.app.Application
import android.app.Application.ActivityLifecycleCallbacks
import android.os.Bundle

/**
 * Created by bo.li
 * Date: 2024/4/19
 * Desc: 监听activity回调，检测更新语言，防止因为时机早晚UI没切换语言的问题
 */
class ActivityLanguagesObserver : ActivityLifecycleCallbacks {

    companion object {
        fun observe(application: Application): ActivityLanguagesObserver {
            val observer = ActivityLanguagesObserver()
            application.registerActivityLifecycleCallbacks(observer)
            return observer
        }
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        MetaLanguages.checkUpdateAppLanguage(activity)
        MetaLanguages.checkUpdateAppLanguage(activity.application)
    }

    override fun onActivityStarted(activity: Activity) {}
    override fun onActivityResumed(activity: Activity) {
        MetaLanguages.checkUpdateAppLanguage(activity)
        MetaLanguages.checkUpdateAppLanguage(activity.application)
    }

    override fun onActivityPaused(activity: Activity) {}
    override fun onActivityStopped(activity: Activity) {}
    override fun onActivityDestroyed(activity: Activity) {}
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
}