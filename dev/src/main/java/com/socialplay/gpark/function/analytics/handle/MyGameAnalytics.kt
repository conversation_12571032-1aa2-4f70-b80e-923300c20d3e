package com.socialplay.gpark.function.analytics.handle

import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: 我的游戏相关埋点
 */
object MyGameAnalytics {

    fun trackDeleteMenuShow(gameId: Long, packageName: String, playedDuration: Long) {
        Analytics.track(EventConstants.EVENT_MY_GAME_DELETE_SHOW) {
            put("gameid", gameId)
            put("packagename", packageName)
            put("playedduration", playedDuration)
        }
    }

    fun trackDeleteMenuClick(gameId: Long, packageName: String, playedDuration: Long) {
        Analytics.track(EventConstants.EVENT_MY_GAME_DELETE_CLICK){
            put("gameid", gameId)
            put("packagename", packageName)
            put("playedduration", playedDuration)
        }
    }

    fun trackEditMode(fromLongClick: Boolean) {
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO) {
            put("fromedittype", if (fromLongClick) 2 else 1)
        }
    }

    fun trackSelectDeleteGame(gameId: Long, packageName: String, playedDuration: Long) {
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT) {
            put("gameid", gameId)
            put("packagename", packageName)
            put("playedduration", playedDuration)
        }
    }

    fun trackUnselectedDeleteGame(gameId: Long, packageName: String, playedDuration: Long) {
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT_CANCEL) {
            put("gameid", gameId)
            put("packagename", packageName)
            put("playedduration", playedDuration)
        }
    }

    fun trackSelectAllClick() {
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT_ALL)
    }

    fun trackClickDelete(selectCount: Int) {
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_DELETE) {
            put("selectedcount", selectCount)
        }
    }

    fun trackDeleteDialogShow(){
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_SHOW)
    }

    fun trackDeleteDialogCancel(){
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_CANCEL)
    }

    fun trackDeleteDialogDelete(){
        Analytics.track(EventConstants.EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_DELETE)
    }

}