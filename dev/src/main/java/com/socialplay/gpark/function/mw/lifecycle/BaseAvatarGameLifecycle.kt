package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.app.Application
import com.socialplay.gpark.function.umw.GameType
import com.socialplay.gpark.function.umw.UMW


/**
 * 角色游戏生命周期处理Base类，可以感知角色游戏进入和退出消费态及编辑态
 *
 * 视图流转状态
 * 1、EnterViewModeView
 * 2、-> ExitViewModeView
 * 3、-> EnterEditModeView
 * 4、-> ExitEditModeView
 * 5、-> EnterViewModeView
 *
 * NOTE:
 *      EnterViewModeView 时无Activity引用
 *      ExitViewModeView 时无Activity引用
 *
 *      EnterEditModeView 时有Activity引用(编辑模式的Activity)
 *      EnterViewModeView 时有Activity引用(编辑模式的Activity)
 *
 */
abstract class BaseAvatarGameLifecycle(
    protected val umw: UMW
) : MWLifecycle() {

    private var activityRefCount: Int = 0

    private lateinit var viewMode: AvatarGameViewMode

    private val isAvatarGame: Boolean get() = umw.getGameType() == GameType.Avatar

    protected val avatarGameViewMode: AvatarGameViewMode get() = viewMode

    override fun onAfterApplicationCreated(app: Application) {
        super.onAfterApplicationCreated(app)
        if (!isAvatarGame) {
            throw IllegalStateException("BaseAvatarGameLifecycle only support avatar game")
        }

        // 默认状态下时View状态
        activityRefCount = 0
        viewMode = AvatarGameViewMode.VIEW_VIEW_MODE
        onEnterViewModeView()
    }

    override fun onActivityCreated(activity: Activity) {
        super.onActivityCreated(activity)
        activityRefCount++

        if (isAvatarGame && activityRefCount == 1) {
            val wasViewMode = viewMode == AvatarGameViewMode.VIEW_VIEW_MODE
            if (wasViewMode) {
                onExitViewModeView()
            }
            viewMode = AvatarGameViewMode.EDIT_VIEW_MODE
            onEnterEditModeView(activity)
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        super.onActivityDestroyed(activity)
        activityRefCount--
        if (umw.getGameType() == GameType.Avatar && activityRefCount == 0) {
            val wasEditMode = viewMode == AvatarGameViewMode.EDIT_VIEW_MODE
            if (wasEditMode) {
                onExitEditModeView(activity)
            }
            viewMode = AvatarGameViewMode.VIEW_VIEW_MODE
            onEnterViewModeView()
        }
    }

    /**
     * 进入角色编辑器的View模式视图
     * NOTE:这个只是界面状态发生变化，不表示游戏状态发生变化，因为有可能游戏都还没加载成功
     */
    protected open fun onEnterViewModeView() {
    }

    /**
     *退出角色编辑器View模式视图
     * NOTE:这个只是界面状态发生变化，不表示游戏状态发生变化，因为有可能游戏都还没加载成功
     */
    protected open fun onExitViewModeView() {

    }

    /**
     * 进入角色编辑器编辑模式视图
     * NOTE:这个只是界面状态发生变化，不表示游戏状态发生变化，因为有可能游戏都还没加载成功
     */
    protected open fun onEnterEditModeView(activity: Activity) {
    }

    /**
     * 退出角色编辑器编辑模式视图
     * NOTE:这个只是界面状态发生变化，不表示游戏状态发生变化，因为有可能游戏都还没加载成功
     */
    protected open fun onExitEditModeView(activity: Activity) {
    }

    /**
     * 角色游戏视图模式
     */
    protected enum class AvatarGameViewMode {
        /*角色展示状态*/
        VIEW_VIEW_MODE,

        /*角色编辑状态*/
        EDIT_VIEW_MODE
    }
}