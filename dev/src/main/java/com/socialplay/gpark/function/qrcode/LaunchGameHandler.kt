package com.socialplay.gpark.function.qrcode

import android.content.Context
import androidx.fragment.app.Fragment
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.mgs.MgsScanQrLaunchGameParameter
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.MWDeveloper
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import org.koin.core.context.GlobalContext

/**
 * 扫码启动游戏
 */
object LaunchGameHandler : QRCodeHandler {

    const val KEY_LAUNCH_GAME_PARAMETER = "key.launch.game.parameter"

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }

    override suspend fun process(
        context: Context,
        fragment: Fragment,
        request: ScanRequestData,
        result: ScanResultData
    ): Boolean {
        val customData = request.customData

        val parameter = customData.getParcelable<MgsScanQrLaunchGameParameter>(KEY_LAUNCH_GAME_PARAMETER) ?: return false

        val gamePkgName = parameter.gamePkg
        val gameId = parameter.gameId
        val gameType = parameter.gameType

        if (gameType == "TS") {
            val gameInfo = tsLaunch.createTSGameDetailInfo(gameId, gamePkgName, "")
            val resIdBean = ResIdBean().setGameId(gameId).setCategoryID(CategoryId.QR_CODE_START)
                .setTsType(ResIdBean.TS_TYPE_NORMAL)
            val params = TSLaunchParams(gameInfo, resIdBean).apply {
                custom.putAll(MWDeveloper(metaKV).invokeParams())
            }
            tsLaunch.onLaunchListener {
                onLaunchGameEnd { params, e ->
                    TSLaunchFailedWrapper.show(fragment, params, e)
                }
            }
            tsLaunch.launch(context, params)
//            MWBizBridge.startGame(gameId){
//                custom["dsVersion"] = metaKV.mw.getDevDsVersion()
//            }
        } else {
        }

        return true
    }

}
