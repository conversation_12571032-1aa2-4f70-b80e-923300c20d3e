package com.socialplay.gpark.function.editor

import android.content.Context
import android.os.Looper
import android.text.TextUtils
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.ugc.exception.EditorException
import com.meta.biz.ugc.model.GameTransform
import com.meta.biz.ugc.model.GameTransformPlatform
import com.meta.biz.ugc.model.NewStartGame
import com.meta.biz.ugc.model.RebootStartGame
import com.meta.biz.ugc.model.TGameFeatMsg
import com.meta.biz.ugc.model.TSUserDataLoad
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.mgs.MgsGameConfigData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.pandora.Pandora
import com.socialplay.gpark.function.mw.launch.TsLaunchUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.ifNullOrBlank
import com.socialplay.gpark.util.simJson
import com.socialplay.gpark.util.simMsg
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import kotlin.coroutines.resumeWithException
import kotlin.jvm.Throws

/**
 * <AUTHOR>
 * @date 2022/5/27
 * 移动编辑器游戏相关的交互逻辑
 * 1.游戏跳游戏杀进程处理
 * 2.游戏打开新进程游戏
 * 3.单个游戏状态的变换
 */
object EditorGameInteractHelper {

    //角色展示和编辑游戏ID
    private var ROLE_GAME_ID = ""

    private val mainScope by lazy { MainScope() }

    private val onTransforms = mutableSetOf<OnTsGameTransform>()

    // 横屏页用：从横屏角色游戏启动游戏进程时，显示loading
    var loadingCallback: ((Boolean) -> Unit)? = null

    // 横屏页用：游戏进程 跳转 主进程横屏角色
    private var roleGameToEditCallback: IObserverCallbacks<FullScreenEditorActivityArgs>? = null

    /**
     * 横竖屏用：角色游戏加载流程，角色遮罩不能用此判断
     * 0：没调StartGame
     * 1：调了StartGame
     * 2：回调了开始加载角色游戏的协议
     * 3：回调了开始加载角色形象的协议
     * 4：回调了结束加载角色形象的协议
     */
    val roleUserDataLiveData by lazy { MutableLiveData(ROLE_STATE_NONE) }

    const val ROLE_STATE_NONE = 0
    const val ROLE_STATE_START = 1
    const val ROLE_STATE_GAME_LOAD_START = 2
    const val ROLE_STATE_ROLE_LOAD_START = 3
    const val ROLE_STATE_ROLE_LOAD_END = 4

    /**
     * @return true：角色游戏开始加载了，说明已经登录完毕
     */
    @Deprecated("backend have handled error if role game not login")
    fun checkRoleGameReady(): Boolean {
        return (roleUserDataLiveData.value ?:0) >= ROLE_STATE_GAME_LOAD_START
    }

    // 都用：是否应当展示角色遮罩
    val roleMaskLiveData by lazy { MutableLiveData(true) }

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    private val _avatarStatus = MutableStateFlow(AvatarStatus(false, GameTransform.STATUS_ROLE_VIEW))
    val avatarStatus:StateFlow<AvatarStatus> get() = _avatarStatus

    private val transformVersion = AtomicLong(1)
    val currentTransformVersion get() = transformVersion.get()

    // 记录游戏内的状态，用于将游戏和客户端的状态同步
    private val _gameInternalTransformStatus = MutableStateFlow<GameInternalTransformStatus?>(null)
    val gameInternalTransformStatus:StateFlow<GameInternalTransformStatus?> = _gameInternalTransformStatus

    private var needCheckRoleData = true

    init {
        ROLE_GAME_ID = metaKV.tsKV.roleGameId
    }

    suspend fun resetLoadStatus() {
        roleUserDataLiveData.value = ROLE_STATE_NONE
        roleMaskLiveData.value = true
        _avatarStatus.value = AvatarStatus(false, GameTransform.STATUS_ROLE_VIEW)
    }

    fun registerMainActivity(context: Context, lifecycleOwner: LifecycleOwner) {
        roleGameToEditCallback = ObserverCallbacks.observe(lifecycleOwner, Lifecycle.State.CREATED) { game ->
            if (game.status == GameTransform.STATUS_ROLE_EDIT) {
                Timber.d("open landscape role game from game process")
                lifecycleOwner.lifecycleScope.launchWhenResumed {
                    val resIdBean = saveRoleResIdBean(CategoryId.JUMP_GAME_DIF_PROCESS,
                        game.targetGameId).setParamExtra(game.targetGameId)
                    Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
                        putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                        put("from", "jump_game_difprocess")
                        put("from_gameid", game.fromGameId)
                    }
                    MetaRouter.MobileEditor.fullScreenRole(context, game)
                }
            }
        }
    }

    //获取角色游戏ID
    fun getRoleGameId(): String {
        return ROLE_GAME_ID
    }

    //设置角色游戏ID
    fun setRoleGameId(roleGameId: String) {
        ROLE_GAME_ID = roleGameId
        metaKV.tsKV.roleGameId = ROLE_GAME_ID
    }

    //游戏跳游戏杀进程处理
    @Deprecated("游戏跳游戏逻辑在MW引擎层实现")
    fun rebootStartGame(gameId: String, roomIdFromCp: String, inviteOpenId: String, gameType: Int, gamePkg: String) {
        when (gameType) {
            RebootStartGame.TYPE_STAND_ALONE, RebootStartGame.TYPE_NETWORK -> {
                mainScope.launch {
                    MgsBiz.saveMgsConfig(gameId, gamePkg)
                    metaKV.mgsKV.saveMgsGameConfig(MgsGameConfigData(gameId), gamePkg)
                    delay(1000)
//                    MWBizBridge.startGame(gameId) {
//                        mgs.inviteOpenId = inviteOpenId
//                        mgs.roomIdFromCp = roomIdFromCp
//                        custom["dsVersion"] = metaKV.mw.getDevDsVersion()
//                    }
                }
            }
            else                                                           -> {}
        }
    }

    //从主进程打开游戏进程的TS游戏
    @Deprecated("Not needed anymore")
    fun newStartGameFromHostProcess(
        context: Context,
        currentGameId: String,
        gameId: String,
        gamePkg: String,
        roomIdFromCp: String,
        inviteOpenId: String,
        gameType: Int,
        isUgc: Boolean,
    ) {
        if (TextUtils.equals(currentGameId, ROLE_GAME_ID)) {
            mainScope.launch { //切换游戏到小屏幕
                //启动新的游戏
                loadingCallback?.invoke(true)
                when (gameType) {
                    NewStartGame.TYPE_STAND_ALONE, NewStartGame.TYPE_NETWORK -> {
                        if (!TextUtils.isEmpty(gameId)) {
                            TsLaunchUtil.resumeFromGame(
                                gameId,
                                gamePkg,
                                context,
                                isUgc,
                                inviteOpenId,
                                roomIdFromCp
                            )
                        }
                    }
                }
                gameTransform(ROLE_GAME_ID, GameTransform.STATUS_ROLE_VIEW, false)
            }
        }
    }

    //从游戏进程打开主进程的TS游戏
    fun newStartGameFromMProcess(currentGameId: String, currentGamePkg: String, gameId: String, roomIdFromCp: String, inviteOpenId: String, gameType: Int, gameParams: String, gamePkg: String) {
        Timber.d("newStartGameFromMProcess $ROLE_GAME_ID")
        if (TextUtils.equals(gameId, ROLE_GAME_ID)) {
            val status = runCatching { JSONObject(gameParams).optString("status") }.getOrElse { GameTransform.STATUS_ROLE_VIEW }
            val args = FullScreenEditorActivityArgs(
                status = status, fromGameId = currentGameId,fromGamePkg = currentGamePkg,
                targetGameId = gameId, targetGamePkg = gamePkg, categoryId = CategoryId.JUMP_GAME_DIF_PROCESS,
                roomIdFromCp = roomIdFromCp, inviteOpenId = inviteOpenId
            )
            roleGameToEditCallback?.postData(args)
        }
    }

    //注册Ts游戏转换监听
    fun registerOnGameTransform(onTransform: OnTsGameTransform) {
        if (!onTransforms.contains(onTransform)) {
            onTransforms.add(onTransform)
        }
    }

    //移除Ts游戏转换监听
    fun removeOnGameTransform(onTransform: OnTsGameTransform) {
        if (onTransforms.contains(onTransform)) {
            onTransforms.remove(onTransform)
        }
    }

    //游戏转换
    fun gameTransform(gameId: String, status: String, fastFinish: Boolean = true) {
        if (TextUtils.equals(ROLE_GAME_ID, gameId)) {
            onTransforms.onEach { it.onTransform(status, fastFinish) }
            Timber.d("checkcheck_role received ${if (status == GameTransform.STATUS_ROLE_EDIT) "landscape" else "vertical"} protocol")
        }
    }


    /**
     * 同步游戏内的状态到客户端
     */
    fun syncTransformStatus(status: String, transformVersion: String?) {
        Timber.d("checkcheck_role syncTransformStatus target:${status} current:${_gameInternalTransformStatus.value} transformVersion:${transformVersion}")
        _gameInternalTransformStatus.value = GameInternalTransformStatus(status, transformVersion)
    }

    fun sendTsGameTransform(
        status: String,
        fakeProgressNeeded: Boolean,
        transformOpacityData: String? = null,
        customData: String? = null,
        tryOnData: RoleGameTryOn? = null,
        callback: (() -> Unit)? = null
    ) {
        mainScope.launch(Dispatchers.Default) {
            if (MWBizProxy.isAvailable()) {
                Timber.d(
                    "sendTsGameTransform status:${status}" +
                            " opacityData:${transformOpacityData}" +
                            " fakeProgressNeeded:${fakeProgressNeeded}" +
                            " isAvailable:${MWBizProxy.isAvailable()}" +
                            " current:${_avatarStatus.value.status}" +
                            " currentOpacityData:${_avatarStatus.value.opacityData}" +
                            " transformVersion:${transformVersion.get()}"
                )

                transformVersion.incrementAndGet()
                // 请求游戏更新状态之前，先更新本地记录的游戏内的状态
                _gameInternalTransformStatus.value = GameInternalTransformStatus(status, transformVersion.get().toString())

                UGCProtocolSender.sendProtocol(
                    ProtocolSendConstant.PROTOCOL_ROLE_TRANSFORM,
                    0,
                    GameTransformPlatform().apply {
                        this.gameId = ROLE_GAME_ID
                        this.status = status
                        this.data = transformOpacityData ?: ""
                        this.customData = customData.ifNullOrBlank {
                            if (status == GameTransform.STATUS_ROLE_VIEW) {
                                "{}"
                            } else {
                                ""
                            }
                        }
                        this.tryOnData = tryOnData?.let {
                            if (it.sendWithTransform) {
                                GsonUtil.safeToJson(it)
                            } else {
                                null
                            }
                        }
                    }
                )

                _avatarStatus.value = AvatarStatus(fakeProgressNeeded, status, transformOpacityData)

                callback?.invoke()

                tryOnData?.let {
                    if (!it.sendWithTransform) {
                        sendTryOn(tryOnData)
                    }
                }
            }
        }
    }

    fun sendTryOn(tryOn: RoleGameTryOn?) {
        if (tryOn == null || !tryOn.hasTryOnData) return
        mainScope.launch(Dispatchers.Default) {
            if (MWBizProxy.isAvailable()) {
                val params = mapOf(
                    "tryOnUserId" to tryOn.tryOnUserId,
                    "from" to tryOn.from,
                    "change_photo" to tryOn.changePhoto.toString(),
                    "allowTryOn" to tryOn.allowTryOn,
                    "roleId" to tryOn.roleId,
                    "roleData" to tryOn.roleData,
                    "clothesItemId" to tryOn.clothesItemId,
                    "itemId" to tryOn.itemId,
                )

                UGCProtocolSender.sendProtocol(
                    ProtocolSendConstant.PROTOCOL_CLIENT_GAME_FEATURE,
                    0,
                    TGameFeatMsg.roleTryOn(
                        ROLE_GAME_ID,
                        params.simJson()
                    )
                )
            }
        }
    }

    //TS游戏进入加载状态消息，此时可以取消233对Ts游戏的遮挡，UI过渡顺滑
    fun gameTsLoading() {
        Timber.d("checkcheck_role gameTsLoading")
        changeRoleGameLoadState(ROLE_STATE_GAME_LOAD_START)
        changeShowMaskData(true)
    }


    fun isRoleGameLoaded(): Boolean {
        return roleUserDataLiveData.value == ROLE_STATE_ROLE_LOAD_END
    }

    //TS游戏用户角色数据加载，true:开始加载，false:加载结束
    fun useDataLoad(userDataLoad: TSUserDataLoad) {
        if (userDataLoad.isStart()) {
            MainScope().launch {
                Timber.d("checkcheck_role userDataLoad.isStart()")
                changeRoleGameLoadState(ROLE_STATE_ROLE_LOAD_START)
                changeShowMaskData(true)
            }
        }
        if (userDataLoad.isComplete()) {
            MainScope().launch {
                Timber.d("checkcheck_role userDataLoad.isComplete()")
                changeRoleGameLoadState(ROLE_STATE_ROLE_LOAD_END)
                changeShowMaskData(false)
                checkRoleData()
            }
        }
    }

    fun changeShowMaskData(show: Boolean) {
        Timber.d("checkcheck_role changeShowMaskData ${if (show) "open" else "close"}")
        roleMaskLiveData.postValue(show)
    }

    fun resetCheckRoleData() {
        needCheckRoleData = true
    }

    fun checkRoleData() {
        Timber.d("checkcheck_role checkRoleData")
        if (!needCheckRoleData) return
        needCheckRoleData = false
        Timber.d("checkcheck_role checkRoleData sendProtocol")
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_CLIENT_GAME_FEATURE,
            0,
            TGameFeatMsg.getRoleData(getRoleGameId(), emptyMap<String, String>().simJson())
        )
    }

    /**
     * 启动游戏
     */
    @Throws(Exception::class)
    suspend fun startGameUseView(context: Context, gameId: String, categoryId: Int) {
        Timber.d("checkcheck_role startGameUseView")

        changeRoleGameLoadState(ROLE_STATE_START)
        changeShowMaskData(true)

        startGameUseViewInternal(context, gameId, categoryId)
    }

    private suspend fun startGameUseViewInternal(
        context: Context,
        gameId: String,
        categoryId: Int
    ) {
        return suspendCancellableCoroutine {
            val tsLaunch = TSLaunch()

            tsLaunch.onLaunchListener {
                onLaunchGameEnd { params, e ->
                    if (e == null) {
                        it.resumeWith(Result.success(Unit))
                    } else {
                        it.resumeWithException(e)
                    }
                }
            }
            val gameInfo = tsLaunch.createTSGameDetailInfo(gameId, "", "")
            val resIdBean = ResIdBean().setGameId(gameId).setCategoryID(categoryId)
            val params = TSLaunchParams(gameInfo, resIdBean)
            tsLaunch.launchView(context, params)
        }
    }

    private fun changeRoleGameLoadState(state: Int) {
        if(Looper.getMainLooper() == Looper.myLooper()){
            roleUserDataLiveData.value = state
        }else{
            roleUserDataLiveData.postValue(state)
        }
    }

    fun analyticsLoaderException(exception: EditorException) {
        when (exception.type) {
            EditorException.ERROR_DOWNLOAD -> {
                Pandora.send(EventConstants.EVENT_UGC_PROJECT_DOWNLOAD_ABNORMAL) {
                    put("reason", "${exception.simMsg()}")
                }
            }

            EditorException.ERROR_COPY -> {
                Pandora.send(EventConstants.EVENT_UGC_PROJECT_COPY_ABNORMAL) {
                    put("reason", "${exception.cause.simMsg()}")
                    put("log", "${exception.message}")
                }
            }

            EditorException.ERROR_UNZIP -> {
                Pandora.send(EventConstants.EVENT_UGC_PROJECT_UNZIP_ABNORMAL) {
                    put("reason", "${exception.simMsg()}")
                }
            }
        }
    }

    fun hasStartedRoleGame(): Boolean {
        return roleUserDataLiveData.value != ROLE_STATE_NONE
    }

    fun saveRoleResIdBean(categoryId: Int, gameId: String): ResIdBean {
        val resIdBean = ResIdBean()
            .setGameId(gameId)
            .setClickGameTime(System.currentTimeMillis())
            .setCategoryID(categoryId)
        metaKV.analytic.saveLaunchResIdBean(gameId, resIdBean)
        return resIdBean
    }
}

fun onTsGameTransform(call: (status: String, fastFinish: Boolean) -> Unit) =
    object : OnTsGameTransform {
        override fun onTransform(status: String, fastFinish: Boolean) {
            call.invoke(status, fastFinish)
        }
    }

interface OnTsGameTransform {
    fun onTransform(status: String, fastFinish: Boolean)
}

data class GameInternalTransformStatus(val status: String?, val transformVersion: String?)