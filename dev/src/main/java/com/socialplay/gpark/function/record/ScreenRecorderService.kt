package com.socialplay.gpark.function.record

import android.app.Notification
import com.socialplay.gpark.function.record.ScreenRecordInteractor.startRecord
import android.content.Intent
import timber.log.Timber
import android.app.NotificationManager
import android.app.NotificationChannel
import android.app.Service
import android.content.Context
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi
import com.socialplay.gpark.R

class ScreenRecorderService : Service() {
    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        Timber.i("startCommand...")
        createNotificationChannel()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            startRecord()
        } else {
            stopForeground(true)
            stopSelf()
        }
        return START_NOT_STICKY
    }

    private fun createNotificationChannel() {
        val builder = Notification.Builder(this.applicationContext) //获取一个Notification构造器
        builder.setContentText(applicationContext.getString(R.string.recording))
            .setWhen(System.currentTimeMillis())

        /*以下是对Android 8.0的适配*/
        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(CHANNEL_ID)
        }
        //前台服务notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            val channel = NotificationChannel(CHANNEL_ID, applicationContext.getString(R.string.recording), NotificationManager.IMPORTANCE_LOW)
            notificationManager.createNotificationChannel(channel)
        }
        val notification = builder.build() // 获取构建好的Notification
        startForeground(11210, notification)
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onDestroy() {
        stopForeground(true)
        super.onDestroy()
    }

    companion object {
        const val CHANNEL_ID = "screen_record"

        @RequiresApi(api = Build.VERSION_CODES.O)
        fun startService(context: Context) {
            val service = Intent(context, ScreenRecorderService::class.java)
            context.startForegroundService(service)
        }

        fun stopService(context: Context) {
            val service = Intent(context, ScreenRecorderService::class.java)
            context.stopService(service)
        }
    }
}