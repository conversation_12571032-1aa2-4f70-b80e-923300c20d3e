package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.app.Application
import android.content.Context
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.model.AndroidCommonResult
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.PlotChooseImageEvent
import com.socialplay.gpark.data.model.moments.PlotChoiceFriendItem
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.ui.moments.main.MomentsChoiceFriendGameView
import com.socialplay.gpark.ui.moments.main.MomentsRecordLoadingView
import com.socialplay.gpark.ui.moments.main.SimMultiItem
import org.greenrobot.eventbus.Subscribe
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * 2023/9/20
 */
class PlotChoiceFriendsLifecycle : MWLifecycle(), KoinComponent {

    private val accountInteractor: AccountInteractor by inject()
    private val friendInteractor: FriendInteractor by inject()
    private val metaKV: MetaKV by inject()

    private lateinit var friendGameView: MomentsChoiceFriendGameView
    private lateinit var recordLoadingView: MomentsRecordLoadingView

    private val avatarList = mutableListOf<SimMultiItem>()
    private val myAvatarList = mutableListOf<SimMultiItem>()
    private val friendAvatarList = mutableListOf<SimMultiItem>()

    private var curResumedActivity: Activity? = null

    override fun onAfterApplicationCreated(app: Application) {
        super.onAfterApplicationCreated(app)
        initData(app)
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        curResumedActivity = activity
        if (!this::friendGameView.isInitialized) {
            friendGameView = MomentsChoiceFriendGameView(activity)
        }
        if (!this::recordLoadingView.isInitialized) {
            recordLoadingView = MomentsRecordLoadingView(activity)
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        curResumedActivity = null
        super.onActivityDestroyed(activity)
    }

    private fun initData(context: Context) {
        //设置AvatarList
        accountInteractor.accountLiveData.observeForever { info ->
            myAvatarList.clear()
            info?.let {
                myAvatarList.add(
                    PlotChoiceFriendItem(-1, it.portrait ?: "", it.nickname ?: "", it.uuid ?: "", true)
                )
            }
            notifyAvatarList()
        }
        //默认数据
        friendAvatarList.clear()

        friendInteractor.friendList.observeForever { list ->
            friendAvatarList.clear()
            val friendAvatars = list.map {
                PlotChoiceFriendItem(-1, it.avatar ?: "", it.name ?: "", it.uuid, false)
            }
            if (friendAvatars.isNotEmpty()) {
                friendAvatarList.addAll(friendAvatars)
            }
            notifyAvatarList()
        }
        notifyAvatarList()

    }

    private fun notifyAvatarList() {
        synchronized(avatarList) {
            avatarList.clear()
            if (myAvatarList.isNotEmpty()) {
                avatarList.addAll(myAvatarList)
                if (friendAvatarList.isNotEmpty()) {
                    avatarList.addAll(friendAvatarList)
                }
            }
        }
    }

    fun showChooseFriend(selectCount: Int, onResult: (List<Pair<String, Int>>) -> Unit) {
        synchronized(avatarList) {
            friendGameView.setOnSaveListener {
                onResult.invoke(it)
            }
            friendGameView.showWithData(selectCount, avatarList)
        }
    }

    suspend fun showRecordLoading(time: Long, hint: String) {
        recordLoadingView.show(time, hint)
    }

    suspend fun closeRecordLoading() {
        recordLoadingView.close()
    }

    fun goChooseImage(ratioWidth: Int, ratioHeight: Int, useClip: Boolean, messageId: Int, request: GameCommonFeature) {
        curResumedActivity?.let {
            CpEventBus.register(this)
            MetaRouter.Plot.chooseImage(it, ratioWidth, ratioHeight, useClip, messageId, request.gameId)
        } ?: run {
            UGCProtocolSender.sendProtocol(
                ProtocolSendConstant.PROTOCOL_CLIENT_GAME_RESULT,
                0,
                AndroidCommonResult(
                    request.feature,
                    request.gameId,
                    hashMapOf(
                        "code" to 2002,
                        "data" to null,
                        "errMsg" to "application is null"
                    )
                ).getDataMapPackedResult()
            )
        }
    }

    @Subscribe
    fun onPlotProtocolEvent(event: PlotChooseImageEvent) {
        if (StartupContext.get().processType != StartupProcessType.M) {
            return
        }
        val data = if (event.url.isNullOrBlank()) {
            null
        } else {
            hashMapOf("url" to event.url)
        }
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_CLIENT_GAME_RESULT,
            event.messageId,
            AndroidCommonResult(
                GameCommonFeature.FEATURE_CHOOSE_IMAGE,
                event.gameId,
                hashMapOf(
                    "code" to event.code,
                    "data" to data,
                    "errMsg" to event.message
                )
            ).getDataMapPackedResult()
        )
        CpEventBus.unregister(this)
    }
}