package com.socialplay.gpark.function.qrcode

import android.content.Context
import android.net.Uri
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.function.router.MetaRouter

/**
 * Web地址处理
 */
object WebUrlHandler : QRCodeHandler {

    override suspend fun process(
        context: Context,
        fragment: Fragment,
        request: ScanRequestData,
        result: ScanResultData
    ): Boolean {

        val content = result.content

        if (content.startsWith("http") && fragment.isAdded) {
            val uri = kotlin.runCatching { Uri.parse(result.content) }.getOrNull()
            val isInnerLink = TrustLinkHandler.isTrustedUrlLink(uri)
            MetaRouter.Web.navigate(fragment, url = content, isWebOutside = !isInnerLink)
            return true
        }

        return false
    }
}