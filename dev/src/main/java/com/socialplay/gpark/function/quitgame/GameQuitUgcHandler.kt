package com.socialplay.gpark.function.quitgame

import android.app.Activity
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.game.GameQuitInfo
import com.socialplay.gpark.data.model.gamereview.CheckAppraisedRequest
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.editor.detail.comment.UgcCommentDialog
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.firstActiveChildFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/05/11
 *     desc   :
 * </pre>
 */
object GameQuitUgcHandler : IGameQuitHandler {

    private val repository by lazy { GlobalContext.get().get<IMetaRepository>() }

    override suspend fun onGameQuit(
        activity: Activity,
        dialogFragment: DialogFragment?,
        gameInfo: GameQuitInfo,
        quitReason: Int
    ): Boolean {
        if (!gameInfo.isUgcGame) {
            Timber.d("GameQuitUgcHandler: not ugc")
            return false
        }
        if (!PandoraToggle.ugcCommentPopupShow) {
            Timber.d("GameQuitUgcHandler: comment dialog toggle off")
            return false
        }
        if (!PandoraToggle.enableUgcDetail) {
            Timber.d("GameQuitUgcHandler: ugc detail disabled")
            return false
        }
        val ugcId = gameInfo.gameId
        if (ugcId.isNullOrBlank()) {
            Timber.d("GameQuitUgcHandler: ugc id empty")
            return false
        }
        if (!canComment(ugcId)) {
            Timber.d("GameQuitUgcHandler: cannot comment")
            return false
        }
        return withContext(Dispatchers.Main) {
            val activeFragment = if (dialogFragment?.canShowDialog == true) {
                dialogFragment
            } else if (activity.hasWindowFocus()) {
                findFragment(activity)?.let {
                    if (it.canShowDialog) {
                        it
                    } else {
                        null
                    }
                }
            } else {
                null
            }
            if (activeFragment == null) {
                Timber.d("GameQuitUgcHandler: no active fragment in $activity")
                false
            } else {
                UgcCommentDialog.show(activeFragment, ugcId)
                Timber.d("GameQuitUgcHandler: ok")
                true
            }
        }
    }

    /**
     * 判断自己是否可发言 & 有无评论
     */
    private suspend fun canComment(ugcId: String): Boolean {
        val banned = runCatching {
            repository.queryUserMuteStatus().invoke().isMuted == true
        }.getOrDefault(false)
        Timber.d("GameQuitUgcHandler: isMuted $banned")
        if (banned) return false
        val hasComment = runCatching {
            repository.checkIfHaveComment(
                CheckAppraisedRequest.MODULE_TYPE_REPEATABLE_GAME_APPRAISE,
                ugcId
            ).invoke()
        }.getOrDefault(false)
        Timber.d("GameQuitUgcHandler: hasUgcComment $hasComment")
        return !hasComment
    }

    private fun findFragment(activity: Activity): Fragment? {
        return runCatching {
            if (activity is MainActivity) {
                activity.firstActiveChildFragment()
            } else {
                null
            }
        }.getOrNull()
    }
}