package com.socialplay.gpark.function.mw

import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.lifecycle.*
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.MwGameStartSceneBinding
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.LoadGameResourceDialog
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.doOnLayoutSized
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setSize
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * xingxiu.hou
 * 2022/2/15
 */
class MWGameStartScenes(val fragment: Fragment) : LifecycleEventObserver,
    LoadGameResourceEventCallback {

    companion object {
        private val mainThread by lazy { Looper.getMainLooper().thread }
        private val VALUE = Any()

        private const val VERSION_VIEW = 1
        private const val VERSION_DIALOG = 2
    }

    private val isShowing = AtomicBoolean(false)

    private val scenesView: View

    private var viewGroup: ViewGroup?
    private var binding: MwGameStartSceneBinding? = null
    private val lifecycleScope: LifecycleCoroutineScope = fragment.lifecycleScope

    private val observers: ConcurrentHashMap<LoadGameResourceEventCallback, Any> by lazy { ConcurrentHashMap() }
    private var dialog: LoadGameResourceDialog? = null

    private var prevHorizontal = false
    private var preAutoHide = false

    init {
        fragment.lifecycle.addObserver(this)

        scenesView =
            MwGameStartSceneBinding.inflate(LayoutInflater.from(fragment.requireContext())).also { binding = it }.root
        viewGroup = runCatching {
            fragment.requireActivity().window.decorView as ViewGroup
        }.getOrNull()
    }

    private fun isMainThread() = mainThread == Thread.currentThread()

    fun register(observer: LoadGameResourceEventCallback) {
        if (!isMainThread()) {
            throw Exception("observe must main thread")
        }
        if (fragment.lifecycle.currentState == Lifecycle.State.DESTROYED) {
            return
        }
        observers[observer] = VALUE
    }

    fun unregister(observer: LoadGameResourceEventCallback) {
        observers.remove(observer)
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_STOP || event == Lifecycle.Event.ON_DESTROY) {
            if (event == Lifecycle.Event.ON_DESTROY) {
                observers.clear()
            }
            hide()
        }
    }

    fun show(
        horizontal: Boolean = false,
        autoHide: Boolean = true,
        type: String = EditorGameLaunchHelper.TYPE_NORMAL
    ) {
        prevHorizontal = horizontal
        preAutoHide = autoHide
        if (type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
            dialog?.let {
                runCatching {
                    if (it.isAdded && !it.isDetached) {
                        it.dismissAllowingStateLoss()
                    }
                }
            }
            dialog = LoadGameResourceDialog.show(fragment, this)
        } else if (isShowing.compareAndSet(false, true)) {
            if (horizontal) {
                binding?.container?.doOnLayoutSized {
                    val width = it.width
                    it.setSize(
                        it.height,
                        width
                    )
                    it.pivotX = 0f
                    it.pivotY = 0f
                    it.animate()
                        .translationX(width.toFloat())
                        .setDuration(0)
                        .rotation(90f)
                        .start()
                    changeBackgroundOrientation(true)
                }
            }
            addView(scenesView)
            fragment.requireActivity().window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            if (autoHide) {
                //自动延时10秒关闭
                lifecycleScope.launch(Dispatchers.Main) {
                    delay(10_000)
                    hide()
                }
            }
        }
    }

    fun changeBackgroundOrientation(horizontal: Boolean = false) {
        binding?.bg?.setImageResource(if (horizontal) R.drawable.mwpageframe else R.drawable.mwpageframe_portrait)
    }

    fun hide() {
        val dialog = dialog
        if (dialog != null) {
            runCatching {
                if (dialog.isAdded && !dialog.isDetached) {
                    dialog.dismissAllowingStateLoss()
                }
            }
            this.dialog = null
        } else if (isShowing.compareAndSet(true, false)) {
            closeView(scenesView)
        }
    }


    private fun addView(view: View) {
        runCatching {
            if (fragment is BaseDialogFragment) {
                viewGroup = fragment.dialog?.window?.decorView as? ViewGroup
            }
            viewGroup?.also {
                val activity = fragment.activity
                if (activity != null) {
                    val navHeight = ScreenUtil.getNavigationBarHeightIfRoom(activity)
                    Timber.d("MwStartScene addView navHeight:$navHeight")
                    if (navHeight > 0) {
                        binding?.bottomContainer?.setMargin(bottom = navHeight)
                    }
                }
                it.addView(
                    view, ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
                if (activity != null && fragment.isStatusBarTextDark()) {
                    StatusBarUtil.setDarkMode(activity)
                }
            }
        }
    }

    private fun closeView(view: View) {
        runCatching {
            viewGroup?.removeView(view)
            val activity = fragment.requireActivity()
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            if (fragment.isStatusBarTextDark()) {
                StatusBarUtil.setLightMode(activity)
            }
        }
    }

    fun Fragment.isStatusBarTextDark(): Boolean {
        return when (this) {
            is BaseFragment<*> -> {
                isStatusBarTextDark()
            }
            is BaseDialogFragment -> {
                isStatusBarTextDark()
            }
            else -> {
                true
            }
        }
    }

    fun isShowing(): Boolean {
        return isShowing.get() || dialog != null
    }

    override fun onCancel() {
        for (observer in observers.keys()) {
            observer.onCancel()
        }
    }

    override fun onDismiss() {
        this.dialog = null
    }

    fun switchToScene() {
        show(horizontal = prevHorizontal, autoHide = preAutoHide)
        dialog?.dismissAllowingStateLoss()
        dialog = null
    }
}

interface LoadGameResourceEventCallback {
    fun onCancel()
    fun onDismiss() {}
}