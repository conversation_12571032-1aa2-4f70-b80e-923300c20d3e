package com.socialplay.gpark.function.analytics.resid

import com.socialplay.gpark.BuildConfig
import java.lang.RuntimeException
import java.util.HashMap

/**
 * create by: bin on 2020-02-26
 */
object ResIdUtils {

    @JvmOverloads
    fun getAnalyticsMap(resIdBean: ResIdBean?, isDownload: Boolean = false): HashMap<String, Any> {
        val map = HashMap<String, Any>()

        val pre = if (isDownload) "download_" else "show_"
        val fix = if (isDownload) "download_" else "" // 必须加前缀区分 , 否则play_game会覆盖参数


        if (resIdBean != null) {
            map["ugc_type"] = resIdBean.getTsType()
            map["fileid"] = resIdBean.getFileId().toString()
            map["ugc_parent_id"] = resIdBean.getGameCode() ?: ""

            map[pre + "categoryID"] = resIdBean.getCategoryID()
            map[pre + "param1"] = resIdBean.getParam1()
            map[pre + "param2"] = resIdBean.getParam2()
            map[pre + "paramExtra"] = resIdBean.getParamExtra()?:""
            map[pre + "source"] = resIdBean.getSource()
            map[pre + "icon_type"] = resIdBean.getIconType().toString()
            map[fix + "gameId"] = resIdBean.getGameId() ?: ""
            map[fix + "reqId"] = resIdBean.getReqId() ?: ""
            map[fix + "isSpec"] = resIdBean.getIsSpec()
            map[fix + "type"] = resIdBean.getType()
            map[fix + "typeID"] = resIdBean.getTypeID() ?: ""
            map[fix + "iconId"] = resIdBean.getIconID()
            map[fix + "icon_type"] = resIdBean.getIconType().toString()
            map[fix + "rerank_method"] = resIdBean.getReRankMethod()
            resIdBean.getExtras()?.let {
                map.putAll(it)
            }
            if (resIdBean.getGameVersionName()?.isNotEmpty() == true) {
                map["game_version_name"] = resIdBean.getGameVersionName() ?: ""
            }

        }

        return map
    }

}
