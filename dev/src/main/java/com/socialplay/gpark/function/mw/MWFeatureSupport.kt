package com.socialplay.gpark.function.mw

import com.meta.biz.ugc.model.FeatureSupportResultMsg
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import timber.log.Timber


object MWFeatureSupport {

    private const val MEMBER = "member"
    private const val IS_IAP_ENABLE = "isIAPEnable"
    private const val IS_PARTY_EDITOR_VIEW_RECHARGE_ENABLE = "isPartyEditorViewRechargeEnable"
    private const val AVATAR_HALF_SCREEN = "avatarHalfScreen"
    private const val IS_FOLLOW_ENABLE = "isFollowEnable"
    private const val IS_OPEN_PLAYER_HOME_ENABLE = "isOpenPlayerHomeEnable"
    private const val IS_EDITOR_VIEW_RECHARGE_ENABLE = "isEditorViewRechargeEnable"
    private const val IS_SUPPORT_IAP_DIALOG = "isSupportIAPDialog"
    private const val CHOOSE_IMAGE = "choose_image_v2"
    // 穿搭style社区
    private const val STYLE_COMMUNITY = "styleCommunity"
    private const val CHOOSE_SINGLE_IMAGE_BY_LOCAL = "chooseSingleImageByLocal"
    private const val SAVE_SINGLE_IMAGE_TO_GALLERY = "saveSingleImageToGallery"
    private const val CLOTHES_UGC = "clothesUgcV2"
    private const val IS_POINT_ENABLE = "isPointEnable"
    private const val FEATURE_IM_SDK = "isImSdkEnable"
    // 直接分享
    const val COMMON_SHARE_DIRECT = "share.common.direct"
    // OC短剧分享
    const val TS_SHARE_OC_MOMENT = "share.ts.ocMoment"
    const val PUBLISH_POST_GPARK = "publish_post_gpark"
    // 服装ugc
    const val ROLE_UGC_STORE = "isSupportUGCStore"

    const val SHARE_ROLE_SCREENSHOT = "share.role.screenshot"

    const val BUILD_UGC_GUIDE = "build.ugc.guide"
    const val NOTIFY_EVENT = "notify_event"

    data class MWSupportFunction(
        private val functionName: String,
        val isSupport: (isHost: Boolean) -> Boolean = { true }
    ) {

        companion object {
            val map: HashMap<String, MWSupportFunction> = hashMapOf()
            val member = MWSupportFunction(MEMBER) {
                return@MWSupportFunction (PandoraToggleWrapper.IAP_PREMIUM != "0")
            }
            val iap = MWSupportFunction(IS_IAP_ENABLE)
            val isPartyEditorViewRechargeEnable = MWSupportFunction(IS_PARTY_EDITOR_VIEW_RECHARGE_ENABLE, isSupport = { _ ->
                    // 派对才有这个 feature
                    return@MWSupportFunction EnvConfig.isParty()
                })
            val uploadFile = MWSupportFunction(GameCommonFeature.FEATURE_UPLOAD_FILE)
            val avatarHalfScreen = MWSupportFunction(AVATAR_HALF_SCREEN){
                return@MWSupportFunction PandoraToggle.openAvatarHalfScreen
            }
            val isFollowEnable = MWSupportFunction(IS_FOLLOW_ENABLE)
            val isOpenPlayerHomeEnable = MWSupportFunction(IS_OPEN_PLAYER_HOME_ENABLE)
            val isEditorViewRechargeEnable = MWSupportFunction(IS_EDITOR_VIEW_RECHARGE_ENABLE)
            val isSupportIAPDialog = MWSupportFunction(IS_SUPPORT_IAP_DIALOG)

            // 剧情创作选择并裁剪图片
            val chooseImage = MWSupportFunction(CHOOSE_IMAGE) { isHost ->
                return@MWSupportFunction !isHost
            }

            val jumpWeb = MWSupportFunction(GameCommonFeature.FEATURE_JUMP_WEB)
            val isPointEnable = MWSupportFunction(IS_POINT_ENABLE)

            val styleCommunity = MWSupportFunction(STYLE_COMMUNITY){
                return@MWSupportFunction PandoraToggle.enableStyleCommunity
            }
            // 拉起相册选取一张图片，并复制到指定目录里
            val localDuplicateImage = MWSupportFunction(CHOOSE_SINGLE_IMAGE_BY_LOCAL)
            // 保存单张图片到图库
            val saveSingleImageToGallery = MWSupportFunction(SAVE_SINGLE_IMAGE_TO_GALLERY)
            // 服装ugc
            val clothesUgc = MWSupportFunction(CLOTHES_UGC) {
                return@MWSupportFunction PandoraToggle.openClothesUgcV2
            }

            val isImSdkEnable = MWSupportFunction(FEATURE_IM_SDK, { true })
            val tsOcMomentShare = MWSupportFunction(TS_SHARE_OC_MOMENT) {
                PandoraToggle.enableShareOcMoment && !it
            }

            val commonDirectShare = MWSupportFunction(COMMON_SHARE_DIRECT)

            val publishPostGpark = MWSupportFunction(PUBLISH_POST_GPARK) {
                false
            }

            val ugcDesignFeed = MWSupportFunction(ROLE_UGC_STORE) {
                MainBottomNavigationItem.hasAssetTab && it
            }

            val copyRoleScreenshot = MWSupportFunction(GameCommonFeature.FEATURE_COPY_ROLE_SCREENSHOT) {
                it
            }
            val shareRoleScreenshot = MWSupportFunction(SHARE_ROLE_SCREENSHOT) {
                it
            }
            val buildUgcGuide = MWSupportFunction(BUILD_UGC_GUIDE) {
                it
            }
        }

        init {
            check(!map.containsKey(functionName)) { "must unique functionName" }
            map[functionName] = this@MWSupportFunction
        }

    }

    fun isSupportFeature(feature: String, messageId: Int, isHost: Boolean): Boolean {
        Timber.i("support: $feature")
        val isSupport = MWSupportFunction.map[feature]?.isSupport?.invoke(isHost) == true
        sendToMW(feature, messageId, isSupport)
        return isSupport
    }

    private fun sendToMW(feature: String, messageId: Int, isSupport: Boolean) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_MEMBER_DETECT_RESULT,
            messageId,
            hashMapOf<String, Any>().apply {
                FeatureSupportResultMsg(feature, isSupport).addJsonData(this)
            })
        //todo 角色编辑器如果是独立进程需要使用 MWBizProxy
    }
}