package com.socialplay.gpark.function.share

import android.app.Activity
import android.net.Uri
import com.socialplay.gpark.data.model.share.ShareData

/**
 * 视频分享
 */
object MetaShare {

    /**
     * 分享到系统
     */
    fun shareVideo(
        context: Activity,
        videoUri: Uri,
        shareChannel: ShareWrapper.RecordShareChannelInfo,
        index: Int,
        shareText: String = ""
    ) {
        ShareWrapper.shareVideo(context, videoUri, shareChannel, index, shareText)
    }


    fun share(context: Activity, shareData: ShareData) {
        ShareWrapper.share(context, shareData)
    }
}