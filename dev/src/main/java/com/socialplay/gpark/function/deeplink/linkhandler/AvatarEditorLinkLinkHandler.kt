package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

// adb shell am start -a android.intent.action.VIEW -d "gpark://gpark.fun?action=avatarEditor&categoryId=1&data=xxxx"
class AvatarEditorLinkLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink AvatarEditorLinkLinkHandler handle uri:%s", data.uri)

        val categoryId =
            data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: -1

        val opacityData = data.uri.getQueryParameter("data")

        MetaRouter.MobileEditor.avatarEditor(data.activity, categoryId, opacityData)

        chain.handle(data)

        return LinkHandleResult.Success
    }
}