package com.socialplay.gpark.function.mw.bean

import androidx.annotation.Keep
import org.json.JSONObject

/**
 * Created by bo.li
 * Date: 2022/11/8
 * Desc:
 */
@Keep
data class MWStartDeveloper(
    val viewerId: String = "",
    val language: String = "",
) {
    internal fun toJsonObject(): JSONObject {
        return JSONObject().apply {
            if (viewerId.isNotEmpty()) {
                put("viewerId", viewerId)
            }
            if (language.isNotEmpty()) {
                put("language", language)
            }
        }
    }
}