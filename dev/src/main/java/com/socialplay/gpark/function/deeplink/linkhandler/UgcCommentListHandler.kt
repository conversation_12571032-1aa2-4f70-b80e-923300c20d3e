package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/14
 *     desc   :
 * </pre>
 */
class UgcCommentListHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink UgcCommentListHandler handle uri:%s", data.uri)

        val ugcId = data.uri.getQueryParameter(MetaDeepLink.PARAM_GAME_ID)
        if (ugcId.isNullOrBlank()) return LinkHandleResult.Failed("no ugc id")
        val categoryId = data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: -1
        val targetCommentId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_COMMENT_ID)
        val targetReplyId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_REPLY_ID)

        MetaRouter.MobileEditor.ugcDetail(
            data.navHost,
            ugcId,
            null,
            ResIdBean().setCategoryID(categoryId),
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId
        )
        return LinkHandleResult.Success
    }
}