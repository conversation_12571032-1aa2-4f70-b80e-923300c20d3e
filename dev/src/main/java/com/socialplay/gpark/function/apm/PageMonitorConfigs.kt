package com.socialplay.gpark.function.apm

import com.socialplay.gpark.ui.editor.create.EditorCreateV2FormworkFragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2MineFragment
import com.socialplay.gpark.ui.editor.create.v3.EditorCreateV3FormworkFragment
import com.socialplay.gpark.ui.editor.create.v4.EditorCreateV4FormworkFragment
import com.socialplay.gpark.ui.editor.home.v2.EditorHomeFragmentV3
import com.socialplay.gpark.ui.editor.module.UgcModuleHomeFragment
import com.socialplay.gpark.ui.editor.module.UgcModuleMyWorkFragment
import com.socialplay.gpark.ui.editor.module.UgcModuleTabFragment
import com.socialplay.gpark.ui.editor.module.rookie.UgcAssetRookieFragment
import com.socialplay.gpark.ui.gamedetail.unify.PgcGameDetailFragment
import com.socialplay.gpark.ui.gamedetail.unify.UgcGameDetailFragment
import com.socialplay.gpark.ui.im.ConversationListFragment
import com.socialplay.gpark.ui.main.MainFragment
import com.socialplay.gpark.ui.main.maps.HomeMapsFragment
import com.socialplay.gpark.ui.main.maps.newgame.NewGamesFragment
import com.socialplay.gpark.ui.outfit.ProfileUgcDesignFragment
import com.socialplay.gpark.ui.outfit.UgcDesignDetailFragment
import com.socialplay.gpark.ui.outfit.UgcDesignFeedFragment
import com.socialplay.gpark.ui.outfit.feed.UgcAssetFeedTabFragment
import com.socialplay.gpark.ui.outfit.feed.UgcAssetRookieTabFragment
import com.socialplay.gpark.ui.post.feed.profile.ProfileCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.tag.RecommendCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.tag.TagCommunityFeedFragment
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabFragment
import com.socialplay.gpark.ui.post.topic.detail.TopicSortFeedFragment
import com.socialplay.gpark.ui.post.topic.square.TopicSquareFragment
import com.socialplay.gpark.ui.post.v2.PostDetailFragment
import com.socialplay.gpark.ui.profile.HeProfileFragment
import com.socialplay.gpark.ui.profile.MeProfileFragment
import com.socialplay.gpark.ui.profile.home.ProfileFragment
import com.socialplay.gpark.ui.profile.outfit.ProfileTabOutfitFragment
import com.socialplay.gpark.ui.profile.recent.ProfileTabRecentFragment
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcFragment
import com.socialplay.gpark.ui.recommend.RecommendFragment
import org.koin.ext.getFullName

/**
 * 2024/1/24
 */
fun PageMonitor.pageMonitorConfig() {
    page(MainFragment::class.getFullName()) {
        eventPageClassName = "MainFragment"
    }
    page(HomeMapsFragment::class.getFullName()) {
        eventPageClassName = "HomeMapsFragment"
    }
    page(RecommendFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "RecommendFragment"
    }
    page(NewGamesFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "NewGamesFragment"
    }
    page(CommunityFeedTabFragment::class.getFullName()) {
        eventPageClassName = "CommunityFeedTabFragment"
    }
    page(RecommendCommunityFeedFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "RecommendCommunityFeedFragment"
    }
    page(TagCommunityFeedFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "TagCommunityFeedFragment"
    }
    page(TopicSortFeedFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "TopicSortFeedFragment"
    }
    page(TopicSquareFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "TopicSquareFragment"
    }
    page(PgcGameDetailFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "PgcGameDetailFragment"
    }
    page(UgcGameDetailFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcGameDetailFragment"
    }
    page(UgcDesignFeedFragment::class.getFullName()) {
        eventPageClassName = "UgcDesignFeedFragment"
    }
    page(UgcAssetFeedTabFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcAssetFeedTabFragment"
    }
    page(UgcAssetRookieTabFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcAssetRookieTabFragment"
    }
    page(UgcAssetRookieFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcAssetRookieFragment"
    }
    page(UgcDesignDetailFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcDesignDetailFragment"
    }
    page(PostDetailFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "PostDetailFragment"
    }
    page(ConversationListFragment::class.getFullName()) {
//        requestApi = true 这个页面需要以来第三方链接之后，才会请求接口，这个先不监控接口
        eventPageClassName = "ConversationListFragment"
    }
    page(EditorCreateV2Fragment::class.getFullName()){
        eventPageClassName = "EditorCreateV2Fragment"
    }
    page(EditorCreateV2FormworkFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "EditorCreateV2FormworkFragment"
    }
    page(EditorCreateV3FormworkFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "EditorCreateV3FormworkFragment"
    }
    page(EditorCreateV4FormworkFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "EditorCreateV4FormworkFragment"
    }
    page(EditorCreateV2MineFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "EditorCreateV2MineFragment"
    }
    page(EditorHomeFragmentV3::class.getFullName()) {
        eventPageClassName = "EditorHomeFragmentV3"
    }
    page(MeProfileFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "MeProfileFragment"
    }
    page(HeProfileFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "HeProfileFragment"
    }
    page(ProfileFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfileFragment"
    }
    page(ProfileTabRecentFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfileTabRecentFragment"
    }
    page(ProfilePublishedUgcFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfilePublishedUgcFragment"
    }
    page(ProfileCommunityFeedFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfileCommunityFeedFragment"
    }
    page(ProfileTabOutfitFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfileTabOutfitFragment"
    }
    page(ProfileUgcDesignFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "ProfileUgcDesignFragment"
    }
    page(UgcModuleTabFragment::class.getFullName()) {
        eventPageClassName = "UgcModuleTabFragment"
    }
    page(UgcModuleHomeFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcModuleHomeFragment"
    }
    page(UgcModuleMyWorkFragment::class.getFullName()) {
        requestApi = true
        eventPageClassName = "UgcModuleMyWorkFragment"
    }
}
