package com.socialplay.gpark.function.share.screenshot

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.graphics.BitmapFactory
import android.graphics.Point
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.TextUtils
import android.view.WindowManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Locale

/**
 * Created by <PERSON><PERSON> on 2024/3/25
 * <AUTHOR>
 */
object ScreenshotEventDispatcher {

    private var screenRealSize: Point? = null

    /**
     * Listener brought with Config configuration, only holds one and will be replaced by a new page config at any time
     */
    private var screenshotConfigListener: IScreenshotListener? = null

    /**
     * ### Set screenshot config
     * @param screenshotListener [IScreenshotListener]?
     */
    fun setListener(screenshotListener: IScreenshotListener?) {
        screenshotConfigListener = screenshotListener
    }

    /**
     * ### Refresh screenshot config
     */
    fun unsetListener() {
        screenshotConfigListener = null
    }

    /**
     * ### Handle media content change (get 1st data and determine whether it is a screenshot file)
     * @param contentUri Uri
     * @param context Context?
     * @param startListenTime Long
     */
    suspend fun handleMediaContentChange(
        contentUri: Uri,
        context: Context?,
        startListenTime: Long?
    ) {
        if (context == null) {
            return
        }
        if (screenRealSize == null) screenRealSize = getRealScreenSize(context)

        var cursor: Cursor? = null

        try {
            cursor = getContentResolverCursor(contentUri, context)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        if (cursor == null) {
            return
        }

        if (!cursor.moveToFirst()) {
            if (!cursor.isClosed) cursor.close()
            return
        }

        // Get all of colum index
        with(cursor) {
            val dataIndex = getColumnIndex(MediaStore.Images.ImageColumns.DATA)
            val dateAddedIndex = getColumnIndex(MediaStore.Images.ImageColumns.DATE_ADDED)
            val widthIndex = getColumnIndex(MediaStore.Images.ImageColumns.WIDTH)
            val heightIndex = getColumnIndex(MediaStore.Images.ImageColumns.HEIGHT)

            // Handle media row data
            // File path
            val filePath = cursor.getScreenshotFilePath(dataIndex)
            if (!isFilePathLegal(filePath)) return@with
            // File Date Added
            val dateAdded = cursor.getScreenshotFileDateAdded(dateAddedIndex)
            if (!isFileCreationTimeLegal(dateAdded, startListenTime)) return@with
            // File Size
            val (width, height) = cursor.getScreenshotFileSize(
                filePath,
                widthIndex,
                heightIndex
            )
            if (!isFileSizeLegal(width, height)) return@with
            handleScreenshot(filePath)
        }
        if (!cursor.isClosed) cursor.close()
    }

    /**
     * ### Handle screenshot
     * @param filePath String
     */
    private suspend fun handleScreenshot(filePath: String?) = withContext(Dispatchers.Main) {
        screenshotConfigListener?.onShot(filePath)
    }

    /**
     * ### Get screenshot file path
     * @receiver [Cursor]
     * @return [String]
     */
    private fun Cursor.getScreenshotFilePath(dataIndex: Int): String =
        this.getString(dataIndex) ?: ""

    /**
     * ### Get screenshot file date added
     * @receiver [Cursor]
     * @return [Long]
     */
    private fun Cursor.getScreenshotFileDateAdded(dateAddedIndex: Int): Long =
        (this.getLong(dateAddedIndex) * 1000)

    /**
     * ### Get screenshot file size
     * @receiver [Cursor]
     * @return [Pair]
     */
    private fun Cursor.getScreenshotFileSize(
        filePath: String,
        widthIndex: Int,
        heightIndex: Int
    ): Pair<Int, Int> =
        if (widthIndex >= 0 && heightIndex >= 0) {
            Pair(this.getInt(widthIndex), this.getInt(heightIndex))
        } else {
            // Before API 16, the width and height need to be obtained manually.
            val size = getImageSize(filePath)
            Pair(size.x, size.y)
        }

    /**
     * ### Determine the file path
     * @param filePath String
     * @return Boolean
     */
    private fun isFilePathLegal(filePath: String?): Boolean {
        // File path is not empty
        if (filePath == null || TextUtils.isEmpty(filePath)) {
            return false
        }

        // File path contains screenshot KEYWORDS
        var hasValidScreenshot = false
        val lowerPath = filePath.lowercase(Locale.getDefault())
        for (keyWork: String in KEYWORDS) {
            if (lowerPath.contains(keyWork)) {
                hasValidScreenshot = true
                break
            }
        }
        return hasValidScreenshot
    }

    /**
     * ### Determine the file creation time
     * If the time added to the database is before the start of listening or the difference is greater than 10 seconds from the current time, the screenshot file is considered not the current file.
     * @param dateAdded Long
     * @param startListenTime Long
     * @return Boolean
     */
    private fun isFileCreationTimeLegal(dateAdded: Long?, startListenTime: Long?) =
        !(dateAdded == null
                || startListenTime == null
                || dateAdded / 1000 < startListenTime / 1000
                || (System.currentTimeMillis() - dateAdded) > MAX_COST_TIME)

    /**
     * ### Determine the file size.
     * If the image size exceeds the screen, it is considered that the current screenshot file is not a local screenshot.
     * @param width Int
     * @param height Int
     * @return Boolean
     */
    private fun isFileSizeLegal(width: Int?, height: Int?) =
        screenRealSize?.let {
            if (width == null || height == null) {
                false
            } else if (!((width <= it.x && height <= it.y) || (height <= it.x && width <= it.y))) {
                false
            } else {
                true
            }
        } ?: false


    /**
     * ### Get real screen size
     * @param context Context
     * @return [Point]?
     */
    private fun getRealScreenSize(context: Context): Point? {
        var screenSize: Point? = null
        try {
            screenSize = Point()
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val defaultDisplay = windowManager.defaultDisplay
            defaultDisplay.getRealSize(screenSize)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return screenSize
    }

    /**
     * ### Get image size
     * @param imagePath String
     * @return [Point]
     */
    private fun getImageSize(imagePath: String): Point {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(imagePath, options)
        return Point(options.outWidth, options.outHeight)
    }

    /**
     * ### Get content cursor
     * @param contentUri Uri
     * @param context Context
     * @param maxCount Int
     * @return [Cursor]
     */
    private fun getContentResolverCursor(
        contentUri: Uri,
        context: Context,
        maxCount: Int = 1
    ) = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val bundle = Bundle().apply {
            putStringArray(
                ContentResolver.QUERY_ARG_SORT_COLUMNS,
                arrayOf(MediaStore.Images.ImageColumns.DATE_MODIFIED)
            )
            putInt(
                ContentResolver.QUERY_ARG_SORT_DIRECTION,
                ContentResolver.QUERY_SORT_DIRECTION_DESCENDING
            )
            putInt(ContentResolver.QUERY_ARG_LIMIT, maxCount)
        }
        context.contentResolver.query(
            contentUri,
            MEDIA_PROJECTIONS_API_16,
            bundle,
            null,
        )
    } else {
        context.contentResolver.query(
            contentUri,
            MEDIA_PROJECTIONS,
            null,
            null,
            "${MediaStore.Images.ImageColumns.DATE_MODIFIED} desc limit ${maxCount}",
        )
    }

    private const val MAX_COST_TIME = 10 * 1000
    private val MEDIA_PROJECTIONS = arrayOf(
        MediaStore.Images.ImageColumns.DATA,
        MediaStore.Images.ImageColumns.DATE_ADDED,
    )
    private val MEDIA_PROJECTIONS_API_16 = arrayOf(
        MediaStore.Images.ImageColumns.DATA,
        MediaStore.Images.ImageColumns.DATE_ADDED,
        MediaStore.Images.ImageColumns.WIDTH,
        MediaStore.Images.ImageColumns.HEIGHT,
    )
    private val KEYWORDS = arrayOf(
        "screenshot", "screen_shot", "screen-shot", "screen shot",
        "screencapture", "screen_capture", "screen-capture", "screen capture",
        "screencap", "screen_cap", "screen-cap", "screen cap",
    )
}