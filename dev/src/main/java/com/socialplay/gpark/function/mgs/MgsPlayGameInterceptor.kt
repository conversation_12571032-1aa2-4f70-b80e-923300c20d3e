package com.socialplay.gpark.function.mgs

import android.os.Bundle
import com.meta.biz.mgs.MgsBiz
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.mgs.MgsGameConfigData
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.interceptor.IPlayGameInterceptor
import com.socialplay.gpark.function.interceptor.InterceptorController.KEY_IS_MGS
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class MgsPlayGameInterceptor : IPlayGameInterceptor,
    KoinComponent {

    private val metaKv: MetaKV by inject()

    override suspend fun onIntercept(pkgName: String, gameId: String, bundle: Bundle, resIdBean: ResIdBean?): Pair<<PERSON><PERSON><PERSON>, Bundle> {
        if (pkgName.isEmpty()) {
            return false to bundle
        }
        val isMgs = bundle.getBoolean(KEY_IS_MGS)
        if (!isMgs) {
            return false to bundle
        }
        MgsBiz.saveMgsConfig(gameId, pkgName)
        metaKv.mgsKV.saveMgsGameConfig(MgsGameConfigData(gameId), pkgName)
        //拉起mgs游戏前,需要检查是否链接融云,
        RongImHelper.needConnect()
        return false to bundle
    }
}