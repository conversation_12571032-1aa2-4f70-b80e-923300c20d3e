package com.socialplay.gpark.function.mw.launch

import com.socialplay.gpark.function.ad.AdProxy
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference

/**
 * xingxiu.hou
 * 2023/3/7
 */
typealias OnParamsCall = (params: TSLaunchParams) -> Unit

typealias OnLaunchEndCall = (params: TSLaunchParams, e: Throwable?) -> Unit

class SimpleOnTSLaunchListener : OnTSLaunchListener {

    private val log by lazy { Timber.tag("SimpleOnTSLaunchListener") }

    //引擎开始下载
    fun onStartDownload(call: OnParamsCall) {
        onStartDownload.set(call)
    }

    private val onStartDownload: AtomicReference<OnParamsCall> = AtomicReference()
    override fun onStartDownload(params: TSLaunchParams) {
        log.d("onStartDownload")
        onStartDownload.get()?.invoke(params)
    }

    //引擎暂停下载
    fun onPauseDownload(call: OnParamsCall) {
        onPauseDownload.set(call)
    }

    private val onPauseDownload: AtomicReference<OnParamsCall> = AtomicReference()
    override fun onPauseDownload(params: TSLaunchParams) {
        log.d("onPauseDownload")
        onPauseDownload.get()?.invoke(params)
    }

    //开始准备拉起（引擎已经下载完，会回调此方法）
    fun onLaunchPrepare(call: OnParamsCall) {
        onLaunchPrepare.set(call)
    }

    private val onLaunchPrepare: AtomicReference<OnParamsCall> = AtomicReference()
    override fun onLaunchPrepare(params: TSLaunchParams) {
        log.d("onLaunchPrepareStart")
        onLaunchPrepare.get()?.invoke(params)
    }

    //开始启动游戏，所有的客户端逻辑都走完了，下一步要执行内核的拉起方法
    fun onLaunchGame(call: OnParamsCall) {
        onLaunchGame.set(call)
    }

    private val onLaunchGame: AtomicReference<OnParamsCall> = AtomicReference()
    override fun onLaunchGame(params: TSLaunchParams) {
        log.d("onLaunchGame")
        onLaunchGame.get()?.invoke(params)
    }

    //启动游戏结束，Throwable为空时表示启动成功，否则表示启动失败
    private val onLaunchGameEnd: AtomicReference<OnLaunchEndCall> = AtomicReference()
    fun onLaunchGameEnd(call: OnLaunchEndCall) {
        onLaunchGameEnd.set(call)
    }

    override fun onLaunchGameEnd(params: TSLaunchParams, e: Throwable?) {
        log.d("onLaunchGameEnd")
        if (e == null) {
            //记录游戏启动来源,用于播放测试广告
            AdProxy.gameLaunchParamMap[params.gameId] = params.fromDev
        }
        onLaunchGameEnd.get()?.invoke(params, e)
    }
}

interface OnTSLaunchListener {

    /**开始下载引擎*/
    fun onStartDownload(params: TSLaunchParams) {}

    /**暂停下载引擎*/
    fun onPauseDownload(params: TSLaunchParams) {}

    /**准备开始拉起参数和环境*/
    fun onLaunchPrepare(params: TSLaunchParams) {}

    /**开始拉起游戏*/
    fun onLaunchGame(params: TSLaunchParams) {}

    /**拉起游戏结束*/
    fun onLaunchGameEnd(params: TSLaunchParams, e: Throwable?) {}


}