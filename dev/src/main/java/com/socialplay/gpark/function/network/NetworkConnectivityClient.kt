// Copyright Epic Games, Inc. All Rights Reserved.
package com.socialplay.gpark.function.network

interface NetworkConnectivityClient {
    enum class NetworkTransportType {
        WIFI, VPN, ETHERNET, CELLULAR, BLUETOOTH, UNKNOWN,M2G,M3G,M4G,M5G,
    }

    interface Listener {
        fun onNetworkAvailable(networkTransportType: NetworkTransportType)
        fun onNetworkLost()
    }


    abstract class ListenerAdapter : Listener{
        override fun onNetworkAvailable(networkTransportType: NetworkTransportType) {
        }

        override fun onNetworkLost() {
        }
    }


    /**
     * See [NetworkConnectivityClient.addListener]
     */
    fun addListener(listener: Listener): Bo<PERSON>an

    /**
     * @param listener The listener to add. Will be stored as a weak reference so a hard reference
     * must be saved externally.
     * @param fireImmediately Whether to trigger the listener with the current network state
     * immediately after adding.
     * @return Whether the change listener was added. Will be false if already registered.
     */
    fun addListener(listener: Listener, fireImmediately: Boolean): Boolean

    /**
     * Remove a given listener.
     * @return Whether the change listener was removed. Will be false if not currently registered.
     */
    fun removeListener(listener: Listener): <PERSON><PERSON>an

    /**
     * Check for network connectivity
     */
    fun checkConnectivity()
}