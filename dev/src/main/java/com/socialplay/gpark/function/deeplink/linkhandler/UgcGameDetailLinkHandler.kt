package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/09
 *     desc   :
 * </pre>
 */
class UgcGameDetailLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink UgcGameDetailLinkHandler handle uri:%s", data.uri)

        val ugcId = data.uri.getQueryParameter(MetaDeepLink.PARAM_GAME_ID) ?: return LinkHandleResult.Failed("no ugc id")
        val parentId = data.uri.getQueryParameter(MetaDeepLink.PARAM_PARENT_ID)
        val categoryId =
            data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: -1
        val resId = ResIdBean
            .newInstance()
            .setTsType(ResIdBean.TS_TYPE_UCG)
            .setGameCode(parentId)
            .setCategoryID(categoryId)
            .setGameId(ugcId)
        val targetCommentId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_COMMENT_ID)
        val targetReplyId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_REPLY_ID)
        MetaRouter.MobileEditor.ugcDetail(
            data.navHost,
            ugcId,
            parentId,
            resId,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
            source = data.source
        )
        return LinkHandleResult.Success
    }
}