package com.socialplay.gpark.function.deeplink

import android.app.Activity
import android.net.Uri
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.NavHostFragment
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.deeplink.linkhandler.*
import com.socialplay.gpark.function.deeplink.linkhandler.PublishPostLinkHandler
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-05-25 8:51 下午
 * @desc: deepLink 统一处理入口
 * 示例：https://newplayza.page.link?amv=0&apn=com.prodigy.game.playza&link=https://playza.com?action=friendShare&share_id=${shareId}&source=xxx&type=add_friend
 * *注意*：其中link=后面跟的所有字符都是link的、有嵌套关系的参数，使用时需要将link=后的所有字符进行URL-encoded，否则解析不到所有参数（可以网上直接搜一个在线网址）
 */
object MetaDeepLink {
    const val KEY_JUMP_ACTION = "KEY_JUMP_ACTION"

    const val KEY_ACTION = "action"
    const val KEY_COMMAND = "command"
    const val KEY_COMMON_BACK = "common_back"   // 正常返回，可以直接使用上一个页面
    const val KEY_PUSH_LINK_VALUE = "linkValue"
    const val KEY_PUSH_LINK_TYPE = "linkType"

    const val KEY_PUSH_MSG_EXTRA = "msgExtra"

    const val KEY_PUSH_TASK_ID = "taskId"
    const val KEY_PUSH_TASK_CONFIG_ID = "taskConfigId"
    const val KEY_PUSH_TEMPLATE_ID = "pushTemplateId"
    // 本地Bundle形式跳转tab
    const val ACTION_JUMP_TAB = "tab"
    // 跳转任意tab
    const val ACTION_JUMP_TAB_ANY = "tabAny"
    // 从游戏跳转到详情页
    const val ACTION_JUMP_GAME_DETAIL_FROM_GAME = "jump_game_detail_from_game"
    //从游戏跳转UGC详情页
    const val ACTION_JUMP_UGC_GAME_DETAIL_FROM_GAME = "jump_ugc_game_detail_from_game"
    //跳转发帖页
    const val ACTION_JUMP_POST_PUBLISH = "postPublish"
    // 跳转界面
    private const val ACTION_NAVIGATE = "navigate"
    // 跳转扫一扫
    const val ACTION_SCAN = "scan"
    // 跳转充值
    const val ACTION_CHARGE = "charge"
    // 跳转登录
    const val ACTION_LOGIN = "login"
    // 跳转账号设置页面
    const val ACTION_BIND_ACCOUNT = "bind_account"
    // 跳转编辑用户信息
    const val ACTION_EDIT_PROFILE = "edit_profile"
    // 展示游戏评价弹窗
    const val ACTION_POP_UP_GAME_REVIEW = "pop_up_game_review"
    // 跳转游戏详情页
    const val ACTION_GAME_DETAIL = "gameDetail"
    // 跳转ugc详情页
    const val ACTION_UGC_GAME_DETAIL = "ugcGameDetail"
    // mgs房间分享
    private const val ACTION_MGS_SHARE = "mgsShare"
    // 邀请好友分享
    private const val ACTION_FRIEND_SHARE = "friendShare"
    // 游戏评价列表
    private const val ACTION_GAME_COMMENT_LIST = "gameCommentList"
    // 个人主页
    const val ACTION_USER_HOME = "userHome"
    // 帖子详情
    private const val ACTION_POST_DETAIL = "postDetail"
    // 聊天界面
    private const val ACTION_CONVERSATION = "conversation"
    // 跳转应用市场--更新
    private const val ACTION_MARKET = "market"
    // 好友申请页面
    private const val ACTION_FRIEND_REQUEST = "friendRequest"
    // Moments列表页面
    private const val ACTION_MOMENTS = "moments"
    // 建造页
    private const val ACTION_EDITOR_CREATE = "editorCreate"
    // 通知页
    private const val ACTION_GROUP_NOTIFICATION = "groupNotification"
    // ugc评论列表
    private const val ACTION_UGC_COMMENT_LIST = "ugcCommentList"
    // 视频流
    private const val ACTION_VIDEO_FEED = "videoFeed"
    // 话题广场
    private const val ACTION_TOPIC_SQUARE = "topicSquare"
    // 角色编辑器 编辑态
    const val ACTION_JUMP_AVATAR_EDITOR = "avatarEditor"
    // 反馈
    const val ACTION_FEED_BACK = "feedback"
    // web
    const val ACTION_WEB = "web"
    // 激活
    const val ACTION_ACTIVATE = "activate"
    // 分享给联系人
    const val ACTION_CONTACTS = "contacts"
    // ugc服装详情页
    const val ACTION_UGC_DESIGN_DETAIL = "ugcDesignDetail"
    // 币余额页面
    const val ACTION_COIN_BALANCE = "coinBalance"
    // 币余额页面
    const val ACTION_TOPIC_DETAIL = "topicDetail"
    // 实名认证页面
    const val ACTION_REAL_NAME = "real_name"

    // 底栏id
    const val PARAM_TAB_ID = "tab_id"
    // 来源游戏包名
    const val PARAM_FROM_GAME_PACKAGE_NAME = "from_game_package_name"
    // 来源游戏id
    const val PARAM_FROM_GAME_ID = "from_game_id"
    // 来源游戏类型
    const val PARAM_FROM_GAME_TYPE = "from_game_type"
    // 额外参数，可传args
    const val PARAM_EXTRA_BUNDLE = "extra_bundle"
    // 是否自动下载
    const val PARAM_GAME_AUTO_DOWNLOAD = "game_auto_download"
    // 游戏code
    const val PARAM_GAME_ID = "gameId"
    // 父模板id
    const val PARAM_PARENT_ID = "parentId"
    // 物品id
    const val PARAM_ITEM_ID = "itemId"

    // 游戏包名
    const val PARAM_GAME_PACKAGE_NAME = "gamePackageName"
    // category id
    const val PARAM_CATEGORY_ID = "categoryId"
    //ugc_id
    // 来源游戏id
    const val PARAM_FROM_UGC_GAME_ID = "from_ugc_game_id"
    // 用户id
    const val PARAM_USER_ID = "uid"
    // 来自
    const val PARAM_FROM = "from"
    // 帖子id
    const val PARAM_POST_ID = "postId"
    // 定位评论id
    const val PARAM_TARGET_COMMENT_ID = "targetCommentId"
    // 定位回复id
    const val PARAM_TARGET_REPLY_ID = "targetReplyId"
    // 作者id
    const val PARAM_AUTHOR_ID = "authorId"
    // uuid
    const val PARAM_OTHER_UUID = "otherUid"
    // 用户名
    const val PARAM_OTHER_USER_NAME = "otherUserName"
    // 系统消息groupId
    const val PARAM_GROUP_ID = "groupId"

    const val PARAM_BOTTOM_TAB_PENDING_DATA = "bottom_tab_pending_data"
    // 用户反馈：gameId
    const val PARAM_FEEDBACK_GAME_ID = "feedbackGameId"
    // 默认选中type
    const val PARAM_DEFAULT_TYPE = "defaultType"
    // 业务来源
    const val PARAM_SOURCE = "source"
    // url
    const val PARAM_URL = "url"
    // 分享key
    const val PARAM_SHARE_REQUEST_KEY = "shareRequestKey"
    // 分享内容
    const val PARAM_SHARE_CONTENT = "shareContent"

    const val PARAM_BROWSER = "browser"

    const val PARAM_TOPIC_ID = "topicId"
    const val PARAM_TAB = "tab"

    // 公共
    const val KEY_FROM_GAME_ID = "fromGameId"
    const val KEY_NEED_BACK_GAME = "needBackGame"
    const val PARAM_SOURCE_FROM = "sourceFrom"
    const val KEY_NEED_BACK_ROLE = "needBackRole"


    const val SOURCE_NOTIFICATION_AVATAR_NOTIFICATION_CLICKED = 1
    const val SOURCE_NOTIFICATION_AVATAR_NOTIFICATION_LAUNCH_CLICKED = 2

    const val ENABLE_SHARE_ACTIVATION = true

    private val linkHandlers: MutableMap<String, LinkHandlerChain> = mutableMapOf()

    init {
        addLinkHandler(ACTION_JUMP_TAB, TabLinkHandler())
        addLinkHandler(ACTION_JUMP_TAB_ANY, TabAnyLinkHandler())
        addLinkHandler(ACTION_NAVIGATE, BasicNavigateLinkHandler())
        addLinkHandler(ACTION_SCAN, ScanLinkHandler())
        addLinkHandler(ACTION_JUMP_GAME_DETAIL_FROM_GAME, GameDetailFromGameLinkHandler())
        addLinkHandler(ACTION_FRIEND_SHARE, FriendShareLinkHandler())
        addLinkHandler(ACTION_MGS_SHARE, MgsShareLinkHandler())
        addLinkHandler(ACTION_LOGIN, LoginHandler())
        addLinkHandler(ACTION_BIND_ACCOUNT, BindAccountAndPasswordHandler())
        addLinkHandler(ACTION_GAME_DETAIL, GameDetailLinkHandler())
        addLinkHandler(ACTION_EDIT_PROFILE, EditProfileLinkHandler())
        addLinkHandler(ACTION_JUMP_UGC_GAME_DETAIL_FROM_GAME, UGCGameDetailFromGameLinkHandler())
        addLinkHandler(ACTION_GAME_COMMENT_LIST, GameCommentListLinkHandler())
        addLinkHandler(ACTION_UGC_GAME_DETAIL, UgcGameDetailLinkHandler())
        addLinkHandler(ACTION_USER_HOME, UserHomeLinkHandler())
        addLinkHandler(ACTION_POST_DETAIL, PostDetailLinkHandler())
        addLinkHandler(ACTION_JUMP_POST_PUBLISH, PublishPostLinkHandler())
        addLinkHandler(ACTION_UGC_COMMENT_LIST, UgcCommentListHandler())
        addLinkHandler(ACTION_VIDEO_FEED, VideoFeedLinkHandler())
        addLinkHandler(ACTION_CONVERSATION, ConversationLinkHandler())
        addLinkHandler(ACTION_MARKET, MarketLinkHandler())
        addLinkHandler(ACTION_FRIEND_REQUEST, FriendRequestLinkHandler())
        addLinkHandler(ACTION_MOMENTS, MomentsLinkHandler())
        addLinkHandler(ACTION_EDITOR_CREATE, EditorCreateLinkHandler())
        addLinkHandler(ACTION_GROUP_NOTIFICATION, GroupNotificationLinkHandler())
        addLinkHandler(ACTION_TOPIC_SQUARE, TopicSquareLinkHandler())
        addLinkHandler(ACTION_FEED_BACK, FeedbackLinkHandler())
        addLinkHandler(ACTION_JUMP_AVATAR_EDITOR, AvatarEditorLinkLinkHandler())
        addLinkHandler(ACTION_POP_UP_GAME_REVIEW, GameReviewLinkHandler())
        addLinkHandler(ACTION_WEB, WebLinkLinkHandler())
        addLinkHandler(ACTION_ACTIVATE, ActivationLinkHandler())
        addLinkHandler(ACTION_CONTACTS, ContactsLinkHandler())
        addLinkHandler(ACTION_UGC_DESIGN_DETAIL, UgcDesignDetailHandler())
        addLinkHandler(ACTION_COIN_BALANCE, CoinBalanceHandler())
        addLinkHandler(ACTION_TOPIC_DETAIL, TopicDetailLinkHandler())
        addLinkHandler(ACTION_REAL_NAME, RealNameLinkHandler())
    }

    fun addLinkHandler(action: String, handler: LinkHandler): MetaDeepLink {
        val chain = linkHandlers.getOrPut(action) { LinkHandlerChain() }
        chain.addHandler(handler)
        return this
    }

    fun removeLinkHandler(action: String, handler: LinkHandler): MetaDeepLink {
        val chain = linkHandlers.get(action) ?: return this
        chain.removeHandler(handler)
        return this
    }

    /**
     * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
     */
    fun handle(activity: Activity, navHostFragment: Fragment, tabSelector: TabSelector?, uri: Uri, source:String):LinkHandleResult {
        val validUri = MetaRouterWrapper.Main.convertUri(uri)
        DeeplinkAnalysisUtil.distributeStart(validUri.toString())
        val action = validUri.getQueryParameter(KEY_ACTION) ?: return LinkHandleResult.Failed("action is null")
        val linkHandlerChain = linkHandlers[action] ?: return LinkHandleResult.Failed("linkHandlerChain is null")
        DeeplinkAnalysisUtil.distributeAction(validUri.toString(), action)

        return try {
            val f = if (navHostFragment is NavHostFragment) {
                navHostFragment.childFragmentManager.fragments.firstOrNull { it.isResumed && it.isVisible }
                    ?: navHostFragment.childFragmentManager.fragments.firstOrNull { it.isVisible }
                    ?: navHostFragment
            } else {
                navHostFragment
            }
            linkHandlerChain.handle(LinkData(activity, f, tabSelector, validUri, source))
        } catch (e: Exception) {
            Timber.w(e, "Failed to process deep link")
            LinkHandleResult.Failed(e.toString())
        } finally {
            linkHandlerChain.reset()
        }
    }

    fun containsSchemeUri(uri: Uri): Pair<String, Boolean> {
        val validUri = MetaRouterWrapper.Main.convertUri(uri)
        val action = validUri.getQueryParameter(KEY_ACTION) ?: return "" to false
        return action to linkHandlers.containsKey(action)
    }
}


