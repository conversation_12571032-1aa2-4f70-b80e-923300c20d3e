package com.socialplay.gpark.function.mw.launch

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicReference

/**
 * xingxiu.hou
 * 2023/3/6
 */
class OnLifeLaunchListener {

    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    private val listener: AtomicReference<OnTSLaunchListener> = AtomicReference(null)

    fun bindListener(callback: OnTSLaunchListener) {
        listener.set(callback)
    }

    fun call(call: OnTSLaunchListener.() -> Unit) {
        runOnMain { listener.get()?.apply(call) }
    }

    private fun runOnMain(invoke: () -> Unit) {
        mainHandler.post(invoke)
    }

    fun bindLifecycle(owner: LifecycleOwner) {
        val bindInvoker = {
            owner.lifecycle.addObserver(object : LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        listener.set(null)
                    }
                }
            })
        }
        if (isMainThread()) {
            bindInvoker.invoke()
        } else {
            owner.lifecycleScope.launch(Dispatchers.Main) { bindInvoker.invoke() }
        }
    }

    private fun isMainThread(): Boolean {
        return Looper.getMainLooper().thread === Thread.currentThread()
    }
}
