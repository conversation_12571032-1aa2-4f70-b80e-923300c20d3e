package com.socialplay.gpark.function.member

import android.app.Activity
import com.meta.biz.ugc.model.MemberRechargeMsg
import com.meta.biz.ugc.model.RechargeResultMgs
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.UserMemberInteractor
import com.socialplay.gpark.databinding.ViewPremiumTipBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.intermodal.base.GameViewShowHelper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * MW会员相关交互
 */
object MwMemberInteractor {

    private val mainScope by lazy { MainScope() }
    private val userMemberInteractor: UserMemberInteractor by lazy { GlobalContext.get().get() }


    init {
        Timber.i("MwMemberInteractor init method called.")
        MainScope().launch {
            userMemberInteractor.vipPlusLiveData.observeForever {
                if (it != null) {
                    Timber.i("send vip plus info to mw, ${it.status}")
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.PROTOCOL_MEMBER_INFO,
                        0,
                        HashMap<String, Any>().apply {
                            this["type"] =  it.type
                            this["status"] =  it.status
                            this["level"] =  it.level
                            this["startTime"] =  it.startTime
                            this["endTime"] =  it.endTime
                        }
                    )
                }
            }
        }

    }


    /**
     * 获取会员信息
     */
    fun refreshUserMemberInfo(messageId: Int) {
        userMemberInteractor.refreshUserMemberInfo(messageId)
    }

    /**
     * 游戏内订阅充值提示
     */
    fun goMemberRecharge(curActivity: Activity, msg: MemberRechargeMsg?, rechargeResultMgs: RechargeResultMgs?) {
        val payView = ViewPremiumTipBinding.inflate(curActivity.layoutInflater)
        payView.tvSure.setOnAntiViolenceClickListener {
            GameViewShowHelper.getInstance().dismissView(curActivity, payView.root)
            val url = GlobalContext.get().get<H5PageConfigInteractor>().getH5PageUrl(H5PageConfigInteractor.VIP_STATUS).toHttpUrl()
            val newUrl= url.newBuilder().addQueryParameter("source", "ts_game")
                .addQueryParameter("type", (msg?.type?:3).toString())
                .addQueryParameter("gameid", msg?.gameId?:"")
                .build().toString()
            val navHostFragment = if (curActivity is MainActivity) {
                curActivity.findNavHostFragment()
            } else {
                null
            }
            MetaRouter.Web.navigate(
                curActivity.application,
                fragment = navHostFragment,
                title = "",
                url = newUrl,
                showTitle = false,
                statusBarColor = null,
                showStatusBar = false,
                extra= rechargeResultMgs?.let { it1 -> GsonUtil.safeToJson(it1) }
            )
            Analytics.track(EventConstants.EVENT_SUBSCRIBE_PRIVILEGE_CLICK, mapOf("gamecode" to (msg?.gameId?:"") ))
        }
        payView.tvCancel.setOnAntiViolenceClickListener {
            GameViewShowHelper.getInstance().dismissView(curActivity, payView.root)
        }
        GameViewShowHelper.getInstance().displayView(curActivity, curActivity, payView.root,
            GameViewShowHelper.VIEW_DEFAULT_PAGE
        )
        Analytics.track(EventConstants.EVENT_SUBSCRIBE_PRIVILEGE_SHOW, mapOf("gamecode" to (msg?.gameId?:"") ))
    }
}