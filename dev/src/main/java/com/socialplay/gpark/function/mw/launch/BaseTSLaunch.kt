package com.socialplay.gpark.function.mw.launch

import androidx.lifecycle.LifecycleOwner
import com.google.common.util.concurrent.AtomicDouble
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.mw.bean.MWLaunchParams
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.exception.TSEngineException
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference

/**
 * <AUTHOR>
 * @date 2022/11/22
 */

typealias OnDownloadListener = (info: GameDetailInfo, percent: Float) -> Unit

enum class LaunchStatus {
    NONE,//默认状态
    DOWNLOADING,//引擎下载状态
    PAUSE,//引擎暂停下载状态
    LAUNCHING//开始执行拉起状态
}

abstract class BaseTSLaunch {

    private val launchViewModel: TSLaunchViewModel by lazy { GlobalContext.get().get() }

    private val launchListener: OnLifeLaunchListener = OnLifeLaunchListener()

    private val launchParam: AtomicReference<TSLaunchParams> = AtomicReference()

    private val currentStatus: AtomicReference<LaunchStatus> = AtomicReference(LaunchStatus.NONE)

    //如果有下载,返回当前下载进度,否则默认值为0
    private val engineDownloadProcess = AtomicDouble(0.0)

    protected val defScope by lazy { MainScope() }

    fun onDownloadListener(owner: LifecycleOwner, listener: OnDownloadListener) {
        MWLifeCallback.downloadProgress.observe(owner) {
            engineDownloadProcess.set(it.toDouble())
            launchParam.get()?.also { p -> listener.invoke(p.gameInfo, it) }
        }
    }

    fun onLaunchListener(
        owner: LifecycleOwner? = null,
        listener: SimpleOnTSLaunchListener.() -> Unit,
    ) {
        if (owner != null) {
            launchListener.bindLifecycle(owner)
        }
        launchListener.bindListener(SimpleOnTSLaunchListener().apply(listener))
    }

    /**
     * 提前获取游戏启动参数
     */
    fun preLoadLaunchParams(gameInfo: GameDetailInfo) {
        launchViewModel.preLoadLaunchParams(gameInfo)
    }

    /**
     * 获取启动参数和引擎状态
     */
    protected fun requireLaunchParamsFlow(params: TSLaunchParams): Flow<MWLaunchParams> {
        launchParam.set(params)
        call { onLaunchPrepare(params) }
        return launchViewModel.getLaunchFromAPI(params)
            .combine(getMWEngineDownloadFlow(params)) { launchParams, engineAvailable ->
                Timber.tag("BaseTSLaunch").d("prepareTSLaunch combine $engineAvailable")
                if (!engineAvailable) throw TSEngineException(getStringByGlobal(R.string.ugc_game_engine_not_ready))
                launchParams.copy(engineAvailable = true)
            }
    }

    /**
     * 获取引擎状态
     */
    private fun getMWEngineDownloadFlow(params: TSLaunchParams): Flow<Boolean> {
        Timber.tag("BaseTSLaunch").d("getMWEngineDownloadFlow")
        return if (MWBiz.isAvailable()) {
            Timber.tag("BaseTSLaunch").d("getMWEngineDownloadFlow available : true")
            updateCurrentState(LaunchStatus.LAUNCHING)
            flowOf(true)
        } else {
            Timber.tag("BaseTSLaunch").d("getMWEngineDownloadFlow start download")
            call { onStartDownload(params) }
            updateCurrentState(LaunchStatus.DOWNLOADING)
            MWStatus.engineAvailableFlow()
        }
    }

    /**
     * 回调结果
     */
    protected fun call(call: OnTSLaunchListener.() -> Unit) {
        launchListener.call(call)
    }

    //更新当前状态
    protected fun updateCurrentState(status: LaunchStatus) {
        currentStatus.set(status)
    }

    fun isLaunching(gameId: String): Boolean {
        return launchParam.get()?.gameId == gameId && currentStatus.get() == LaunchStatus.LAUNCHING
    }

    protected fun isDownloading(): Boolean {
        return currentStatus.get() == LaunchStatus.DOWNLOADING
    }

    fun isDownloadingWithId(gameId: String): Boolean {
        return launchParam.get()?.gameId == gameId && isDownloading()
    }

    /**
     * 发送埋点
     */
    protected fun sendEvent(event: Event, launchParams: TSLaunchParams, params: Map<String, Any>) {
        Analytics.track(event) {
            putAll(params)
            putAll(ResIdUtils.getAnalyticsMap(launchParams.resIdBean))
            put("game_type", "ts")
            put("status", "trigger")
            put("gameid", launchParams.gameId)
            put("packagename", launchParams.packageName)
            put("mw_server_type", launchParams.getMWServerType())
        }
    }
}