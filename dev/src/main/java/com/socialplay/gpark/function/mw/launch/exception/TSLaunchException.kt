package com.socialplay.gpark.function.mw.launch.exception

import com.socialplay.gpark.util.ifNullOrEmpty

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
//游戏引擎版本不匹配
open class BaseTSLaunchException(private val errorType: String) : RuntimeException() {

    fun getErrorType(): String {
        return errorType
    }

    companion object {
        const val ERROR_TYPE_MGS_API_FAILED = "mgs_api_failed"
        const val ERROR_TYPE_INFO_API_FAILED = "info_api_failed"
        const val ERROR_TYPE_ENGINE_FAILED = "engine_failed"
        const val ERROR_TYPE_OFFLINE_FAILED = "offline_failed"
        const val ERROR_TYPE_MGS_FAILED = "mgs_failed"
        const val ERROR_TYPE_LOGIN_FAILED = "login_failed"
        const val ERROR_TYPE_SYSTEM_FAILED = "system_failed"
        const val ERROR_TYPE_NOT_FOREGROUND_FAILED = "not_foreground_failed"
        const val ERROR_TYPE_MW_CORE_FAILED = "mw_core_failed"
        const val ERROR_TYPE_ENGINE_NOT_MATCH = "engine_not_match"
        const val ERROR_TYPE_VERSION_COMPATIBLE_FAILED = "version_compatible"
        const val ERROR_TYPE_OTHER_FAILED = "other_failed"
        const val ERROR_TYPE_USER_CANCELLED = "user_cancelled"
    }
}

class TSEngineVersionNotMatchException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_ENGINE_NOT_MATCH) {
    companion object {
        const val API_ERROR_CODE = 1003
    }
}

class TSMGSAPIParamsException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_MGS_API_FAILED) {
    var toastMsg: String? = null
}

class TSInfoAPIParamsException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_INFO_API_FAILED) {
    var toastMsg: String? = null
}

//引擎下载或加载失败
class TSEngineException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_ENGINE_FAILED)

//游戏下线状态
class TSOfflineException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_OFFLINE_FAILED) {
    var toastMsg: String? = null
}

//mgs拦截
class TSMGSException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_MGS_FAILED)

//需要登录
class TSLoginException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_LOGIN_FAILED)

//系统版本限制
class TSSysVersionException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_SYSTEM_FAILED) {
    var showDialogText = ""
}

//进程不在前台
class TSNotForegroundException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_NOT_FOREGROUND_FAILED)

//MW内核异常
class TSMWCoreException(private val errorType: String, override val message: String = "") :
    BaseTSLaunchException(errorType.ifEmpty { ERROR_TYPE_MW_CORE_FAILED })


//引擎和游戏版本不兼容
class TSVersionCompatibleException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_VERSION_COMPATIBLE_FAILED) {
    var showDialogText = ""

    companion object {
        const val API_ERROR_CODE = 1015
    }
}

class TSUserCancelledException(override val message: String = "") :
    BaseTSLaunchException(ERROR_TYPE_USER_CANCELLED)