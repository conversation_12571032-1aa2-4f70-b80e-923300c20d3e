package com.socialplay.gpark.function.apm.page.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R

/**
 * 2024/1/18
 */
object PageMonitorLayoutInflaterFactory2 {
    fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ApmMonitor)
        val speedMonitor = typedArray.getBoolean(0, false)
        typedArray.recycle()
        return if (speedMonitor) PageMonitorLayout.replace(context, name, attrs) else null
    }

}