package com.socialplay.gpark.function.intermodal.base;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.socialplay.gpark.util.ScreenUtil;

import java.util.Map;

/**
 * <pre>
 *     <AUTHOR> yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/03/02
 *     desc   :游戏内的View基类，承载基础的view操作
 * </pre>
 */
public abstract class BaseGamePage extends BaseGamePageHelper implements IGamePage {

    /**
     * 显示状态
     */
    private final int STATUS_SHOWED = 0;
    /**
     * 关闭状态
     */
    private final int STATUS_CLOSED = 1;
    /**
     * 当前View的状态
     */
    private int currentStatus;
    /**
     * 当前View
     */
    private View currentView;
    /**
     * View的状态监听
     */
    private OnViewStatusListener onViewStatusListener;

    private int mX = 0;
    protected int mY = 0;
    private int mGravity = Gravity.LEFT | Gravity.TOP;

    private void loadView(Context context) {
        View view = LayoutInflater.from(context).inflate(getLayoutId(context), null, false);
        setCurrentView(view);
        onShowBefore(getCurrentView());
        showView(context, getCurrentView());
        onShowAfter(getCurrentView());
        initView(getCurrentView());
        initData();
    }

    @Override
    public int getGravity() {
        return mGravity;
    }

    @Override
    public int getWidth() {
        return WindowManager.LayoutParams.MATCH_PARENT;
    }

    @Override
    public int getHeight() {
        return WindowManager.LayoutParams.MATCH_PARENT;
    }

    @Override
    public int getX() {
        return mX;
    }

    @Override
    public int getY() {
        return mY;
    }

    /**
     * @param context
     * @return 根据屏幕方向获取资源布局
     */
    private int getLayoutId(final Context context) {
        if (ScreenUtil.INSTANCE.isHorizontalScreen(context)) {
            return layoutLandId() != 0 ? layoutLandId() : layoutId();
        }
        return layoutId();
    }

    /**
     * 监听View的状态
     *
     * @param onViewStatusListener 状态监听器
     */
    public void setOnViewStatusListener(OnViewStatusListener onViewStatusListener) {
        this.onViewStatusListener = onViewStatusListener;
    }

    /**
     * 显示View和设置数据
     *
     * @param data     数据
     * @param activity 当前Activity
     */
    void showAndData(Map<String, Object> data, Activity activity, Context context) {
        showAndData(data, activity, context, Gravity.LEFT | Gravity.TOP, 0, 0);
    }

    /**
     * 显示View和设置数据
     *
     * @param data     数据
     * @param activity 当前Activity
     */
    void showAndData(Map<String, Object> data, Activity activity, Context context,int gravity,int x,int y) {
        if (activity == null) {
            return;
        }
        this.mX = x;
        this.mY = y;
        this.mGravity = gravity;
        setCurrentActivity(activity);
        //赋值数据
        setCurrentData(data);
        //加载布局
        loadView(context);
    }

    /**
     * 显示当前View
     *
     * @param view 当前View
     */
    private void showView(Context context, View view) {
        if (view != null && getCurrentActivity() != null) {
            //显示View
            GameViewShowHelper.getInstance().displayView(context, getCurrentActivity(), this);
            //改变状态
            changeViewStatus(STATUS_SHOWED);
        }
    }

    /**
     * 关闭当前View
     */
    private void closeView() {
        if (getCurrentView() != null && getCurrentActivity() != null) {
            GameViewShowHelper.getInstance().dismissView(getCurrentActivity(), getCurrentView());
            changeViewStatus(STATUS_CLOSED);
        }
    }

    /**
     * 改变View状态
     */
    private void changeViewStatus(int status) {
        currentStatus = status;
        if (onViewStatusListener != null) {
            switch (status) {
                case STATUS_SHOWED:
                    onViewStatusListener.onViewShowed();
                    break;
                case STATUS_CLOSED:
                    onViewStatusListener.onViewClosed();
                    break;
            }
        }
    }

    /**
     * 当前view的展示状态
     */
    public boolean isShow() {
        return currentStatus == STATUS_SHOWED;
    }

    /**
     * @param
     */
    public void setCurrentView(View currentView) {
        this.currentView = currentView;
    }

    /**
     * @return 获取当前的View
     */
    public View getCurrentView() {
        return currentView;
    }

    /**
     * 关闭View
     */
    public void close() {
        closeView();
    }

    /**
     * @return 获取当前View的状态
     */
    public int getCurrentStatus() {
        return currentStatus;
    }

    /**
     * View显示之前
     *
     * @param view 当前View
     */
    protected void onShowBefore(@NonNull View view) {
    }

    /**
     * View显示之后
     *
     * @param view 当前View
     */
    protected void onShowAfter(@NonNull View view) {
    }

    /**
     * @return 布局ID(横屏)
     */
    public abstract int layoutLandId();

    /**
     * @return 布局ID
     */
    public abstract int layoutId();


    /**
     * 初始化View
     *
     * @param view 当前View
     */
    public abstract void initView(@NonNull View view);

    /**
     * 初始化数据
     */
    public abstract void initData();

}