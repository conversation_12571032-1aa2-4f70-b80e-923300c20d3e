package com.socialplay.gpark.function.startup.core.task

import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.function.startup.core.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlin.coroutines.CoroutineContext

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/11
 * desc   :
 * </pre>
 */


class Task private constructor(val name: String, val type: String, val project: Project, val processType: ProcessType) {

    companion object {

        fun direct(name: String, project: Project, processType: ProcessType) =
            Task(name, "direct", project, processType)

        fun async(name: String, project: Project, processType: ProcessType) =
            Task(name, "async", project, processType)

        fun withContext(name: String, project: Project, processType: ProcessType) =
            Task(name, "withContext", project, processType)
    }


    lateinit var deferred: Deferred<Int>

    suspend fun await(): Int {
        if (this::deferred.isInitialized) {
            return this.deferred.await()
        }
        return 0
    }

    suspend fun withContext(context: CoroutineContext, block: suspend CoroutineScope.() -> Unit) {
        if (project.matchProcess(processType)) {
            project.onTaskStart(this)
            val startTime = System.currentTimeMillis()
            kotlinx.coroutines.withContext(context) {
                block.invoke(this)
                project.onTaskFinished(this@Task, System.currentTimeMillis() - startTime)
            }
        }
    }

    fun invoke(block: () -> Unit) {
        if (project.matchProcess(processType)) {
            project.onTaskStart(this)
            val startTime = System.currentTimeMillis()
            block.invoke()
            project.onTaskFinished(this, System.currentTimeMillis() - startTime)
        }
    }

    fun async(context: CoroutineContext, start: CoroutineStart, block: suspend CoroutineScope.() -> Unit) {
        if (project.matchProcess(processType)) {
            project.onTaskStart(this)
            val startTime = System.currentTimeMillis()
            deferred = project.async(context, start) {
                block.invoke(this)
                project.onTaskFinished(this@Task, System.currentTimeMillis() - startTime)
                1
            }
        }
    }


}