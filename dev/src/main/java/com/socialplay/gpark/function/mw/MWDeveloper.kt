package com.socialplay.gpark.function.mw

import com.socialplay.gpark.data.kv.MetaKV
import org.json.JSONObject

/**
 * 2024/7/11
 */
class MWDeveloper(metaKV: MetaKV) {

    private val params = mutableMapOf<String, Any>()

    init {
        //旧版参数,直接放在了最外层，后面MW的调试参数，用mwDevelop包一层
        metaKV.mw.getDevDsVersion().takeIf { it.isNotEmpty() }
            ?.also { params["dsVersion"] = it }

        // MW的调试参数，用mwDevelop包一层
        val customEnv = metaKV.mw.mwCustomDevelopEnvParams
        val devObj = if (customEnv.isNotEmpty()) {
            parseCustomEnv(customEnv)
        } else {
            JSONObject()
        }

//        if (metaKV.mw.mwResourceTest) {
//            devObj.put("openResourceTest", true)
//        } else {
//            devObj.put("openResourceTest", false)
//        }
//        if (metaKV.mw.mwDisableAssetCache) {
//            devObj.put("disableAssetCache", true)
//        } else {
//            devObj.put("disableAssetCache", false)
//        }
        params["mwDevelop"] = devObj.toString()

    }


    private fun parseCustomEnv(env: String): JSONObject {
        return runCatching {
            JSONObject(env)
        }.getOrElse {
            JSONObject().put("customEnv", env)
        }
    }


    fun invokeParams() = params

}