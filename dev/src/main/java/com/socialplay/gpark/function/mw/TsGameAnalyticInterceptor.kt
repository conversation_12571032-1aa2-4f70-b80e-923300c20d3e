package com.socialplay.gpark.function.mw

import android.os.Bundle
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.handle.GameLaunchAnalytics
import com.socialplay.gpark.function.analytics.observer.GameCrashHostObserve
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.interceptor.IPlayGameInterceptor
import com.socialplay.gpark.function.interceptor.InterceptorController.KEY_IS_UGC
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.function.editor.PlayedDatabaseTask
import com.socialplay.gpark.function.interceptor.InterceptorController.KEY_SAVE_PLAYED_DB
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/7/22
 * Desc:
 */
class TsGameAnalyticInterceptor : IPlayGameInterceptor,
    KoinComponent {

    private val metaRepository: IMetaRepository by inject()
    private val metaKV: MetaKV by inject()

    override suspend fun onIntercept(pkgName: String, gameId: String, bundle: Bundle, resIdBean: ResIdBean?): Pair<Boolean, Bundle> {
        val saveDB = bundle.getBoolean(KEY_SAVE_PLAYED_DB)
        val isUgc = bundle.getBoolean(KEY_IS_UGC)
        Timber.tag(PlayedDatabaseTask.TAG).d("launch onIntercept gid:${gameId}, saveDB: ${saveDB}")
        if (saveDB) {
            CoroutineScope(Dispatchers.IO).launch {
                metaRepository.onLaunchGame(gameId, pkgName)
            }
        }
        if (isUgc) {
            resIdBean?.setTsType(ResIdBean.TS_TYPE_UCG)
        }
        //存在拉起游戏时间，表示启动过此游戏，此时设置不是首次拉起标识
        if (metaKV.analytic.getStartLaunchTime(pkgName) > 0) {
            metaKV.analytic.setGameNotFirstLaunch(pkgName)
        }
        metaKV.account.setGamePlayed(gameId)
        withContext(Dispatchers.IO) {
            val gameStatus = MWBizBridge.gameStatus(gameId, "")
            GameLaunchAnalytics.launchStart(gameId, pkgName, gameStatus.isAlive, true, resIdBean)
        }
        GameCrashHostObserve.recordStartGame(pkgName, gameId, true)
        return false to bundle
    }
}