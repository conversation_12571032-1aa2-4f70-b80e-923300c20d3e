package com.socialplay.gpark.function.lock

import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.TTaiKV
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber


// 应用商店上架审核锁
// https://meta.feishu.cn/wiki/SMHxwXdvkiGsiNkWXzpcSLMCnlc
object AppStoreReviewLock {

    private var _isLocked: Boolean? = null
    private val mutex = Mutex()

    suspend fun isLocked(): Boolean {
        if (_isLocked == null) {
            mutex.withLock {
                if (_isLocked == null) {
                    _isLocked = isAppStoreReviewLocked()
                }
            }
        }
        return _isLocked ?: false
    }

    /**
     * 是否处于上架商店审核锁定状态
     */
    private suspend fun isAppStoreReviewLocked(): Boolean {
        var isLocked = false
        val repository = GlobalContext.get().get<IMetaRepository>()

        runCatching {
            val tTaiConfig =
                repository.getTTaiConfigByIdV2(TTaiKV.ID_APP_STORE_REVIEW_LOCK).invoke()
            // { "1": [ { "appVersion": "[12402,999999)", "resId": 32 } ], "2": [ { "appVersion": "[12402,999999)", "resId": 33 } ] }

            val keyAndroid = "1"
            val keyAppVersion = "appVersion"

            val jo = JSONObject(tTaiConfig.value)

            val androidLockConfigList = jo.optJSONArray(keyAndroid)

            if (androidLockConfigList != null) {
                for (i in 0 until androidLockConfigList.length()) {
                    val ruleJo = androidLockConfigList.getJSONObject(i)

                    val appVersion = ruleJo.optString(keyAppVersion)
                    if (appVersion.isEmpty()) {
                        continue
                    }
                    if (isWithInRange(appVersion, BuildConfig.VERSION_CODE)) {
                        isLocked = true
                        break
                    }
                }
            }
        }.getOrElse {
            Timber.w(it, "isAppStoreReviewLocked error")
        }

        return isLocked
    }

    private fun isWithInRange(rangeStr: String, value: Int): Boolean {
        if (rangeStr.isEmpty()) return false

        // 只有一个值的情况下，直接判断等于
        if (!rangeStr.contains(",")) {
            return rangeStr.toIntOrNull() == value
        }

        val lowerBoundChar = rangeStr.first()
        val upperBoundChar = rangeStr.last()

        if (lowerBoundChar != '[' && lowerBoundChar != '(') return false
        if (upperBoundChar != ']' && upperBoundChar != ')') return false

        val lowerBoundInclusive = lowerBoundChar == '['
        val upperBoundInclusive = upperBoundChar == ']'

        val bounds = rangeStr.substring(1, rangeStr.length - 1).split(",")
        if (bounds.size != 2) {
            return false
        }

        val lowerBound = bounds[0].toIntOrNull()
        val upperBound = bounds[1].toIntOrNull()

        if (lowerBound == null || upperBound == null) {
            return false
        }

        val lowerCheck = if (lowerBoundInclusive) value >= lowerBound else value > lowerBound
        val upperCheck = if (upperBoundInclusive) value <= upperBound else value < upperBound

        return lowerCheck && upperCheck
    }

}