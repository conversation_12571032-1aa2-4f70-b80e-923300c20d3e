package com.socialplay.gpark.function.mw.launch.setp

import android.content.Context
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams

/**
 * xingxiu.hou
 * 2022/10/25
 */

interface ITSGameLaunchWrapStep : ITSGameCheckStep, ITSGameProcessStep, ITSGameLaunchStep

interface ITSGameCheckStep {
    @Throws(Exception::class)
    suspend fun check(context: Context, params: TSLaunchParams)

}

interface ITSGameProcessStep {
    @Throws(Exception::class)
    suspend fun process(context: Context, params: TSLaunchParams)

}
interface ITSGameLaunchStep {

    @Throws(Exception::class)
    suspend fun launch(context: Context, params: TSLaunchParams)
}
