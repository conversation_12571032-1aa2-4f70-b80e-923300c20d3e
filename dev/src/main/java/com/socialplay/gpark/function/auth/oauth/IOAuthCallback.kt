package com.socialplay.gpark.function.auth.oauth

import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.OAuthResponse

/**
 * @author: ning.wang
 * @date: 2021-09-27 11:07 上午
 * @desc:
 */
interface IOAuthCallback {
    fun onSuccess(response: OAuthResponse)
    fun onFailed(oauthThirdWay: LoginWay, msg: String? = null, code: Int, reason: String? = null)
    fun onCancel(oauthThirdWay: LoginWay)
}