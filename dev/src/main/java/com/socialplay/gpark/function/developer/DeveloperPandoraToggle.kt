package com.socialplay.gpark.function.developer

import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2021/6/7
 */
object DeveloperPandoraToggle {

    private const val PANDORA_TOGGLE_DEVELOPER_ENABLE = "pandora_toggle_developer_enable"
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    fun setEnable(enable: Boolean) {
        metaKV.developer.putBoolean(PANDORA_TOGGLE_DEVELOPER_ENABLE, enable)
    }

    fun isEnable(): Boolean {
        return metaKV.developer.getBoolean(PANDORA_TOGGLE_DEVELOPER_ENABLE, false)
    }

    fun <T> getValue(key: String, defaultValue: T): T? {
        val temp = metaKV.developer.getString(key, "")
        if (temp.isNullOrEmpty()) {
            return defaultValue
        }
        val result = when (defaultValue) {
            is String -> temp
            is Int -> kotlin.runCatching { temp.toInt() }.getOrElse { defaultValue }
            is Long -> kotlin.runCatching { temp.toLong() }.getOrElse { defaultValue }
            is Float -> kotlin.runCatching { temp.toFloat() }.getOrElse { defaultValue }
            is Double -> kotlin.runCatching { temp.toDouble() }.getOrElse { defaultValue }
            is Boolean -> kotlin.runCatching { temp.toBoolean() }.getOrElse { defaultValue }
            else -> temp
        }
        return kotlin.runCatching { result as T }.getOrElse { defaultValue }
    }

    fun setValue(key: String, value: String): Boolean {
        return kotlin.runCatching {
            metaKV.developer.putString(key, value)
            true
        }.getOrElse { false }
    }

}