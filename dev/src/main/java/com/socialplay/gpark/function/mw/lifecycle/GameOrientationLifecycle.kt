package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import androidx.activity.ComponentActivity
import androidx.core.util.Consumer
import com.meta.biz.ugc.model.GetOrientationMsg
import com.meta.biz.ugc.model.GetOrientationResultMsg
import com.meta.biz.ugc.model.OrientationChangedMsg
import com.meta.biz.ugc.model.SetOrientationMsg
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.umw.UMW
import com.socialplay.gpark.function.umw.impl.ext.addProtocolObserver
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import timber.log.Timber
import java.lang.ref.WeakReference


/**
 * 游戏方向控制Lifecycle
 * 用于处理接收游戏内请求横竖屏的协议
 */
class GameOrientationLifecycle(
    private val umw: UMW
) : MWLifecycle() {

    private val _requestedOrientation = MutableStateFlow<Int?>(null)
    val requestedOrientation: StateFlow<Int?> get() = _requestedOrientation

    private val _deviceOrientation = MutableStateFlow<Int?>(null)
    val deviceOrientation: StateFlow<Int?> get() = _deviceOrientation

    private var activityRef: WeakReference<ComponentActivity>? = null

    private val setOrientationObserver = object :
        SingleProtocolListener<SetOrientationMsg>(ProtocolReceiveConstants.PROTOCOL_SET_ORIENTATION) {
        override fun handleProtocol(message: SetOrientationMsg?, messageId: Int) {
            if (message == null) return
            if (MWBizBridge.currentGameId() == EditorGameInteractHelper.getRoleGameId()) return
            requestOrientation(message.orientation)
        }
    }

    private val getOrientationObserver = object :
        SingleProtocolListener<GetOrientationMsg>(ProtocolReceiveConstants.PROTOCOL_GET_ORIENTATION) {
        override fun handleProtocol(message: GetOrientationMsg?, messageId: Int) {
            val activity = activityRef?.get() ?: return

            val orientation = when (activity.resources.configuration.orientation) {
                Configuration.ORIENTATION_PORTRAIT -> {
                    SetOrientationMsg.ORIENTATION_PORTRAIT
                }

                else -> {
                    SetOrientationMsg.ORIENTATION_LANDSCAPE
                }
            }

            umw.sendProtocol(
                protocol = ProtocolSendConstant.PROTOCOL_GET_ORIENTATION_RESULT,
                platformMsg = GetOrientationResultMsg(orientation)
            )
        }
    }

    private val configurationObserver = Consumer<Configuration> { configuration ->
        if (_deviceOrientation.value != configuration.orientation) {
            _deviceOrientation.value = configuration.orientation

            Timber.d("MWGameOrientationController orientation changed ${configuration.orientation}")

            dispatchOrientationChanged(configuration)
        }
    }

    override fun onActivityCreated(activity: Activity) {
        super.onActivityCreated(activity)
        Timber.d("MWGameOrientationController onActivityCreated activity $activity")
        (activity as? ComponentActivity)?.let { bindInternal(it) }
        dispatchOrientationChanged(activity.resources.configuration)
    }

    override fun onActivityDestroyed(activity: Activity) {
        Timber.d("MWGameOrientationController onActivityDestroyed activity $activity")
        (activity as? ComponentActivity)?.let { unbindInternal(it) }
        super.onActivityDestroyed(activity)
    }

    private fun bindInternal(activity: ComponentActivity) {
        Timber.d("MWGameOrientationController bindInternal activity $activity")
        this.activityRef = WeakReference(activity)

        umw.addProtocolObserver(setOrientationObserver)
        umw.addProtocolObserver(getOrientationObserver)

        activity.addOnConfigurationChangedListener(configurationObserver)
    }

    private fun unbindInternal(activity: ComponentActivity) {
        Timber.d("MWGameOrientationController unbindInternal activity $activity")

        umw.removeProtocolObserver(setOrientationObserver)
        umw.removeProtocolObserver(getOrientationObserver)

        activity.removeOnConfigurationChangedListener(configurationObserver)
    }

    private fun dispatchOrientationChanged(configuration: Configuration) {

        val orientation = when (configuration.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {
                SetOrientationMsg.ORIENTATION_PORTRAIT
            }

            else -> {
                SetOrientationMsg.ORIENTATION_LANDSCAPE
            }
        }

        dispatchOrientationChanged(orientation)
    }



    private fun dispatchOrientationChanged(newOrientation: Int) {
        umw.sendProtocol(
            protocol = ProtocolSendConstant.PROTOCOL_ORIENTATION_CHANGED,
            platformMsg = OrientationChangedMsg(newOrientation)
        )
    }

    private fun requestOrientation(orientation: Int) {
        Timber.d("MWGameOrientationController requestOrientation orientation:${orientation} activityRef:${activityRef?.get()}")
        _requestedOrientation.value = orientation

        val activity = activityRef?.get() ?: return
        when (orientation) {
            SetOrientationMsg.ORIENTATION_PORTRAIT -> {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_USER_PORTRAIT
            }

            SetOrientationMsg.ORIENTATION_PORTRAIT_LOCKED -> {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }

            SetOrientationMsg.ORIENTATION_LANDSCAPE -> {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_USER_LANDSCAPE
            }

            SetOrientationMsg.ORIENTATION_LANDSCAPE_LOCKED -> {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            }

            else -> {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
            }
        }
    }
}