package com.socialplay.gpark.function.ad

import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.ad.AdTTaiConfig
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

object AdToggleCtrl {

    val adSdkBridge: IAdSdkBridge by lazy { GlobalContext.get().get() }
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val metaRepository: IMetaRepository by lazy { GlobalContext.get().get() }
    private val scope = MainScope()

    val isWithinAdCountLimit: Boolean
        get() {
            Timber.d("adPreDayShowCount: ${metaKV.appKV.adPreDayShowCount} ,adShowAmount: ${PandoraToggle.adShowAmount}")
            return metaKV.appKV.adPreDayShowCount < PandoraToggle.adShowAmount
        }

    val isEnoughAdShowInterval: Boolean
        get() {
            Timber.d("adShowTimestamp: ${metaKV.appKV.adShowTimestamp} ,adShowInterval: ${PandoraToggle.adShowInterval}")
            return System.currentTimeMillis() - metaKV.appKV.adShowTimestamp > PandoraToggle.adShowInterval * 1000
        }

    fun isActivatedRewardedAd(gameId: String?, gamePkg: String?): Boolean {
        val isActiveResult =
            PandoraToggle.rewardedAdIsActivated && !PandoraToggle.isInBlacklist(gamePkg) && !PandoraToggle.isInBlacklist(
                gameId
            )
        Timber.d(
            "isActiveResult:$isActiveResult, isActivatedRewardedAd:${PandoraToggle.rewardedAdIsActivated}, gameId:$gameId, gamePkg: $gamePkg, blackList: ${
                PandoraToggle.gameAdBlackList
            }"
        )
        return isActiveResult
    }

    //applovin聚合广告是否就绪
    val isRewardedReady: Boolean
        get() = adSdkBridge.isRewardedAdReady()

    fun getAdTtaiConfig(): AdTTaiConfig? {
        val itemText = metaKV.tTaiKV.adConfig
        if (itemText.isEmpty()) {
            scope.launch {
                metaRepository.getTTaiConfigById(TTaiKV.ID_AD_CONFIG).singleOrNull()
            }
            return null
        }
        return GsonUtil.gsonSafeParse<AdTTaiConfig>(itemText)
    }

}