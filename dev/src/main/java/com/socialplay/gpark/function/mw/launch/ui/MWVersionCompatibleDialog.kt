package com.socialplay.gpark.function.mw.launch.ui

import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogMwVersionCompatibleBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding

/**
 * 2023/9/15
 */
class MWVersionCompatibleDialog : BaseDialogFragment() {

    companion object {

        private const val ARG_CONTENT = "ARG_CONTENT"
        private const val ARG_GAME_IMG = "ARG_GAME_IMG"
        fun show(fragmentManager: FragmentManager, content: String, gameImg: String) {
            MWVersionCompatibleDialog().apply {
                arguments = bundleOf(ARG_CONTENT to content, ARG_GAME_IMG to gameImg)
                show(fragmentManager, "MWVersionCompatibleDialog")
            }
        }
    }

    override val binding by viewBinding(DialogMwVersionCompatibleBinding::inflate)

    override fun init() {
        Analytics.track(EventConstants.EVENT_MW_TS_INCOMPATIBLE_SHOW)
        binding.ivClose.setOnClickListener { dismiss() }
        binding.tvConfirm.setOnClickListener { dismiss() }

        val defContent = getString(R.string.mw_engine_version_not_compatible)
        val content = arguments?.getString(ARG_CONTENT, defContent).ifNullOrEmpty { defContent }
        binding.tvContent.text = content
        val gameImg = arguments?.getString(ARG_GAME_IMG, "")
        if (!gameImg.isNullOrEmpty()) {
            Glide.with(this).load(gameImg).placeholder(R.mipmap.ic_launcher)
                .transform(CircleCrop())
                .error(R.mipmap.ic_launcher).into(binding.ivIcon)
        }
    }

    override fun loadFirstData() {}
    override fun getStyle() = R.style.DetailHalfModeDialogStyle

}