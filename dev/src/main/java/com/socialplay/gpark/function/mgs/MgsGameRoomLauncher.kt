package com.socialplay.gpark.function.mgs

import android.content.Context
import androidx.fragment.app.Fragment
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.meta.biz.mgs.data.model.MgsRoomCacheInfo
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.succeeded
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.errMsg
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

object MgsGameRoomLauncher : KoinComponent {

    // 是否可以加载游戏（当前无正在加载的游戏）
    private var launcherAvailable = AtomicBoolean(true)
    private val metaRepository: com.socialplay.gpark.data.IMetaRepository by inject()
    //    private val metaVerseInteractor: MWLaunchGameInteractor by inject()
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }
    private val launchEndListener = ConcurrentHashMap<String, (String) -> Unit>()

    fun registerLaunchEndListener(key: String, listener: (String) -> Unit) {
        synchronized(launchEndListener) {
            launchEndListener[key] = listener
        }
    }

    fun removeLaunchEndListener(key: String) {
        synchronized(launchEndListener) {
            launchEndListener.remove(key)
        }
    }

    /**
     * 从233加入房间
     */
    fun enterMgsGame(
        fragment: Fragment,
        toPackageName: String,
        toGameId: String,
        roomInfo: MgsBriefRoomInfo?,
        source: String?,
        joinMode: Int,
        fromUuid: String?,
        useNewCheck:Boolean = false,
        resIdBean: ResIdBean? = null,
        roomName: String? = null
    ):Job? {
        fragment.context ?: return null
        return GlobalScope.launch {
            suspendEnterMgsGame(fragment, toPackageName, toGameId, roomInfo, source, joinMode, fromUuid, useNewCheck, resIdBean, roomName)
        }
    }

    /**
     * 从233加入房间
     */
    suspend fun suspendEnterMgsGame(
        fragment: Fragment,
        toPackageName: String,
        toGameId: String,
        roomInfo: MgsBriefRoomInfo?,
        source: String?,
        joinMode: Int,
        fromUuid: String?,
        useNewCheck: Boolean = false,
        resIdBean: ResIdBean? = null,
        roomName: String? = null,
    ){
        val context = fragment.context ?: return
        withContext(Dispatchers.IO) {
            checkMgsGameInfo(context, fragment, toPackageName, toGameId, roomInfo, source, joinMode, fromUuid = fromUuid, useNewCheck, resIdBean, roomName)
        }
    }

    private suspend fun checkMgsGameInfo(
        context: Context,
        fragment: Fragment,
        packageName: String,
        gameId: String,
        roomInfo: MgsBriefRoomInfo?,
        source: String?,
        joinMode: Int,
        fromUuid: String? = null,
        useNewCheck: Boolean,
        resIdBean: ResIdBean?,
        roomName:String? = null
    ){
        if (!checkParamsLegal(context, packageName, gameId)) {
            onLaunchEnd()
            return
        }
        onLaunchStart()
        withContext(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                ToastUtil.showShort(context, R.string.game_start_launching)
            }
            if (roomInfo != null && !roomInfo.roomIdFromCp.isNullOrEmpty()) {
                // 检查是否可以加入房间
                val canJoinRoomResult = if (useNewCheck) {
                    val firstOrNull = metaRepository.canJoinRoom(roomInfo.roomIdFromCp!!).firstOrNull()
                    if (firstOrNull?.succeeded == true) {
                        firstOrNull.data?.getMessage(context)
                    } else {
                        firstOrNull?.message
                    }
                } else {
                    val tempResult = MgsBiz.canJoinMgsGameRoomV2(gameId, roomInfo.roomIdFromCp ?: "")
                    if (tempResult.succeeded) null else tempResult.errMsg()
                }
                if (useNewCheck){
                    HomeRoomAnalytics.trackRoomDetailEnterCLick(
                        ChatRoomInfo(
                            roomId = roomInfo.roomIdFromCp ?: "",
                            platformGameId = gameId,
                            roomName = roomName?:""
                        ), canJoinRoomResult ?: "success", resIdBean?.getCategoryID() ?: 0
                    )
                }
                if (canJoinRoomResult.isNullOrEmpty()) {
                    cacheRoomInfo(packageName, gameId, roomInfo, joinMode, source, fromUuid) {
                        withContext(Dispatchers.IO){
                            handleStartMgsGame(context, fragment, roomInfo, packageName, gameId, resIdBean)
                        }
                    }
                    trackJoinRoomResult(gameId, packageName, "success")

                } else {
                    withContext(Dispatchers.Main) {
                        ToastUtil.showShort(context, canJoinRoomResult)
                    }
                    trackJoinRoomResult(gameId, packageName, canJoinRoomResult)
                    onLaunchEnd()
                }
            } else {
                handleStartMgsGame(context, fragment, null, packageName, gameId, resIdBean)
            }
        }
    }

    private fun trackJoinRoomResult(gameId: String, packageName: String, canJoinRoomResult: String) {
        Analytics.track(EventConstants.JOIN_ROOM_RESULT) {
            putAll(
                mapOf(
                    "gameid" to gameId,
                    "gamepkg" to packageName,
                    "result" to canJoinRoomResult
                )
            )
        }
    }

    private fun checkParamsLegal(context: Context, packageName: String, gameId: String): Boolean {
        if (!launcherAvailable.get()) {
            ToastUtil.showShort(context, R.string.mgs_game_is_launching)
            return false
        }
        if (packageName.isEmpty() || gameId.isEmpty()) {
            Timber.e("launching mgs game error: packageName:${packageName} or gameId:${gameId} is empty")
            ToastUtil.showShort(context, R.string.fetch_game_detail_failed)
            return false
        }
        return true
    }

    private suspend fun handleStartMgsGame(
        context: Context,
        fragment: Fragment,
        roomInfo: MgsBriefRoomInfo?,
        packageName: String,
        gameId: String,
        resIdBean: ResIdBean?
    ) {
        val ugcInfo = metaRepository.getTsTypeInfo(gameId).data
        val isUgcGame = ugcInfo?.isUgcGame() == true
        Timber.d("get ugcInfo by gameId: $gameId, isUgcGame:${ugcInfo?.isUgcGame()}, isMgs:${ugcInfo?.isMgsGame()}")
        if (isUgcGame) {
            // ugc游戏
            startUgcGame(fragment,ugcInfo?.packageName?:"", gameId, ugcInfo?.displayName?:"", context)
            return
        }
        val gameInfo = getGameInfoFromNet(gameId)
        Timber.d("get gameInfo by gameId: $gameId, isMgs:${gameInfo?.isMgsGame()}")
        if (gameInfo == null) {
            Timber.d("gameInfo == null")
            withContext(Dispatchers.Main) {
                ToastUtil.showShort(context, R.string.failed_to_get_role_id)
            }
            onLaunchEnd()
            return
        }
        // ts 游戏
        startTsGame(gameInfo, roomInfo, packageName, gameId, context, fragment, resIdBean)
    }

    private suspend fun startTsGame(
        gameInfo: GameDetailInfo,
        roomInfo: MgsBriefRoomInfo?,
        packageName: String,
        gameId: String,
        context: Context,
        fragment: Fragment,
        resIdBean: ResIdBean?
    ) {
        if (!MWBiz.isAvailable()) {
            // 引擎没好，去详情页
            if (metaRepository.getTsTypeInfo(gameId).data?.isUgcGame() == true) {
                // 判断是ugc游戏，不要跳到详情页
                Timber.d("ugc game, engine not ready, packageName:${packageName},gameId:${gameId}")
                withContext(Dispatchers.Main) {
                    ToastUtil.showShort(context, R.string.ugc_game_engine_not_ready)
                }
                onLaunchEnd()
            } else {
                Timber.d("need download mgs game,packageName:${packageName},gameId:${gameId}")
                goDownloadGame(context, fragment, gameInfo)
            }
        } else {
            // 直接启动
            startNormalGame(context,fragment, gameInfo, resIdBean, roomInfo)
        }
    }

    private suspend fun startUgcGame(fragment: Fragment, packageName: String, gameId: String, gameName: String, context: Context) {
        if (!MWBiz.isAvailable()) {
            // 不要跳到详情页
            Timber.d("ugc game, engine not ready, packageName:${packageName},gameId:${gameId}")
            withContext(Dispatchers.Main) {
                ToastUtil.showShort(context, R.string.ugc_game_engine_not_ready)
            }
            onLaunchEnd()
        } else {
            // 直接启动
            tsLaunch.onLaunchListener {
                onLaunchGameEnd { params, e ->
                    TSLaunchFailedWrapper.show(fragment, params, e)
                    onLaunchEnd()
                }
            }
            val resIdBean = ResIdBean().setTsType(ResIdBean.TS_TYPE_UCG).setGameId(gameId).setCategoryID(CategoryId.MGS_INVITE)
            val gameInfo = tsLaunch.createTSGameDetailInfo(gameId, packageName, gameName)
            val params = TSLaunchParams(gameInfo, resIdBean).apply { isUgcGame = true }
            tsLaunch.launchUgc(context, params)
            /*getGameLaunch().launchGame(context, packageName, gameId, from, null, isMgs = true, gameName, isUgc = true,
                resIdBean = ResIdBean().setTsType(ResIdBean.TS_TYPE_UCG).setGameId(gameId).setCategoryID(CategoryId.MGS_INVITE))*/
//            onLaunchEnd()
        }
    }

    /**
     * 缓存房间信息
     */
    private suspend fun cacheRoomInfo(
        packageName: String,
        gameId: String,
        roomInfo: MgsBriefRoomInfo?,
        joinMode: Int,
        source: String?,
        fromUuid: String?,
        callBack: suspend () -> Unit
    ) {
        Timber.d("save room cache: packageName: $packageName, gameId: $gameId, fromUuid: $fromUuid")
        if (fromUuid.isNullOrEmpty()) {
            MgsBiz.saveMgsGameRoom(
                MgsRoomCacheInfo(joinMode, false, packageName, gameId, roomInfo, source, null),
                packageName
            )
            return
        }
        withContext(Dispatchers.IO) {
            MgsBiz.getOpenIdByUuid(fromUuid, gameId).collect {
                val inviteOpenId =  it.data?.get(0)?.openId
                MgsBiz.saveMgsGameRoom(
                    MgsRoomCacheInfo(
                        joinMode,
                        false,
                        packageName,
                        gameId,
                        roomInfo,
                        source,
                        inviteOpenId
                    ), packageName
                )
                callBack.invoke()
            }
        }
    }

    /**
     * 去MGS下载界面下载游戏
     * 统一launchEnd
     */
    private suspend fun goDownloadGame(
        context: Context,
        fragment: Fragment,
        gameInfo: GameDetailInfo
    ) {
        onLaunchEnd()
        // 去游戏详情页下载游戏
        Timber.d("goTo game detail to download game")
        withContext(Dispatchers.Main) {
            MetaRouter.GameDetail.navigate(
                fragment,
                gameInfo.id,
                ResIdBean().setGameId(gameInfo.id).setCategoryID(CategoryId.MGS_INVITE),
                gameInfo.packageName,
                autoDownload = true,
                type = gameInfo.typeToString()
            )
        }
    }

    private fun onLaunchStart() {
        launcherAvailable.set(false)
    }

    private fun onLaunchEnd() {
        launcherAvailable.set(true)
        synchronized(launchEndListener) {
            launchEndListener.onEach { it.value.invoke(it.key) }
        }
    }

    private suspend fun getGameInfoFromNet(gameId: String): GameDetailInfo? {
        return metaRepository.getGameInfoByGameIdWithoutFlow(gameId).data
    }

    /**
     * 启动游戏
     */
    private suspend fun startNormalGame(
        context: Context,
        fragment: Fragment,
        gameInfo: GameDetailInfo,
        resIdBean: ResIdBean?,
        roomInfo: MgsBriefRoomInfo?
    ) {
        // 直接启动
        tsLaunch.onLaunchListener {
            onLaunchGameEnd { params, e ->
                TSLaunchFailedWrapper.show(fragment, params, e)
                onLaunchEnd()
            }
        }
        val resId = resIdBean ?: ResIdBean().setGameId(gameInfo.id)
            .setCategoryID(CategoryId.MGS_INVITE).setTsType(ResIdBean.TS_TYPE_NORMAL)
        val params = TSLaunchParams(gameInfo, resId, roomIdFromCp = roomInfo?.roomIdFromCp ?: "")
        tsLaunch.launch(context, params)
   /*     getGameLaunch().launchGame(context, gameInfo.packageName, gameInfo.id, from, gameInfo.startupExtension,
            isMgs = gameInfo.isMgsGame(), gameInfo.name, isUgc = false,
            resIdBean = ResIdBean().setGameId(gameInfo.id).setCategoryID(CategoryId.MGS_INVITE))
        onLaunchEnd()*/
    }

/*    private fun getGameLaunch(): ILaunchGameInteractor {
        return metaVerseInteractor
    }*/
}