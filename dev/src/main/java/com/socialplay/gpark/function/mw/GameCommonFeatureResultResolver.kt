package com.socialplay.gpark.function.mw

import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.model.GameCommonFeatureResult
import com.socialplay.gpark.data.model.editor.UpdateRoleDataEvent

/**
 * Created by bo.li
 * Date: 2023/4/23
 * Desc:
 */
object GameCommonFeatureResultResolver {
    /**
     * 分发游戏结果回调
     */
    fun dispatchCommonFeatureResult(result: GameCommonFeatureResult) {
        when(result.feature) {
            GameCommonFeature.FEATURE_GET_ROLE_INFO -> {
                val roleId = result.params?.get("roleId") as? String
                val wholeBodyImage = result.params?.get("wholeBodyImage") as? String
                CpEventBus.post(UpdateRoleDataEvent(roleId, wholeBodyImage))
            }
        }
    }
}