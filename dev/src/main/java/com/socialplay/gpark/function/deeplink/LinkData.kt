package com.socialplay.gpark.function.deeplink

import android.app.Activity
import android.net.Uri
import androidx.fragment.app.Fragment

class LinkData(val activity: Activity, val navHost: Fragment, val selector: TabSelector?, val uri: Uri, val source:String? = null) {
    companion object {
        // 来源：位置兜底
        const val SOURCE_UNKNOWN = "unknown"
        // 来源：本地跳转
        const val SOURCE_LOCAL = "local"
        // 来源：本地扫码
        const val SOURCE_QR_CODE = "qr_code"
        // 来源：h进程mw协议
        const val SOURCE_HOST_MW = "host_mw"
        // 来源：m进程mw协议
        const val SOURCE_GAME_MW = "game_mw"
        // 来源：push
        const val SOURCE_PUSH = "push"
        // 来源：系统消息
        const val SOURCE_SYS_MESSAGE = "sys_message"
        // 来源：精选运营位
        const val SOURCE_CHOICE_OPERATION = "choice_operation"
        // 来源：造物岛运营位
        const val SOURCE_EDITOR_CREATE = "editorCreate"
        // 来源：首页半屏飞轮位
        const val SOURCE_HALF_HOME = "half_home"
        // 来源：首页半屏弹窗
        const val SOURCE_AVATAR_POPUP = "avatar_popup"
        // 来源：kol飞轮位
        const val SOURCE_KOL_FLY_WHEEL = "kol_fly_wheel"
        // 来源：kol banner
        const val SOURCE_KOL_BANNER = "kol_banner"
        // 数据中转
        const val SOURCE_DATA_RELAY = "data_relay"
        // 分享数据中转
        const val SOURCE_SHARE_DATA_RELAY = "share_data_relay"
        // 来源：个人页banner
        const val SOURCE_PROFILE_BANNER = "profile_banner"

        const val SOURCE_UGC_ASSET_FEED_NOTICE = "ugc_asset_feed_notice"
        // 来源：模组运营位
        const val SOURCE_MODULE_CREATE = "module_create"
    }
}
