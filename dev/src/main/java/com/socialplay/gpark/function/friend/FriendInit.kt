package com.socialplay.gpark.function.friend

import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.function.startup.core.ProcessType
import kotlinx.coroutines.*
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/30
 *     desc   : 好友相关的数据初始化
 */
object FriendInit {

    private val friendInteractor: FriendInteractor by lazy { GlobalContext.get().get() }

    suspend fun init(processType: ProcessType) {
        withContext(Dispatchers.Main) {
            Timber.i("zhuwei init friends start")
            friendInteractor.init(processType)
            Timber.i("zhuwei init friends finish")
        }
    }
}