package com.socialplay.gpark.function.apm.page

import android.os.Handler
import android.os.HandlerThread
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.page.data.PageConfig
import com.socialplay.gpark.function.apm.page.data.PageObject
import timber.log.Timber

/**
 * 2024/1/19
 */
object PageMonitorCore {

    /*页面HashCode , 页面属性对象*/
    private val pageObjects = LinkedHashMap<String, PageObject>()

    /*页面超时检测*/
    private val timeoutMonitor by lazy { PageTimeoutMonitor(8_000, this::pageTimeout) }
    private val scheduledTasks by lazy { ScheduledTasks(this::processScheduledTasks) }

    fun init() {
        PageMonitorDeque.start()
        PageMonitorDeque.processMessage(this::dispatchMessage)
        timeoutMonitor.start()
        scheduledTasks.start()
    }


    /*定时任务，检测接口*/
    private fun processScheduledTasks(pageKey: String) {
        syncPageObjects {
            val pageObject = get(pageKey)
            if (pageObject != null && !pageObject.isEnd()) {
                //处理接口回来之后，是否还有绘制事件，没有事件则直接判定结束
                Timber.d("processScheduledTasks,${pageObject.pageKey},${pageObject.pageClassName}")
                val newPageObject = pageObject.copy(status = PageObject.COMPLETE).analytics()
                put(pageKey, newPageObject)
            }
        }
    }

    /**
     * 分发处理消息
     */
    private suspend fun dispatchMessage(message: IMonitorMessage) = synchronized(pageObjects) {
        when (message) {
            is PageLifeMonitorMessage -> monitorPage(message)
            is ApiMonitorMessage -> requestApi(message)
            is DrawMonitorMessage -> drawPage(message)
        }
    }

    /*页面检测超时*/
    private fun pageTimeout(pageKey: String) {
        syncPageObjects {
            val pageObject = get(pageKey)
            if (pageObject != null && !pageObject.isEnd()) {
                Timber.d("pageTimeout,${pageObject.pageKey},${pageObject.pageClassName}")
                val newPageObject = pageObject.copy(status = PageObject.TIMEOUT).analytics()
                put(pageKey, newPageObject)
            }
        }
    }

    /*消息分发*/

    private fun monitorPage(message: PageLifeMonitorMessage) {
        val config = PageMonitor.getConfig(message.pageClassName) ?: return
        when (message.life) {
            PageLifeMonitorMessage.LIFE_CREATE -> onCreatePage(message, config)
            PageLifeMonitorMessage.LIFE_VIEW_CREATED -> onViewCreatedPage(message)
            PageLifeMonitorMessage.LIFE_START -> onStartPage(message)
            PageLifeMonitorMessage.LIFE_RESUME -> onResumePage(message)
            PageLifeMonitorMessage.LIFE_DESTROY_VIEW -> onDestroyPage(message)
        }
    }


    private fun requestApi(message: ApiMonitorMessage) {
        when (message.status) {
            ApiMonitorMessage.API_STATUS_START -> requestApiStart(message)
            ApiMonitorMessage.API_STATUS_END -> requestApiEnd(message)
            ApiMonitorMessage.API_STATUS_ERROR -> requestApiError(message)
        }
    }

    /*消息处理*/

    private fun onCreatePage(message: PageLifeMonitorMessage, config: PageConfig) {
        syncPageObjects {
            Timber.d("onCreatePage,${message.only()},${message.pageClassName},${message.time}")
            if (get(message.only())?.isEnd() == true) return@syncPageObjects
            val page = PageObject(
                pageName = message.pageName, pageClassName = message.pageClassName,
                pageKey = message.pageKey, config = config, onCreateTime = message.time,
            )
            put(message.only(), page)
        }
    }

    private fun onViewCreatedPage(message: PageLifeMonitorMessage) {
        syncPageObject(message) {
            Timber.d("onViewCreatedPage,${message.only()},${message.pageClassName},${message.time}")
            if (isEnd() || onViewCreatedTime > 0) return@syncPageObject null
            copy(onViewCreatedTime = message.time)
        }
    }

    private fun onStartPage(message: PageLifeMonitorMessage) {
        syncPageObject(message) {
            Timber.d("onStartPage,${message.only()},${message.pageClassName},${message.time}")
            if (isEnd() || onStartTime > 0) return@syncPageObject null
            copy(onStartTime = message.time)
        }
    }

    private fun onResumePage(message: PageLifeMonitorMessage) {
        syncPageObject(message) {
            Timber.d("onResumePage,${message.only()},${message.pageClassName},${message.time}")
            if (isEnd() || onResumeTime > 0) return@syncPageObject null
            timeoutMonitor.monitor(message.only())
            copy(onResumeTime = message.time)
        }
    }

    private fun onDestroyPage(message: PageLifeMonitorMessage) {
        syncPageObject(message) {
            Timber.d("onDestroyPage,${message.only()},${message.pageClassName},${message.time}")
            if (isEnd()) return@syncPageObject null
            copy(onDestroyTime = message.time, status = PageObject.CLOSE).analytics()
        }
    }

    private fun drawPage(message: DrawMonitorMessage) {
        syncPageObject(message) {
            if (isEnd()) return@syncPageObject null
            Timber.d("drawPage,${message.only()},${message.time} ${config.requestApi}")
            if (config.requestApi) {//需求请求接口
                if (isApiRequestEnd()) {//接口请求结束，接口成功但是无数据或者接口失败，都可能没有绘制事件
                    copy(apiEndDrawTime = message.time, status = PageObject.COMPLETE).analytics()
                } else {
                    if (firstDrawTime <= 0) {
                        copy(firstDrawTime = message.time)
                    } else null
                }
            } else {
                if (firstDrawTime <= 0) {
                    copy(firstDrawTime = message.time, status = PageObject.COMPLETE).analytics()
                } else null
            }
        }
    }

    private fun requestApiStart(message: ApiMonitorMessage) {
        syncPageObject(message) {
            if (isEnd()) return@syncPageObject null
            Timber.d("requestApiStart,${message.only()},${message.pageClassName},${message.time}")
            if (apiStartTime <= 0) copy(apiStartTime = message.time) else null
        }
    }

    private fun requestApiEnd(message: ApiMonitorMessage) {
        syncPageObject(message) {
            if (isEnd()) return@syncPageObject null
            Timber.d("requestApiEnd,${message.only()},${message.pageClassName},${message.time}")
            if (apiStartTime > 0 && apiSuccessTime <= 0 && apiErrorTime <= 0) {
                //接口请求结束，接口成功但是无数据或者接口失败，都可能没有绘制事件
                scheduledTasks.scheduled(message.only())
                copy(apiSuccessTime = message.time)
            } else null
        }
    }

    private fun requestApiError(message: ApiMonitorMessage) {
        syncPageObject(message) {
            if (isEnd()) return@syncPageObject null
            Timber.d("requestApiEnd,${message.only()},${message.pageClassName},${message.time}")
            if (apiStartTime > 0 && apiSuccessTime <= 0 && apiErrorTime <= 0) {
                //接口请求结束，接口成功但是无数据或者接口失败，都可能没有绘制事件
                scheduledTasks.scheduled(message.only())
                copy(apiErrorTime = message.time)
            } else null
        }
    }

    /*逻辑处理*/
    private fun syncPageObjects(block: LinkedHashMap<String, PageObject>.() -> Unit) {
        synchronized(pageObjects) {
            block.invoke(pageObjects)
        }
    }

    private fun syncPageObject(message: TimeMonitorMessage, block: PageObject.() -> PageObject?) {
        syncPageObjects {
            get(message.only())?.let(block)?.also {
                put(message.only(), it)
            }
        }
    }
}

class PageTimeoutMonitor(
    private val timeoutTime: Long = 0,
    private val onMonitor: (key: String) -> Unit,
) : HandlerThread("PageTimeoutMonitor") {

    private val monitorHistory = mutableSetOf<String>()

    companion object {
        private const val MESSAGE_KEY_TIMEOUT = 1010
    }

    private val handler by lazy {
        Handler(looper) {
            if (it.what == MESSAGE_KEY_TIMEOUT) {
                onMonitor.invoke((it.obj as? String) ?: "")
            }
            true
        }
    }

    fun monitor(key: String) {
        if (monitorHistory.contains(key)) return
        monitorHistory.add(key)
        val message = handler.obtainMessage(MESSAGE_KEY_TIMEOUT, key)
        handler.sendMessageDelayed(message, timeoutTime)
    }

}

class ScheduledTasks(
    private val onScheduled: (key: String) -> Unit,
) : HandlerThread("PageMonitorScheduledTasks") {

    private val scheduledHistory = mutableSetOf<String>()

    companion object {
        private const val MESSAGE_KEY_SCHEDULED = 1011
    }

    private val handler by lazy {
        Handler(looper) {
            if (it.what == MESSAGE_KEY_SCHEDULED) {
                onScheduled.invoke((it.obj as? String) ?: "")
            }
            true
        }
    }

    fun scheduled(key: String, delay: Long = 5000) {
        if (scheduledHistory.contains(key)) return
        scheduledHistory.add(key)
        val message = handler.obtainMessage(MESSAGE_KEY_SCHEDULED, key)
        handler.sendMessageDelayed(message, delay)
    }

}