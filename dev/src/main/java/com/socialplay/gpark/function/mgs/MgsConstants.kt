package com.socialplay.gpark.function.mgs

object MgsConstants {
    // 加入方式：不加入房间
    const val MODE_OTHER = -1
    // 加入方式：加入房间
    const val MODE_JOIN_ROOM = 0

    // 房间状态：准备中
    const val STATE_GAME_PREPARE = 0
    // 房间状态：游戏中
    const val STATE_GAME_PLAYING = 1
    // 房间状态：游戏结束
    const val STATE_GAME_OVER = 2

    // MGS_游戏启动 用户状态：游客
    const val USER_TYPE_VISITOR = "user_type_visitor"
    // MGS_游戏启动 用户状态：已登录
    const val USER_TYPE_USER = "user_type_user"

    // MGS_游戏启动 游戏详情页
    const val SOURCE_GAME_DETAIL = "source_game_detail"
    // MGS_游戏启动：扫一扫
    const val SOURCE_SCAN = "source_scan"
    // MGS_游戏启动 私聊消息
    const val SOURCE_FRIEND_PRIVATE_CONVERSATION = "source_friend_private_conversation"
    // MGS_游戏启动 搜索房间
    const val SOURCE_FRIEND_SEARCH = "source_friend_search"

    // MGS_用户资料卡_加好友_点击：位置：房间tab
    const val SOURCE_ROOM_TAB = "room_tab"
    // MGS_用户资料卡_加好友_点击：位置：用户资料卡
    const val SOURCE_USER_CARD = "user_card"

    // 邀请好友_好友列表页面_展示：type：自己未登录
    const val TYPE_SELF_NOT_LOGGED_FORM = "self_not_logged_form"
    // 邀请好友_好友列表页面_展示：type：没有好友在线
    const val TYPE_NO_FRIEND_ONLINE_FORM = "no_friend_online_form"
    // 邀请好友_好友列表页面_展示：type：已登陆且有好友在线
    const val TYPE_NORMAL_FORM = "normal_form"


    /**
     * 邀请生成参数约定类型
     */

    // 面对对邀请的shareChannel参数
    const val TYPE_FACE_TO_FACE = "233"

    // 复制链接的shareChannel参数
    const val TYPE_COPY_LINK = "LINK"

    // 系统分享的shareChannel参数
    const val TYPE_SYSTEM = "SYSTEM"

    //截图分享
    const val TYPE_SCREEN_SHOT ="QRcode"

}