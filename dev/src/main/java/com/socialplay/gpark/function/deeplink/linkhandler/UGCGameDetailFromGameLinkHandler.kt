package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/07/20
 *     desc   :
 *
 */
class UGCGameDetailFromGameLinkHandler : LinkHandler {

    override fun handle(chain: <PERSON>HandlerChain, data: LinkData): LinkHandleResult {
        runCatching {
            val dataString = data.uri.getQueryParameter("data") ?: return LinkHandleResult.Failed("no data")
            val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
            val extraBundle = arguments.getBundle(MetaDeepLink.PARAM_EXTRA_BUNDLE)
            if (extraBundle != null) {
                MetaRouter.MobileEditor.ugcDetail(data.navHost, extraBundle)
            }
        }
        return LinkHandleResult.Success
    }
}