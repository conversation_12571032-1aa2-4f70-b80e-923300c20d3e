package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/14
 *     desc   :
 * </pre>
 */
class UgcDesignDetailHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink UgcDesignDetailHandler handle uri:%s", data.uri)

        val itemId = data.uri.getQueryParameter(MetaDeepLink.PARAM_ITEM_ID)
        if (itemId.isNullOrBlank()) return LinkHandleResult.Failed("no item id")
        val targetCommentId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_COMMENT_ID)
        val targetReplyId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_REPLY_ID)
        val categoryId = data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: CategoryId.SCHEME_DEFAULT

        MetaRouter.UgcDesign.detail(
            fragment = data.navHost,
            itemId = itemId,
            categoryId = categoryId,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
        )
        return LinkHandleResult.Success
    }
}