package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.app.Application
import android.content.Context
import com.socialplay.gpark.function.analytics.handle.GameLaunchAnalytics
import com.socialplay.gpark.function.mw.OnMWGameLifecycleInterceptor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by bo.li
 * Date: 2021/5/26
 * Desc:
 */
class LaunchResultLifeCycle(private val onMWInterceptor: OnMWGameLifecycleInterceptor? = null) :
    MWLifecycle() {

    var isFirstLaunch = true

    override fun onBeforeApplicationCreated(app: Application) {
        super.onBeforeApplicationCreated(app)
        isFirstLaunch = GameLaunchAnalytics.getIsFirstLaunch(getPackageName(app))
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        val packageName = getPackageName(activity)
        val appName = getAppName(activity)
        val gameId = getGameId()
        val canAnalytic = GameLaunchAnalytics.launchEnd(packageName, appName, gameId, isTsGame())
        if (!canAnalytic) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            delay(5000)
            GameLaunchAnalytics.launchEffective(getPackageName(activity), appName, gameId, isTsGame())
            isFirstLaunch = false
        }
    }

    private fun getPackageName(context: Context):String{
        return onMWInterceptor?.getPackageName()?:context.packageName
    }

    private fun getGameId(): String {
        return onMWInterceptor?.getGameId() ?: ""
    }

    private fun isTsGame(): Boolean {
        return onMWInterceptor != null
    }

    private fun getAppName(activity: Activity):String{
        return onMWInterceptor?.getAppName() ?: kotlin.runCatching {
            val packageManager = activity.applicationContext.packageManager
            val appInfo = packageManager.getApplicationInfo(activity.packageName, 0)
            activity.applicationContext.packageManager.getApplicationLabel(appInfo)
        }.getOrNull().toString()
    }

}