package com.socialplay.gpark.function.gamereview

import com.socialplay.gpark.data.base.*
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType

/**
 * Created by bo.li
 * Date: 2022/4/22
 * Desc:
 */
object LoadMoreHelper {
    fun <T, H> commonFetchLoadMoreData(originList: MutableList<T>?, netList: List<T>?, isRefresh: <PERSON><PERSON><PERSON>, result: DataResult<H>, isEnd: Boolean): Pair<LoadStatus, MutableList<T>?> {
        val status = LoadStatus()
        val list = if (isRefresh) arrayListOf() else originList ?: arrayListOf()
        status.status = when {
            isRefresh          -> {
                if (!result.succeeded) {
                    // RefreshFailed
                    status.message = result.message
                }
                if (isEnd) {
                    // RefreshEnd
                    LoadType.RefreshEnd
                } else {
                    // Refresh/RefreshEmpty
                    LoadType.Refresh
                }
            }
            result.succeeded -> {
                if (!isEnd) {
                    // LoadMore
                    LoadType.LoadMore
                } else {
                    // LoadEnd
                    LoadType.End
                }
            }
            else               -> {
                // LoadMoreFailed
                status.message = result.message
                LoadType.Fail
            }
        }
        netList?.let { data ->
            list.addAll(data)
        }
        return status to list
    }
}