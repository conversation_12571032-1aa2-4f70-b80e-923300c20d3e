package com.socialplay.gpark.function.deeplink

import android.app.Activity
import androidx.fragment.app.Fragment
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.mgs.MgsShareContent
import com.socialplay.gpark.ui.compliance.MetaProtocol
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.GsonUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-12-23 4:39 下午
 * @desc:
 */
object DispatchMgsShare {

    private val floatNoticeInteractor by lazy { GlobalContext.get().get<FloatNoticeInteractor>() }
    private val metaKv by lazy { GlobalContext.get().get<MetaKV>() }

    private var processedShareIdByDeepLink = ""

    suspend fun dispatchMgsShare(activity: Activity, fragment: Fragment?): Boolean {
        if (MetaProtocol.needLegal() && !MetaProtocol.agreedProtocol) {
            // 如果需要同意协议，但还没同意用户协议，就先直接返回false，避免说不合规
            return false
        }
        val clipTxt = ClipBoardUtil.getClipBoardContent(activity)
        Timber.d("leownnn - clipBoard txt : $clipTxt")
        val mgsShareContent = GsonUtil.gsonSafeParse<MgsShareContent>(clipTxt) ?: return false
        var type = if (mgsShareContent.type.isNullOrEmpty()) {
            MgsShareContent.TYPE_JOIN_ROOM
        } else {
            // 这里旧版本用户收到新版本用户的小屋房间邀请，也会弹出加好友弹窗
            mgsShareContent.type
        }
        val metaKv = GlobalContext.get().get<MetaKV>()
        val clickTime = metaKv.appKV.dispatchClickTime + 60 * 1000 // 1分钟内 只处理一次
        val time = mgsShareContent.time
        // 请求过不再处理
        if (mgsShareContent.time <= clickTime) {
            ClipBoardUtil.clearClipBoard(activity)
            return false
        }
        val result = handleMgsShare(activity, fragment, mgsShareContent.shareId, type)
        metaKv.appKV.dispatchClickTime = mgsShareContent.time
        ClipBoardUtil.clearClipBoard(activity)
        return result
    }

    private fun handleMgsShare(activity: Activity, fragment: Fragment?, shareId: String, type: String): Boolean {
        if (processedShareIdByDeepLink == shareId) {
            return false
        }
        floatNoticeInteractor.showFloatNotice(GlobalContext.get().get(), activity, fragment, shareId, type)
        return true
    }

    fun setProcessedShareIdByDeepLink(shareId: String) {
        processedShareIdByDeepLink = shareId
    }

}