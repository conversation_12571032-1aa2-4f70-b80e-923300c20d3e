package com.socialplay.gpark.function.qrcode

import android.content.Context
import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.util.extension.toast
import timber.log.Timber

/**
 * 不支持的二维码
 */
object UnsupportedHandler : QRCodeHandler {

    override suspend fun process(
        context: Context,
        fragment: Fragment,
        request: ScanRequestData,
        result: ScanResultData
    ): Boolean {

        context.toast(context.getString(R.string.not_support_action_qr))
        Timber.d("UnsupportedHandler:${result.content}")
        return true
    }
}