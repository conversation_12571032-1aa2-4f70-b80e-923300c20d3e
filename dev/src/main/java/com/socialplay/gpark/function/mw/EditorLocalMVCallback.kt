package com.socialplay.gpark.function.mw

import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorLocalMsg
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import java.io.File

object EditorLocalMVCallback {
    val scope = MainScope()
    val metaKV: MetaKV = GlobalContext.get().get()

    fun handlerEditorLocal(msg: EditorLocalMsg, messageId: Int) {
        when (msg.type) {
            // 1. 模板列表API
            1 -> getTemplateList(msg.lastId, messageId)
            // 2. 我的已发布API
            2 -> getPublishedList(msg.lastId, messageId)
            // 3. 本地工程目录列表
            3 -> getEditorLocalGames(messageId)
            // 4. 下载和copy某一模板文件并返回本地路径相关信息
            4 -> downloadTemplate(msg.typeData, messageId)
            // 5. 获取审核状态
            5 -> getEditorLocalStatus()
        }
    }


    private fun getEditorLocalStatus() {
        scope.launch(Dispatchers.IO) {
            val jsonEntity: EditorConfigJsonEntity? = kotlin.runCatching {
                val path = metaKV.analytic.getLaunchResIdBean(MWBizBridge.currentGamePkg())?.getPath()?:return@runCatching null
                EditorLocalHelper.getEditorConfigEntity(EditorLocalHelper.getJsonFile(File(path)))
            }.getOrNull()
            val metaRepository: IMetaRepository = GlobalContext.get().get()
            if (jsonEntity == null) {
                callUENoId()
                return@launch
            }

            val id :String? = if (!jsonEntity.id.isNullOrEmpty()) {
                jsonEntity.id
            } else if (!jsonEntity.packageName.isNullOrEmpty()) {
                metaRepository.getUgcIdByPackageName(jsonEntity.packageName!!).firstOrNull()?.data
            } else {
                callUENoId()
                return@launch
            }

            if (id.isNullOrEmpty()){
                callUENoId()
                return@launch
            }

            val result = metaRepository.getEditorLocalStatus(listOf(id))

            callUe(
                hashMapOf(
                    "type" to 5,
                    "code" to result.code,
                    "message" to result.message,
                    "typeData" to result.data?.list?.firstOrNull(),
                ),
                0
            )
        }
    }

    private fun callUENoId() {
        callUe(hashMapOf("type" to 5, "code" to 504, "message" to "未发布, 拿不到id"),0)
    }

    private fun downloadTemplate(typeData: EditorTemplate?, messageId: Int) {
        typeData ?: return
        scope.launch(Dispatchers.IO) {
            val downloadFile = EditorLocalHelper.downloadProjectFile(typeData)
            if (downloadFile == null) {
                callUe(
                    hashMapOf(
                        "type" to 4,
                        "code" to 501,
                        "message" to "download failed",
                    ),
                    messageId
                )
                return@launch
            }
            val copyProjectFiles = withContext(Dispatchers.IO) {
                EditorLocalHelper.copyProjectFiles(downloadFile, EditorLocalHelper.getLocalUnzipFile(downloadFile.name))
            }
            if (copyProjectFiles == null) {
                callUe(
                    hashMapOf(
                        "type" to 4,
                        "code" to 502,
                        "message" to "copy failed",
                    ),
                    messageId
                )
            } else {
                callUe(
                    hashMapOf(
                        "type" to 4,
                        "code" to 200,
                        "message" to "success",
                        "typeData" to hashMapOf(
                            "path" to copyProjectFiles.absolutePath,
                            "parentId" to typeData.gid // 此处的parentId是父模板的内容库gameId
                        )
                    ),
                    messageId
                )
            }
        }
    }


    private fun getEditorLocalGames(messageId: Int) {
        scope.launch(Dispatchers.IO) {
            val localGames = EditorLocalHelper.getAllLocalGamesWithoutPreload {
                it.jsonConfig.id != null && it.path != null
            }.map {
                // 此处的parentId是父模板的内容库gameId
                hashMapOf("gameId" to it.jsonConfig.id, "path" to it.path, "parentId" to it.jsonConfig.gid)
            }
            callUe(hashMapOf("type" to 3, "typeData" to localGames), messageId)
        }
    }

    private fun getPublishedList(lastId: String?, messageId: Int) {
        scope.launch(Dispatchers.IO) {
            val metaRepository: com.socialplay.gpark.data.IMetaRepository = GlobalContext.get().get()
            metaRepository.getEditorPublished(lastId?:"")
                .collect { data ->
                    callUe(
                        hashMapOf(
                            "type" to 2,
                            "code" to (data.code ?: 200),
                            "message" to (data.message ?: "success"),
                            "typeData" to (data.data ?: UgcGameInfo(mutableListOf(), true, 0))
                        ),
                        messageId
                    )
                }
        }
    }

    private fun getTemplateList(lastId: String?, messageId: Int) {
        scope.launch(Dispatchers.IO) {
            val metaRepository: com.socialplay.gpark.data.IMetaRepository = GlobalContext.get().get()
            metaRepository.getUgcTemplateList(10, lastId, null)
                .collect { data ->
                    callUe(
                        hashMapOf<String, Any>(
                            "type" to 1,
                            "code" to (data.code ?: 200),
                            "message" to (data.message ?: "success"),
                            "typeData" to (data.data ?: arrayListOf())
                        ),
                        messageId
                    )
                }
        }
    }

    private fun callUe(params: Map<String, Any?>, messageId: Int) {
        UGCProtocolSender.sendProtocol(ProtocolSendConstant.PROTOCOL_TEMPLATE_GAME_RESULT, messageId, params)
    }
}