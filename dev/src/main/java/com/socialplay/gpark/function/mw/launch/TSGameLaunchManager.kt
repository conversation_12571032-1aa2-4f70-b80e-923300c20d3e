package com.socialplay.gpark.function.mw.launch

import android.content.Context
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.setp.ITSGameCheckStep
import com.socialplay.gpark.function.mw.launch.setp.ITSGameLaunchStep
import com.socialplay.gpark.function.mw.launch.setp.ITSGameLaunchWrapStep
import com.socialplay.gpark.function.mw.launch.setp.ITSGameProcessStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameLaunchLocalStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameLaunchNormalStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameLaunchPlotStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameLaunchUGCStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameLaunchViewStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessConfigStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessLocalStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessMGSStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessNormalStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessPlotStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessUGCStep
import com.socialplay.gpark.function.mw.launch.setp.TSGameProcessViewStep
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference

/**
 * xingxiu.hou
 * 2022/10/25
 */
object TSGameLaunchManager {

    val tsLaunch: ITSGameLaunchWrapStep = BuildLaunchStep {
        addProcess(TSGameProcessConfigStep())
        addProcess(TSGameProcessMGSStep())
        addProcess(TSGameProcessNormalStep())
        addLaunch(TSGameLaunchNormalStep())
    }

    val tsUGCLaunch: ITSGameLaunchWrapStep = BuildLaunchStep {
        addProcess(TSGameProcessConfigStep())
        addProcess(TSGameProcessMGSStep())
        addProcess(TSGameProcessUGCStep())
        addLaunch(TSGameLaunchUGCStep())
    }

    val tsLocalLaunch: ITSGameLaunchWrapStep = BuildLaunchStep {
        addProcess(TSGameProcessConfigStep())
        addProcess(TSGameProcessLocalStep())
        addLaunch(TSGameLaunchLocalStep())
    }

    val tsViewLaunch: ITSGameLaunchWrapStep = BuildLaunchStep {
        addProcess(TSGameProcessConfigStep())
        addProcess(TSGameProcessViewStep())
        addLaunch(TSGameLaunchViewStep())
    }

    val tsPlotLaunch: ITSGameLaunchWrapStep = BuildLaunchStep {
        addProcess(TSGameProcessConfigStep())
        addProcess(TSGameProcessMGSStep())
        addProcess(TSGameProcessPlotStep())
        addLaunch(TSGameLaunchPlotStep())
    }
}

private class BuildLaunchStep(builder: TypeLaunchGameBuilder.() -> Unit) : ITSGameLaunchWrapStep {

    private val builder by lazy { TypeLaunchGameBuilder() }

    init {
        builder.invoke(this.builder)
        checkNotNull(this.builder.launch.get()) { "must be call 'launch()' method" }
    }

    override suspend fun check(context: Context, params: TSLaunchParams) {
        builder.checkList.forEach {
            Timber.tag("LaunchStep").d("${it.javaClass.name} check")
            it.check(context, params)
        }
    }

    override suspend fun process(context: Context, params: TSLaunchParams) {
        builder.processList.forEach {
            Timber.tag("LaunchStep").d("${it.javaClass.name} process")
            it.process(context, params)
        }
    }

    override suspend fun launch(context: Context, params: TSLaunchParams) {
        Timber.tag("LaunchStep").d("${builder.launch.get().javaClass.name} launch")
        return builder.launch.get().launch(context, params)
    }
}

class TypeLaunchGameBuilder {

    val checkList = mutableListOf<ITSGameCheckStep>()
    val processList = mutableListOf<ITSGameProcessStep>()
    val launch: AtomicReference<ITSGameLaunchStep> = AtomicReference()

    fun addProcess(step: ITSGameProcessStep) {
        processList.add(step)
    }

    fun addCheck(step: ITSGameCheckStep) {
        checkList.add(step)
    }

    fun addLaunch(onLaunch: ITSGameLaunchStep) {
        launch.set(onLaunch)
    }
}

