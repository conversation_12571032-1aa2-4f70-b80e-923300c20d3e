package com.socialplay.gpark.function.mw.launch.bean

import androidx.annotation.Keep
import org.json.JSONObject

/**
 * 2023/8/9
 */
@Keep
data class TSLaunchResult(
    val gameId: String,
    val result: String,
    val errorType: String,
    val reason: String,
) {

    companion object {
        fun fromJson(json: String): TSLaunchResult {
            return JSONObject(json).let {
                val gameId = it.optString("gameId", "")
                val result = it.optString("result", "")
                val errorType = it.optString("errorType", "")
                val reason = it.optString("reason", "")
                return TSLaunchResult(gameId, result, errorType, reason)
            }
        }
    }

    fun isSuccess() = result == "success"

}