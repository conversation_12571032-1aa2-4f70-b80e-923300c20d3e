package com.socialplay.gpark.data.model.account

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 * </pre>
 */
data class PrivacySetting(
    val id: Int,
    val init: Boolean = true,
    val enable: Boolean = init,
    val titleRes: Int = 0,
    val title: String? = null,
    val showLabelRedDot: Boolean = false
) {

    companion object {
        const val SWITCH_TRY_ON = 1
        const val SWITCH_SCREENSHOT_SHARE = 2
        const val SWITCH_MESSAGE_ON = 3
        const val SWITCH_RELATION_LIST = 4
        const val SWITCH_FRIEND_REQUESTS_NOTIFY = 5
    }

    val isChanged: Boolean get() = init != enable
    val isScreenshot get() = id == SWITCH_SCREENSHOT_SHARE
}

data class PrivacySwitch(
    val ootdPrivateSwitch: Boolean? = null,
    val chatMessageSwitch: Boolean? = null,
    val followerShowSwitch: Boolean? = null,
)