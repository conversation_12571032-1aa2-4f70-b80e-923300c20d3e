package com.socialplay.gpark.data.model.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.socialplay.gpark.data.model.aibot.BotInfo

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/06/26
 *     desc   :
 *
 */
@Entity(tableName = "ai_message")
data class AIMessageEntity(
    val senderUserID: String, //发送方id
    val receiverUserID: String, //接收方id
    val timestamp: Long, //当前消息的存储本地时间
    val messageType: Int, //消息类型 1，文本，2系统通知
    val messageDirection: Int, //消息来源，是发送方还是接受方 1send ,2接受
    val isRead: Boolean, // 已读还是未读
    var content: String, //消息内容
    val extra: String?, //其他扩展信息
    val botInfo: BotInfo?, //当前对角色详情信息
    val status: Int = STATUS_DEFAULT,
    @PrimaryKey(autoGenerate = true)
    val messageId: Long = 0, // 消息id
){
    fun isSend(): Boolean {
        return messageDirection == MESSAGE_SEND
    }
    companion object{
        const val TEXT_MESSAGE = 1
        const val CMD_MESSAGE = 2
        const val INFO_MESSAGE = 3
        const val INFO_GREETING = 4
        const val STATUS_RECEIVING = 1
        const val STATUS_DEFAULT = 0
        const val STATUS_START = -1
        const val MESSAGE_SEND = 1
        const val MESSAGE_RECEIVE = 2
    }
}


