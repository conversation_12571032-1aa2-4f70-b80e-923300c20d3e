package com.socialplay.gpark.data.model

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/08/13
 * desc   :
 * </pre>
 */

enum class NetType(val desc: String) {
    Unknown("unknow"),
    Wifi("wifi"),
    M2G("2G"),
    M3G("3G"),
    M4G("4G"),
    M5G("5G"),
}

enum class NetStatus(private val desc: String) {
    Unknown("unknow"),
    Wifi("wifi"),
    Mobile("mobile"),
    Unavailable("unavailable") // 不可用的
}