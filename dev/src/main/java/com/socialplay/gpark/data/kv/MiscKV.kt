package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.MMKVScope
import com.tencent.mmkv.MMKV

/**
 * Created by bo.li
 * Date: 2023/1/3
 * Desc:
 */
class MiscKV(override val mmkv: MMKV) : MMKVScope {

    companion object {
        private const val KEY_ROLE_FLY_WHEEL = "key_role_fly_wheel"
        private const val KEY_FEED_BACK_CACHE_TYPES = "key_feed_back_cache_types_new"
        private const val KEY_PROFILE_BANNER_CLOSE = "key_profile_banner_close"
    }

    /**
     * 获取UGC游戏gameId set
     */
    fun getRoleFlyWheelList(): List<UniJumpConfig>? = GsonUtil.gsonSafeParseCollection(mmkv.getString(KEY_ROLE_FLY_WHEEL, "") ?: "")

    /**
     * 保存UGC游戏gameId
     */
    fun replaceRoleFlyWheelList(list: List<UniJumpConfig>?) {
        mmkv.putString(KEY_ROLE_FLY_WHEEL, GsonUtil.gson.toJson(list))
    }

    /**
     * 保存意见反馈类型列表
     */
    fun replaceFeedbackTypeList(list: List<FeedbackConfigItem>?) {
        mmkv.putString(KEY_FEED_BACK_CACHE_TYPES, GsonUtil.gson.toJson(list))
    }

    /**
     * 保存意见反馈类型列表
     */
    fun getFeedbackTypeList(): List<FeedbackConfigItem>? {
        return GsonUtil.gsonSafeParseCollection(mmkv.getString(KEY_FEED_BACK_CACHE_TYPES, "") ?: "")
    }

    fun updateProfileBannerClose(records: Map<String, String>) {
        mmkv.putString(KEY_PROFILE_BANNER_CLOSE, GsonUtil.safeToJson(records))
    }

    fun getProfileBannerClose(): Map<String, String>? {
        return GsonUtil.gsonSafeParseCollection<Map<String, String>>(
            mmkv.getString(
                KEY_PROFILE_BANNER_CLOSE,
                null
            )
        )
    }
}