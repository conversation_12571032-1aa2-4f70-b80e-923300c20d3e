package com.socialplay.gpark.data.repository

import com.google.gson.reflect.TypeToken
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.model.gift.GetSendGiftUserRequestBody
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.data.model.gift.SendGiftConditionsRequestBody
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.data.model.gift.SwitchSendGiftRequestBody
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class GiftsRepository(
    val metaApi: Meta<PERSON><PERSON>,
    private val diskCache: SimpleDiskLruCache
) {
    companion object {
        const val GIFT_TYPE_FLOWER = 1
        const val GIFTS_CACHE_PREFIX = "gifts_cache_"
    }

    private fun flowerCacheKey(): String {
        return GIFTS_CACHE_PREFIX + GIFT_TYPE_FLOWER
    }

    fun getFlowerGifts(): Flow<DataResult<List<GiftFlower>>> = flow {
        // 当前群成员缓存就是最新版本, 直接返回
        val cache: List<GiftFlower>? = diskCache.get(
            flowerCacheKey(),
            object : TypeToken<List<GiftFlower>?>() {
            }.type
        )
        if (cache != null) {
            emit(DataResult.Success(cache))
        }
        val result = DataSource.getDataResultForApi {
            metaApi.getProductGifts(GIFT_TYPE_FLOWER)
        }
        if (result.succeeded && result.data?.list != null) {
            val giftFlower = result.data!!.list!!
            diskCache.put(flowerCacheKey(), giftFlower)
            emit(DataResult.Success(giftFlower))
        } else {
            emit(DataResult.Error(result.code ?: 0, result.message ?: "", result.exception))
        }
    }

    suspend fun getSendGiftConditions(gameId: String): DataResult<SendGiftConditionsInfo> {
        val result = DataSource.getDataResultForApi {
            metaApi.getSendGiftConditions(SendGiftConditionsRequestBody(gameId = gameId))
        }
        return if (result.succeeded && result.data?.giveaway != null) {
            DataResult.Success(result.data!!.giveaway!!)
        } else {
            DataResult.Error(result.code ?: 0, result.message ?: "", result.exception)
        }
    }

    suspend fun switchSendGift(gameId: String, giveaway: Boolean): DataResult<Int> {
        return DataSource.getDataResultForApi {
            metaApi.switchSendGift(SwitchSendGiftRequestBody(gameId = gameId, giveaway = giveaway))
        }
    }

    suspend fun getSendGiftUserList(
        gameId: String,
        count: Int = 5
    ): DataResult<List<SendGiftData>> {
        return DataSource.getDataResultForApi {
            metaApi.getSendGiftUserList(
                GetSendGiftUserRequestBody(
                    gameId = gameId,
                    querySize = count
                )
            )
        }
    }
}