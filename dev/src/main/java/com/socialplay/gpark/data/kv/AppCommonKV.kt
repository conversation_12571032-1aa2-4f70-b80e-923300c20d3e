package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.kv.AppCommonKV.Companion.RECENT_DAYS_LIMIT
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV
import timber.log.Timber

/**
 * <pre>
 *     <AUTHOR> hailong.dong
 *     e-mail : <EMAIL>
 *     time   : 2021/06/08
 *     desc   :
 * </pre>
 */
class AppCommonKV(override val mmkv: MMKV, val timeKV: TimeKV) : MMKVScope {
    companion object {
        /**
         * 总启动天数
         */
        private const val KEY_DAYS_LAUNCH_SUM = "key_days_launch_sum"

        /**
         *是否是新一天
         */
        private const val KEY_IS_NEW_DAY = "key_is_new_day"

        /**
         *上次启动是今年的第几天，如果和今天不一样，就认为不是同一天启动，登陆天数可以加1
         */
        private const val KEY_TODAY_YESTERDAY = "key_last_launch_day_of_year"

        /**
         * 用户离开app时候，停留在哪个底部tab上
         */
        private const val KEY_LAST_TAB_STAY = "KEY_LAST_TAB_STAY"

        /**
         * 超级推荐位是否已经展示过
         */
        private const val SUGGEST_GAME_IS_ALREADY_SHOW = "suggest_game_is_already_show"

        /**
         * 充值入口提示
         */
        private const val WALLET_ENTRANCE_TIP = "wallet_entrance_tip"

        /**
         * 充值入口提示
         */
        private const val PROFILE_WALLET_ENTRANCE_TIP = "profile_wallet_entrance_tip"

        /**
         * 视频缓存临时key
         */
        private const val KEY_VIDEO_CACHE_KEY = "key_video_cache_key"

        /**
         * 今天广告展示次数
         */
        private const val KEY_AD_PRE_DAY_SHOW_COUNT = "key_ad_pre_day_show_count"

        /**
         * 广告展示时间戳
         */
        private const val KEY_AD_SHOW_TIMESTAMP = "key_ad_show_timestamp"

        /**
         * 上一次弹app通知弹窗
         */
        private const val KEY_APP_SHOW_NOTIFICATION_RECORD = "key_app_show_notification_record"

        private const val KEY_SCREENSHOT_SHARE = "key_screenshot_share"
        private const val KEY_SCREENSHOT_SHARE_USER_IS_ACTIVE_CLOSE = "key_screenshot_share_user_is_active_close"
        private const val KEY_SCREENSHOT_SHARE_IS_SHOW_SETTING_RED_DOT = "key_screenshot_share_is_show_setting_red_dot"
        private const val KEY_SCREENSHOT_SHARE_IS_SHOW_SETTING_RED_DOT_CLICKED = "key_screenshot_share_is_show_setting_red_dot_clicked"
        private const val KEY_SCREENSHOT_SHARE_IS_SHOW_PRIVACY_SETTING_RED_DOT = "key_screenshot_share_is_show_privacy_setting_red_dot"
        private const val KEY_SCREENSHOT_SHARE_IS_SHOW_PRIVACY_SETTING_RED_DOT_CLICKED = "key_screenshot_share_is_show_privacy_setting_red_dot_clicked"
        private const val KEY_SHARE_REQUEST_ID = "key_share_request_id"
        private const val KEY_LAST_UPDATE_ID = "key_last_update_id"

        /**
         * UGC是否需要引导
         */
        private const val KEY_GUIDE_UGC_IS_NEED = "key_guide_ugc_is_need"

        /**
         * 键盘高度
         */
        private const val KEY_KEYBOARD_HEIGHT = "key_keyboard_height"

        /**
         * 键盘显示的动画时间
         */
        private const val KEY_KEYBOARD_ANIMATE_SHOW_TIME = "key_keyboard_animate_show_time"

        /**
         * 键盘隐藏的动画时间
         */
        private const val KEY_KEYBOARD_ANIMATE_HIDE_TIME = "key_keyboard_animate_hide_time"

        /**
         * 是否已经展示过引导评分内部弹框
         */
        private const val KEY_IS_SHOWED_COMMENT_GUIDE_INNER_DIALOG = "key_is_showed_comment_guide_inner_dialog"

        /**
         * 是否已经展示过商店评价弹框
         */
        private const val KEY_IS_SHOWED_STORE_COMMENT_DIALOG = "key_is_showed_store_comment_dialog"

        /**
         * 上次展示商店评价弹框的时间戳
         */
        private const val KEY_LAST_TIME_SHOW_STORE_COMMENT_DIALOG_TIME = "key_last_time_show_store_comment_dialog_time"

        /**
         * 上次展示引导评分内部弹框的时间戳
         */
        private const val KEY_LAST_TIME_SHOW_COMMENT_GUIDE_INNER_DIALOG_TIME = "key_last_time_show_comment_guide_inner_dialog_time"

        /**
         * 引导评分内部弹框点赞次数统计
         */
        private const val KEY_COMMENT_GUIDE_LIKE_COUNT = "key_comment_guide_like_count"

        /**
         * 是否点击了引导评分内部弹框的不喜欢
         */
        private const val KEY_CLICK_COMMENT_GUIDE_DISLIKE = "key_click_comment_guide_dislike"

        /**
         * onlyId为维度，最近[RECENT_DAYS_LIMIT]天内的活跃日期（一天内登录多次算一次）
         */
        private const val KEY_RECENT_USE_DAYS_DATE = "key_recent_use_days_date"

        /**
         * onlyId为维度，最近30天内的活跃日期（一天内登录多次算一次）
         */
        const val RECENT_DAYS_LIMIT = 30
    }

    var isNeedShowGuide by kvProperty(com.socialplay.gpark.BuildConfig.NEED_SHOW_GUIDE)
    var needShowNotificationPermission by kvProperty(true)
    var appUseDays by kvProperty<Int>(key = KEY_DAYS_LAUNCH_SUM)
    var isNewDay by kvProperty<Boolean>(key = KEY_IS_NEW_DAY)
    var logInDays by kvProperty<Int>(key = KEY_TODAY_YESTERDAY)
    var appOpenTimes by kvProperty<Long>()//app 启动次数
    var isShowedCommentGuideInnerDialog by kvProperty<Boolean>(key = KEY_IS_SHOWED_COMMENT_GUIDE_INNER_DIALOG, defValue = false)  // 是否已经展示过引导评论内部弹框
    var isShowedStoreCommentDialog by kvProperty<Boolean>(key = KEY_IS_SHOWED_STORE_COMMENT_DIALOG, defValue = false)  // 是否已经展示过商店评价弹框
    var isDislikeCommentGuideInnerDialog by kvProperty<Boolean>(key = KEY_CLICK_COMMENT_GUIDE_DISLIKE, defValue = false)  // 是否点击了引导评分内部弹框的不喜欢
    var lastTimeShowStoreCommentDialogTime by kvProperty<Long>(key = KEY_LAST_TIME_SHOW_STORE_COMMENT_DIALOG_TIME, defValue = 0L)  // 最后一次展示商店评分弹框的时间戳
    var lastTimeShowCommentGuideInnerDialogTime by kvProperty<Long>(key = KEY_LAST_TIME_SHOW_COMMENT_GUIDE_INNER_DIALOG_TIME, defValue = 0L)  // 最后一次展示内部引导评分弹框的时间戳
    var commentGuideLikeCount by kvProperty<Int>(key = KEY_COMMENT_GUIDE_LIKE_COUNT, defValue = 0)  // 点赞次数统计
    var lastTab by kvProperty<Int>(key = KEY_LAST_TAB_STAY)
    var hostStatus by kvProperty(0)
    var lastUpdateId by kvProperty<Long>(defValue = -1, key = KEY_LAST_UPDATE_ID)

    var isInformationCollectDialogShown by kvProperty(defValue = false)

    var superGameId by kvProperty(defValue = -1L)

    // 数据中转连续获取到值
    var dispatchClickTime by kvProperty(defValue = 0L)
    var lastHomeItemId by kvProperty<String?>(defValue = null)

    // 分享的数据中转数据处理时间
    var shareRelayDataProcessTime by kvProperty(defValue = -1L)

    var isSuggestGameAlreadyShow by kvProperty<Boolean>(key = SUGGEST_GAME_IS_ALREADY_SHOW, defValue = false)
    var isUgcGuideShow1 by kvProperty<Boolean>(defValue = false)
    var isUgcGuideShow2 by kvProperty<Boolean>(defValue = false)
    var isWalletGuidShow by kvProperty<Boolean>(key = WALLET_ENTRANCE_TIP, defValue = false)
    var isFeedbackGuidShow by kvProperty<Boolean>(defValue = false)
    var isProfileWalletGuidShow by kvProperty<Boolean>(key = PROFILE_WALLET_ENTRANCE_TIP, defValue = false)
    var tempVideoCacheKey by kvProperty("", KEY_VIDEO_CACHE_KEY)

    var adPreDayShowCount: Int
        get() {
            if (timeKV.dayOnce(KEY_AD_PRE_DAY_SHOW_COUNT)) {
                mmkv.putInt(KEY_AD_PRE_DAY_SHOW_COUNT, 0)
            }
            return mmkv.getInt(KEY_AD_PRE_DAY_SHOW_COUNT, 0)
        }
        set(value) {
            mmkv.putInt(KEY_AD_PRE_DAY_SHOW_COUNT, value)
        }

    var adShowTimestamp by kvProperty<Long>(key = KEY_AD_SHOW_TIMESTAMP, defValue = 0L)

    //app通知弹窗展示记录
    var appShowNotificationRecord by kvProperty<String>(key = KEY_APP_SHOW_NOTIFICATION_RECORD, defValue = "")

    var shareRequestId by kvProperty<String>(key = KEY_SHARE_REQUEST_ID, defValue = "")
    var guideUgcIsNeed by kvProperty<Boolean>(key = KEY_GUIDE_UGC_IS_NEED, defValue = false)
    var keyboardHeight by kvProperty<Int>(key = KEY_KEYBOARD_HEIGHT, defValue = -1)
    var keyboardAnimateShowTime by kvProperty<Long>(key = KEY_KEYBOARD_ANIMATE_SHOW_TIME, defValue = -1)
    var keyboardAnimateHideTime by kvProperty<Long>(key = KEY_KEYBOARD_ANIMATE_HIDE_TIME, defValue = -1)

    var enableScreenshotShare by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE, defValue = false)
    var userCloseScreenshotShare by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE_USER_IS_ACTIVE_CLOSE, defValue = false)
    var iShowScreenshotSettingRedHot by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE_IS_SHOW_SETTING_RED_DOT, defValue = false)
    var iShowScreenshotSettingRedHotClicked by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE_IS_SHOW_SETTING_RED_DOT_CLICKED, defValue = false)
    var iShowScreenshotPrivacyRedHot by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE_IS_SHOW_PRIVACY_SETTING_RED_DOT, defValue = false)
    var iShowScreenshotPrivacyRedHotClicked by kvProperty<Boolean>(key = KEY_SCREENSHOT_SHARE_IS_SHOW_PRIVACY_SETTING_RED_DOT_CLICKED, defValue = false)


    /**
     * onlyId为维度，最近[RECENT_DAYS_LIMIT]天内的活跃日期（时间戳）（一天内登录多次算一次）
     */
    var recentUseDaysDate: List<Long>
        get() {
            val rawData = mmkv.getString(KEY_RECENT_USE_DAYS_DATE, null)
            val dateList = GsonUtil.gsonSafeParseCollection<List<Long>>(rawData)
            Timber.d("check_blacklist recentUseDaysDate get: ${dateList}")
            return dateList ?: emptyList()
        }
        set(value) {
            val jsonData = GsonUtil.safeToJson(value)
            Timber.d("check_blacklist recentUseDaysDate set: ${value}")
            mmkv.putString(KEY_RECENT_USE_DAYS_DATE, jsonData)
        }

    /**
     * onlyId为维度，最近[RECENT_DAYS_LIMIT]天内的活跃日期（时间戳）（一天内登录多次算一次）
     */
    fun getRawUseDaysDate(): String? = mmkv.getString(KEY_RECENT_USE_DAYS_DATE, null)

    /**
     * [recentUseDaysDate] 移除记录的过早的时间
     */
    fun removeOutRangeRecentUseDays(recentList: MutableList<Long>) {
        val today = DateUtil.getToday()
        val earliestDate = DateUtil.getDayAnchorToday(RECENT_DAYS_LIMIT)
        recentList.removeAll { it !in earliestDate..today }
    }
}