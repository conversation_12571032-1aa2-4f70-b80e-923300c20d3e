package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.StartupInfo
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/08/09
 * desc   :
 * </pre>
 */


class PlayGameKV(override val mmkv: MMKV, private val metaKV: MetaKV) : MMKVScope {

    companion object {
        private const val KEY_LAST_PLAY_TIMESTAMP_PREFIX = "key_last_play_timestamp_"
        private const val KEY_THIS_ROUND_PLAY_TIME_PREFIX = "key_this_round_play_time_"
        private const val KEY_CUR_PLAY_GAME_STARTUP_INFO_PREFIX = "key_cur_play_game_startup_info_"
        private const val KEY_GAME_PROCESS_PID_PKG_PREFIX = "key_game_process_pid_pkg_prefix_"
    }

    /**
     * 统计玩游戏次数
     */
    var playGameTimes by kvProperty<Int>(0)

    /**
     * 获取：本次游戏时长
     */
    fun getThisRoundPlayTime(gameId: String): Long {
        return mmkv.getLong("$KEY_THIS_ROUND_PLAY_TIME_PREFIX$gameId", 0)
    }

    /**
     * 覆盖：本次游戏时长
     */
    fun addThisRoundPlayTime(gameId: String, addition: Long) {
        if (addition > 0) {
            mmkv.putLong("$KEY_THIS_ROUND_PLAY_TIME_PREFIX$gameId", getThisRoundPlayTime(gameId) + addition)
        }
    }

    /**
     * 覆盖：本次游戏时长
     */
    fun replaceThisRoundPlayTime(gameId: String, time: Long) {
        mmkv.putLong("$KEY_THIS_ROUND_PLAY_TIME_PREFIX$gameId", time)
    }

    /**
     * 获取上次玩游戏时间戳
     */
    fun getLastPlayTimeStamp(): Long {
        return mmkv.getLong("$KEY_LAST_PLAY_TIMESTAMP_PREFIX${metaKV.account.uuid}", 0)
    }

    /**
     * 保存上次玩游戏时间戳
     */
    private fun saveLastPlayTimeStamp(currentTime: Long, uuid: String) {
        mmkv.putLong("$KEY_LAST_PLAY_TIMESTAMP_PREFIX$uuid", System.currentTimeMillis())
    }

    fun saveCurStartupInfo(packageName: String, info: StartupInfo) {
        mmkv.putString(KEY_CUR_PLAY_GAME_STARTUP_INFO_PREFIX + packageName, GsonUtil.gson.toJson(info))
    }

    fun getCurStartupInfo(packageName: String): StartupInfo? {
        val str = mmkv.getString(KEY_CUR_PLAY_GAME_STARTUP_INFO_PREFIX + packageName, null)
        if (str.isNullOrEmpty()) return null
        return GsonUtil.gsonSafeParse(str)
    }

    fun savePidToPackageName(packageName: String?, myPid: Int) {
        if (!packageName.isNullOrEmpty()) {
            mmkv.putString("$KEY_GAME_PROCESS_PID_PKG_PREFIX$myPid", packageName)
        }
    }

    fun getPkgByPid(pid: Int): String? {
        return mmkv.getString("$KEY_GAME_PROCESS_PID_PKG_PREFIX$pid", null)
    }

}