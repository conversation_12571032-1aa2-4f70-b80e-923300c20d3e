package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import android.os.SystemClock
import com.google.gson.annotations.SerializedName
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.model.community.PostMedia
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.util.extension.dropAt
import com.socialplay.gpark.util.extension.ifNullOrBlank
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/26
 *     desc   :
 * </pre>
 */
data class OpinionRequestBody(
    val resId: String,
    val resType: Int,
    val opinion: Int
) {
    companion object {
        const val TYPE_POST = 1
        const val TYPE_COMMENT = 3
        const val TYPE_REPLY = 4

        const val OPINION_NOTHING = 0
        const val OPINION_LIKE = 1

        fun postLike(resId: String, isLike: Boolean): OpinionRequestBody {
            return OpinionRequestBody(resId, TYPE_POST, like2Opinion(isLike))
        }

        fun commentLike(resId: String, isLike: Boolean): OpinionRequestBody {
            return OpinionRequestBody(resId, TYPE_COMMENT, like2Opinion(isLike))
        }

        fun replyLike(resId: String, isLike: Boolean): OpinionRequestBody {
            return OpinionRequestBody(resId, TYPE_REPLY, like2Opinion(isLike))
        }

        fun isLike(opinion: Int): Boolean {
            return opinion == OPINION_LIKE
        }

        fun like2Opinion(isLike: Boolean): Int {
            return if (isLike) {
                OPINION_LIKE
            } else {
                OPINION_NOTHING
            }
        }
    }
}

@Parcelize
data class PostDetail(
    val postId: String,
    val nickname: String?,
    val uid: String?,
    val avatar: String?,
    val mediaList: List<PostMediaResource>?,
    val gameCardList: List<PostCardInfo>?,
    val tagList: List<PostTag>?,
    val likeCount: Long,
    val opinion: Int,
    val commentCount: Long,
    // 分享数
    val shareCount: Long,
    // 话题列表
    val communityTagList: List<TopicBean>?,
    val followStatus: Boolean,
    val content: String?,
    val createTime: Long,
    @IgnoredOnParcel
    val userStatus: FriendStatus? = null,
    val user: UserOfficialExtra?,
    val styleCardList: List<PostStyleCard>?,
    val plotCardList: List<PostMomentCard>?,
    val clothesCardList: List<PostUgcDesignCard>?,
    val labelInfo: LabelInfo? = null,
    val status: Int = STATUS_OK
) : Parcelable {

    companion object {
        const val STATUS_DELETED = 0
        const val STATUS_OK = 1
        const val STATUS_REVIEW_AUTO_IN_PROGRESS = 2
        const val STATUS_REVIEW_AUTO_FAIL = 4
        const val STATUS_REVIEW_AUTO_PASS_MAN_IN_PROGRESS = 5
        const val STATUS_REVIEW_MAN_FAIL = 6
        const val STATUS_SCHEDULED = 7

        fun convertReviewStatus2TrackParam(status: Int): Int = when {
            isOk(status) -> 0
            isInProgress(status) -> 1
            isFail(status) -> 2
            else -> -1
        }

        fun isOk(status: Int) = status == STATUS_OK
        fun isInProgress(status: Int) = status == STATUS_REVIEW_AUTO_IN_PROGRESS || status == STATUS_REVIEW_AUTO_PASS_MAN_IN_PROGRESS
        fun isFail(status: Int) = status == STATUS_REVIEW_AUTO_FAIL || status == STATUS_REVIEW_MAN_FAIL
    }

    val tagIds get() = user?.tagIds
    val imageUrls
        get() = mediaList
            ?.filter { it.isImage && it.isNetResource }
            ?.map { it.resourceValue }
            ?.toTypedArray()
    val netThumbNail
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.cover
            ?: mediaList?.firstOrNull { it.isNetResource && it.isImage }?.resourceValue
    val netVideo
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.resourceValue
    val isOfficial get() = user?.isOfficial == true
    val canGoRoom get() = userStatus != null && userStatus.toLocalStatus() == FriendStatus.PLAYING_GAME && userStatus.gameStatus?.room?.roomIdFromCp != null
    val outfit: PostStyleCard?
        get() = if (canTryOn) styleCardList?.firstOrNull { it.isOutfit } else null
    val canTryOn: Boolean
        get() = user?.ootdPrivateSwitch ?: false
    val ugcDesign: PostUgcDesignCard?
        get() = clothesCardList?.firstOrNull { it.isUgcDesign }

    val reviewOk get() = isOk(status)
    val reviewInProgress get() = isInProgress(status)
    val reviewFail get() = isFail(status)
    val reviewStatus2TrackParam get() = convertReviewStatus2TrackParam(status)

    fun toPostPublish() = PostPublish(
        content = content.orEmpty(),
        medias = mediaList,
        games = gameCardList,
        communityTagList = communityTagList,
        postId = postId,
        source = null,
        styleCardList = styleCardList,
        localExistTags = null,
        plotCardList = plotCardList,
        syncVideoFeed = null,
        clothesCardList = clothesCardList
    )
}

data class PostShareDetail(
    val postId: String,
    val content: String,
    val image: String?,
    val video: String?
)

data class PostCommentListRequestBody(
    val moduleTypeCode: Int,
    val moduleContentId: String,
    val queryType: Int,
    val pageSize: Int,
    val pageNum: Int,
    val replySize: Int,
    val replyQueryType: Int,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val withAuthorReply: Boolean = false,
    val filterType: Int? = null,
    /**
     * 查询是否带有作者赞过的信息
     */
    val isAuthorLike: Boolean = true
) {
    companion object {
        const val QUERY_TYPE_RECENT = 1
        const val QUERY_TYPE_LIKE = 3
        const val QUERY_TYPE_TOP = 5
        const val QUERY_TYPE_DEFAULT = 8

        const val FILTER_AUTHOR = 1
        const val FILTER_SELF = 2
    }
}

@Parcelize
data class PostComment(
    val commentId: String,
    val uid: String,
    val likeCount: Long,
    val content: String?,
    val commentTime: Long,
    val opinion: Int,
    val top: Boolean?,
    val nickname: String?,
    val avatar: String?,
    val replyCommonPage: ReplyData?,
    val user: UserOfficialExtra?,
    val authorReply: List<PostReply>? = null,
    val authorReplied: Boolean? = null, // ui状态, 当前评论是否被作者回复过
    val collapse: Boolean = false, // ui状态
    val replyStatus: Int = REPLY_STATUS_FIRST, // ui状态
    val loading: Boolean = false, // ui状态
    val page: Int = 0, // 回复列表页数
    val initAuthorReplyCount: Int = 0, // 初始作者回复数
    @SerializedName(value = "userLabelInfo", alternate = ["labelInfo"])
    val userLabelInfo: LabelInfo? = null,
    val showReply: List<PostReply>? = null, // 外显回复
    val mediaList: List<PostMedia>? = null, // 图片列表
    val authorLike: Boolean? = null, // 是否被作者点赞
) : Parcelable {

    companion object {
        const val REPLY_STATUS_INIT = 1 // 普通回复3条
        const val REPLY_STATUS_FIRST = 2 // 普通回复13条
        const val REPLY_STATUS_MORE = 3  // 普通回复10条
    }

    @IgnoredOnParcel
    var isNewAdd = false

    @IgnoredOnParcel
    var needLocate = false

    @IgnoredOnParcel
    var highlight = false

    @IgnoredOnParcel
    var underReview = false

    @IgnoredOnParcel
    var expandState = ExpandableTextView.STATE_SHRINK

    val isLike get() = OpinionRequestBody.isLike(opinion)

    // 加载完毕
    val isEnd
        get() = replyCommonPage == null || replyCommonPage.end == true || (replyCommonPage.dataList?.size
            ?: 0) >= replyCommonPage.total
    val hasReply get() = replyCommonPage != null && replyCommonPage.total > 0 && !replyCommonPage.dataList.isNullOrEmpty()

    // 当前回复数
    val replyCount get() = (replyCommonPage?.dataList?.size ?: 0).toLong()

    // 当前作者回复数
    val authorReplyCount get() = (authorReply?.size ?: 0).toLong()

    // 回复总数
    val replyTotalCount get() = (replyCommonPage?.total ?: 0L).coerceAtLeast(replyCount).coerceAtLeast(authorReplyCount)

    // 最后一条回复的id
    val lastReplyId get() = replyCommonPage?.dataList?.lastOrNull()?.replyId

    // 是官方号
    val isOfficial get() = user?.isOfficial == true

    // 展示加载更多/收起按钮
    val showReplyButtons get() = replyTotalCount > 0

    // 能展示回复
    val canShowReplies get() = authorReplyCount + replyCount > 0 && isMoreStatus

    // 能展示收起
    val canShowCollapse get() = !collapse && canShowReplies

    // 已收起
    val isCollapse get() = collapse && canShowReplies

    // 能加载更多
    val canShowExpand get() = !isEnd || isCollapse

    // 展示"more"
    val isMoreStatus get() = replyStatus >= REPLY_STATUS_FIRST

    val imageCount get() = mediaList?.count { it.isImage } ?: 0

    val tagIds get() = user?.tagIds

    fun restore(old: PostComment): PostComment {
        expandState = old.expandState
        underReview = old.underReview
        return this
    }

    fun switchLike(): PostComment {
        val newLikeCount: Long
        val newOpinion: Int
        if (isLike) {
            newLikeCount = (likeCount - 1).coerceAtLeast(0)
            newOpinion = OpinionRequestBody.OPINION_NOTHING
        } else {
            newLikeCount = likeCount + 1
            newOpinion = OpinionRequestBody.OPINION_LIKE
        }
        return copy(likeCount = newLikeCount, opinion = newOpinion).restore(this)
    }

    fun pin(pin: Boolean): PostComment {
        return copy(top = pin).restore(this)
    }

    fun insertReply(reply: PostReply, position: Int): PostComment {
        val newReplyList = replyCommonPage?.dataList.insertAt(position, reply) ?: listOf(reply)
        val newReplyTotal = replyTotalCount + 1
        val newReplyListWrapper = ReplyData(
            newReplyTotal,
            newReplyList,
            replyCommonPage?.end
        )
        return copy(replyCommonPage = newReplyListWrapper).restore(this)
    }

    fun updateLoadingStatus(loading: Boolean): PostComment {
        return copy(loading = loading).restore(this)
    }

    fun insertReplies(
        replies: List<PostReply>,
        position: Int,
        pagingApiResult: PagingApiResult<PostReply>
    ): PostComment {
        val newReplyList = replyCommonPage?.dataList.insertAt(position, replies) ?: replies
        val newReplyTotal = if (pagingApiResult.total != 0L) {
            pagingApiResult.total
        } else {
            replyTotalCount
        }
        newReplyList.forEach {
            it.forceShow = false
        }
        val newReplyListWrapper = ReplyData(
            newReplyTotal,
            newReplyList,
            pagingApiResult.end
        )
        return copy(
            replyCommonPage = newReplyListWrapper,
            replyStatus = if (replyStatus == REPLY_STATUS_INIT) REPLY_STATUS_FIRST else REPLY_STATUS_MORE,
            loading = false,
            page = if (replyStatus == REPLY_STATUS_INIT) 0 else page + 1,
            collapse = false
        ).restore(this)
    }

    fun deleteReply(reply: PostReply, position: Int, isAuthorReply: Boolean): PostComment {
        return if (isAuthorReply) {
            val newAuthorReplyList = authorReply.dropAt(position, reply)
            val newReplyTotal = (replyTotalCount - 1).coerceAtLeast(0)
            val newReplyListWrapper = ReplyData(
                newReplyTotal,
                replyCommonPage?.dataList,
                replyCommonPage?.end
            )
            copy(
                replyCommonPage = newReplyListWrapper,
                authorReply = newAuthorReplyList,
                collapse = collapse && newReplyTotal > 0
            )
        } else {
            val newReplyList = replyCommonPage?.dataList.dropAt(position, reply)
            val newReplyTotal = (replyTotalCount - 1).coerceAtLeast(0)
            val newReplyListWrapper = ReplyData(
                newReplyTotal,
                newReplyList,
                replyCommonPage?.end
            )
            copy(replyCommonPage = newReplyListWrapper, collapse = collapse && newReplyTotal > 0)
        }.restore(this)
    }

    fun replaceReply(reply: PostReply, position: Int, isAuthorReply: Boolean): PostComment {
        return if (isAuthorReply) {
            val newAuthorReplyList = authorReply.replaceAt(position, reply)
            copy(authorReply = newAuthorReplyList)
        } else {
            val newReplyList = replyCommonPage?.dataList.replaceAt(position, reply)
            val newReplyListWrapper = ReplyData(
                replyTotalCount,
                newReplyList,
                replyCommonPage?.end
            )
            copy(replyCommonPage = newReplyListWrapper).restore(this)
        }.restore(this)
    }

    fun collapse(collapse: Boolean): PostComment {
        replyCommonPage?.dataList?.forEach {
            it.forceShow = false
        }
        return copy(collapse = collapse).restore(this)
    }

    @Parcelize
    data class ReplyData(
        val total: Long,
        val dataList: List<PostReply>?,
        val end: Boolean?
    ) : Parcelable
}

data class PostCommentRequestBody(
    val content: String,
    val moduleTypeCode: Int,
    val moduleContentId: String,
    val score: Int? = null,
    val mediaList: List<PostMedia>? = null
) {

    fun toPostComment(commentId: String, userInfo: MetaUserInfo?, ts: Long): PostComment {
        return PostComment(
            commentId,
            userInfo?.uuid.orEmpty(),
            0,
            content,
            ts,
            OpinionRequestBody.OPINION_NOTHING,
            false,
            userInfo?.nickname,
            userInfo?.portrait,
            null,
            UserOfficialExtra(
                userInfo?.isOfficial() == true,
                userInfo?.tags,
                labelInfo = userInfo?.labelInfo
            ),
            userLabelInfo = userInfo?.labelInfo,
            mediaList = mediaList
        )
    }
}

data class PostReplyListRequestBody(
    val pageSize: Int,
    val commentId: String,
    val queryType: Int,
    val pageNum: Int? = null, // 分页查询
    val lastReplyId: String? = null // 滚动查询
) {
    companion object {
        const val QUERY_LATEST = 1
        const val QUERY_OLDEST = 2
        const val QUERY_DEPTH = 6
    }
}

@Parcelize
data class PostReply(
    val replyId: String,
    val uid: String,
    val nickname: String?,
    val avatar: String?,
    val replyContentId: String?,
    val replyName: String?,
    val replyUid: String?,
    val replyTime: Long,
    val content: String?,
    val opinion: Int,
    val likeCount: Long,
    val commentId: String,
    val user: UserOfficialExtra?,
    val mediaList: List<PostMedia>?,
    val labelInfo: LabelInfo? = null,
) : Parcelable {

    @IgnoredOnParcel
    var expandState = ExpandableTextView.STATE_SHRINK
    @IgnoredOnParcel
    var forceShow: Boolean = false
    @IgnoredOnParcel
    var highlight = false

    @IgnoredOnParcel
    var needLocate = false

    @IgnoredOnParcel
    var underReview = false

    val isLike get() = OpinionRequestBody.isLike(opinion)
    val isOfficial get() = user?.isOfficialV2 == true

    val imageCount get() = mediaList?.count { it.isImage } ?: 0

    val tagIds get() = user?.tagIds

    fun restore(old: PostReply): PostReply {
        expandState = old.expandState
        forceShow = old.forceShow
        return this
    }

    fun switchLike(): PostReply {
        val newLikeCount: Long
        val newOpinion: Int
        if (isLike) {
            newLikeCount = (likeCount - 1).coerceAtLeast(0)
            newOpinion = OpinionRequestBody.OPINION_NOTHING
        } else {
            newLikeCount = likeCount + 1
            newOpinion = OpinionRequestBody.OPINION_LIKE
        }
        return copy(likeCount = newLikeCount, opinion = newOpinion).restore(this)
    }
}

data class PostReplyRequestBody(
    val content: String,
    val uid: String,
    val commentId: String,
    val replyUid: String? = null,
    val replyNickname: String? = null,
    val replyContentId: String? = null,
    val mediaList: List<PostMedia>? = null
) {
    constructor(
        content: String,
        uid: String,
        commentId: String
    ) : this(
        content,
        uid,
        commentId,
        null,
        null,
        null
    )

    fun toPostReply(replyId: String, userInfo: MetaUserInfo?, ts: Long): PostReply {
        return PostReply(
            replyId,
            uid,
            userInfo?.nickname,
            userInfo?.portrait,
            replyContentId,
            replyNickname,
            replyUid,
            ts,
            content,
            OpinionRequestBody.OPINION_NOTHING,
            0,
            commentId,
            UserOfficialExtra(
                userInfo?.isOfficial() == true,
                userInfo?.tags,
                labelInfo = userInfo?.labelInfo
            ),
            mediaList,
            labelInfo = userInfo?.labelInfo
        )
    }
}

data class AddPostCommentReplyTarget(
    val data: Any?,
    val commentId: String?,
    val commonPosition: Int,
    val type: Int,
    val toNickname: String?,
    val mark: Long = SystemClock.elapsedRealtime()
) {
    constructor(contentId: String, toNickname: String?) : this(contentId, null, -1, TYPE_POST, toNickname)
    constructor(comment: PostComment, commonPosition: Int) : this(
        comment,
        null,
        commonPosition,
        TYPE_COMMENT,
        comment.nickname
    )

    constructor(reply: PostReply, commentId: String, commonPosition: Int) : this(
        reply,
        commentId,
        commonPosition,
        TYPE_REPLY,
        reply.nickname
    )

    companion object {
        const val TYPE_POST = 1
        const val TYPE_COMMENT = 2
        const val TYPE_REPLY = 3
    }

    val isTargetPost get() = type == TYPE_POST
    val isTargetComment get() = type == TYPE_COMMENT
    val isTargetReply get() = type == TYPE_REPLY
    val asComment get() = data as PostComment
    val asReply get() = data as PostReply
    val asPost get() = data as String
    val targetId get() = when {
        isTargetComment -> {
            asComment.commentId
        }

        isTargetReply -> {
            asReply.replyId
        }

        isTargetPost -> {
            asPost
        }

        else -> {
            null
        }
    }
}

data class AddPostCommentReplyResult(
    val mark: Long,
    val ts: Long = System.currentTimeMillis(),
    var result: Boolean = true
) {
    fun fail(): AddPostCommentReplyResult {
        result = false
        return this
    }
}