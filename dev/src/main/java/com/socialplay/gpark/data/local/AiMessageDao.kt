package com.socialplay.gpark.data.local

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import com.socialplay.gpark.data.model.entity.AIConversationEntity
import com.socialplay.gpark.data.model.entity.AIMessageEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/06/26
 *     desc   :
 *
 */
@Dao
interface AiMessageDao {
    @Insert
    suspend fun insertMessage(aiMessageEntity: AIMessageEntity): Long?

    @Insert
    suspend fun insertMessageList(list: List<AIMessageEntity>)

    @Update
    suspend fun updateMessage(aiMessageEntity: AIMessageEntity)

    @Delete(entity = AIMessageEntity::class)
    suspend fun deleteMessage(aiMessageEntity: AIMessageEntity)

    @Query("SELECT * FROM ai_message WHERE (senderUserID = :targetId  and receiverUserID = :userId)  OR  (senderUserID = :userId  and receiverUserID = :targetId)   ORDER BY messageId DESC LIMIT :index, :size")
    suspend fun getHistoryMessageList(
        targetId: String,
        userId: String,
        index: Int,
        size: Int
    ): List<AIMessageEntity>
    @Query("SELECT * FROM ai_message WHERE (senderUserID = :targetId  and receiverUserID = :userId)  OR  (senderUserID = :userId  and receiverUserID = :targetId)   ORDER BY messageId DESC LIMIT :size")
    suspend fun getNewHistoryMessageList(
        targetId: String,
        userId: String,
        size: Int
    ): List<AIMessageEntity>

    @Query("DELETE FROM ai_message WHERE (senderUserID = :targetId  and receiverUserID = :userId)  OR  (senderUserID = :userId  and receiverUserID = :targetId)")
    suspend fun cleanAllMessage(targetId: String, userId: String)

    @Query("SELECT COUNT(messageId) FROM ai_message WHERE messageId = :messageId")
    suspend fun idCount(messageId: Long): Int

    @Query("DELETE FROM ai_message WHERE messageId = :messageId  ")
    suspend fun deleteMessageById(messageId: Long)
}

suspend fun AiMessageDao.exists(messageId: Long): Boolean {
    return idCount(messageId) > 0
}

class AIMessageDaoDelegate(private val aiMessageDao: AiMessageDao) : AiMessageDao by aiMessageDao {
    override suspend fun deleteMessage(aiMessageEntity: AIMessageEntity) {
        try {
            aiMessageDao.deleteMessage(aiMessageEntity)
        } catch (e: Exception) {

        }

    }

    override suspend fun deleteMessageById(messageId: Long) {
        try {
            aiMessageDao.deleteMessageById(messageId)
        } catch (e: Exception) {

        }
    }

    override suspend fun cleanAllMessage(targetId: String, userId: String){
        try {
            aiMessageDao.cleanAllMessage(targetId, userId)
        } catch (e: Exception) {

        }
    }

    override suspend fun insertMessage(aiMessageEntity: AIMessageEntity): Long?{
     return   try {
            aiMessageDao.insertMessage(aiMessageEntity)
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun insertMessageList(list: List<AIMessageEntity>) {
        try {
            aiMessageDao.insertMessageList(list)
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun updateMessage(aiMessageEntity: AIMessageEntity){
        try {
            aiMessageDao.updateMessage(aiMessageEntity)
        } catch (e: Exception) {
            null
        }
    }

    override suspend  fun getHistoryMessageList(
        targetId: String,
        userId: String,
        index: Int,
        size: Int
    ): List<AIMessageEntity>{
        return try {
            aiMessageDao.getHistoryMessageList(targetId, userId, index, size)
        }catch (e :Exception){
            Timber.e(e)
            emptyList()
        }
    }

    override suspend fun getNewHistoryMessageList(
        targetId: String,
        userId: String,
        size: Int
    ): List<AIMessageEntity> {
        return try {
            aiMessageDao.getNewHistoryMessageList(targetId, userId, size)
        } catch (e: Exception) {
            Timber.e(e)
            emptyList()
        }

    }

}