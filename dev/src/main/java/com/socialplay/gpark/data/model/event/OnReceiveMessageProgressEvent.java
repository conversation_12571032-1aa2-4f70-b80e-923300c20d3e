package com.socialplay.gpark.data.model.event;


import com.ly123.tes.mgs.metacloud.model.Message;

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/06/28
 * desc   : 上传图片进度
 */
public class OnReceiveMessageProgressEvent {
    Message message;
    int     progress;

    public OnReceiveMessageProgressEvent() {
    }

    public int getProgress() {
        return this.progress;
    }

    public Message getMessage() {
        return this.message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }
}