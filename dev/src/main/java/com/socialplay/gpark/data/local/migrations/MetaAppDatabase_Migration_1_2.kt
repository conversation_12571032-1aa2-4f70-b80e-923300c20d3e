package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Created by bo.li
 * Date: 2023/9/14
 * Desc:
 */
internal class MetaAppDatabase_Migration_1_2: Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `recent_ugc_game` (`id` TEXT PRIMARY KEY NOT NULL, `packageName` TEXT NOT NULL, `gameName` TEXT, `gameCode` TEXT NOT NULL, `gameIcon` TEXT, `username` TEXT, `userAvatar` TEXT, `likeCount` INTEGER NOT NULL, `likeIt` INTEGER NOT NULL, `popularity` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, `releaseTime` INTEGER NOT NULL, `visitTime` INTEGER NOT NULL)")
        } catch (ignore: Throwable) {
        }
    }
}