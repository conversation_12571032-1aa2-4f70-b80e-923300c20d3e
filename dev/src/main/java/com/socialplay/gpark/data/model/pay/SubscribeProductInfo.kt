package com.socialplay.gpark.data.model.pay

data class SubscribeProductInfo(
    /**
     * 我们商品id
     */
    val id: String?,
    /**
     * 会员订阅的时候, 才会用这个字段
     */
    val parentProductId: String?,
    /**
     * google的商品id
     */
    val productId: String,
    /**
     * 是否在充值页面显示此充值项
     */
    val show: Boolean?,
    val extendInfo: ExtendInfo?,
    val memberInfo: MemberInfo?,
    val coinInfo: CoinInfo?,
)

data class ExtendInfo(
    val rewardCoinNum: String? = "",
    val rewardLecoinRatio: String? = ""
)

/**
 * 会员扩展
 */
data class MemberInfo(
    // 赠送币数
    val rewardCoinNum: String? = "",
    // 赠送币比例
    val rewardLecoinRatio: String? = "",
)

/**
 * 币商品扩展
 */
data class CoinInfo(
    // 基本币数量
    val baseLecoinNum: String? = "",
    // 赠送币数
    val awardLecoinNum: String? = "",
)
/*
{
  "code": 200,
  "message": "OK",
  "data": [
    {
      "productId": "online001-bp01",
      "parentProductId": "online_gpsub_001",
      "subscribeInfo": {
        "status": 4,
        "endTime": 1741037476000,
        "platformType": 2
      },
      "extendInfo": {
        "rewardCoinNum": 1700,
        "rewardLecoinRatio": "10",
        "period": "3",
        "goodsIcon": "https://qn-basic-content.gpark.io/online/MYAjkM6B1n4V1716781276720.png"
      },
      "memberInfo": {
        "rewardCoinNum": 1700,
        "rewardLecoinRatio": "10",
        "timeType": "3",
        "goodsIcon": "https://qn-basic-content.gpark.io/online/MYAjkM6B1n4V1716781276720.png"
      }
    }
  ]
}
*/