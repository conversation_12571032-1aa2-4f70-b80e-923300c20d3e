package com.socialplay.gpark.data.interactor

import android.content.Context
import android.os.SystemClock
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.post.CommonPostPublishReceiveEvent
import com.socialplay.gpark.data.model.post.CommonPostPublishSendEvent
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.post.PublishTrackData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.dropAt
import com.socialplay.gpark.util.extension.replaceAt
import com.socialplay.gpark.util.toJSON
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/14
 *     desc   :
 * </pre>
 */
class PublishPostInteractor(
    private val context: Context,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    private val uploadFileInteractor: UploadFileInteractor
) {

    companion object {
        const val METHOD_PUBLISH_POST = "publishPost"
        const val METHOD_GET_CURRENT_POST = "getCurrentPost"
        const val METHOD_GET_CURRENT_PUBLISH_STATUS = "getCurrentPublishStatus"
        const val METHOD_CAN_PUBLISH = "canPublish"
        const val METHOD_IS_CHECKING = "isChecking"
        const val METHOD_IS_UPLOADING = "isUploading"
        const val METHOD_IS_PUBLISHING = "isPublishing"
        const val METHOD_IS_SUCCEEDED = "isSucceeded"
        const val METHOD_IS_FAILED = "isFailed"
    }

    private val scope = CoroutineScope(Dispatchers.IO)
    private val mainScope = MainScope()

    private val _publishPostFlow = MutableStateFlow(PostPublishStatus())

    val publishPostFlow by lazy { _publishPostFlow.asStateFlow() }
    val publishPostSharedFlow by lazy { _publishPostFlow.shareIn(mainScope, SharingStarted.Lazily, replay = 0) }
    val publishPostLiveData by lazy { _publishPostFlow.asLiveData() }
    private val _publishPostSingleLiveData = SingleLiveData<PostPublishStatus>()
    val publishPostSingleLiveData: LiveData<PostPublishStatus> = _publishPostSingleLiveData


    var isMainProcess = false
        private set
    var processName = ""
        private set

    fun init(isMainProcess: Boolean, processName: String) {
        this.isMainProcess = isMainProcess
        this.processName = processName
        CpEventBus.register(this)
        if (isMainProcess) {
            _publishPostFlow.collectIn(scope) {
                _publishPostSingleLiveData.postValue(it)
                it.printDebugLog()
            }
        }
    }

    /**
     * 发布帖子
     */
    fun publishPost(
        data: PostPublish?,
        resIdBean: ResIdBean,
        event: CommonPostPublishSendEvent? = null,
        checkMute: Boolean = true
    ) = scope.launch {
        val startTime = SystemClock.elapsedRealtime()
        // 帖子不可为null
        if (data == null) {
            event?.sendBack(data = null, code = 400, errMsg = context.getString(R.string.invalid_post_content))
            return@launch
        }
        // 当前有其他帖子正在发布
        if (!canPublish()) {
            event?.sendBack(data = data, code = 412, errMsg = context.getString(R.string.post_publishing_tip))
            return@launch
        }
        _publishPostFlow.value = _publishPostFlow.value.check(data)
        delay(200)
        // 检查强制前置条件
        val (check, msg) = PostPublish.check(data)
        if (!check) {
            val errMsg = msg ?: context.getString(R.string.invalid_post_content)
            event?.sendBack(data = data, code = 400, errMsg = errMsg)
            _publishPostFlow.value = _publishPostFlow.value.fail(errMsg)
            return@launch
        }
        // 检查用户信息
        val uid = accountInteractor.curUuidOrNull
        if (uid.isNullOrBlank()) {
            event?.sendBack(data = data, code = 401, errMsg = context.getString(R.string.login_before_further_operations))
            _publishPostFlow.value = _publishPostFlow.value.fail(context.getString(R.string.login_before_further_operations))
            return@launch
        }
        // 检查禁言状态
        if (checkMute) {
            val isMuted = kotlin.runCatching { repo.queryUserMuteStatus(uid).invoke().isMuted }
                .getOrNull()
            if (isMuted == true) {
                event?.sendBack(data = data, code = 403, errMsg = context.getString(R.string.banned_from_posting))
                _publishPostFlow.value = _publishPostFlow.value.fail(context.getString(R.string.banned_from_posting))
                return@launch
            }
        }
        // 过滤数据
        val validData = data.validateByApi()
        // 待上传文件
        var toUploadSize = 0L
        val toUploadList = buildList {
            validData.medias?.forEachIndexed { index, media ->
                if (!media.isNetResource) { // 多媒体文件需要上传
                    if (!media.localExist) { // 本地多媒体文件必须存在且可以访问
                        event?.sendBack(data = data, code = 400, errMsg = context.getString(R.string.invalid_post_content))
                        _publishPostFlow.value = _publishPostFlow.value.fail(
                            context.getString(R.string.invalid_post_content),
                            post = validData.copy(medias = validData.medias.dropAt(index, media))
                        )
                        return@launch
                    }
                    val file = File(media.localPath!!)
                    toUploadSize += file.length()
                    add(index to file)
                }
            }
        }
        if (toUploadList.isNotEmpty()) {
            _publishPostFlow.value = _publishPostFlow.value.upload(0, toUploadList.size, 0.0)
            var tempMedias = validData.medias
            uploadFileInteractor.uploadList1By1(
                UploadFileInteractor.BIZ_CODE_COMMUNITY,
                toUploadList.map { it.second },
                progressHandler = { _, curCount, totalCount, _, percent ->
                    _publishPostFlow.value = _publishPostFlow.value.upload(
                        curCount,
                        totalCount,
                        percent
                    )
                }
            ).collect {
                val uploadData = it.data
                if (it.succeeded && uploadData != null) { // 上传成功，替换字段
                    val curIdx = toUploadList[uploadData.curCount - 1].first
                    tempMedias?.get(curIdx)?.let { oldMedia ->
                        val newMedia = oldMedia.copy(
                            resourceValue = uploadData.url,
                            cover = if (oldMedia.isVideo) uploadData.getVideoFirstFrame() else oldMedia.cover
                        )
                        tempMedias = tempMedias.replaceAt(curIdx, newMedia)
                    }
                    if (uploadData.curCount == uploadData.totalCount) {
                        publishPostHelper(
                            validData.copy(medias = tempMedias),
                            resIdBean,
                            event,
                            PublishTrackData(startTime, uploadData.totalCount, toUploadSize)
                        )
                    }
                } else { // 上传失败
                    val errorMsg = it.message ?: context.getString(R.string.failed_to_upload)
                    val newData = validData.copy(medias = tempMedias)
                    event?.sendBack(data = newData, code = 417, errMsg = errorMsg)
                    _publishPostFlow.value = _publishPostFlow.value.fail(errorMsg, newData)
                }
            }
        } else {
            publishPostHelper(validData, resIdBean, event, PublishTrackData(startTime, 0, 0))
        }
    }

    private suspend fun publishPostHelper(
        data: PostPublish,
        resIdBean: ResIdBean,
        event: CommonPostPublishSendEvent?,
        trackData: PublishTrackData
    ) {
        val circleId = if (data.source == PostPublish.SOURCE_VIDEO) {
            repo.getVideoPublishCircleId().singleOrNull().orEmpty()
        } else {
            repo.getGlobalCircleId().orEmpty()
        }

        val body = data.toPublishPostRequest(circleId)
        _publishPostFlow.value = _publishPostFlow.value.publish(data)
        val result = if (body.postId.isNullOrEmpty()) {
            repo.savePostV3(body)
        } else {
            repo.editPost(body)
        }
        val postId = result.data?.postId ?: body.postId
        if (result.succeeded && !postId.isNullOrBlank()) {

            val commonParams = ResIdUtils.getAnalyticsMap(resIdBean)

            val gameIdsStr = data.games?.let { it.map { it.gameId }.joinToString(",") } ?: ""

            val communityTagList = data.communityTagList?.let { it.map { it.tagName }} ?: emptyList()
            val tagsStr = communityTagList.joinToString(",")

            val params = mapOf(
                "postId" to postId,
                "game_card_list" to gameIdsStr,
                "tag_list" to tagsStr
            )

            Analytics.track(
                EventConstants.ADD_POST_PUBLISH_SUCCESS,
                commonParams + params
            )

            val timeCost = SystemClock.elapsedRealtime() - trackData.startMills
            Analytics.track(
                EventConstants.POST_SUCCESS_TIME_CONSUMING,
                "postid" to postId,
                "time_cost" to timeCost,
                "file_count" to trackData.uploadCount,
                "file_size" to trackData.uploadSize,
            )

            val newData = data.copy(
                postId = postId,
                localExistTags = result.data?.tagList,
                plotCardList = result.data?.plotCardList ?: data.plotCardList
            )
            event?.sendBack(data = newData.toJSON())
            _publishPostFlow.value = _publishPostFlow.value.succeed(newData)
        } else {
            val errorMsg = result.message ?: context.getString(R.string.unknown_error)
            event?.sendBack(data = data, code = 417, errMsg = errorMsg)
            _publishPostFlow.value = _publishPostFlow.value.fail(errorMsg)
        }
    }

    /**
     * 获取当前帖子
     */
    fun getCurrentPost(): PostPublish? {
        return _publishPostFlow.value.post
    }

    /**
     * 获取当前发布状态
     */
    fun getCurrentPublishStatus(): PostPublishStatus {
        return _publishPostFlow.value
    }

    /**
     * 是否能发帖
     * 非[上传文件中|发布中]可发帖
     */
    fun canPublish(): Boolean {
        return _publishPostFlow.value.status !in PostPublishStatus.STATUS_CHECKING..PostPublishStatus.STATUS_PUBLISHING
    }

    /**
     * 是否检查中
     */
    fun isChecking(): Boolean {
        return _publishPostFlow.value.status == PostPublishStatus.STATUS_CHECKING
    }

    /**
     * 是否上传文件中
     */
    fun isUploading(): Boolean {
        return _publishPostFlow.value.status == PostPublishStatus.STATUS_UPLOADING
    }

    /**
     * 是否发布中
     */
    fun isPublishing(): Boolean {
        return _publishPostFlow.value.status == PostPublishStatus.STATUS_PUBLISHING
    }

    /**
     * 是否发布成功
     */
    fun isSucceeded(): Boolean {
        return _publishPostFlow.value.status == PostPublishStatus.STATUS_SUCCEEDED
    }

    /**
     * 是否发布失败
     */
    fun isFailed(): Boolean {
        return _publishPostFlow.value.status == PostPublishStatus.STATUS_FAILED
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSendEvent(event: CommonPostPublishSendEvent) {
        if (!isMainProcess) return
        val data: Any? = when (event.method) {
            METHOD_PUBLISH_POST -> {
                publishPost(
                    GsonUtil.gsonSafeParse(event.data as? String),
                    ResIdBean.newInstance(),
                    event
                )
                return
            }

            METHOD_GET_CURRENT_POST -> getCurrentPost()
            METHOD_GET_CURRENT_PUBLISH_STATUS -> getCurrentPublishStatus()
            METHOD_CAN_PUBLISH -> canPublish()
            METHOD_IS_CHECKING -> isChecking()
            METHOD_IS_UPLOADING -> isUploading()
            METHOD_IS_PUBLISHING -> isPublishing()
            METHOD_IS_SUCCEEDED -> isSucceeded()
            METHOD_IS_FAILED -> isFailed()
            else -> {
                event.sendBack(data = event.data, code = 404, errMsg = context.getString(R.string.not_support_cap))
                return
            }
        }
        event.sendBack(data = data)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveEvent(event: CommonPostPublishReceiveEvent) {
        if (event.processName != processName) return
        GameCommonFeatureResolver.relayPublishPostEventResult(event)
    }

}