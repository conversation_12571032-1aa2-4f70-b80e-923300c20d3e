package com.socialplay.gpark.data.model.event

/**
 * @des:
 * @author: li<PERSON><PERSON><PERSON>
 * @date: 2021/12/31 15:28
 */
class ScreenRecordUserActionEvent(val action: Int) {
    var showEndDialog: Boolean = true

    companion object {
        const val ACTION_START_RECORD = 1
        const val ACTION_STOP_RECORD = 2
        const val ACTION_OPEN_AUDIO = 3
        const val ACTION_CLOSE_AUDIO = 4
    }
}