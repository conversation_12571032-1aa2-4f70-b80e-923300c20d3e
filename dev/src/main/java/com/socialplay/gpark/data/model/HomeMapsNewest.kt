package com.socialplay.gpark.data.model

data class HomeMapsNewest(

    var list: List<MapsNewestList>? = null,
    var isEnd: Boolean = false,
)

data class MapsNewestList(
    var id: String,
    var ugid: String,
    var ugcGameName: String? = null,
    var packageName: String? = null,
    var banner: String? = null,
    var releaseTime: Long?,
    var updateTime: Long?,
    var showTimeType: Int = 1,   // 1发布时间 0修改时间
    var orderNum: Long = 0,  // 排序号，下一页的时候给传上次最后一个Item的排序号
    var isStrongInsertionGame: Boolean? = false,  // 是否是自己发布的强插游戏
)

