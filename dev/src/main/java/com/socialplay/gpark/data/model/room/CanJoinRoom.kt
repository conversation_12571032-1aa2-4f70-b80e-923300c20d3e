package com.socialplay.gpark.data.model.room

import android.content.Context
import com.socialplay.gpark.R

/**
 * create by: bin on 2023/7/17
 */
data class Can<PERSON>oin<PERSON>oom(val roomStatus: Int) {

    //版本不匹配
    fun versionNotMatch() = roomStatus == 3

    fun getMessage(context: Context): String? {
        return when (roomStatus) {
            0    -> {
                null
            }

            1    -> {
                context.getString(R.string.join_room_error1)
            }

            2    -> {
                context.getString(R.string.join_room_error2)
            }

            3    -> {
                context.getString(R.string.join_room_error3)
            }

            4    -> {
                context.getString(R.string.join_room_error4)
            }

            5    -> {
                context.getString(R.string.join_room_error5)
            }

            6    -> {
                context.getString(R.string.join_room_error6)
            }

            7    -> {
                context.getString(R.string.join_room_error7)
            }

            else -> {
                context.getString(R.string.join_room_error8)
            }
        }
    }

}
