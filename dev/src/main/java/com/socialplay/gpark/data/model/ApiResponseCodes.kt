package com.socialplay.gpark.data.model

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/09/16
 *     desc   : 服务端的返回码
 */

// 接口header：token
const val API_HEADER_TOKEN = "token"

// refresh token无效, 重新登录
const val RESPONSE_CODE_REFRESH_TOKEN_INVALID = 402

// 封禁状态
const val RESPONSE_CODE_ACCOUNT_BANNED = ********
// 禁言状态
const val RESPONSE_CODE_ACCOUNT_MUTE = 321509
// 警告状态
const val RESPONSE_CODE_ACCOUNT_WARNING = ********

// accessToken无效, 获取新的accessToken
const val RESPONSE_CODE_ACCESS_TOKEN_INVALID = 401

// 命中下面的code，不重试刷新token
 val notRetryTokenList = listOf(RESPONSE_CODE_REFRESH_TOKEN_INVALID, RESPONSE_CODE_ACCOUNT_BANNED, RESPONSE_CODE_ACCOUNT_WARNING)

//请求成功
const val RESPONSE_CODE_SUCCESS = 200