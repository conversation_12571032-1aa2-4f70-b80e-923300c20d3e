package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

/**
 * Created by yaqi.liu on 2021/5/13
 */
class DeviceKV(private val mmKV: MMKV) {
    companion object {
        private const val DEVICE_IMEI = "meta_device_imei"
        private const val DEVICE_OAID = "key_android_oaid"
        private const val DEVICE_SMID = "key_sm_id"
        private const val DEVICE_TDID = "key_td_id"
        private const val DEVICE_ANDROIDID = "meta_device_android_id"
        private const val DEVICE_UNIQUE_PSUEDO_ID = "key_unique_psuedo_id"
        private const val DEVICE_CHANNEL_KEY = "channel"
        private const val DEVICE_SUB_CHANNEL_KEY = "sub_channel"
        private const val DEVICE_META_TRACKING_KEY = "meta_tracking_flag"
        private const val DEVICE_PLAY_INSTALL_REFERER_KEY = "play_install_referer_key"

    }
    var supProcessTDid: String? //只为了给非主进程使用
        set(value) {
            mmKV.putString(DEVICE_TDID, value)
        }
        get() {
            return mmKV.getString(DEVICE_TDID, "")
        }
    var imei: String?
        set(value) {
            mmKV.putString(DEVICE_IMEI, value)
        }
        get() {
            return mmKV.getString(DEVICE_IMEI, "")
        }

    var oaid: String?
        set(value) {
            mmKV.putString(DEVICE_OAID, value)
        }
        get() {
            return mmKV.getString(DEVICE_OAID, "")
        }

    var smid: String?
        set(value) {
            mmKV.putString(DEVICE_SMID, value)
        }
        get() {
            return mmKV.getString(DEVICE_SMID, "")
        }

    var android: String?
        set(value) {
            mmKV.putString(DEVICE_ANDROIDID, value)
        }
        get() {
            return mmKV.getString(DEVICE_ANDROIDID, "")
        }

    var unipsuedoid: String?
        set(value) {
            mmKV.putString(DEVICE_UNIQUE_PSUEDO_ID, value)
        }
        get() {
            return mmKV.getString(DEVICE_UNIQUE_PSUEDO_ID, "")
        }

    var channelId: String?
        set(value) {
            mmKV.putString(DEVICE_CHANNEL_KEY, value)
        }
        get() {
            return mmKV.getString(DEVICE_CHANNEL_KEY, "")
        }

    var subChannelId: String?
        set(value) {
            mmKV.putString(DEVICE_SUB_CHANNEL_KEY, value)
        }
        get() {
            return mmKV.getString(DEVICE_SUB_CHANNEL_KEY, "")
        }

    var metaTracking: String?
        set(value) {
            mmKV.putString(DEVICE_META_TRACKING_KEY, value)
        }
        get() {
            return mmKV.getString(DEVICE_META_TRACKING_KEY, "")
        }

    var playInstallReferer: String?
        set(value) {
            mmKV.putString(DEVICE_PLAY_INSTALL_REFERER_KEY, value)
        }
        get() {
            return mmKV.getString(DEVICE_PLAY_INSTALL_REFERER_KEY, null)
        }

}