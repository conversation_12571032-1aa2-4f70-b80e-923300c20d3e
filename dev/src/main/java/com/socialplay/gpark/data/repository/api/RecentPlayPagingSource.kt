package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.profile.recent.GameEntity
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Request
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * created by liyanfeng on 2022/7/27 3:59 下午
 * @describe:
 */
class RecentPlayPagingSource(val uuid: String, val metaApi: MetaApi, val pageSize: Int) : PagingSource<Int, GameEntity>() {

    private val accountInteractor = GlobalContext.get().get<AccountInteractor>()

    override fun getRefreshKey(state: PagingState<Int, GameEntity>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, GameEntity> {

        val cur = params.key ?: 1
        val prevKey = null

     /*   if (accountInteractor.accountLiveData.value?.uuid == uuid && !accountInteractor.isRealLogin()) {
            return LoadResult.Page(emptyList(), prevKey, null)
        }*/

        return try {
            //val data = DataSource.getDataResultForApi { metaApi.getRecentPlayGameList(cur, pageSize, uuid) }
            val data = metaApi.getRecentPlayGameList(cur, pageSize, uuid)
            val items = data.data?.dataList ?: emptyList()
            val isEnd = data.data?.end == true || items.isNullOrEmpty()
            val nextKey = if (isEnd) null else cur + 1
            Timber.d("RecentPlayPagingSource：cur:$cur, prevKey:$prevKey, nextKey:$nextKey, params:$params\n$items")
            if (data != null && data.code == 200) {
                LoadResult.Page(items, prevKey, nextKey)
            } else {
                LoadResult.Error(NullPointerException())
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}
class RecentPlayPagingSourceV2(val uuid: String, val metaApi: MetaApi, val pageSize: Int) : PagingSource<Int, RecentPlayListV2Response.Game>() {

    private val accountInteractor = GlobalContext.get().get<AccountInteractor>()

    override fun getRefreshKey(state: PagingState<Int, RecentPlayListV2Response.Game>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, RecentPlayListV2Response.Game> {

        val cur = params.key ?: 1
        val prevKey = null

        /*   if (accountInteractor.accountLiveData.value?.uuid == uuid && !accountInteractor.isRealLogin()) {
               return LoadResult.Page(emptyList(), prevKey, null)
           }*/

        return try {
            //val data = DataSource.getDataResultForApi { metaApi.getRecentPlayGameList(cur, pageSize, uuid) }
            val data = metaApi.getRecentPlayGameListV2(RecentPlayListV2Request(cur, pageSize, uuid, false))
            val items = data.data?.validList.orEmpty()
            val isEnd = data.data?.end == true || items.isNullOrEmpty()
            val nextKey = if (isEnd) null else cur + 1
            Timber.d("RecentPlayPagingSource：cur:$cur, prevKey:$prevKey, nextKey:$nextKey, params:$params\n$items")
            if (data != null && data.code == 200) {
                LoadResult.Page(items, prevKey, nextKey)
            } else {
                LoadResult.Error(NullPointerException())
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}