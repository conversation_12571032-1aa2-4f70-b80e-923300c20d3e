package com.socialplay.gpark.data.repository

import android.app.Application
import com.meta.biz.mgs.MgsBiz
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.ShareRecordDao
import com.socialplay.gpark.data.model.LoginFromType
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.PayOrderInfo
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.account.DeviceParamsRequest
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.account.UserCreateInfo
import com.socialplay.gpark.data.model.entity.ShareRecordEntity
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.notRetryTokenList
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.profile.request.RelationListRequest
import com.socialplay.gpark.data.model.user.AccessTokenRefreshResult
import com.socialplay.gpark.data.model.user.AccountLoginRequest
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.LOGIN_REQUEST_TYPE_ONLY_LOGIN
import com.socialplay.gpark.data.model.user.LOGIN_REQUEST_TYPE_ONLY_REGISTER
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.ThirdBindRequest
import com.socialplay.gpark.data.model.user.ThirdLoginRequest
import com.socialplay.gpark.data.model.user.VisitorInfoApiResult
import com.socialplay.gpark.data.model.user.request.GetAccessTokenRequestBody
import com.socialplay.gpark.di.CommonParamsProvider
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import com.socialplay.gpark.function.overseabridge.bridge.IUpdateBride
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.Md5Util
import com.socialplay.gpark.util.RsaUtil
import com.socialplay.gpark.util.extension.replaceChinese
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-15 11:44 上午
 * @desc:
 */
open class UserRepository(
    private val metaApi: MetaApi,
    private val detokenMetaApi: MetaDTokenApi,
    private val metaKV: MetaKV,
    private val authRepository: AuthRepository,
    private val shareRecordDao: ShareRecordDao
) {

    private val app: Application = GlobalContext.get().get()
    protected val accountInteractor by GlobalContext.get().inject<AccountInteractor>()
    protected val commonParamsProvider by GlobalContext.get().inject<CommonParamsProvider>()

    suspend fun refreshAccessToken(): AccessTokenRefreshResult? {
        val localAccessToken = metaKV.account.accessToken
        val localRefreshToken = metaKV.account.refreshToken
        if (localAccessToken.isNullOrEmpty() || localRefreshToken.isNullOrEmpty() || metaKV.account.accessTokenExpireTime == -1L) {
            Timber.tag(CheckTokenInterceptor.TAG)
                .d("refreshAccessToken localAccessToken or localRefreshToken is null or refreshTime < 0")
            return null
        }
        var retryTime = 3
        var result: AccessTokenRefreshResult? = null
        while (retryTime > 0) {
            try {
                val apiResult = suspendApiNotNull {
                    detokenMetaApi.refreshAccessToken(
                        GetAccessTokenRequestBody(
                            localAccessToken,
                            localRefreshToken
                        )
                    )
                }.invoke()
                if (apiResult.accessToken.isNotEmpty()) {
                    result = apiResult
                    break
                }
            } catch (it: Throwable) {
                if (it is ApiResultCodeException && it.code in notRetryTokenList) {
                    Timber.tag(CheckTokenInterceptor.TAG).e("refreshAccessToken api error: ${it.code}")
                    clearAccessTokenNextRefreshTime()
                    break
                } else {
                    Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken api error: ${it}")
                }
            }
            retryTime--
        }
        result?.let {
            saveAccessToken(it.accessToken, it.accessTokenExpire)
        }
        return result
    }

    suspend fun visitorLogin(loginType: LoginType): Flow<DataResult<VisitorInfoApiResult>> = flow {
        metaKV.account.accessToken = null
        clearAccessTokenNextRefreshTime()
        val dataResult = DataSource.getDataResultForApi { metaApi.visitorLogin() }
        Timber.tag("Test-IdToken").d("visitorLogin loginType:${loginType}, dataResult=$dataResult")
        val userInfo = dataResult.data?.userInfo
        if (dataResult.succeeded && userInfo != null) {
            Analytics.track(EventConstants.EVENT_LOGIN_SUCCEED) {
                put(EventConstants.KEY_LOGIN_WAY, LoginWay.Tourist.way)
                put(EventConstants.KEY_LOGIN_TYPE, loginType.type)
            }
            saveToken(
                dataResult.data?.customToken,
                dataResult.data?.refreshToken,
                dataResult.data?.accessToken,
                dataResult.data?.accessTokenExpire
            )
            accountInteractor.initTokenExpireCheck()
        }
        emit(dataResult)
    }

    // 获取用户信息
    suspend fun getMetaUserInfoFromNet(): Flow<DataResult<MetaUserInfo>> = flow {
        val dataResult = DataSource.getDataResultForApi { metaApi.getMetaUserInfo() }
        emit(dataResult)
    }

    fun processAuthLogin(
        dataResult: DataResult<AuthInfoApiResult>,
        loginType: LoginType,
        loginWay: LoginWay,
        oAuthToken: String
    ) = flow {
        Timber.tag("Test-IdToken").d("processAuthLogin loginType:${loginType}, loginWay:${loginWay}, dataResult=$dataResult")
        val userInfo = dataResult.data?.userInfo
        userInfo?.firstBinding = dataResult.data?.firstBinding ?: false
        if (dataResult.succeeded && userInfo != null) {
            Analytics.track(EventConstants.EVENT_LOGIN_SUCCEED) {
                put(EventConstants.KEY_LOGIN_WAY, loginWay.way)
                put(EventConstants.KEY_LOGIN_TYPE, loginType.type)
            }
            accountInteractor.updateCustomToken(dataResult.data?.customToken)
            saveToken(
                dataResult.data?.customToken,
                dataResult.data?.refreshToken,
                dataResult.data?.accessToken,
                dataResult.data?.accessTokenExpire
            )
            accountInteractor.initTokenExpireCheck()
            emit(LoginState.Succeeded(userInfo))
            // 不阻塞更新用户信息
            signInWithCredential(loginWay.toLoginFromType(), oAuthToken)
        } else {
            val violateInfo = GsonUtil.gsonSafeParseCollection<ViolateMessage>(dataResult.message).let {
                it?.copy(localType = it.getTypeByCode(dataResult.code))
            }
            emit(
                LoginState.Failed(
                    dataResult.message.replaceChinese(app.getString(R.string.login_fail)),
                    dataResult.code ?: 0,
                    violateInfo
                )
            )
        }
    }

    private fun signInWithCredential(loginFromType: LoginFromType?, token: String) = GlobalScope.launch(Dispatchers.IO) {
        authRepository.signInWithCredential(loginFromType, token)
    }

    suspend fun bindByParentEmail(email: String, code: String): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(bindType = "parentEmail", bindKey = email, bindToken = code)
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    suspend fun bindByEmail(email: String, code: String): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(bindType = "email", bindKey = email, bindToken = code)
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    suspend fun bindEmailChange(newEmail: String, newEmailCode: String, oldEmailCode: String): Flow<DataResult<Boolean>> = flow {
        val map = mapOf<String, String>("newEmail" to newEmail, "newEmailCode" to newEmailCode, "oldEmailCode" to oldEmailCode, "type" to "0")
        val dataResult = DataSource.getDataResultForApi { metaApi.bindEmailChange(map) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    suspend fun bindParentEmailChange(newEmail: String, newEmailCode: String, oldEmailCode: String): Flow<DataResult<Boolean>> = flow {
        val map = mapOf("newEmail" to newEmail, "newEmailCode" to newEmailCode, "oldEmailCode" to oldEmailCode, "type" to "1")
        val dataResult = DataSource.getDataResultForApi { metaApi.bindEmailChange(map) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    suspend fun bindAccountAndPassword(bindKey: String, password: String): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(bindType = "account", bindKey = bindKey, bindToken = passwordEncrypt(password))
//        val map = mapOf("bindType" to "account", "bindKey" to bindKey, "password" to password)
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    suspend fun bindPasswordByGparkId(bindKey: String, password: String): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(bindType = "gpark_id", bindKey = bindKey, bindToken = passwordEncrypt(password))
//        val map = mapOf("bindType" to "account", "bindKey" to bindKey, "password" to password)
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    fun saveMetaUserInfo(metaUserInfo: MetaUserInfo) {
        metaKV.account.userInfo = GsonUtil.gson.toJson(metaUserInfo)
        metaKV.account.uuid = metaUserInfo.uuid ?: ""
    }

    private fun clearAccessTokenNextRefreshTime() {
        metaKV.account.accessTokenExpireTime = -1
    }

    private fun saveAccessToken(accessToken: String?, accessRefreshTime: Long?) {
        Timber.tag(CheckTokenInterceptor.TAG).d("saveAccessToken, accessRefreshTime: ${accessRefreshTime}, $accessToken ")
        if (accessRefreshTime != null && accessRefreshTime <= BaseAccountInteractor.ACCESS_TOKEN_REFRESH_INTERVAL) {
            // 过期时间比设置时间少，主动刷新不生效
            metaKV.account.accessTokenExpireTime = 0
        } else if (accessRefreshTime != null && accessRefreshTime >= 0) {
            metaKV.account.accessTokenExpireTime = System.currentTimeMillis() + accessRefreshTime
        }
        accessToken?.let {
            metaKV.account.accessToken = it
        }
    }

    /**
     * 保存用户token
     */
    protected suspend fun saveToken(
        customToken: String?,
        refreshToken: String?,
        accessToken: String?,
        accessRefreshTime: Long?,
    ) {
        withContext(Dispatchers.IO) {
            customToken?.let {
                metaKV.accountWrapper.customToken = customToken
            }
            refreshToken?.let {
                metaKV.account.refreshToken = refreshToken
            }
            saveAccessToken(accessToken, accessRefreshTime)
        }
    }


    /**
     * @param notifyBackend 是否通知后端，token过期时退出登录不需要通知后端
     */
    fun logout(notifyBackend: Boolean): Flow<DataResult<Boolean>> = flow {
        authRepository.logout()
        MgsBiz.logout()
        val result = if (notifyBackend) {
            DataSource.getDataResultForApi { metaApi.logout(mapOf("token" to metaKV.account.accessToken)) }
        } else {
            DataResult.Success(true)
        }
        metaKV.account.clear()
        metaKV.accountWrapper.clear()
        emit(result)
    }

    /**
     * 用户玩游戏心跳
     */
    suspend fun updateUserPlayGame(gameId: String): Flow<DataResult<String?>> = flow {
        val dataResult = DataSource.getDataResultForApi { metaApi.updateUserPlayGame(mapOf("gameId" to gameId)) }
        emit(dataResult)
    }

    /**
     * 开始游戏（游戏切到前台）上报
     */
    suspend fun updateUserStartGame(gameId: String) {
        val dataResult = DataSource.getDataResultForApi { metaApi.updateUserStartGame(mapOf("gameId" to gameId)) }
        Timber.d("updateUserStartGame $dataResult")
    }

    /**
     * 结束游戏（游戏切到后台）上报
     */
    suspend fun updateUserStopGame(gameId: String) {
        val dataResult = DataSource.getDataResultForApi { metaApi.updateUserStopGame(mapOf("gameId" to gameId)) }
        Timber.d("updateUserStopGame $dataResult")
    }

    suspend fun accountSignup(
        account: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> {
        val request = ThirdLoginRequest(
            requestType = LOGIN_REQUEST_TYPE_ONLY_REGISTER,
            loginKey = account,
            loginToken = passwordEncrypt(password),
            loginType = LoginWay.Account.way,
            simCountryCode = commonParamsProvider.simCountryCode
        )
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginByThird(request)
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Account, "")
    }

    open suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val request = ThirdLoginRequest(
            requestType = LOGIN_REQUEST_TYPE_ONLY_LOGIN,
            loginKey = account,
            loginToken = passwordEncrypt(password),
            loginType = LoginWay.Account.way,
            simCountryCode = commonParamsProvider.simCountryCode
        )
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginByThird(request)
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Account, "")
    }

    open suspend fun gparkIdLogin(
        id: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> {
        return processAuthLogin(
            DataResult.Error(404, "Not supported"),
            loginType,
            LoginWay.Account,
            ""
        )
    }

    suspend fun ditout(code: String): Flow<DataResult<Boolean>> = flow {
        val map = mapOf<String, String>("code" to code)
        val dataResult = DataSource.getDataResultForApi { metaApi.ditout(map) }
        if (dataResult.succeeded) {
            emit(DataSource.getDataResult { true })
            return@flow
        }
        emit(dataResult)
    }

    fun queryUserProfile(uid: String): Flow<DataResult<UserProfileInfo>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.queryUserProfile(uid) }
        emit(result)
    }

    suspend fun getUserCreate(): Flow<DataResult<UserCreateInfo?>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getUserCreate() }
        emit(result)
    }

    suspend fun receiveBadge(badgeCode: String): Flow<DataResult<Boolean>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.receiveBadge(badgeCode) }
        emit(result)
    }

    fun modifyUserFullBodyImg(fullBodyImUrl: String): Flow<DataResult<Any?>> = flow {
        val result =
            DataSource.getDataResultForApi { metaApi.modifyUserFullBodyImg(mapOf("bgMaskImage" to fullBodyImUrl)) }
        emit(result)
    }

    fun queryUserMuteStatus(uid: String? = null) = suspendApiNotNull {
        val realUid = if (uid.isNullOrBlank()) {
            GlobalContext.get().get<AccountInteractor>().curUuidOrNull
        } else {
            uid
        }
        metaApi.queryUserMuteStatus(mapOf("uid" to realUid), realUid)
    }

    fun getRelationList(request: RelationListRequest): Flow<RelationListResult> = flow {
        emit(suspendApiNotNull { metaApi.getRelationList(request) }.invoke())
    }

    fun getRelationCount(request: RelationCountRequest): Flow<RelationCountResult> = flow {
        emit(suspendApiNotNull { metaApi.getRelationCount(request) }.invoke())
    }

    fun getFriendCount(request: RelationCountRequest): Flow<DataResult<RelationCountResult>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getRelationCount(request) }
        emit(result)
    }

    fun getNotificationSwitch(): Flow<DataResult<Map<String, Boolean>>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getNotificationSwitch() }
        emit(result)
    }

    fun setNotificationSwitch(checked: Boolean): Flow<DataResult<Boolean>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.setNotificationSwitch(mapOf("noticeSwitch" to checked)) }
        emit(result)
    }

    /**
     * 获取设备最后登录账号的信息
     */
    fun getContinueAccount(): Flow<DataResult<ContinueAccountInfo>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.getContinueAccount()
        }
        emit(result)
    }

    suspend fun setPrivacySwitch(switch: PrivacySwitch): DataResult<Boolean> =
        DataSource.getDataResultForApi {
            metaApi.setPrivacySwitch(switch)
        }

    fun getPrivacySwitch() = suspendApiNotNull {
        metaApi.getPrivacySwitch()
    }


    fun userAgentUpload() = flow {
        val result =
            DataSource.getDataResultForApi {
                metaApi.userAgentUpload(
                    DeviceParamsRequest(
                        commonParamsProvider.simCountryCode
                    )
                )
            }
        emit(result)
    }

    fun mwPay(hashMap: Map<String, Any?>): Flow<DataResult<String?>> = flow {
        val createOrderResult = DataSource.getDataResultForApi { metaApi.mwCreateOrder(hashMap) }.map { it.orderCode }
        if (!createOrderResult.succeeded || hashMap["isDebug"] == true || hashMap["isDebug"].toString() == "true") {
            emit(createOrderResult)
            return@flow
        }
        val prepay = DataSource.getDataResultForApi {
            metaApi.createPrepaidOrder(
                PayOrderInfo(
                    orderCode = createOrderResult.data,
                    payChannel = hashMap["payChannel"]?.toString()?.toFloatOrNull()?.toInt(),
                    payTunnel = hashMap["payTunnel"].toString().toFloatOrNull()?.toInt().toString()
                )
            )
        }
        if (!prepay.succeeded) {
            emit(prepay.map { createOrderResult.data })
            return@flow
        }

        val state = getPayState(createOrderResult, 1)// 轮询5次

        if (state.succeeded && state.data?.get("paid") == true) {
            emit(DataResult.Success(createOrderResult.data))
        } else {
            emit(DataResult.Error(505, "pay state error $state", data = null))
        }

    }

    private suspend fun getPayState(
        createOrderResult: DataResult<String>,
        i: Int
    ): DataResult<Map<String, Boolean>> {
        delay(1000L * i)
        val temp = DataSource.getDataResultForApi { metaApi.mwPayState(createOrderResult.data ?: "") }
        return if (temp.data?.get("paid") == true) {
            temp
        } else if (i <= 5) {
            getPayState(createOrderResult, i + 1)
        } else {
            temp
        }
    }

    fun insertShareRecord(shareRecordId: String, platform: String) = flow {
        emit(shareRecordDao.insert(ShareRecordEntity(shareRecordId, platform)))
    }.catch {
        emit(-1L)
    }

    fun existsShareRecord(shareRecordId: String) = flow {
        emit(shareRecordDao.getById(shareRecordId) != null)
    }.catch { emit(false) }

    open fun passwordReset(
        email: String,
        code: String,
        newPassword: String
    ): Flow<DataResult<Boolean>> = flow {
        val np = passwordEncrypt(newPassword) ?: newPassword
        emit(DataSource.getDataResultForApi {
            metaApi.passwordReset(
                mapOf(
                    "email" to email,
                    "code" to code,
                    "newPassword" to np,
                    "type" to AccountLoginRequest.TYPE
                )
            )
        })
    }

    fun passwordChange(oldPassword: String, newPassword: String): Flow<DataResult<Boolean>> = flow {
        val op = passwordEncrypt(oldPassword) ?: oldPassword
        val np = passwordEncrypt(newPassword) ?: newPassword
        emit(DataSource.getDataResultForApi {
            metaApi.passwordChange(
                mapOf(
                    "oldPassword" to op,
                    "newPassword" to np,
                    "type" to AccountLoginRequest.TYPE
                )
            )
        })
    }

    protected fun passwordEncrypt(password: String): String? {
        val result = kotlin.runCatching {
            RsaUtil.encryptByPublicKey(RsaUtil.ACCOUNT_PUBLIC_KEY, password)
        }.getOrNull()
        if (BuildConfig.DEBUG) {
            Timber.d("passwordEncrypt $password->$result")
        }
        return result
    }
}
