package com.socialplay.gpark.data.repository

import android.app.Application
import android.text.TextUtils
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.FollowReachLimitException
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.account.EditUserInfoRequest
import com.socialplay.gpark.data.model.reportBlock.ReportRequest
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.util.Md5Util
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-09-15 11:44 上午
 * @desc:
 */
class AccountRepository(
    private val metaApi: MetaApi,
    private val metaKV: MetaKV,
    private val mataMapper: MetaMapper,
) {

    private val app: Application = GlobalContext.get().get()

    fun sendEmail(email: String?, scene: String): Flow<DataResult<Boolean>> = flow {
        val map = if (TextUtils.isEmpty(email)) mapOf("scene" to scene) else mapOf("email" to email!!, "sendScene" to scene)
        emit(DataSource.getDataResultForApi { metaApi.sendEmail(map) })
    }

    fun checkEmail(email: String, code: String, scene: String): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.checkEmail(mapOf("email" to email, "code" to code, "sendScene" to scene)) })
    }

    suspend fun updateUserInfo(birthday: Long?, gender: Int, nickname: String?, portrait: String?, city: String?, signature: String?, reviewBirth: Boolean) = flow {
        emit(
            DataSource.getDataResultForApi {
                metaApi.updateUserInfo(
                    EditUserInfoRequest(
                        birth = birthday,
                        gender = if (gender > -1) gender else null,
                        nickname = if (!nickname.isNullOrEmpty()) nickname else null,
                        portrait = if (!portrait.isNullOrEmpty()) portrait else null,
                        city = if (!city.isNullOrEmpty()) city else null,
                        signature = if (!signature.isNullOrEmpty()) signature else null,
                        // 如需要检验是否填写生日，传EditUserInfoRequest.BIRTH_SWITCH_CLOSE
                        ageSwitch = if (reviewBirth) EditUserInfoRequest.BIRTH_SWITCH_U13 else EditUserInfoRequest.BIRTH_SWITCH_CLOSE
                    )
                )
            }
        )
    }

    suspend fun getUserRelation(relationType: RelationType, targetUid: String, remark: String = "") = flow {
        val result = DataSource.getDataResultForApi { metaApi.getUserRelation(mapOf("relationType" to relationType.value, "remark" to remark, "targetUid" to targetUid)) }
        emit(result.map { mataMapper.map(it) })
    }

    suspend fun reportAdd(
        reportType: String,
        reportedId: String,
        reportContent: String?,
        reportedUid: String?,
        reportReason: String?,
        attachmentUrlList: List<String>?,
        reportSource: String?,
        reportAdditional: String?,
    ) = flow {
        val request = ReportRequest(
            reportType,
            reportedId,
            reportContent.orEmpty(),
            reportedUid.orEmpty(),
            reportReason.orEmpty(),
            attachmentUrlList,
            reportSource,
            reportAdditional
        )
        emit(DataSource.getDataResultForApi { metaApi.reportAdd(request) })
    }

    fun relationAdd(targetUid: String, relationType: String) = flow {
        val map = mapOf(
            "targetUid" to targetUid,
            "relationType" to relationType
        )
        emit(DataSource.getDataResultForApi {
            val result = metaApi.relationAdd(map)
            if (relationType == RelationType.Follow.value && result.code == CommunityUtil.FOLLOW_REACH_LIMIT_CODE) {
                throw FollowReachLimitException()
            }
            result
        })
    }

    fun relationDel(targetUid: String, relationType: String) = flow {
        val map = mapOf(
            "targetUid" to targetUid,
            "relationType" to relationType
        )
        emit(DataSource.getDataResultForApi { metaApi.relationDel(map) })
    }

    fun relationAddV2(targetUid: String, relationType: String): suspend () -> Boolean =
        suspendApiNotNull {
            val result = metaApi.relationAdd(
                mapOf(
                    "targetUid" to targetUid,
                    "relationType" to relationType
                )
            )
            if (relationType == RelationType.Follow.value && result.code == CommunityUtil.FOLLOW_REACH_LIMIT_CODE) {
                throw FollowReachLimitException()
            }
            result
        }

    fun relationDelV2(targetUid: String, relationType: String): suspend () -> Boolean =
        suspendApiNotNull {
            metaApi.relationDel(
                mapOf(
                    "targetUid" to targetUid,
                    "relationType" to relationType
                )
            )
        }

    /**
     * 判断用户是否大于13岁
     */
    suspend fun isUserAbove13() = flow {
        emit(DataSource.getDataResultForApi { metaApi.isUserAbove13() })
    }

    /**
     * 随机获取一个昵称
     */
    suspend fun getRandomNickname() = flow {
        emit(DataSource.getDataResultForApi { metaApi.getRandomNickname() })
    }

    fun getRandomNicknameV2() = suspendApiNotNull { metaApi.getRandomNicknameV2() }

    fun checkNickname(nickname: String, uid: String) = suspendApiNotNull {
        metaApi.checkNickname(mapOf("nickname" to nickname, "uid" to uid))
    }
}