package com.socialplay.gpark.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.room.withTransaction
import com.bumptech.glide.util.LruCache
import com.ly123.tes.mgs.im.ImHelper
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.ValueCallback
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Conversation.ConversationType
import com.ly123.tes.mgs.metacloud.model.PagingResult
import com.ly123.tes.mgs.metacloud.model.UserInfo
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.*
import com.socialplay.gpark.data.interactor.OfflineInteractor
import com.socialplay.gpark.data.local.AiConversationDao
import com.socialplay.gpark.data.local.AiMessageDao
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.MetaSimpleUserDao
import com.socialplay.gpark.data.local.exists
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.entity.AIConversationEntity
import com.socialplay.gpark.data.model.entity.AIMessageEntity
import com.socialplay.gpark.data.model.entity.AIMessageEntity.Companion.STATUS_DEFAULT
import com.socialplay.gpark.data.model.im.*
import com.socialplay.gpark.data.model.im.ImUpdateType.*
import com.socialplay.gpark.data.model.im.request.IMCheckRequest
import com.socialplay.gpark.data.repository.api.SysMessageListPagingSource
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import java.util.ArrayList
import kotlin.collections.filter
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * @author: ning.wang
 * @date: 2021-06-23 9:03 下午
 * @desc:
 */
class ImRepository(
    val metaApi: MetaApi,
    private val db: AppDatabase,
    private val dao: MetaSimpleUserDao,
    val aiMessageDao: AiMessageDao,
    val aiConversationDao: AiConversationDao,
) {

    private val simUserInfoCache by lazy {
        object : LruCache<String, MetaSimpleUserEntity>(256) {}
    }

    suspend fun fetchConversationListV2(
        seq: String?,
        count:Int = 20,
        conversationTypes: Set<ConversationType> = setOf(Conversation.ConversationType.PRIVATE, Conversation.ConversationType.GROUP),
    ): PagingResult<List<MetaConversation>>? {
        return kotlin.runCatching {
            suspendCoroutine { coroutine ->
                MetaCloud.getConversationListByPageV2(
                    seq,
                    count,
                    conversationTypes,
                    callback = object : ValueCallback<PagingResult<List<MetaConversation>>> {
                        override fun onError(code: Int, desc: String) {
                            coroutine.resumeWithException(Exception("Failed to load conversation list code:$code msg:$desc"))
                        }

                        override fun onSuccess(data: PagingResult<List<MetaConversation>>) {
                            coroutine.resume(data)
                        }
                    }
                )
            }
        }.getOrDefault(null)
    }

    suspend fun fetchAllConversationList(): List<MetaConversation> {
        var nextSeq: String? = null
        val conversationList = mutableListOf<MetaConversation>()
        while (true) {
            try {
                val page = fetchConversationListV2(nextSeq) ?: break
                conversationList.addAll(page.data)
                if (page.isFinished) {
                    break
                } else {
                    nextSeq = page.nextSeq
                }
            } catch (e: Exception) {
                break
            }
        }
        return conversationList
    }

    suspend fun queryFriendInfo(uuid: String): DataResult<FriendInfo> {
        return DataSource.getDataResultForApi { metaApi.queryFriendInfo(uuid) }
    }

    /**
     * 获取Im key & token
     */
    suspend fun getImInfo(): DataResult<ImInfo> {
        return DataSource.getDataResultForApi { metaApi.getImInfo() }
    }

    suspend fun refreshUserInfoForNet(): Unit = withContext(Dispatchers.IO) {
        val list =
            kotlin.runCatching { MetaCloud.getConversationList(Conversation.ConversationType.PRIVATE)
                .filter { it.targetId.isNotBlank() && it.targetId!= OfflineInteractor.OFFLINE_MANAGER }
            }.getOrNull() ?: return@withContext

        list.filter { it.targetId.isNotBlank() }
            .mapNotNull { getSimpleUserInfo(it.targetId) }
            .forEach {
                putUserInfoCache(it)
            }
    }

    suspend fun putUserInfoCache(userinfo: MetaSimpleUserEntity) {
        if (userinfo.uuid.isEmpty()) {
            return
        }
        simUserInfoCache.put(userinfo.uuid, userinfo)
        runCatching {
            db.withTransaction {
                if (dao.exists(userinfo.uuid)) {
                    dao.updateInfo(userinfo)
                } else {
                    dao.insertInfo(userinfo)
                }
            }
        }
        val userInfo = UserInfo(
            userinfo.uuid,
            if (userinfo.remark.isNullOrBlank()) userinfo.nickname else userinfo.remark,
            userinfo.avatar
        )
        userInfo.remark = userinfo.remark
        userInfo.avatar = userinfo.avatar
        Timber.tag("metacloud").d("setSimpleUserInfo.. userInfo ${userInfo.name}")
        ImHelper.setSimpleUserInfo(userInfo)
    }

    suspend fun getSimpleUserInfo(uuid: String): MetaSimpleUserEntity? {
        if (uuid == OfflineInteractor.OFFLINE_MANAGER) {
            return null
        }
        simUserInfoCache.get(uuid)?.let {
            Timber.tag("LeoWn").d("getSimpleUserInfo.. cache ${it.nickname}")
            return it
        }
        val dao = db.metaSimpleUserDao()
        if (dao.exists(uuid)) {
            dao.getUserInfo(uuid)?.let {
                putUserInfoCache(it)
                Timber.tag("LeoWn").d("getSimpleUserInfo.. db ${it.nickname}")
                return it
            }
        }
        val result = queryFriendInfo(uuid)
        Timber.tag("LeoWn").d("getSimpleUserInfo.. net ${result.data?.name}")
        result.data?.let {
            val simpleUser = it.toSimpleUser()
            putUserInfoCache(simpleUser)
            return simpleUser
        }
        return null
    }

    suspend fun clearUserInfoCache() = GlobalScope.launch {
        simUserInfoCache.clearMemory()
        val dao = db.metaSimpleUserDao()
        dao.deleteAll()
    }

    suspend fun getUnReadCount(targetId: String, callback: (Int) -> Unit) {
        MetaCloud.getUnReadCount(Conversation.ConversationType.PRIVATE, targetId) {
            callback(it)
        }
    }

    suspend fun getUnReadCount(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    ) {
        MetaCloud.getUnReadCount(conversationType, targetId) {
            callback.invoke(ImUpdate(GET_UN_READ_COUNT, conversationType, targetId, it))
        }
    }


    //lib新增接口

    suspend fun removeConversation(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    ) {
        MetaCloud.removeConversation(conversationType, targetId) {
            callback(ImUpdate(REMOVE, conversationType, targetId, it))
        }
        getUnReadCount(conversationType, targetId, {})
    }

    suspend fun deleteMessages(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    ) {
        MetaCloud.clearMessages(conversationType, targetId)
        {
            callback(ImUpdate(DELETE_MESSAGE, conversationType, targetId, it))
        }
    }

    suspend fun setConversationToTop(
        conversationType: Conversation.ConversationType,
        targetId: String,
        isTop: Boolean,
        callback: (ImUpdate) -> Unit?
    ) {
        MetaCloud.setConversationToTop(conversationType, targetId, isTop) {
            callback(ImUpdate(SET_TOP, conversationType, targetId, it))
        }
    }

    fun syncConversationReadStatus(
        type: Conversation.ConversationType,
        targetId: String,
        timestamp: Long
    ) {
        MetaCloud.syncConversationReadStatus(type, targetId, timestamp) {
        }
    }

    suspend fun clearMessageUnReadStatus(
        conversationType: Conversation.ConversationType?,
        targetId: String?,
        callback: (ImUpdate) -> Unit?
    ) {
        conversationType ?: return
        targetId ?: return

        return suspendCancellableCoroutine {
            MetaCloud.clearMessageUnReadStatus(conversationType, targetId) {
                callback(ImUpdate(CLEAR_UN_READ, conversationType, targetId, it))
            }
        }
    }

    /**
     * 检查各种业务场景的聊天是否合法
     * @param authCode 业务码
     * @param content 文本内容
     * @param gameId 透传给数美后台看的参数
     */
    suspend fun reviewMessageRisk(
        authCode: String,
        content: String,
        gameId: String?
    ): Flow<DataResult<ReviewTextRiskResult?>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.commonReviewTextRisk(
                IMCheckRequest(
                    authCode,
                    content,
                    gameId
                )
            )
        }
        emit(result)
    }

    suspend fun getSysHeaderInfo(): Flow<DataResult<List<SysHeaderInfo>>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getSysHeaderInfo() }
        emit(result)
    }

    fun getSysActivitiesInfo(
        groupId: Long,
        pageSize: Int,
        subGroupKey: String?,
        lastRecordTime: Long?,
    ): Flow<PagingData<SysActivitiesInfo>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 2
            ),
            initialKey = null
        ) {
            SysMessageListPagingSource(metaApi, pageSize, groupId)
        }.flow
    }

    fun getSysUnReadCount(): Flow<DataResult<Int>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getSysUnreadCount() }
        emit(result)
    }

    /**
     *  AIbotMessage 本地数据库
     */
    fun addAiTextMessage(
        sendUserId: String,
        receiverUserID: String,
        content: String,
        botInfo: BotInfo?,
        messageType: Int? = 1
    ): Flow<AIMessageEntity> = flow {
        val messageDirection = if (sendUserId == botInfo?.id) {
            1
        } else {
            2
        }
        val aiMessage = AIMessageEntity(
            sendUserId,
            receiverUserID = receiverUserID,
            timestamp = System.currentTimeMillis(),
            messageType = messageType ?: 1,
            messageDirection,
            true,
            content,
            null,
            botInfo
        )
        aiMessageDao.insertMessage(aiMessage).let {
            val aiMessageEntity = AIMessageEntity(
                aiMessage.senderUserID,
                aiMessage.receiverUserID,
                aiMessage.timestamp,
                aiMessage.messageType,
                aiMessage.messageDirection,
                aiMessage.isRead,
                aiMessage.content,
                aiMessage.extra,
                aiMessage.botInfo,
                STATUS_DEFAULT,
                it ?: 0
            )
            emit(aiMessageEntity)
        }
    }

    /**
     * 更新
     */
    fun updateAiBotConversation(
        targetId: String,
        uuid: String,
        messageId: Long?,
        aiMessageEntity: AIMessageEntity?
    ): Flow<Boolean> = flow {
        val conversationEntity = aiConversationDao.getConversationByUUId(targetId, uuid)
        if (conversationEntity != null) {
            conversationEntity.latestMessageId = messageId
            conversationEntity.timestamp = System.currentTimeMillis()
            aiConversationDao.updateConversation(conversationEntity)
        } else {
            val conversationEntity = AIConversationEntity(
                targetId,
                uuid,
                messageId,
                aiMessageEntity?.messageType ?: 0,
                System.currentTimeMillis(),
                aiMessageEntity?.botInfo?.name ?: "",
                aiMessageEntity?.botInfo?.icon ?: ""
            )
            aiConversationDao.insertConversation(conversationEntity)
        }
        emit(true)
    }

    fun cleanAllAiBotMessageHistory(targetId: String, uuid: String): Flow<Boolean> = flow {
        aiMessageDao.cleanAllMessage(targetId, uuid)
        updateAiBotConversation(targetId, uuid, null, null)
        emit(true)
    }

    fun deleteLocalAiBotConversation(targetId: String, uuid: String): Flow<Boolean> = flow {
        aiConversationDao.deleteConversationByUUid(targetId, uuid)
        emit(true)
    }

    fun getAiBotHistoryMessageList(
        targetId: String,
        uuid: String,
        pageNum: Int?,
        pageSize:Int
    ): Flow<ArrayList<AIMessageEntity>> = flow {
        if (pageNum == null || pageNum == 0) {
            val data = aiMessageDao.getNewHistoryMessageList(targetId, uuid, pageSize)
            emit(ArrayList(data))
        } else {
            val data = aiMessageDao.getHistoryMessageList(targetId, uuid, pageNum * pageSize, pageSize)
            emit(ArrayList(data))
        }
    }

    fun getAllAiBotConversationList(
        uuid: String
    ): Flow<List<AIConversationEntity>> =
        flow {

            emit(aiConversationDao.getAllConversationList(uuid))
        }

    fun getConversationList(
        lastIndex: Int,
        uuid: String
    ): Flow<List<AIConversationEntity>> = aiConversationDao.getConversationList(lastIndex, 10, uuid)

    suspend fun getAiBotConversation(uuid: String, targetId: String): Flow<AIConversationEntity?> =
        flow {
            emit(aiConversationDao.getConversationByUUId(targetId, uuid))
        }

    fun addAiMessageList(list: ArrayList<AIMessageEntity>): Flow<List<AIMessageEntity>> =
        flow {
            val newList = list.filter { it.status == STATUS_DEFAULT }
            val resultList =  ArrayList<AIMessageEntity>()
            newList.forEachIndexed { index, aiMessageEntity ->
                val id = aiMessageDao.insertMessage(aiMessageEntity)
                val aiMessage = AIMessageEntity(
                    aiMessageEntity.senderUserID,
                    receiverUserID = aiMessageEntity.receiverUserID,
                    timestamp = aiMessageEntity.timestamp,
                    messageType = aiMessageEntity.messageType ?: 1,
                    aiMessageEntity.messageDirection,
                    true,
                    aiMessageEntity.content,
                    null,
                    aiMessageEntity.botInfo,
                    messageId = id ?: aiMessageEntity.timestamp
                )
                resultList.add(aiMessage)
            }
            emit(resultList)
        }

    fun deleteAiMessage(messageId: Long): Flow<DataResult<Boolean?>> = flow {
        aiMessageDao.deleteMessageById(messageId)
        emit(DataResult.Success(true))
    }


}

