package com.socialplay.gpark.data.model.outfit


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcDesignFeedWrapper(
    val feeds: List<UgcDesignFeed>?,
    val flag: Boolean = false
)

data class UgcDesignFeed(
    val cover: String?,
    val feedId: String,
    val feedType: Int,
    val isFavorite: Boolean,
    val favorites: Long,
    val popularity: Long,
    val sales: Long,
    val title: String?,
    val userIcon: String?,
    val userName: String?,
    val uuid: String?,
    val views: Long,
    val tags: List<String>?,
    val guid: String?,
    val editable: Boolean,
) {

    companion object {
        const val FEED_TYPE_UGC_DESIGN = 0
        const val FEED_TYPE_UGC_MODULE = 1
    }

    val trackId get() = guid ?: feedId
    val trackIsRecreate get() = if (editable) "yes" else "no"
    val isUgcModule get() = feedType == FEED_TYPE_UGC_MODULE

    fun switchLike(): UgcDesignFeed {
        val newLikeCount: Long = if (isFavorite) {
            (favorites - 1).coerceAtLeast(0)
        } else {
            favorites + 1
        }
        return copy(isFavorite = !isFavorite, favorites = newLikeCount)
    }
}

data class UgcDesignFeedRequest(
    val sortType: Int,
    val pageNumber: Int,
    val pageSize: Int
) {

    companion object {
        const val ORDER_DEFAULT = 0
        const val ORDER_HOT = 1
        const val ORDER_LATEST = 2
    }
}

data class UgcDesignFeedResponse(
    val cacheKey: String?,
    val isLastPage: Boolean,
    val pageNumber: Int,
    val pageSize: Int,
    val totalCount: Int,
    val data: List<UgcDesignFeed>?
)

data class UgcAssetNotice(
    val tagName: String?,
    val tagColor: String?,
    val noticeId: Int,
    val noticeTitle: String?,
    val noticeUrl: String?,
)