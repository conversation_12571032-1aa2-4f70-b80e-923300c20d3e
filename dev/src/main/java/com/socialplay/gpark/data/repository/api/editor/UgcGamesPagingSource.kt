package com.socialplay.gpark.data.repository.api.editor

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.*
import com.socialplay.gpark.data.kv.TTaiKV.Companion.ID_PGC_GAME_LIST
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.editor.MultiTsGameResult
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.GsonUtil

class UgcGamesPagingSource(val metaApi: MetaApi, val pageSize: Int) : PagingSource<Int, MultiTsGameResult>() {

    override fun getRefreshKey(state: PagingState<Int, MultiTsGameResult>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, MultiTsGameResult> {

        val cur = params.key ?: 1
        val prevKey = null

        return try {
            val items = arrayListOf<MultiTsGameResult>()
            var isEnd = true
            var isSuccess = true
            var errorMsg: String? = null
            if (PandoraToggle.fetchPGCList && cur == 1) {
                val result = fetchPgcGameList()
                result.data?.let { info ->
                    items.addAll(info.map { MultiTsGameResult(MultiTsGameResult.TYPE_PGC, null, it) })
                }
                isSuccess = result.succeeded
                errorMsg = result.message
            }
            if (PandoraToggle.fetchUGCList) {
                val data = DataSource.getDataResultForApi { metaApi.getUgcGameList(hashMapOf("page" to cur)) }
                data.data?.games?.let { info ->
                    items.addAll(info.map { MultiTsGameResult(MultiTsGameResult.TYPE_UGC, it, null) })
                }
                isEnd = data.data?.games.isNullOrEmpty() || data.data?.end == true
                isSuccess = data.succeeded
                errorMsg = data.message
            }
            if (isSuccess) {
                val nextKey = if (isEnd) null else cur + 1
                LoadResult.Page(items, prevKey, nextKey)
            } else {
                LoadResult.Error(NullPointerException(errorMsg))
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    private suspend fun fetchPgcGameList(): DataResult<List<GameSuggestionInfo>?> {
        val data = DataSource.getDataResultForApi { metaApi.getTTaiConfig(ID_PGC_GAME_LIST) }
        val idList = GsonUtil.gsonSafeParseCollection<List<String>>(data.data?.value)?.take(50)
        if (data.succeeded && data.data != null) {
            if (idList.isNullOrEmpty()) {
                return DataResult.Success(listOf())
            }
            return DataSource.getDataResultForApi {
                metaApi.getGameListByIds(idList)
            }
        } else {
            return DataResult.Error(0, data.message?:"")
        }
    }
}