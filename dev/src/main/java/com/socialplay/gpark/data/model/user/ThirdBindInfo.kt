package com.socialplay.gpark.data.model.user

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2023/2/8
 * Desc: 三方绑定信息
 */
@Parcelize
data class ThirdBindInfo(
    val google: ThirdBindObj? = null,
    val facebook: ThirdBindObj? = null,
    val email: ThirdBindObj? = null,
    var parentEmail: ThirdBindObj? = null,
    val account: ThirdBindObj? = null,
    val leyuan: ThirdBindObj? = null
) : Parcelable

@Parcelize
data class ThirdBindObj(
    val bindId: String?,
    val portrait: String?,
    val nickname: String?,
) : Parcelable