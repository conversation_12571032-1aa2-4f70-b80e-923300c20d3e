package com.socialplay.gpark.data

import androidx.paging.PagingData
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Conversation.ConversationType
import com.ly123.tes.mgs.metacloud.model.PagingResult
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.MgsUserInfo
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.model.BodyRequestOrder
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.GameItem
import com.socialplay.gpark.data.model.GameRoomList
import com.socialplay.gpark.data.model.GameRoomStatus
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.data.model.QrResult
import com.socialplay.gpark.data.model.ReviewGameInfo
import com.socialplay.gpark.data.model.SearchGameItem
import com.socialplay.gpark.data.model.SnsInfo
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.UGGameSuggestionInfo
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserCreateInfo
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageResult
import com.socialplay.gpark.data.model.aibot.AIBotCreateRequest
import com.socialplay.gpark.data.model.aibot.AIBotCreateResult
import com.socialplay.gpark.data.model.aibot.AIBotGenerateInfo
import com.socialplay.gpark.data.model.aibot.AIBotStyle
import com.socialplay.gpark.data.model.aibot.AiBotConversationResult
import com.socialplay.gpark.data.model.aibot.AiBotFollowResult
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotInfoCreate
import com.socialplay.gpark.data.model.auth.QRCodeAuthScanResult
import com.socialplay.gpark.data.model.choice.CardAll
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardListApiResult
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.Recommend
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.creator.CreatorFrameResult
import com.socialplay.gpark.data.model.creator.RecommendCreatorResult
import com.socialplay.gpark.data.model.creator.RecommendKolUgcResult
import com.socialplay.gpark.data.model.creator.RecommendUgcResult
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.data.model.creator.morecreator.LabelCreatorResult
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorResult
import com.socialplay.gpark.data.model.creator.moreugc.FollowedCreatorUgcWrapper
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.editor.DeleteRoleStyleResponse
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.editor.EditorLocalStatusInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.data.model.editor.GroupedData
import com.socialplay.gpark.data.model.editor.LikeRoleStyleResponse
import com.socialplay.gpark.data.model.editor.MultiTsGameResult
import com.socialplay.gpark.data.model.editor.MyCreationsV3Request
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import com.socialplay.gpark.data.model.editor.PgcGameDetail
import com.socialplay.gpark.data.model.editor.QueryPositiveComment
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.RoleStyleListResponse
import com.socialplay.gpark.data.model.editor.TSTypeInfo
import com.socialplay.gpark.data.model.editor.UgcBannerInfo
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.data.model.editor.UgcFormWorkV4Data
import com.socialplay.gpark.data.model.editor.UgcGameConfig
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.editor.cloud.ProjectLimit
import com.socialplay.gpark.data.model.editor.cloud.UgcBackupInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcCloudProject
import com.socialplay.gpark.data.model.editor.share.AvatarShareCompositeBackground
import com.socialplay.gpark.data.model.entity.AIConversationEntity
import com.socialplay.gpark.data.model.entity.AIMessageEntity
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardData
import com.socialplay.gpark.data.model.friend.CommonShareRequest
import com.socialplay.gpark.data.model.friend.CommonShareResult
import com.socialplay.gpark.data.model.friend.FriendRequestInfoWrapper
import com.socialplay.gpark.data.model.friend.FriendSearchInfo
import com.socialplay.gpark.data.model.game.OperationList
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerListData
import com.socialplay.gpark.data.model.gamereview.AddAppraiseReplyRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListResult
import com.socialplay.gpark.data.model.gamereview.AttentionRequest
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.data.model.gamereview.GameReviewResult
import com.socialplay.gpark.data.model.gamereview.GameScoreResult
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_GAME_ONLY
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfoList
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfoPage
import com.socialplay.gpark.data.model.groupchat.GroupChatMemberInfo
import com.socialplay.gpark.data.model.groupchat.MgsGroupApplyPageRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatListRequest
import com.socialplay.gpark.data.model.im.HomeOperationNotification
import com.socialplay.gpark.data.model.im.ImInfo
import com.socialplay.gpark.data.model.im.ImUpdate
import com.socialplay.gpark.data.model.im.MetaSimpleUserEntity
import com.socialplay.gpark.data.model.im.ReviewTextRiskResult
import com.socialplay.gpark.data.model.im.RiskQueryResult
import com.socialplay.gpark.data.model.im.SystemNotification
import com.socialplay.gpark.data.model.im.request.RiskTaskQueryRequest
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.member.MemberRequest
import com.socialplay.gpark.data.model.mgs.MgsSceneConfig
import com.socialplay.gpark.data.model.moments.MomentsTemplate
import com.socialplay.gpark.data.model.moments.MomentsTemplateBody
import com.socialplay.gpark.data.model.moments.PlotListBody
import com.socialplay.gpark.data.model.moments.PlotMainList
import com.socialplay.gpark.data.model.moments.PlotTemplateList
import com.socialplay.gpark.data.model.pay.CoinType
import com.socialplay.gpark.data.model.pay.CoinsRecords
import com.socialplay.gpark.data.model.pay.SubmitResult
import com.socialplay.gpark.data.model.pay.SubscribeProductInfo
import com.socialplay.gpark.data.model.pay.TakeOderResult
import com.socialplay.gpark.data.model.pay.TripartiteInfo
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.post.CommentResultWrapper
import com.socialplay.gpark.data.model.post.CommunityBlock
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.CommunityTopicBlockWrap
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostPublishResult
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PublishPostRequest
import com.socialplay.gpark.data.model.post.SearchPostCardRequest
import com.socialplay.gpark.data.model.post.topic.TopicDetailInfo
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.friend.OthersFriendList
import com.socialplay.gpark.data.model.profile.recent.GameEntity
import com.socialplay.gpark.data.model.profile.recent.RecentPlayList
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.profile.request.RelationListRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveResponse
import com.socialplay.gpark.data.model.room.CanJoinRoom
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomList
import com.socialplay.gpark.data.model.room.CottageVisitor
import com.socialplay.gpark.data.model.room.GetRoomListResult
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.room.RoomStyle
import com.socialplay.gpark.data.model.room.RoomTemplate
import com.socialplay.gpark.data.model.sdk.AppAccessToken
import com.socialplay.gpark.data.model.sdk.AuthAppInfo
import com.socialplay.gpark.data.model.share.RelayData
import com.socialplay.gpark.data.model.share.RoBuxRecordInfo
import com.socialplay.gpark.data.model.task.DailyTaskInfo
import com.socialplay.gpark.data.model.task.FinishDailyTaskBody
import com.socialplay.gpark.data.model.user.AccessTokenRefreshResult
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.RedBadgeData
import com.socialplay.gpark.data.model.user.UserRelation
import com.socialplay.gpark.data.model.user.VisitorInfoApiResult
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.function.mw.bean.MWGameResourceRequest
import com.socialplay.gpark.function.mw.bean.MWGameResourceResponse
import com.socialplay.gpark.function.mw.bean.MWLaunchGameExpand
import com.socialplay.gpark.function.mw.bean.MWLaunchMgsInfo
import kotlinx.coroutines.flow.Flow

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */

interface IMetaRepository {
    suspend fun isMyPlayedGame(id: String): Boolean
    suspend fun updateMyGameInfo(info: GameDetailInfo, percent: Float)
    suspend fun getGameInfoCache(id: String): DataResult<GameDetailInfo>
    suspend fun deleteMyPlayedGame(id: String)
    suspend fun deleteMyPlayedGameList(ids: List<String>)
    fun fetchPlayedGame(size: Int): Flow<PagingData<MyPlayedGame>>
    fun fetchPlayedGameV2(index: Int = 0, size: Int): Flow<List<MyPlayedGame>>
    fun fetchRecentPlayedPgcCardList(pageSize: Int, pageNum: Int, uuid: String): Flow<PostCardResult>
    fun fetchRecentPlayedPgcCardListV2(pageSize: Int, pageNum: Int, uuid: String): Flow<PostCardResult>
    fun fetchFriendRequests(size: Int): Flow<PagingData<FriendRequestInfoWrapper>>
    fun fetchRecommendGames(size: Int): Flow<PagingData<GameItem>>
    fun fetchSearchGame(searchKey: String, isRelate: Boolean, size: Int, keywordId: String?): Flow<PagingData<SearchGameItem>>
    suspend fun fetchGameInfoById(id: String): Flow<DataResult<GameDetailInfo>>
    fun getGameDetailEnteredTimes(gameId: String): Long
    suspend fun onLaunchGame(gameId: String, packageName: String)
    suspend fun visitorLogin(loginType: LoginType): Flow<DataResult<VisitorInfoApiResult>>
    fun saveMetaUserInfo(metaUserInfo: MetaUserInfo)
    suspend fun refreshAccessToken(): AccessTokenRefreshResult?
    fun logout(notifyBackend: Boolean): Flow<DataResult<Boolean>>
    suspend fun insertGameDetailEntityIfNoExists(gameInfo: GameDetailEntity)
    fun showGuideDone()
    fun isNeedShowGuide(): Boolean

    // 获取用户信息
    suspend fun getMetaUserInfoFromNet(): Flow<DataResult<MetaUserInfo>>
    suspend fun bindByEmail(email: String, code: String): Flow<DataResult<Boolean>>
    suspend fun bindByParentEmail(parentEmail: String, code: String): Flow<DataResult<Boolean>>
    suspend fun bindEmailChange(
        newEmail: String,
        newEmailCode: String,
        oldEmailCode: String
    ): Flow<DataResult<Boolean>>

    suspend fun bindParentEmailChange(
        newEmail: String,
        newEmailCode: String,
        oldEmailCode: String
    ): Flow<DataResult<Boolean>>

    suspend fun bindAccountAndPassword(bindKey: String, password: String): Flow<DataResult<Boolean>>
    suspend fun bindPasswordByGparkId(bindKey: String, password: String): Flow<DataResult<Boolean>>

    suspend fun getImInfo(): DataResult<ImInfo>

    // 刷新user缓存
    suspend fun refreshUserInfoForNet()

    // 获取简单用户信息
    suspend fun getSimpleUserInfo(uuid: String): MetaSimpleUserEntity?

    // 获取指定targetId未读数量
    suspend fun getUnReadCount(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    )

    // 清除指定targetId未读状态
    suspend fun clearMessageUnReadStatus(
        conversationType: Conversation.ConversationType?,
        targetId: String?,
        callback: (ImUpdate) -> Unit?
    )

    // 删除指定targetId会话
    suspend fun removeConversation(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    )

    // 删除指定targetId消息
    suspend fun deleteMessages(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: (ImUpdate) -> Unit?
    )

    //同步未读状态
    fun syncConversationReadStatus(
        type: Conversation.ConversationType,
        targetId: String,
        timestamp: Long,
        callback: (Boolean) -> Unit?
    )

    // 设置指定targetId会话置顶
    suspend fun setConversationToTop(
        conversationType: Conversation.ConversationType,
        targetId: String,
        isTop: Boolean,
        callback: (ImUpdate) -> Unit?
    )

    // 更新im本地用户缓存
    suspend fun putUserInfoCache(userinfo: MetaSimpleUserEntity)

    //保存好友列表到本地
    suspend fun saveNewestFriendWithStateToLocal(friendMap: List<FriendInfo>)

    //从本都读取好友列表
    suspend fun getNewestFriendWithStateFromLocal(): DataResult<List<FriendInfo>>

    //获取二维码地址
    suspend fun getQrCode(): Flow<DataResult<String>>

    //搜索好友
    suspend fun searchFriends(
        clear: Boolean,
        keyword: String,
        pageSize: Int
    ): Flow<PagingData<FriendSearchInfo>>

    suspend fun fetchConversationListV2(
        seq: String? = null,
        count: Int = 20,
        conversationTypes: Set<ConversationType> = setOf(Conversation.ConversationType.PRIVATE, Conversation.ConversationType.GROUP),
    ): PagingResult<List<MetaConversation>>?

    suspend fun fetchAllConversationList(): List<MetaConversation>

    //根据UUID查询好友信息
    suspend fun queryFriendInfo(uuid: String): DataResult<FriendInfo>

    // 上报用户玩游戏心跳
    suspend fun updateUserPlayGame(gameId: String): Flow<DataResult<String?>>

    // 开始游戏（游戏切到前台）上报
    suspend fun updateUserStartGame(gameId: String)

    // 结束游戏（游戏切到后台）上报
    suspend fun updateUserStopGame(gameId: String)

    /* MGS */
    // 用uuid查询用户资料卡片
    suspend fun getPlayerInfoByUuId(
        packageName: String,
        uuid: String
    ): Flow<DataResult<MgsPlayerInfo>>

    // 从本地获取游戏信息
    suspend fun getMgsGameInfoFromLocal(packageName: String): GameDetailInfo?

    // 用mgs包名获取gameId
    fun getMgsGameIdByPackageName(packageName: String): String?
    suspend fun getGameInfoByGameIdWithoutFlow(gameId: String): DataResult<GameDetailInfo>

    //从远程获取游戏信息
    fun fetchGameInfoByIdFromRemoteWithCache(id: String): Flow<DataResult<GameDetailInfo>>

    // 扫描二维码
    suspend fun requestScanQRCode(url: String): DataResult<QRCodeAuthScanResult?>

    // 确认登录
    suspend fun confirmLogin(code: String): DataResult<Any>

    // 根据id搜索审核游戏 for developer
    fun searchReviewGameById(gameId: String): Flow<DataResult<ReviewGameInfo>>

    // 获取t台配置
    fun getTTaiConfigById(resourceId: Int): Flow<DataResult<TTaiConfig>>

    fun getTTaiConfigByIdV2(resourceId: Int): suspend () -> TTaiConfig

    // 获取多个t台配置
    fun getTTaiConfigByIds(ids: List<Int>): Flow<DataResult<List<TTaiConfig>>>

    fun getQrResultByUrl(url: String): Flow<DataResult<QrResult>>

    suspend fun submitNewFeedback(feedbackInfo: SubmitNewFeedbackRequest): Boolean

    fun sendEmail(mail: String?, scene: String): Flow<DataResult<Boolean>>
    fun checkEmail(email: String, code: String, scene: String): Flow<DataResult<Boolean>>
    fun passwordReset(email: String, code: String, newPassword: String): Flow<DataResult<Boolean>>
    fun passwordChange(oldPassword: String, newPassword: String): Flow<DataResult<Boolean>>

    // 使用账号注册新用户
    suspend fun accountSignup(
        account: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>>

    //登录
    suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>>
    suspend fun gparkIdLogin(gparkId: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    suspend fun updateUserInfo(
        birthday: Long? = null,
        gender: Int = 0,
        nickname: String? = null,
        portrait: String? = null,
        city: String? = null,
        signature: String? = null,
        reviewBirth: Boolean
    ): Flow<DataResult<Boolean>>

    suspend fun ditout(code: String): Flow<DataResult<Boolean>>

    // 查询游戏的评分
    suspend fun queryGameScore(resType: String, resId: String): Flow<DataResult<GameScoreResult?>>

    //发布游戏评论
    suspend fun publishGameReview(
        gameId: String,
        content: String,
        score: Int,
        moduleTypeCode: Int?
    ): Flow<DataResult<String?>>

    //    // 添加/更新评论 暂时用不到
    suspend fun updateGameReview(
        commentId: String,
        content: String,
        score: Int
    ): Flow<DataResult<Boolean>>

    // 添加评论点赞
    suspend fun attitudeGameReview(request: AttentionRequest): Flow<DataResult<Boolean>>

    // 查询我的评论
//    suspend fun fetchMyReview(gameId: String): Flow<DataResult<GameReviewData?>>

    // 根据uuid查询用户的评论
    suspend fun fetchAppraiseByUid(
        otherUid: String,
        gameId: String,
        modeType: Int? = MODULE_TYPE_GAME_ONLY
    ): Flow<DataResult<GameReviewResult?>>

    // 分页查询游戏的评论
//    suspend fun fetchAllReviewList(req: RequestGameReviewsParam): Flow<PagingData<GameAppraiseData>>

    // 分页查询其他人的评论
    suspend fun fetchAllReviewList(
        gameId: String,
        queryType: Int?,
        pageSize: Int,
        needLoadMore: Boolean = true,
        targetCommentId: String? = null,
        moduleTypeCode: Int? = null
    ): Flow<PagingData<GameAppraiseData>>

    suspend fun getAppraiseReplyList(request: AppraiseReplyListRequest): Flow<DataResult<AppraiseReplyListResult?>>

    suspend fun addAppraiseReply(request: AddAppraiseReplyRequest): Flow<DataResult<String?>>

    // 分页查询其他人的评论
//    suspend fun fetchReviewListByUuid(uid: String, pageNum: Int, pageSize: Int): Flow<PagingData<GameReviewData>>

    // 删除我的评论
//    suspend fun deleteMyGameReview(commentId: String): Flow<DataResult<Boolean>>

    // 删除评论
    suspend fun deleteGameAppraise(commentId: String): Flow<DataResult<Boolean>>

    suspend fun deleteReply(replyId: String): Flow<DataResult<Boolean>>

    // 置顶
    suspend fun topGameAppraise(commentId: String): Flow<DataResult<String>>
    fun topGameAppraiseV2(commentId: String, isTop: Boolean): suspend () -> String
    fun checkIfHaveComment(moduleType: String, moduleContentId: String): suspend () -> Boolean

    // 查询游戏点赞
    suspend fun fetchLikeByGameId(gameId: String): Flow<DataResult<SnsInfo>>

    //获取运营系统通知列表
    @Deprecated("接口已被服务器弃用, 改用getOperationNoticeListV2")
    suspend fun getOperationNoticeList(
        pageNum: Int,
        pageSize: Int
    ): Flow<DataResult<List<SystemNotification>>>

    //获取运营系统通知列表
    suspend fun getOperationNoticeListV2(): Flow<DataResult<List<UniJumpConfig>>>

    //获取最新一条运营系统通知
    @Deprecated("口已被服务器弃用, 改用getLatestOperationNoticeV2")
    suspend fun getLatestOperationNotice(): Flow<DataResult<SystemNotification?>>

    suspend fun getLatestOperationNoticeV2(): Flow<DataResult<UniJumpConfig>>

    //标记运营系统通知为已读
    suspend fun markOperationNoticeRead(systemNotification: UniJumpConfig): Flow<DataResult<Any>>


    //获取超级推荐位游戏ID列表
    suspend fun getSuggestGameIdList(): Flow<DataResult<List<String>>>

    //获取UG承接的超级推荐位游戏ID
    suspend fun getUgSuggestGameId(): Flow<DataResult<UGGameSuggestionInfo>>

    //获取超级推荐位游戏
    suspend fun getGameListByIds(gameIdList: List<String>): Flow<DataResult<List<GameSuggestionInfo>>>

    //超级推荐位游戏是否已经显示过
    suspend fun isSuggestGameAlreadyShow(): Boolean

    //设置超级推荐位游戏显示状态
    suspend fun updateSuggestGameShowStatus(isAlreadyShow: Boolean)

    //获取App启动次数
    fun getAppLaunchTimes(): Long

    suspend fun getChoiceCardList(): Flow<DataResult<ChoiceCardListApiResult>>

    //信息收集弹窗
    fun isInformationCollectDialogShown(): Boolean

    //更新信息收集弹窗显示状态
    fun updateInformationCollectDialogStatus(isShown: Boolean)

    // 获取ugc游戏id配置
    suspend fun getUgcGameConfig(): Flow<DataResult<UgcGameConfig>>

    // 获取首页ugc游戏列表
    fun getUgcGameList(pageSize: Int): Flow<PagingData<MultiTsGameResult>>

    // 获取我喜欢的ugc游戏列表
    fun getUgcGameLikeList(pageSize: Int): Flow<PagingData<UgcGameInfo.Games>>

    // 获取编辑器已发布列表
    suspend fun getEditorPublished(pageNum: String): Flow<DataResult<UgcGameInfo>>

    fun deleteEditorPublished(ugid: String): Flow<DataResult<Boolean>>

    fun deleteEditorPublishedV2(ugid: String): suspend () -> Boolean

    suspend fun getUgcTemplateList(
        pageSize: Int,
        sinceId: String?,
        currentPage: Int?
    ): Flow<DataResult<ArrayList<EditorTemplate>?>>

    // 判断ts游戏的id对应的ugc游戏、mgs功能
    suspend fun getTsTypeInfo(id: String): DataResult<TSTypeInfo>

    // 获取未读的通知数量
    fun getUnreadNoticeCount(type: Int): Flow<DataResult<Int>>

    // 标记所有的通知为已读
    suspend fun markAllNoticeAsRead(type: Int): DataResult<Boolean>

    // 获取移动编辑器通知
    fun getEditorNotice(pageSize: Int): Flow<PagingData<NoticeWrapper>>

    // 获取移动编辑器最新一条通知
    suspend fun getNewestEditorNotice(): Flow<DataResult<EditorNotice.OuterShowNotice?>>

    //获取MGS用户信息
    suspend fun getMgsUserInfo(gameId: String): Flow<DataResult<MgsUserInfo?>>

    suspend fun insertGameDetailCache(info: GameDetailEntity)

    //获取游戏房间列表（TS）
    suspend fun getGameRoomList(
        gameId: String,
        maxId: String = "0",
        pageSize: Int = 20,
        sortType: Int = 0,
        version: String = ""
    ): DataResult<GameRoomList>

    // 获取MGS场景相关功能配置
    suspend fun getMgsSceneConfig(gameId: String): Flow<DataResult<MgsSceneConfig>>

    // Mgs场景点赞
    fun mgsSceneLike(gameId: String): Flow<DataResult<Boolean>>

    // Mgs场景取消点赞
    fun mgsSceneUnLike(gameId: String): Flow<DataResult<Boolean>>

    // 通过gameId 从本地获取游戏信息, 如果没有从网络获取
    suspend fun getMgsGameInfoByGameId(gameId: String): GameDetailInfo?

    // 获取用户最近玩过游戏列表（支持客态）
    fun getRecentPlayGameList(uid: String, pageSize: Int): Flow<PagingData<GameEntity>>

    fun getRecentPlayGameListV2(uid: String, page: Int, pageSize: Int): Flow<DataResult<RecentPlayList>>

    fun getRecentPlayGameListV3(uid: String, pageSize: Int): Flow<PagingData<RecentPlayListV2Response.Game>>
    fun getRecentPlayGameListV4(uid: String, page: Int, pageSize: Int): Flow<DataResult<RecentPlayListV2Response>>
    fun getRecentPlayGameListMaverick(uid: String, page: Int, pageSize: Int): suspend () -> RecentPlayListV2Response?

    // 查询个人主页
    fun queryUserProfile(uid: String): Flow<DataResult<UserProfileInfo>>
    suspend fun getUserCreate(): Flow<DataResult<UserCreateInfo?>>

    // 领取徽章
    suspend fun receiveBadge(badgeCode: String): Flow<DataResult<Boolean>>

    // 客态好友列表
    fun getOthersFriendList(uid: String, pageNum: Int, pageSize: Int): Flow<DataResult<OthersFriendList?>>

    //app有效性校验
    suspend fun authAppCheck(appKey: String): Flow<DataResult<AuthAppInfo>>

    //获取app玩家登录的token
    suspend fun getAppAccessToken(appKey: String): Flow<DataResult<AppAccessToken>>

    /**
     * 获取MW拉起需要的MGS参数
     */
    fun getLaunchMWMgsInfo(gameId: String): Flow<DataResult<MWLaunchMgsInfo>>

    /**
     * 获取MW拉起需要的扩展参数
     */
    fun getLaunchMWGameExpand(gameCode: String, params: Map<String, String>): Flow<DataResult<MWLaunchGameExpand>>

    // 用户举报/评论举报
    suspend fun reportAdd(
        reportType: String,
        reportedId: String,
        reportContent: String?,
        reportedUid: String?,
        reportReason: String?,
        attachmentUrlList: List<String>?,
        reportSource: String?,
        reportAdditional: String?,
    ): Flow<DataResult<Boolean>>

    // 添加用户关系
    fun relationAdd(targetUid: String, relationType: String): Flow<DataResult<Boolean>>

    // 删除用户关系
    fun relationDel(targetUid: String, relationType: String): Flow<DataResult<Boolean>>

    // 添加用户关系
    fun relationAddV2(targetUid: String, relationType: String): suspend () -> Boolean

    // 删除用户关系
    fun relationDelV2(targetUid: String, relationType: String): suspend () -> Boolean

    // 关注
    fun followUser(targetUid: String): suspend () -> Boolean

    // 取消关注
    fun unfollowUser(targetUid: String): suspend () -> Boolean

    // 获取屏蔽关系
    suspend fun getBlockRelation(otherUuid: String): Flow<DataResult<UserRelation?>>

    fun payResultSubmit(map: Map<String, Any>): Flow<DataResult<SubmitResult>>

    fun privilegePlaceOrder(body: BodyRequestOrder): Flow<DataResult<TakeOderResult>>

    suspend fun rechargingLoop(orderId: String): Boolean

    // 获取ark余额
    suspend fun getBalance(coinType: CoinType): Flow<DataResult<UserBalance>>

    // 获取ark余额
    suspend fun getPoint(): Flow<DataResult<UserBalance>>

    // mw支付 Ark币兑换商品
    suspend fun mwPay(hashMap: Map<String, Any?>): Flow<DataResult<String?>>

    // 获取首页重要通知
    suspend fun getAvailableHomeNotice(): Flow<DataResult<HomeOperationNotification?>>

    // 设置首页重要通知关闭时间
    fun setHomeNoticeClose(noticeId: String, time: Long)

    // 选择默认角色形象
    fun chooseDefaultRole(id: String): Flow<DataResult<Boolean>>

    //获取TS房间信息
    suspend fun getTsGameRoomInfo(id: String, roomId: String): Flow<DataResult<GameRoomStatus>>

    // 获取ugc横幅列表
    suspend fun fetchUgcBannerList(): Flow<DataResult<List<UgcBannerInfo>?>>


    //获取会员信息
    fun getUserMemberInfoList(request: MemberRequest): Flow<DataResult<List<MemberInfo>>>

    // 用gameCode获取模板信息
    suspend fun fetchTemplateInfoByCode(gameCode: String): Flow<DataResult<EditorTemplate?>>

    fun saveCommunityLike(resId: String, option: Int, resType: Int): Flow<DataResult<Boolean>>

    fun report(postId: String, reportType: Int): Flow<DataResult<Boolean>>

    fun getPostOpinion(resId: String, resType: Int): Flow<DataResult<Int>>

    /**
     * 获取MW拉起需要的MGS参数
     */
    fun getLaunchMWMgsInfoWithCache(userUnique: String, gameId: String): Flow<DataResult<MWLaunchMgsInfo>>

    /**
     * 获取MW拉起需要的扩展参数
     */
    fun getLaunchMWGameExpandWithCache(gameCode: String, params: Map<String, String>): Flow<DataResult<MWLaunchGameExpand>>

    // 获取详情页运营位
    suspend fun getGameDetailNotice(gId: String): Flow<DataResult<List<UniJumpConfig>?>>

    // 获取角色飞轮运营位
    fun getRoleFlyWheel(): Flow<DataResult<List<UniJumpConfig>?>>

    // 获取kol飞轮运营位
    fun getKolFlyWheel(): Flow<DataResult<List<UniJumpConfig>?>>

    // 获取kol banner运营位
    fun getKolBanner(): Flow<DataResult<List<UniJumpConfig>?>>

    fun getUgcBannerList(): Flow<DataResult<List<UniJumpConfig>?>>

    fun getModuleBannerList(): Flow<DataResult<List<UniJumpConfig>?>>

    fun getProfileBannerList(): Flow<DataResult<List<UniJumpConfig>?>>

    /**
     * 检查私聊是否合法
     * @param content 内容
     * @param gameId 透传给服务器/审核平台的参数
     */
    suspend fun reviewPrivateMessageRisk(
        content: String,
        gameId: String?
    ): Flow<DataResult<ReviewTextRiskResult?>>

    /**
     * 检查房间聊天是否合法
     * @param content 内容
     * @param gameId 透传给服务器/审核平台的参数
     */
    suspend fun reviewRoomMessageRisk(
        content: String,
        gameId: String?
    ): Flow<DataResult<ReviewTextRiskResult?>>

    /**
     * 判断用户是否U13
     */
    suspend fun isUserAbove13(): Flow<DataResult<Boolean>>

    /**
     * 获取已发布创作列表
     */
    fun getPublishedCreationList(uuid: String?, orderId: String?): Flow<DataResult<UgcGameInfo>>

    /**
     * 获取已发布创作列表
     */
    fun getPublishedCreationListV2(uuid: String?, orderId: String?): suspend () -> UgcGameInfo

    /**
     * 获取已发布创作列表V3
     */
    fun getMyCreations(body: MyCreationsV3Request): suspend () -> UgcGameInfo

    /**
     * 置顶已发布游戏
     */
    fun pinMyCreations(gameId: String, gameType: Int, pinOrNot: Boolean): suspend () -> Any?

    // 用本地工程packageName获取ugcId
    suspend fun getUgcIdByPackageName(packageName: String): Flow<DataResult<String?>>

    // 获取作品发布状态
    suspend fun getEditorLocalStatus(bizIdList: List<String>): DataResult<EditorLocalStatusInfo>

    suspend fun getUgcInfoByIdLIst(ugids: List<String>): DataResult<UgcGameInfo?>

    fun getGameTemplate(crGameId: String, type: Long): Flow<DataResult<EditorTemplate>>

    fun getGameTemplateV2(crGameId: String, type: Long): suspend () -> EditorTemplate

    fun getUgcDetailPage(ugid: String): suspend () -> UgcDetailInfo

    fun getUgcGameDetail(ugid: String): suspend () -> UgcGameDetail

    fun getPgcGameDetail(gameId: String): suspend () -> PgcGameDetail

    suspend fun getFormworkList(page: Int, formworkCode: String?): Flow<DataResult<FormworkList>>

    suspend fun getFormWorkV4List(body: ReqFormWorkV4Body): Flow<DataResult<UgcFormWorkV4Data>>
    fun getFormWorkV4ListMvrk(body: ReqFormWorkV4Body): suspend () -> UgcFormWorkV4Data

    suspend fun checkFormWorkArchive(body: ReqFormWorkArchiveBody): Flow<DataResult<UgcFormWorkArchiveData>>

    fun checkFormWorkArchiveMvrk(body: ReqFormWorkArchiveBody): suspend () -> UgcFormWorkArchiveData?

    suspend fun reportLaunchUgcGame(ugid: String): DataResult<String>


    suspend fun getRandomNickname(): Flow<DataResult<String?>>

    fun getRandomNicknameV2(): suspend () -> String
    fun checkNickname(nickname: String, uid: String): suspend () -> Boolean

    suspend fun getHomeRoomList(
        gameIds: List<String>?,
        pageNum: Int,
        pageSize: Int
    ): DataResult<GetRoomListResult>

    @Throws
    suspend fun getHomeRoomListV2(
        pageNum: Int,
        pageSize: Int
    ): suspend () -> GetRoomListResult

    suspend fun getRoomInfo(
        roomId: String,
    ): Flow<DataResult<ChatRoomInfo>>

    fun canJoinRoom(roomId: String, version: String = ""): Flow<DataResult<CanJoinRoom>>

    suspend fun createRoom(
        roomStyle: RoomStyle,
        roomName: String,
        roomTag: String
    ): Flow<DataResult<ChatRoomInfo>>

    suspend fun getSysHeaderInfo(): Flow<DataResult<List<SysHeaderInfo>>>

    fun getSysActivitiesInfo(
        groupId: Long,
        pageSize: Int,
        subGroupKey: String? = null,
        lastRecordTime: Long? = null,
    ): Flow<PagingData<SysActivitiesInfo>>

    suspend fun getSysUnReadCount(): Flow<DataResult<Int>>

    /**
     * 获取意见反馈可选项
     */
    fun getFeedbackOptionList(): Flow<List<FeedbackConfigItem>?>

    /**
     * 获取意见反馈discord链接
     */
    fun getFeedbackDiscordLink(): suspend () -> String?


    // 上传用户全身照2
    fun modifyUserFullBodyImg(fullBodyImUrl: String): Flow<DataResult<Any?>>


    /**
     * 插入ugc最近玩过的游戏
     */
    fun insertUgcPlayedGame(entity: RecentUgcGameEntity, replaceIfExist: Boolean)

    /**
     * 插入ugc最近玩过的游戏
     */
    fun insertUgcPlayedGame(ugid: String, replaceIfExist: Boolean)

    /**
     * 获取帖子列表
     * @param orderType 帖子查询排序类型（1最新发布、2最新讨论、3用户点赞数、4最早发布、5用户访问量、6计算的score值、8标签热度排序） [com.socialplay.gpark.data.model.post.request.PostTagFeedRequest.ORDER_TYPE_NEWEST]
     * @param postTagType 帖子tag类型（1推荐、2关注、3 话题推荐、4 话题贴） [com.socialplay.gpark.data.model.post.request.PostTagFeedRequest.POST_TYPE_RECOMMEND]
     * @param tagId 话题id
     * @param blockId 版块ID
     * [mock]: https://mock.metaapp.cn/project/595/interface/api/43909
     */
    @Throws
    fun getCommunityFeed(orderType: Int, postTagType: Int?, tagId: Long?, blockId: Long?, pageSize: Int, pageNum: Int): suspend () -> CommunityFeedWrapper

    // 获取用户帖子列表
    fun getCommunityProfileFeed(otherUid: String, pageSize: Int, pageNum: Int): suspend () -> CommunityFeedWrapper

    fun getRandomPostTags(): suspend () -> List<PostTag>
    fun getRecommendPostTags(text: String): suspend () -> List<PostTag>

    @Deprecated(message = "use savePostV3 instead")
    suspend fun savePostV2(body: PublishPostRequest): DataResult<String>

    suspend fun savePostV3(body: PublishPostRequest): DataResult<PostPublishResult>
    suspend fun editPost(body: PublishPostRequest): DataResult<PostPublishResult>
    fun getPostDetailV2(postId: String): suspend () -> PostDetail
    fun deletePostV2(postId: String): suspend () -> Boolean

    fun getPostCommentListV2(body: PostCommentListRequestBody): suspend () -> PagingApiResult<PostComment>
    fun addPostComment(body: PostCommentRequestBody): suspend () -> CommentResultWrapper
    suspend fun addAIBotComment(body: PostCommentRequestBody): DataResult<String?>
    fun deletePostComment(commentId: String): suspend () -> Boolean
    fun deleteUgcComment(gameId: String, commentId: String): suspend () -> Boolean

    fun getPostReplyListV2(body: PostReplyListRequestBody): suspend () -> PagingApiResult<PostReply>
    fun addPostReply(body: PostReplyRequestBody): suspend () -> String
    fun deletePostReply(replyId: String): suspend () -> Boolean

    fun saveOpinion(body: OpinionRequestBody): suspend () -> Boolean

    fun queryUserMuteStatus(uid: String? = null): suspend () -> UserMuteStatus

    fun getRelationList(request: RelationListRequest): Flow<RelationListResult>

    fun getRelationCount(request: RelationCountRequest): Flow<RelationCountResult>
    fun getFriendCount(request: RelationCountRequest): Flow<DataResult<RelationCountResult>>

    fun getIDevelopedPgcList(request: MyCreationsV4Request): suspend () -> PostCardResult

    fun searchPostCardList(request: SearchPostCardRequest): suspend () -> PostCardResult

    fun getRecentUgcCardList(pageSize: Int): Flow<PostCardResult>

    suspend fun getAllPostTagList(): List<PostTag>

    suspend fun getGlobalCircleId(): String?

    fun getCottageRoomList(currentPage: Int): Flow<CottageRoomList?>


    fun getCottageRoomInfo(roomId: String): Flow<CottageRoomInfo?>
    fun getHomeVisitorCount(roomId: String): Flow<List<CottageVisitor>?>

    suspend fun getUserHouse(uuid: String): Flow<DataResult<House>>

    fun getNotificationSwitch(): Flow<DataResult<Map<String, Boolean>>>

    fun setNotificationSwitch(checked: Boolean): Flow<DataResult<Boolean>>

    fun getRoleList(): suspend () -> List<DefaultRoleInfo>

    fun getRoleListV2(): suspend () -> List<DefaultRoleInfo>

    fun getCardAll(cardId: String, offset: Int, pageSize: Int): suspend () -> CardAll

    fun plotTemplateLoveDo(templateId: String): Flow<Boolean>

    fun featPlotMainList(): suspend () -> PlotMainList
    fun featPlotAllList(body: PlotListBody): suspend () -> PlotTemplateList
    fun plotTemplateLoveDoV2(templateId: String, contentType: String): Flow<Boolean>
    fun momentTemplateList(body: MomentsTemplateBody): suspend () -> MomentsTemplate
    fun momentTemplateDelete(id: Long): suspend () -> Boolean

    fun fetchMyChatRoomPublishedList(lastId: String?): suspend () -> UgcGameInfo
    fun fetchChatRoomTemplateList(): suspend () -> RoomTemplate
    suspend fun getRecommend(offset: Int?): Flow<DataResult<Recommend>>

    // 获取设备最后登录账号的信息
    fun getContinueAccount(): Flow<DataResult<ContinueAccountInfo?>>

    fun getVideoFeedList(version: String?, offset: Int, size: Int, pinPostId: String?): Flow<VideoFeedApiResult>

    fun saveTrendingInsertGameInfo(gameInfo: ChoiceGameInfo)

    fun removeTrendingInsertGameInfo(gameId: String)

    suspend fun addTrendingInsertList(dataResultList: MutableList<ChoiceCardInfo>): MutableList<ChoiceCardInfo>

    fun visitPostOutfitCard(postId: String, roleId: String): suspend () -> Any?

    fun getEditorHomeDataList(): Flow<List<GroupedData<*>>>

    fun getChoiceCardListForAvatar(): Flow<DataResult<ChoiceCardListApiResult?>>
    fun getChoiceAiBotCardList(
        tagId: Int? = null,
        gender: Int? = null,
        pageIndex: Int, pageCount: Int
    ): Flow<DataResult<ChoiceCardListApiResult?>>

    fun getRecommendVideoList(
        pageIndex: Int,
        pageCount: Int,
        categoryId: Int,
        targetVideoId: String? = null,
        pinVideoId: String? = null,
    ): Flow<VideoFeedApiResult>

    fun getVideoPublishGuideStatus(): Flow<Boolean>

    fun setVideoPublishGuideShown(): Flow<Boolean>

    fun getVideoPublishCircleId(): Flow<String?>
    suspend fun getTripartiteInfo(orderId: String): Flow<DataResult<TripartiteInfo>>
    fun getSubsProduct(type: String): Flow<DataResult<List<SubscribeProductInfo>?>>

    suspend fun setPrivacySwitch(switch: PrivacySwitch): DataResult<Boolean>
    fun getPrivacySwitch(): suspend () -> PrivacySwitch

    fun changeTopicFollow(follow: Boolean, tagId: Long): suspend () -> Any?

    fun fetchMyFollowTopics(pageSize: Int, pageNum: Int): suspend () -> CommunityTopicBlockWrap

    fun fetchHotTopics(pageSize: Int, pageNum: Int): suspend () -> List<PostTag>?

    fun fetchTopicDetail(tagId: Long): suspend () -> TopicDetailInfo

    suspend fun addTopicViewCount(tagIds: List<String>): DataResult<Any?>
    fun commonAddPvCount(resType: String, resId: String): Flow<DataResult<Boolean?>>

    fun fetchBlockList(vis: Int? = null): suspend () -> List<CommunityBlock>?
    fun addAiTextMessage(
        sendUserId: String,
        receiverUserID: String,
        content: String,
        botInfo: BotInfo?,
        messageType: Int? = 1
    ): Flow<AIMessageEntity>

    fun addAiMessageList(list: ArrayList<AIMessageEntity>): Flow<List<AIMessageEntity>>
    fun updateAiBotConversation(
        targetId: String,
        uuid: String,
        messageId: Long?,
        aiMessageEntity: AIMessageEntity?
    ): Flow<Boolean>

    fun cleanAllAiBotMessageHistory(targetId: String, uuid: String): Flow<Boolean>
    fun getAiBotHistoryMessageList(
        targetId: String,
        uuid: String,
        pageNum: Int?,
        pageSize: Int
    ): Flow<ArrayList<AIMessageEntity>>

    fun getAllAiBotConversationList(
        uuid: String
    ): Flow<List<AIConversationEntity>>

    suspend fun getAiBotConversation(uuid: String, targetId: String): Flow<AIConversationEntity?>
    fun getConversationList(lastId: Int, uuid: String): Flow<List<AIConversationEntity>>
    fun getAiBotInfo(targetId: String): Flow<BotInfo?>
    fun followBot(targetId: String, follow: Boolean): Flow<DataResult<Boolean?>>
    fun getAiBotConversionList(map: Map<String, String>): Flow<DataResult<AiBotConversationResult?>>
    fun deleteAiBotConversion(botId: String, uuid: String): Flow<DataResult<Boolean?>>
    fun updateAiBotConversion(map: Map<String, String>): Flow<DataResult<Boolean?>>
    fun getFollowAiBotList(
        scrollId: String? = null,
        pageSize: Int,
        otherUuid: String? = null
    ): Flow<DataResult<AiBotFollowResult?>>

    suspend fun queryUnreadRedBadge(list: List<String>): Flow<DataResult<RedBadgeData>>

    suspend fun clearRedBadge(list: List<String>): Flow<DataResult<Boolean>>

    @Throws
    fun commonImageRiskCheck(url: String, authCode: String): suspend () -> RiskTaskQueryRequest

    @Throws
    fun queryImageRiskTask(jobId: String): suspend () -> RiskQueryResult

    fun getRoleStyleList(
        isMe: Boolean,
        otherUuid: String,
        beginIndex: Int,
        length: Int
    ): suspend () -> RoleStyleListResponse

    fun likeRoleStyle(styleId: String, isLike: Boolean): suspend () -> LikeRoleStyleResponse

    fun deleteRoleStyle(styleId: String): suspend () -> DeleteRoleStyleResponse
    fun getRoleBannerList(): Flow<List<UniJumpConfig>>


    /**
     * 获取角色弹窗运营位
     */
    fun getAvatarPopupOperationConfig(): Flow<DataResult<UniJumpConfig>>

    /**
     * 标记角色弹窗运营位已显示
     */
    fun setAvatarPopupOperationShowed(uniqueCode: String): Flow<DataResult<Unit>>

    fun postSearchReport(contentId: String, contentType: Int): Flow<DataResult<Any?>>

    suspend fun userAgentUpload(): Flow<DataResult<String?>>

    /**
     * 获取MW游戏需要下载的资源
     */
    fun getMWGameResource(request: MWGameResourceRequest): Flow<DataResult<List<MWGameResourceResponse>>>

    /**
     * 查询kol列表首页-部分数据
     */
    fun getKolFrameList(): Flow<DataResult<CreatorFrameResult>>

    /**
     * 查询kol列表首页-推荐ugc
     */
    fun getKolRecommendUgcGameListByPageV2(offset: Int?): Flow<DataResult<RecommendKolUgcResult>>

    /**
     * 查询kol列表首页-标签推荐ugc列表
     */
    fun getUgcGameListByTag(
        tagId: Int?,
        offset: Int?,
        pageSize: Int
    ): Flow<DataResult<RecommendUgcResult>>

    /**
     * 查询kol列表首页-所有ugc标签
     */
    fun getAllUgcPublishedTag(): Flow<DataResult<List<UgcPublishLabel>?>>

    /**
     * 查询kol列表首页-所有ugc标签
     */
    fun getKolCreatorListByTag(
        tagId: Int,
        sinceId: String?
    ): Flow<DataResult<RecommendCreatorResult>>

    /**
     * 查询kol更多关注ugc
     */
    fun getFollowedCreatorUgcList(pageNum: Int): suspend () -> FollowedCreatorUgcWrapper

    /**
     * 查询kol更多推荐ugc
     */
    fun getKolRecommendUgcGameListByPage(offset: Int?): suspend () -> RecommendKolUgcResult

    /**
     * 查询kol更多关注ugc
     */
    fun getTypeCreatorList(type: Int, sinceId: String?): suspend () -> TypeCreatorResult

    /**
     * 查询kol更多推荐ugc
     */
    fun getLabelCreatorList(tagId: Int, sinceId: String?): suspend () -> LabelCreatorResult

    /**
     * 获取好评状态
     */
    suspend fun queryPositiveComment(): DataResult<QueryPositiveComment>

    /**
     * 提交好评状态
     */
    fun submitPositiveComment(): Flow<DataResult<Boolean>>

    fun addLink(title: String, url: String, id: String, icon: String): Flow<DataResult<ProfileLinkInfo>>
    fun deleteLink(id: String): Flow<DataResult<Boolean>>
    suspend fun getStyleList(): Flow<DataResult<List<AIBotStyle>>>
    suspend fun generateAIBotImage(body: AIBotCreateRequest): Flow<DataResult<AIBotCreateResult?>>
    suspend fun generateAIBotImageResult(resId: String): DataResult<AIBotCreateImageResult?>
    suspend fun generateInfo(map: Map<String, String>): Flow<DataResult<AIBotGenerateInfo?>>
    suspend fun saveAIBotInfo(botInfo: BotInfoCreate): Flow<DataResult<BotInfo?>>
    suspend fun deleteAiMessage(messageId: Long): Flow<DataResult<Boolean?>>

    //查询分享中转数据
    suspend fun queryShareRelayData(ua: String, uniqueId: String? = null): DataResult<RelayData>

    fun createShare(request: CommonShareRequest): suspend () -> CommonShareResult

    fun insertShareRecord(shareRecordId: String, platform: String): Flow<Long>

    fun existShareRecord(shareRecordId: String): Flow<Boolean>
    fun resetAIBOTHistory(map: Map<String, String>): Flow<DataResult<Any?>>

    /**
     * 获取角色保存弹窗分享背景列表
     */
    fun getAvatarSaveShareBackgroundList(): Flow<List<AvatarShareCompositeBackground>>

    fun getUgcBackup(type: Int, fileId: String, gameIdentity: String): suspend () -> List<UgcBackupInfo>?

    suspend fun fetchAllCloudGames(type: Int): List<UgcCloudProject>

    suspend fun mergeCloudList(localList: MutableList<EditorCreationShowInfo>, type: Int)

    fun mergeCloudListFlow(localList: MutableList<EditorCreationShowInfo>, type: Int): Flow<MutableList<EditorCreationShowInfo>>

    suspend fun checkMaxCloud(type: Int): DataResult<Boolean>
    fun checkMaxCloudV2(type: Int): suspend () -> Boolean

    suspend fun deleteAllBackup(type: Int, fileId: String): DataResult<Boolean>
    fun deleteAllBackupV2(type: Int, fileId: String): suspend () -> Boolean

    suspend fun getMaxCloud(type: Int): DataResult<ProjectLimit>
    fun getMaxCloudV2(type: Int): suspend () -> ProjectLimit
    fun recordRoBux(): Flow<DataResult<RoBuxRecordInfo>>

    suspend fun queryDailyTaskInfo(): Flow<DataResult<DailyTaskInfo?>>
    suspend fun finishDailySign(body: FinishDailyTaskBody): Flow<DataResult<Boolean>>
    fun getGameDetailOperationInfo(
        biz: String,
        bizId: String,
        pageNum: Int,
        pageSize: Int
    ): suspend () -> OperationList

    fun resolveQrCode(body: QrCodeResolveApiRequest): Flow<DataResult<QrCodeResolveApiResponse?>>
    fun processAuthLogin(
        dataResult: DataResult<AuthInfoApiResult>,
        loginType: LoginType,
        loginWay: LoginWay,
        oAuthToken: String
    ): Flow<LoginState<MetaUserInfo>>

    suspend fun getCoinsRecord(lastCoinsRecordId: Long?, pageSize: Int): DataResult<CoinsRecords>
    fun getModuleGuideStatus(): Flow<DataResult<Int>>

    fun qrCodeCreateSuspend(body: QrCodeCreateRequest): suspend () -> QrCodeCreateResponse
    fun qrCodeCreateFlow(body: QrCodeCreateRequest): Flow<DataResult<QrCodeCreateResponse>>
    fun qrCodeResolveSuspend(url: String): suspend () -> QrCodeResolveResponse
    fun qrCodeResolveFlow(url: String): Flow<DataResult<QrCodeResolveResponse>>

    fun checkCreateGroupPower(): Flow<Boolean>
    suspend fun createGroupChat(members: List<String>): DataResult<GroupChatDetailInfo>
    fun getGroupChatDetailInfo(chatGroupId: Long): Flow<DataResult<GroupChatDetailInfo>>
    suspend fun getGroupChatInfoList(request: MgsGroupChatListRequest): DataResult<GroupChatInfoPage>
    suspend fun applyJoinGroupChat(request: MgsGroupChatApplyJoinRequest): DataResult<Boolean>
    suspend fun joinGroupChat(groupId: Long): DataResult<GroupChatAddMembers>
    suspend fun getGroupChatPendingRequestList(request: MgsGroupApplyPageRequest): DataResult<GroupChatApplyInfoList>
    suspend fun editGroupChat(request: MgsGroupChatEditRequest): DataResult<GroupChatDetailInfo>
    suspend fun leaveGroupChat(chatGroupId: Long): DataResult<Boolean>
    suspend fun disbandGroupChat(chatGroupId: Long): DataResult<Boolean>
    suspend fun editGroupChatNotification(chatGroupId: Long, enableNotifications: Boolean): DataResult<Boolean>
    suspend fun processApplyJoinGroupChat(askId: Long, accept: Boolean): DataResult<Boolean>
    suspend fun getAllGroupChatMembers(
        chatGroupId: Long,
        forceLoad: Boolean? = null
    ): DataResult<List<GroupChatMemberInfo>>

    suspend fun removeGroupChatMember(chatGroupId: Long, memberId: String): DataResult<Boolean>
    suspend fun inviteGroupChatMembers(
        chatGroupId: Long,
        memberIdList: List<String>
    ): DataResult<GroupChatAddMembers>

    suspend fun getGroupIdByImIds(imIds: List<String>): Map<String, Long>
    fun getGroupChatCount(): Flow<DataResult<GroupChatCount>>
    suspend fun getGroupChatPendingRequestCount(): DataResult<Int>

    /**
     * 获取游戏点赞用户列表
     * @param moduleType 模块类型，固定为"game"
     * @param moduleContentId 模块内容ID，即gameId
     * @param offset 偏移量，第一次请求为null，后续请求使用上一页最后一个元素的offset
     * @param pageSize 每页数量
     */
    fun getLikePlayerList(moduleType: String, moduleContentId: String, offset: Int?, pageSize: Int, expectSize: Int?): Flow<DataResult<LikeAndPlayerListData>>

    /**
     * 获取游戏游玩用户列表
     * @param gameId 游戏ID
     * @param offset 偏移量，第一次请求为null，后续请求使用上一页最后一个元素的offset
     * @param pageSize 每页数量
     */
    fun getFlowerPlayerList(gameId: String, offset: Int?, pageSize: Int, expectSize: Int?): Flow<DataResult<LikeAndPlayerListData>>

    /**
     * 获取送花排行榜
     */
    fun queryFlowerLeaderboard(gameId: String): Flow<DataResult<FlowerLeaderboardData>>

    fun getFlowerGifts(): Flow<DataResult<List<GiftFlower>>>

    suspend fun getSendGiftConditions(gameId: String): DataResult<SendGiftConditionsInfo>
    suspend fun switchSendGift(gameId: String, giveaway: Boolean): DataResult<Int>
    suspend fun getSendGiftUserList(gameId: String, count: Int = 15): DataResult<List<SendGiftData>>

    fun getGroupChatCountByUuid(uuid: String): suspend () -> GroupChatCount
}