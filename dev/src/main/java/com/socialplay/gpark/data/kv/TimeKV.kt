package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV
import java.util.*
import java.util.concurrent.TimeUnit

class TimeKV(private val mmKV: MMKV) {
    companion object {
        private const val INSTALLED_PKG_LIST_REPORT_TIME = "installed_pkg_list_report_time"
        private const val REFRESH_RECOMMEND_FILL_IN_ON_USER_PKG_UPLOADED =
            "refresh_recommend_fill_in_on_user_pkg_uploaded"
        private const val CHECK_LOG_OFF_TIME = "check_log_off_time"
        const val DAY_OPEN_APP = "day_open_app"
    }

    val reportedInstalledPkgListDayOnce: Boolean
        get() = dayOnce(INSTALLED_PKG_LIST_REPORT_TIME)
    val refresh_recommend_fill_in_on_user_pkg_uploadedDayOnce: Boolean
        get() = dayOnce(REFRESH_RECOMMEND_FILL_IN_ON_USER_PKG_UPLOADED)

    /**
     * 同一个key的情况下，一天只会在第一次返回true，后面就都是false。隔天之后第一次依然是true了，
     * 这个是自然日，不是24小时
     * 超过一天，甚至多天，也是第一次为true
     *
     * @param key 只走一次的key
     * @return true：是今天第一次 false：不是第一次
     */
    fun dayOnce(key: String): Boolean {
        val timeStampKey: String = getDayOnceSpKey(key)

        // 上次保存的时间
        val lastTimestamp: Long = mmKV.getLong(timeStampKey, 0L)
        // 如果第一次进来，直接返回 true
        if (lastTimestamp == 0L) {
            // 保存时间
            mmKV.putLong(timeStampKey, System.currentTimeMillis())
            return true
        }
        val todayCalendar = Calendar.getInstance()
        val lastTimeCalendar = Calendar.getInstance()
        lastTimeCalendar.timeInMillis = lastTimestamp

        // 判断是否是同一天
        val isToday =
            todayCalendar[Calendar.YEAR] == lastTimeCalendar[Calendar.YEAR] && todayCalendar[Calendar.MONTH] == lastTimeCalendar[Calendar.MONTH] && todayCalendar[Calendar.DATE] == lastTimeCalendar[Calendar.DATE]
        return if (isToday) {
            false
        } else {
            // 保存时间
            mmKV.putLong(timeStampKey, System.currentTimeMillis())
            true
        }
    }

    private fun getDayOnceSpKey(key: String): String {
        return "day_once_by_timestamp_$key"
    }


    /**
     * 大于两天可以请求位置权限
     */
    fun canRequestLocation():Boolean{
        val twoDaysMills = TimeUnit.DAYS.toMillis(2)
        return System.currentTimeMillis() - mmKV.getLong("request_location_time",0) > twoDaysMills
    }

    /**
     * 保存请求位置权限时间
     */
    fun saveRequestLocationTime() {
        mmKV.putLong("request_location_time", System.currentTimeMillis())
    }
}