package com.socialplay.gpark.data.model.user

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ContinueAccountInfo(
    //用户昵称
    val nickname: String? = null,
    //用户头像
    val portrait: String? = null,
    //是否是游客账号
    val visitor: Boolean = false,
    // Account/邮箱
    var loginKey: String? = null,
    // 登录方式 google facebook apple account visitor  com.socialplay.gpark.data.model.user.ThirdLoginRequestKt.LOGIN_TYPE_FACEBOOK
    val loginType: String?,
    // 登录时间
    val loginTime: Long,
    val uuid: String
) : Parcelable {
}