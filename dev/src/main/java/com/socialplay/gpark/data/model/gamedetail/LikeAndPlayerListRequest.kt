package com.socialplay.gpark.data.model.gamedetail

/**
 * 点赞和游玩列表请求参数
 */
data class LikeAndPlayerListRequest(
    val moduleType: String? = null,  // 新增字段，用于getLikePlayerList接口
    val moduleContentId: String? = null,  // 新增字段，用于getLikePlayerList接口
    val gameId: String? = null,  // 修改为可空，用于getFlowerPlayerList接口
    val offset: Int? = null,  // 新增字段，替代pageNum
    val expectSize: Int? = 50,  // 游玩数量，从外部获取的是多少
    val pageSize: Int
)
