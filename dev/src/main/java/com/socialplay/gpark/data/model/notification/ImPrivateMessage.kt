package com.socialplay.gpark.data.model.notification

import android.content.Context
import com.ly123.tes.mgs.im.ImMessageHelper
import com.ly123.tes.mgs.metacloud.message.CustomShareTopicMessage
import com.ly123.tes.mgs.metacloud.message.ImageMessage
import com.ly123.tes.mgs.metacloud.message.InformationNotificationMessage
import com.ly123.tes.mgs.metacloud.message.InviteMessage
import com.ly123.tes.mgs.metacloud.message.PostCardMessage
import com.ly123.tes.mgs.metacloud.message.StrangerMessage
import com.ly123.tes.mgs.metacloud.message.TextMessage
import com.ly123.tes.mgs.metacloud.model.Message

import com.ly123.tes.mgs.metacloud.model.MessageContent
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.model.FloatNoticeShowData
import com.socialplay.gpark.data.model.mgs.CmdMgsInviteDataMessage
import com.socialplay.gpark.data.model.mgs.MgsInviteData
import com.socialplay.gpark.ui.view.floatnotice.FloatNoticeView.Companion.TYPE_V2
import org.koin.core.module._factoryInstanceFactory

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/07/24
 *     desc   :
 *
 */
data class ImPrivateMessage(
    val portrait: String,
    val nickname: String,
    val sendUserId:String,
    val messageId:String,
    val content:String,
    var receiveTime: Long
) {
    fun toShowData(context: Context): FloatNoticeShowData {
        return FloatNoticeShowData(
            icon = this.portrait,
            title = this.nickname,
            inviteText = content,
            contentText = content,
            agreeText = context.getString(R.string.im_tip_reply),
            gameId = "",
            gameName =  "",
            packageName = "",
            isClickDismiss = false,
            imType =  FloatNoticeInteractor.TYPE_IM_PRIVATE_MESSAGE,
            showTime =  System.currentTimeMillis()
        )
    }

    companion object{
        fun getSummerContent(messageContent: MessageContent?, context: Context): CharSequence {
            if (messageContent == null) {
                return ""
            }
            val messageProvider =
                ImMessageHelper.getInstance().getMessageTemplate(messageContent.javaClass)
            if (messageProvider != null) {
                val content = messageProvider.getContentSummary(context, messageContent)
                content?.let {
                    return content.toString().replace("\n", " ")
                }
            }
            return context.getString(R.string.im_unknown_content)
        }
        fun isSupportMessage(message: Message): Boolean {
            if (message.messageType == Message.MessageType.UNKNOWN) {
                //自定义消息
                return false
            }
            when (message.content) {
                is InformationNotificationMessage -> {
                    //通知类型
                    return false
                }
                is InviteMessage -> {
                    //通知类型
                    return false
                }

                is StrangerMessage -> {
                    //陌生人消息
                    return false
                }

                else         -> {
                    val messageProvider = ImMessageHelper.getInstance().getMessageTemplate(message.content.javaClass)
                    return messageProvider != null
                }
            }
        }
    }
}
