package com.socialplay.gpark.data.model

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.IdRes
import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.function.mw.MWEngineEnvironment
import com.socialplay.gpark.util.extension.getEnvValue
import com.tencent.mmkv.MMKV
import org.koin.core.context.GlobalContext


@Keep
enum class DevEnvType(val value: String) {
    Dev("Dev"), Test("Test"), Pre("Pre"), Online("Online");

    companion object {
        fun create(value: String): DevEnvType? {
            return values().find { it.value == value }
        }
    }
}

enum class DevItemType {
    Space, Group, Jump, Env, ChangedGlobalEnv, BooleanSelect, SingleSelect, Action, MetaAd
}

sealed class DeveloperItem(open val devItemType: DevItemType) {
    val itemType: Int
        get() = devItemType.ordinal
}

data class SpaceItem(
    val heightDp: Float = 5f,
    @ColorRes val colorRes: Int = R.color.color_account_line
) : DeveloperItem(DevItemType.Space) {
    companion object {
        val DIVIDER = SpaceItem(heightDp = 0.5f)
        val GraySpace = SpaceItem(heightDp = 15f, colorRes = R.color.neutral_color_5)
    }
}

data class GroupItem(val name: String) : DeveloperItem(DevItemType.Group)

data class JumpItem(
    val name: String,
    @IdRes val navId: Int,
    val clickAction: ((Fragment) -> Unit)? = null
) : DeveloperItem(DevItemType.Jump)

data class SelectEnvItem(
    val name: String,
    val curEnvType: DevEnvType,
    val selectMap: Map<DevEnvType, String>,
    val mmkvKey: String,
    val mmkv: MMKV,
    override val devItemType: DevItemType = DevItemType.Env,
    val onEnvTypeChanged: (DevEnvType) -> Unit,
) : DeveloperItem(devItemType) {

    val curValue = mmkv.getString(mmkvKey, null) ?: selectMap[curEnvType]!!.also { mmkv.putString(mmkvKey, it) }

    companion object {
        val context by lazy { GlobalContext.get().get<Context>() }
        fun GlobalEnv(
            curEnvType: DevEnvType,
            mmkvKey: String,
            mmkv: MMKV,
        ): SelectEnvItem {
            val selectMap = mapOf(
                DevEnvType.Dev to DevEnvType.Dev.name,
                DevEnvType.Test to DevEnvType.Test.name,
                DevEnvType.Pre to DevEnvType.Pre.name,
                DevEnvType.Online to DevEnvType.Online.name,
            ).filter { it.key.name in BuildConfig.ENV_SCOPE }
            return SelectEnvItem(
                context.getString(R.string.debug_change_global_env),
                curEnvType,
                selectMap,
                mmkvKey = mmkvKey,
                mmkv = mmkv,
                DevItemType.ChangedGlobalEnv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]) }
            )
        }

        fun BaseUrlEnv(
            curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV, selectMap: Map<DevEnvType, String>
        ): SelectEnvItem {
            return SelectEnvItem(
                context.getString(R.string.debug_change_base_url),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]!!) }
            )
        }

        fun PandoraEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): DeveloperItem {
            val selectMap = BuildConfig.ENV_SCOPE
                .mapNotNull { DevEnvType.create(it) }
                .map { it.value }

            return SingleSelectConfigItem(
                context.getString(R.string.debug_pandora),
                mmkv.getString(mmkvKey, BuildConfig.PD_ENV_TYPE) ?: "",
                selectMap,
                SingleSelectConfigItem.ACTION_RESTART,
                onChanged = { mmkv.putString(mmkvKey, it) }
            )
        }

        fun ModAdDexEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): SelectEnvItem {
            val selectMap = mapOf(
                DevEnvType.Dev to DevEnvType.Test.name,
                DevEnvType.Test to DevEnvType.Test.name,
                DevEnvType.Pre to DevEnvType.Pre.name,
                DevEnvType.Online to DevEnvType.Online.name,
            )
            return SelectEnvItem(
                context.getString(R.string.debug_ad_mod),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]!!) }
            )
        }

        fun MGSEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): SelectEnvItem {
            val selectMap = mapOf(
                DevEnvType.Dev to DevEnvType.Dev.name,
                DevEnvType.Test to DevEnvType.Test.name,
                DevEnvType.Pre to DevEnvType.Pre.name,
                DevEnvType.Online to DevEnvType.Online.name,
            )
            return SelectEnvItem(
                context.getString(R.string.debug_mgs),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]!!) }
            )
        }

        fun MWHotfixUrlEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): SelectEnvItem {

            val selectMap = BuildConfig.ENV_SCOPE
                .mapNotNull { DevEnvType.create(it) }
                .associateWith { BuildConfig.MW_CORE_URL.getEnvValue(it.value) }

            return SelectEnvItem(
                context.getString(R.string.debug_mw_hotfix),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]!!) }
            )
        }

        fun MWRoomUrlEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): SelectEnvItem {

            val selectMap = BuildConfig.ENV_SCOPE
                .mapNotNull { DevEnvType.create(it) }
                .associateWith { BuildConfig.MW_ROOM_URL.getEnvValue(it.value) }

            return SelectEnvItem(
                context.getString(R.string.debug_mw_room),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { mmkv.putString(mmkvKey, selectMap[it]!!) }
            )
        }

        fun MWEngineEnv(curEnvType: DevEnvType, mmkvKey: String, mmkv: MMKV): SelectEnvItem {

            var selectMap: Map<DevEnvType, String>
            var defaultEnv: String = ""
            if (!EnvConfig.isParty()) {
                selectMap = mutableMapOf(
                    DevEnvType.Dev to "dev-oversea",
                    DevEnvType.Test to "test-oversea",
                    DevEnvType.Pre to "pre-oversea",
                    DevEnvType.Online to "online-oversea",
                )
                defaultEnv = "online-oversea"
            } else {
                selectMap = mutableMapOf(
                    DevEnvType.Dev to "Dev",
                    DevEnvType.Test to "Test",
                    DevEnvType.Pre to "Pre",
                    DevEnvType.Online to "Online",
                )
                defaultEnv = "Online"
            }

            return SelectEnvItem(
                context.getString(R.string.debug_mw_engine),
                curEnvType,
                selectMap,
                mmkvKey,
                mmkv,
                onEnvTypeChanged = { envType ->
                    MWEngineEnvironment.set(selectMap[envType] ?: defaultEnv) {
                        if (it) mmkv.putString(mmkvKey, selectMap[envType]!!)
                    }
                }
            )
        }
    }
}

data class BooleanSelectConfigItem(
    val name: String,
    val isTrue: Boolean,
    val type: Int = NORMAL,
    val enable: Boolean = true,
    val onChanged: (Boolean) -> Unit
) : DeveloperItem(DevItemType.BooleanSelect) {
    companion object {
        const val NORMAL = 0
        const val SHOW_EVENT_DIALOG = 2
    }
}

data class SingleSelectConfigItem(
    val name: String,
    val curSelectTxt: String,
    val selectItems: List<String>,
    val action: Int = ACTION_NONE,
    val onChanged: (String) -> Unit
) : DeveloperItem(DevItemType.SingleSelect) {
    companion object {
        const val ACTION_NONE = 1
        const val ACTION_CLEAR_USER = 1 shl 1
        const val ACTION_RESTART = 1 shl 2
    }

    fun isClearUser(): Boolean {
        return action and ACTION_CLEAR_USER == ACTION_CLEAR_USER
    }

    fun isRestart(): Boolean {
        return action and ACTION_RESTART == ACTION_RESTART
    }
}

data class ActionItem(
    val name: String,
    val type: Int,
) : DeveloperItem(DevItemType.Action) {

    companion object {
        const val TYPE_EXPORT_LEAK_FILE = 1
    }

    fun isExportLeakFile(): Boolean {
        return type == TYPE_EXPORT_LEAK_FILE
    }
}

data class MetaAdItem(
    val name: String,
    val type: Int,
) : DeveloperItem(DevItemType.MetaAd) {
    companion object {

        const val APPLOVIN_DEBUGGER = -1
        const val SHOW_BANNER = -2
        const val SHOW_INTERSTITIAL = -3
        const val SHOW_REWARDED = -4
    }
}