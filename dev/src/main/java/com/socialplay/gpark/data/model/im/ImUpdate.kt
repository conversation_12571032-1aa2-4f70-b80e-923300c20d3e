package com.socialplay.gpark.data.model.im

import com.ly123.tes.mgs.metacloud.model.Conversation


/**
 * @author: ning.wang
 * @date: 2021-06-28 2:01 下午
 * @desc:
 */
data class ImUpdate(val updateType: ImUpdateType, val conversationType: Conversation.ConversationType, val targetId: String, val value: Any, val msg: String? = "")

enum class ImUpdateType {
    SET_TOP,
    CLEAR_UN_READ,
    GET_UN_READ_COUNT,
    REMOVE,
    DELETE_MESSAGE,
    UPDATE_USER
}