package com.socialplay.gpark.data.interactor

import android.os.ConditionVariable
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bin.cpbus.CpEventBus
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.mgs.data.model.MgsGameNoticeEvent
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.*
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.account.UpdatePrivacySettingsEvent
import com.socialplay.gpark.data.model.editor.Balance
import com.socialplay.gpark.data.model.editor.RoleData
import com.socialplay.gpark.data.model.editor.SparkBalance
import com.socialplay.gpark.data.model.editor.UpdateRoleDataEvent
import com.socialplay.gpark.data.model.event.MgsUserInfoUpdateEvent
import com.socialplay.gpark.data.model.guide.UgcModuleGuidInfo
import com.socialplay.gpark.data.model.guide.UgcModuleGuideEvent
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.*
import com.socialplay.gpark.function.account.ProcessUserInfoChangeEvent
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.mgs.GameModEventConst
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.startup.StartupProcessType.H
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.ui.realname.RealName
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.OneTimeSingleOwnerCallback
import com.socialplay.gpark.util.extension.collectWithTimeout
import com.socialplay.gpark.util.toJSON
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.*

/**
 * Created by yaqi.liu on 2021/5/12
 * 用户信息相关逻辑功能
 */
typealias TokenChangedCallback = (old: String?, new: String?) -> Unit
typealias UserChangedCallback = (old: MetaUserInfo?, new: MetaUserInfo?) -> Unit
typealias PrivacySwitchChangeCallback = (privacySwitch: MetaUserInfo) -> Unit
typealias ModuleGuideStatusChangeCallback = (status: Int) -> Unit

abstract class BaseAccountInteractor(val metaRepository: IMetaRepositoryWrapper, val metaKV: MetaKV) {

    protected val _accountLiveData = MutableLiveData<MetaUserInfo?>()
    public val accountLiveData: LiveData<MetaUserInfo?> = _accountLiveData

    private val _accessTokenLiveData = MutableLiveData<String?>()
    val accessTokenLiveData: LiveData<String?> = _accessTokenLiveData

    // 用户红点相关
    private val _badgeLiveData = MutableLiveData<RedBadgeData?>()
    val badgeLiveData: LiveData<RedBadgeData?> = _badgeLiveData

    private val _sparkBalanceLiveData = MutableLiveData<SparkBalance>()
    val sparkBalanceLiveData: LiveData<SparkBalance> = _sparkBalanceLiveData

    // token改变回调
    private val tokenCallbacks = LifecycleCallback<TokenChangedCallback>()

    // metaUserInfo改变回调
    private val userCallbacks = LifecycleCallback<UserChangedCallback>()

    private var isMainProcess = false

    // 红点
    private var redBadgeJob: Job? = null
    private val redBadgeScope = MainScope()

    private val privacySwitchCallbacks = LifecycleCallback<PrivacySwitchChangeCallback>()

    var enableFriendRequestsNotice = false
        private set

    private var _showCommentPinRedDot: Boolean? = null
    var showCommentPinRedDot: Boolean
        get() {
            if (_showCommentPinRedDot == null) {
                _showCommentPinRedDot = metaKV.account.showCommentPinRedDot
            }
            return _showCommentPinRedDot == true
        }
        set(value) {
            _showCommentPinRedDot = value
            metaKV.account.showCommentPinRedDot = value
        }

    private val accountHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: android.os.Message) {
            super.handleMessage(msg)
            when (msg.what) {
                MSG_RETRY_TOKEN_REFRESH -> {
                    Timber.tag(CheckTokenInterceptor.TAG).d("accountHandler initiative refresh accessToken")
                    GlobalScope.launch(Dispatchers.IO) {
                        refreshAccessToken(metaKV.account.accessToken)
                    }
                }
            }
        }
    }

    private val userChangedCallback = { old: MetaUserInfo?, new: MetaUserInfo? ->
        if (old?.uuid != new?.uuid) {
            getAllRedAllBadgeData()
        }
    }

    val hasBindAccPwd get() = _accountLiveData.value?.hasBindAccPwd == true

    private var ugcDesignDetailGuideInit = true
    var ugcDesignDetailGuide: Boolean = false
        get() {
            if (ugcDesignDetailGuideInit) {
                ugcDesignDetailGuideInit = false
                val temp = metaKV.account.ugcDesignDetailGuide
                field = temp
                return temp
            }
            return field
        }
        set(_) {
            if (field) return
            field = true
            metaKV.account.ugcDesignDetailGuide = true
        }

    private var ugcModelDetailGuideInit = true
    var ugcModelDetailGuide: Boolean = false
        get() {
            if (ugcModelDetailGuideInit) {
                ugcModelDetailGuideInit = false
                val temp = metaKV.account.ugcModelDetailGuide
                field = temp
                return temp
            }
            return field
        }
        set(_) {
            if (field) return
            field = true
            metaKV.account.ugcModelDetailGuide = true
        }

    private var ugcModuleHomeGuideInit = true
    var ugcModuleHomeGuide: Boolean = false
        get() {
            if (ugcModuleHomeGuideInit) {
                ugcModuleHomeGuideInit = false
                val temp = metaKV.account.ugcModuleHomeGuide
                field = temp
                return temp
            }
            return field
        }
        set(_) {
            if (field) return
            field = true
            metaKV.account.ugcModuleHomeGuide = true
        }

    private var assetFirstInteractInit = true
    var assetFirstInteract: Boolean = true
        get() {
            if (assetFirstInteractInit) {
                assetFirstInteractInit = false
                field = metaKV.account.assetFirstInteract
            }
            val temp = field
            if (temp) {
                field = false
                metaKV.account.assetFirstInteract = false
            }
            return temp
        }

    private val moduleGuideStatusCallbacks =
        OneTimeSingleOwnerCallback<ModuleGuideStatusChangeCallback>()
    var moduleGuideStatus: Int = MODULE_GUIDE_STATUS_NEED_INIT
        get() {
            if (field == MODULE_GUIDE_STATUS_NEED_INIT) {
                field = metaKV.account.moduleGuideStatus
                GsonUtil.gsonSafeParseCollection<UgcModuleGuidInfo>(metaKV.tTaiKV.moduleGuideConfig)
                    ?.let {
                        moduleGuideLabelId = it.badgeId
                    }
            }
            if (field < MODULE_GUIDE_STATUS_TODO) {
                getModuleGuideStatus()
            }
            return field
        }
        set(value) {
            if (field != value) {
                field = value
                metaKV.account.moduleGuideStatus = value
            }
            moduleGuideStatusCallbacks.dispatch { invoke(field) }
        }

    var moduleGuideLabelId: Long = 0L
    private var moduleGuideStatusLoading = false

    var moduleGuideShowTime: Long = -1L
        get() {
            if (field == -1L) {
                field = metaKV.account.moduleGuideShowTime
            }
            return field
        }
        set(value) {
            if (field == -1L) {
                field = metaKV.account.moduleGuideShowTime
            }
            if (value <= field) return
            field = value
            metaKV.account.moduleGuideShowTime = value
        }

    companion object {
        private const val MSG_RETRY_TOKEN_REFRESH = 2

        // accessToken主动刷新时间阈值
        const val ACCESS_TOKEN_REFRESH_INTERVAL = 1 * 60 * 1000

        // 红点类型
        const val TYPE_OC_SHORTS_NEW_TEMPLATE = "newTemplate"
        const val TYPE_ROLE_UPDATE = "dressResource"
        const val TYPE_NEW_FOLLOWER = "newFollower"

        // 需要初始化
        const val MODULE_GUIDE_STATUS_NEED_INIT = -3
        // 无状态
        const val MODULE_GUIDE_STATUS_INIT = -2
        // 请求接口失败
        const val MODULE_GUIDE_STATUS_API_FAIL = -1
        // 接口-未完成
        const val MODULE_GUIDE_STATUS_TODO = 0
        // 游戏-已完成
        const val MODULE_GUIDE_STATUS_GAME = 1
        // 已展示新人专区
        const val MODULE_GUIDE_STATUS_NEWBIE = 2
        // 已完成循环引导
        const val MODULE_GUIDE_STATUS_CYCLE = 3
        // 用户-不再提示
        const val MODULE_GUIDE_STATUS_SKIP_USER = 100
        // 接口-已完成
        const val MODULE_GUIDE_STATUS_API_DONE = 200

        const val MODULE_GUIDE_INVOKE_LIKE = 1
        const val MODULE_GUIDE_INVOKE_GET = 2
        const val MODULE_GUIDE_INVOKE_COMMENT = 3
    }

    init {
        registerHermesEventBus()
    }

    /**
     * 初始化用户变化的红点监听，仅主进程
     */
    private fun initUserRedBadge() {
        addUserChangedCallback(userChangedCallback)
    }

    /**
     * 获取全部红点业务数据
     */
    private fun getAllRedAllBadgeData() {
        val types = mutableListOf(
            TYPE_OC_SHORTS_NEW_TEMPLATE,
            TYPE_ROLE_UPDATE,
            TYPE_NEW_FOLLOWER
        ).ifEmpty { null } ?: return
        if (redBadgeJob?.isActive == true) {
            redBadgeJob?.cancel()
        }
        redBadgeJob = redBadgeScope.launch {
            metaRepository.queryUnreadRedBadge(types).collect {
                _badgeLiveData.value = it.data
            }
        }
    }

    /**
     * 清除红点并重新获取红点信息
     */
    suspend fun clearRedBadge(type: String, afterClearInfo: (RedBadgeData?) -> RedBadgeData?) = redBadgeScope.launch {
        metaRepository.clearRedBadge(listOf(type)).map { it.data }.collect {
            if (it == true) {
                _badgeLiveData.postValue(afterClearInfo(_badgeLiveData.value))
            }
        }
    }

    private fun registerHermesEventBus() {
        CpEventBus.register(this)
    }

    fun checkUserInfo(isMainProcess: Boolean) {
        this.isMainProcess = isMainProcess
        initUserInfo(isMainProcess)
        if (isMainProcess) {
            initUserRedBadge()
        }
    }

    fun visitorLoginFLow(loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.visitorLogin(loginType).collect { result ->
            if (result.succeeded && result.data?.userInfo != null) {
                postLoginStatusAndUserInfo(result.data?.userInfo, LoginStatusEvent.LOGIN_SUCCESS)
                // 如果在visitorLogin的flow里执行，会导致重试多次后协程强转崩溃
                metaRepository.updateCustomToken(result.data?.customToken)
                emit(LoginState.Succeeded(result.data?.userInfo))
            } else {
                val violateInfo = GsonUtil.gsonSafeParseCollection<ViolateMessage>(result.message).let {
                    it?.copy(localType = it.getTypeByCode(result.code))
                }
                emit(
                    LoginState.Failed(
                        result.message ?: "",
                        result.code ?: 0,
                        violateInfo
                    )
                )
            }
        }
    }

    suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.accountLogin(account, password, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    suspend fun gparkIdLogin(
        gparkId: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.gparkIdLogin(gparkId, password, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo(
                    (it as LoginState.Succeeded).userInfo,
                    LoginStatusEvent.LOGIN_SUCCESS
                )
            }
            emit(it)
        }
    }

    fun accountSignup(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.accountSignup(account, password, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    fun bindEmail(bindEmail: String, code: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindByEmail(bindEmail, code).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    this.bindEmail = bindEmail
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    fun bindEmailChange(newEmail: String, newEmailCode: String, oldEmailCode: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindEmailChange(newEmail, newEmailCode, oldEmailCode).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    bindEmail = newEmail
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    fun bindParentEmail(bindParentEmail: String, code: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindByParentEmail(bindParentEmail, code).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    if (thirdBindInfo == null) {
                        thirdBindInfo = ThirdBindInfo()
                    }
                    thirdBindInfo!!.parentEmail = ThirdBindObj(bindId = bindParentEmail, null, null)
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    fun bindParentEmailChange(newParentEmail: String, newEmailCode: String, oldEmailCode: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindParentEmailChange(newParentEmail, newEmailCode, oldEmailCode).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    if (thirdBindInfo == null) {
                        thirdBindInfo = ThirdBindInfo()
                    }
                    thirdBindInfo!!.parentEmail = ThirdBindObj(bindId = newParentEmail, null, null)
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    fun updateUser(
        mBirthday: Long? = null,
        mGender: Int = -1,
        mNickname: String? = null,
        mPortrait: String? = null,
        mCity: String? = null,
        mSignature: String? = null,
        reviewBirth: Boolean
    ): Flow<DataResult<Boolean>> = flow {
        metaRepository.updateUserInfo(birthday = mBirthday, gender = mGender, nickname = mNickname, portrait = mPortrait, city = mCity, signature = mSignature, reviewBirth).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    if (mBirthday != null) birth = mBirthday
                    if (mGender >= 0) {
                        gender = mGender
                    }
                    mNickname?.let { nickname = it }
                    mPortrait?.let { portrait = it }
                    mCity?.let { city = it }
                    mSignature?.let { signature = it }
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    /**
     * 更新本地信息
     */
    fun updateLocalInfo(mPortrait: String?, mWholeBodyImage: String?, wholeBodyImage2: String?, mHaveWholeBodyImage: Boolean?) {
        getUserInfoFromCache()?.apply {
            mPortrait?.let { portrait = it }
            mWholeBodyImage?.let { wholeBodyImage = it }
            mHaveWholeBodyImage?.let { haveWholeBodyImage = it }
            wholeBodyImage2?.let { bgMaskImage = it }
            postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
        }
    }


    fun bindAccountAndPassword(bindAccount: String, password: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindAccountAndPassword(bindAccount, password).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    account = bindAccount
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    fun bindPasswordByGparkId(bindAccount: String, password: String): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindPasswordByGparkId(bindAccount, password).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    account = bindAccount
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    /**
     * 初始化话用户信息,主要是旧版本的用户信息存储到数据库中
     */
    private fun initUserInfo(isMainProcess: Boolean) = GlobalScope.launch(Dispatchers.IO) {
        val metaUserInfo = getUserInfoFromCache()
        changeAccountValue(metaUserInfo, true)
        if (!isMainProcess) return@launch
        val refreshToken = metaKV.account.refreshToken
        val accessToken = metaKV.account.accessToken
        val customToken = metaKV.accountWrapper.customToken
        val fetchCustomToken = metaRepository.fetchCustomToken()

        Timber.tag(CheckTokenInterceptor.TAG).d(
            """initUserInfo: 
                accessToken: ${accessToken.isNullOrBlank()},
                refreshToken: ${refreshToken.isNullOrBlank()},
                customToken: ${customToken.isNullOrBlank()},
                fetchCustomToken: ${fetchCustomToken.isNullOrBlank()},
                metaUserInfo: $metaUserInfo"""
        )
        if (metaUserInfo != null && userInfoCacheValid(metaUserInfo, accessToken)) {
            Timber.tag(CheckTokenInterceptor.TAG).d("user ok, visitor: ${metaUserInfo.visitor}")
            // 正式用户/游客
            if (!metaUserInfo.visitor) {
                // 正式用户，获取更新用户信息
                getMetaUserInfoFromNet(isMainProcess)
            }
            // 检查刷新token
            initTokenExpireCheck()
            // 兼容旧版用户firebaseIdToken？
            if (!customToken.isNullOrEmpty() && fetchCustomToken.isNullOrEmpty()) {
                metaRepository.updateCustomToken(customToken)
            }
            return@launch
        }
    }

    fun userInfoCacheValid(): Boolean {
        val metaUserInfo = getUserInfoFromCache()
        val accessToken = metaKV.account.accessToken
        return userInfoCacheValid(metaUserInfo, accessToken)
    }

    fun userInfoCacheValid(metaUserInfo: MetaUserInfo?): Boolean {
        val accessToken = metaKV.account.accessToken
        return userInfoCacheValid(metaUserInfo, accessToken)
    }

    private fun userInfoCacheValid(metaUserInfo: MetaUserInfo?, accessToken: String?): Boolean {
        val tokenOk = !accessToken.isNullOrBlank()
        return tokenOk && metaUserInfo != null && !metaUserInfo.uuid.isNullOrBlank()
    }

    /**
     * 获取网络用户信息
     */
    protected fun getMetaUserInfoFromNet(isMainProcess: Boolean) {
        if (!isMainProcess) return
        MainScope().launch {
            metaRepository.getMetaUserInfoFromNet().collect {
                Timber.tag("Test-IdToken").d("getMetaUserInfoFromNet, ${it}")
                if (it.succeeded) {
                    postLoginStatusAndUserInfo(it.data, LoginStatusEvent.UPDATE)
                    RealName.refreshRealNameInfo()
                }
            }
        }
    }

    /**
     * 获取本地用户信息
     */
    fun getUserInfoFromCache(): MetaUserInfo? {
        return getUserInfoFromMmKv()  //获取mmkv中缓存数据
    }

    /**
     * 从MMKV读取缓存得用户信息
     */
    protected fun getUserInfoFromMmKv(): MetaUserInfo? {
        val userInfo = metaKV.account.userInfo
        if (userInfo.isBlank()) return null
        val metaUserInfo = GsonUtil.gsonSafeParse<MetaUserInfo>(userInfo)
        metaKV.account.sessionId = metaUserInfo?.sessionId ?: ""
        return metaUserInfo
    }

    /**
     * 登录状态处理
     */
    fun postLoginStatusAndUserInfo(
        metaUserInfo: MetaUserInfo?,
        loginStatus: LoginStatusEvent
    ) {
        if (metaUserInfo != null || loginStatus == LoginStatusEvent.LOGOUT_SUCCESS) {
            metaUserInfo?.let {
                metaRepository.saveMetaUserInfo(it) // 刷新本地缓存数据
                MgsBiz.login(it.uuid ?: "", it.visitor, metaKV.account.accessToken ?: "")
            }
            changeAccountValue(metaUserInfo, false)
            CpEventBus.post(ProcessUserInfoChangeEvent(metaUserInfo))// 通知其他进程更新
        }

        EventBus.getDefault().post(loginStatus)
        imConnection(loginStatus, metaUserInfo)

        if (metaUserInfo != null && loginStatus == LoginStatusEvent.LOGIN_SUCCESS) {
            getMetaUserInfoFromNet(isMainProcess)
        }
    }

    /**
     * 融云连接
     */
    private fun imConnection(loginStatus: LoginStatusEvent, userInfo: MetaUserInfo?) {
        when (loginStatus) {
            LoginStatusEvent.LOGOUT_SUCCESS -> RongImHelper.logout()
            LoginStatusEvent.LOGIN_SUCCESS -> userInfo?.let { RongImHelper.login(it) }
            else -> userInfo?.let { RongImHelper.login(it) }
        }
    }

    suspend fun logout(notifyBackend: Boolean): Flow<DataResult<Boolean>> = flow {
        metaRepository.logout(notifyBackend).collect {
            Timber.tag(CheckTokenInterceptor.TAG).d("tokenInterceptor logout")
            MgsBiz.logout()
            postLoginStatusAndUserInfo(null, LoginStatusEvent.LOGOUT_SUCCESS)
            emit(DataResult.Success(true))
            metaKV.communityKV.publishPostDraft = null
        }
    }

    suspend fun ditout(code: String) = flow {
        metaRepository.ditout(code).collect {
            emit(it)
        }
    }

    fun afterSwitchAccount() {
        metaKV.communityKV.publishPostDraft = null
    }

    /**
     * 判断是否是游客登录
     * 包含uuid，并且是游客登录
     */
    fun isVisitorLogin(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return !userInfo.uuid.isNullOrBlank() && userInfo.visitor
    }

    /**
     * 判断是否是正常登录
     * 现在需求不区分游客与正式账户
     * @see AccountInteractor.isRealLoginPermissionButVisitor
     */
    @Deprecated("No need to differentiate visitor and signed-up account")
    fun isRealLogin(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return !userInfo.uuid.isNullOrBlank() && !userInfo.visitor
    }

    public open fun isBindAccount(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return (userInfo.thirdBindInfo?.parentEmail != null && userInfo.bindEmail?.isNullOrEmpty() == false) || (!userInfo.account.isNullOrEmpty())
    }

    /**
     * 游客账号是否和已登录的真实账号拥有相同的权限
     * 1.之前只有登录用户可以使用IM&MGS功能,可以加好友；本版本放开此限制，游客也可以使用好友IM功能 [目前此版本恒为true]
     *
     * created by liyanfeng on 2022/8/15 10:40 上午
     */
    fun isRealLoginPermissionButVisitor(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return !userInfo.uuid.isNullOrBlank()
    }

    fun clearUserInfo() {
        metaKV.account.clear()
        metaKV.accountWrapper.clear()
    }

    fun addTokenChangedCallback(callback: TokenChangedCallback) {
        tokenCallbacks.addCallback(callback)
    }

    fun removeTokenChangedCallback(callback: TokenChangedCallback) {
        tokenCallbacks.removeCallback(callback)
    }

    fun addUserChangedCallback(callback: UserChangedCallback) {
        userCallbacks.addCallback(callback)
    }

    fun removeUserChangedCallback(callback: UserChangedCallback) {
        userCallbacks.removeCallback(callback)
    }

    private fun changeAccountValue(newValue: MetaUserInfo?, isCache: Boolean) {
        val oldToken = _accessTokenLiveData.value
        val newToken = metaKV.account.accessToken
        val oldUserInfo = _accountLiveData.value
        if (oldUserInfo != newValue) {
            userCallbacks.post {
                this.invoke(oldUserInfo, newValue)
            }
            enableFriendRequestsNotice =
                metaKV.account.isFriendRequestsNoticeEnabled(newValue?.uuid)
        }
        _accountLiveData.postValue(newValue)
        _accessTokenLiveData.postValue(newToken)
        if (oldToken != newToken) {
            tokenCallbacks.post {
                this.invoke(oldToken, newToken)
            }
        }
        updateSparkBalance(newValue?.userRemainLightUpQuantity)
    }

    fun checkAccountInit(): Boolean {
        return !_accessTokenLiveData.value.isNullOrEmpty()
    }

    @Subscribe
    fun onEvent(event: MgsGameNoticeEvent) {
        if (PandoraToggle.isMWMgs && event.featureName == GameModEventConst.UPDATE_PROFILE && isMainProcess) {
            getMetaUserInfoFromNet(isMainProcess)
        }
    }

    @Subscribe
    fun onEvent(update: MgsUserInfoUpdateEvent) {
        Timber.i("UpdateUserInfoEvent received")
        val isMainProcess = StartupContext.get().processType == H || H.set.contains(StartupContext.get().processType)
        getMetaUserInfoFromNet(isMainProcess)
    }

    @Subscribe
    fun onEvent(event: ProcessUserInfoChangeEvent) {
        val isMainProcess = StartupContext.get().processType == H || H.set.contains(StartupContext.get().processType)
        if (!isMainProcess) {
            _accountLiveData.postValue(event.metaUserInfo)
            Timber.i("MetaUserInfo received")
        }
    }

    @Subscribe
    fun onEvent(event: UpdateRoleDataEvent) {
        Timber.i("UpdateRoleDataEvent received")
        if (StartupContext.isMainProcess
            && !event.roleId.isNullOrBlank()
            && !event.wholeBodyImage.isNullOrBlank()
        ) {
            metaKV.account.roleData = RoleData(event.roleId, event.wholeBodyImage).toJSON()
        }
    }

    @Subscribe
    fun onEvent(event: UpdatePrivacySettingsEvent) {
        Timber.i("UpdatePrivacySettingsEvent received")
        updatePrivacySwitch(
            ootdPrivateSwitch = event.ootdPrivateSwitch,
            updateCache = StartupContext.isMainProcess
        )
    }

    @Subscribe
    fun onEvent(event: UgcModuleGuideEvent) {
        Timber.i("UgcModuleGuideEvent received")
        when (event.status) {
            MODULE_GUIDE_STATUS_GAME -> {
                when (moduleGuideStatus) {
                    MODULE_GUIDE_STATUS_TODO,
                    MODULE_GUIDE_STATUS_SKIP_USER -> {
                        moduleGuideStatus = event.status
                    }
                }
            }
        }
    }

    fun isAccountBanned(): Boolean {
        return if (accountLiveData.value == null) {
            // 防止游戏内调用没有liveData
            GsonUtil.gsonSafeParse<MetaUserInfo>(metaKV.account.userInfo)?.isBanned() == true
        } else {
            _accountLiveData.value?.isBanned() == true
        }
    }

    /**
     * 功能是否收到U13限制：分享、充值
     */
    @Deprecated("此方法目前用不到，因为目前不处理U13用户", ReplaceWith("false"))
    fun isFuncLimitByU13(): Boolean {
        return false
    }

    /**
     * 符合小于13岁条件
     */
    @Deprecated("此方法目前用不到，因为目前不处理U13用户", ReplaceWith("false"))
    suspend fun isUserU13(): Boolean {
        return false
    }

    val curUuid get() = curUuidOrNull.orEmpty()

    val curUuidOrNull get() = _accountLiveData.value?.uuid

    val curNameOrNull get() = _accountLiveData.value?.nickname

    val isOfficial get() = _accountLiveData.value?.isOfficial() == true

    fun isMe(uuid: String?): Boolean {
        if (uuid.isNullOrBlank()) {
            return false
        }
        return uuid == curUuid
    }

    /**
     * 判断用户是否达到某个年龄限制
     */
    fun isReachLimitAge(age: Int): Boolean {
        return getAge() >= age
    }

    fun getAge(): Int {
        val birth = accountLiveData.value?.birth
        return if (birth != null) {
            DateUtil.getAgeByBirthDay(Date(birth))
        } else {
            0
        }
    }

    private val tokenLockObj = Any()
    private val tokenLockVar: ConditionVariable by lazy { ConditionVariable() }

    /**
     * 刷新accessToken，成功后定时下次刷新时间
     * 只有主进程会调用
     */
    fun refreshAccessToken(currentAccessToken: String?): String? {
        if (StartupContext.get().processType != H) {
            Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken, not h process")
            return null
        }
        Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken, begin")
        synchronized(tokenLockObj) {
            Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken, synchronized start")
            val localAccessToken = metaKV.account.accessToken
            var result: String? = null
            if (localAccessToken != currentAccessToken) {
                // 如果已经改成新的了，不调用接口
                Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken localToken != currentToken")
                result = localAccessToken
                tokenLockVar.open()
            } else {
                CoroutineScope(Dispatchers.IO).launch {
                    val apiResult = metaRepository.refreshAccessToken()
                    result = apiResult?.accessToken
                    apiResult?.accessTokenExpire?.let {
                        initTokenExpireCheck()
                    }
                    _accessTokenLiveData.postValue(metaKV.account.accessToken)
                    tokenLockVar.open()
                }
            }
            tokenLockVar.block()
            tokenLockVar.close()
            Timber.tag(CheckTokenInterceptor.TAG).d("refreshAccessToken synchronized end, result:$result")
            return result
        }
    }

    /**
     * 检查刷新accessToken
     */
    fun initTokenExpireCheck() {
        if (StartupContext.get().processType != H) {
            Timber.tag(CheckTokenInterceptor.TAG).d("initTokenExpireCheck, not h process")
            return
        }
        accountHandler.removeMessages(MSG_RETRY_TOKEN_REFRESH)
        val nextRefreshTime = metaKV.account.accessTokenExpireTime
        val interval = nextRefreshTime - System.currentTimeMillis()
        if (interval in 0..ACCESS_TOKEN_REFRESH_INTERVAL || nextRefreshTime == -1L) {
            // 小于刷新阈值/确认已过期，立即刷新
            Timber.tag(CheckTokenInterceptor.TAG).d("initTokenExpireCheck refresh now interval：${interval}, nextRefreshTime:${nextRefreshTime}")
            accountHandler.sendEmptyMessage(MSG_RETRY_TOKEN_REFRESH)
        } else if (interval > ACCESS_TOKEN_REFRESH_INTERVAL) {
            // 大于刷新阈值，在某时间后刷新
            Timber.tag(CheckTokenInterceptor.TAG).d("initTokenExpireCheck refresh after: ${interval - ACCESS_TOKEN_REFRESH_INTERVAL}")
            accountHandler.sendEmptyMessageDelayed(MSG_RETRY_TOKEN_REFRESH, interval - ACCESS_TOKEN_REFRESH_INTERVAL)
        }
    }

    fun addPrivacySwitchChangeCallback(callback: PrivacySwitchChangeCallback) {
        privacySwitchCallbacks.addCallback(callback)
    }

    fun removePrivacySwitchChangeCallback(callback: PrivacySwitchChangeCallback) {
        privacySwitchCallbacks.removeCallback(callback)
    }

    fun observePrivacySwitchChange(owner: LifecycleOwner, callback: PrivacySwitchChangeCallback) {
        privacySwitchCallbacks.observe(owner, callback)
    }

    fun updatePrivacySwitch(req: PrivacySwitch, updateCache: Boolean = false) {
        updatePrivacySwitch(
            req.ootdPrivateSwitch,
            req.chatMessageSwitch,
            req.followerShowSwitch,
            updateCache
        )
    }

    fun updatePrivacySwitch(
        ootdPrivateSwitch: Boolean? = null,
        chatMessageSwitch: Boolean? = null,
        followerShowSwitch: Boolean? = null,
        updateCache: Boolean = false
    ) {
        _accountLiveData.value?.apply {
            this.ootdPrivateSwitch = ootdPrivateSwitch ?: this.ootdPrivateSwitch
            this.chatMessageSwitch = chatMessageSwitch ?: this.chatMessageSwitch
            this.followerShowSwitch = followerShowSwitch ?: this.followerShowSwitch
            if (privacySwitchCallbacks.getCallbackSize() > 0) {
                privacySwitchCallbacks.dispatchOnMainThread { invoke(this@apply) }
            }

            if (updateCache) {
                metaRepository.saveMetaUserInfo(this)
            }
        }
    }

    fun setFriendRequestsNoticeEnabled(enabled: Boolean) {
        enableFriendRequestsNotice = enabled
        metaKV.account.setFriendRequestsNoticeEnabled(enabled)
    }

    fun updateSparkBalance(balanceList: List<Balance>?) {
        _sparkBalanceLiveData.postValue(Balance.getSparkBalance(balanceList))
    }

    fun getModuleGuideStatus() {
        if (!isMainProcess || moduleGuideStatusLoading) return
        moduleGuideStatusLoading = true
        MainScope().launch {
            val tTaiInteractor: TTaiInteractor = GlobalContext.get().get()
            val result = metaRepository.getModuleGuideStatus().combine(
                tTaiInteractor.getTTaiWithTypeV3<UgcModuleGuidInfo>(TTaiKV.ID_MODULE_GUIDE_ID)
            ) { status, guideInfo ->
                if (guideInfo == null) {
                    DataResult.Error(0, "")
                } else {
                    moduleGuideLabelId = guideInfo.badgeId
                    status
                }
            }.collectWithTimeout(1_000)
            if (moduleGuideStatus < MODULE_GUIDE_STATUS_TODO) {
                moduleGuideStatus = if (result?.succeeded == true && result.data != null) {
                    result.data!!
                } else {
                    MODULE_GUIDE_STATUS_API_FAIL
                }
            }
            moduleGuideStatusLoading = false
        }
    }

    fun observeModuleGuideStatusChange(
        owner: LifecycleOwner,
        callback: ModuleGuideStatusChangeCallback
    ) {
        moduleGuideStatusCallbacks.observe(owner, callback)
    }
}