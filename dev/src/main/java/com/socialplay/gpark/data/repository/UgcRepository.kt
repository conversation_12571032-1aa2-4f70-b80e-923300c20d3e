package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.editor.LightUpBody
import com.socialplay.gpark.data.model.outfit.UgcDesignDeleteRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignDetailRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignEditRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignFeedRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignGetRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignLikeRequest
import com.socialplay.gpark.data.model.outfit.ProfileCurrentClothesRequest
import com.socialplay.gpark.data.model.outfit.UgcAssetProfileEntrance
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTagRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
class UgcRepository(private val api: MetaApi) {

    fun likeUgcDesign(body: UgcDesignLikeRequest) = suspendApiNotNull {
        api.likeUgcDesign(
            body.feedId,
            body.positive
        )
    }

    fun getUgcDesign(body: UgcDesignGetRequest) = suspendApiNotNull {
        api.getUgcDesign(
            body.feedIds
        )
    }

    fun editUgcDesign(body: UgcDesignEditRequest) = suspendApiNotNull {
        api.editUgcDesign(
            body.feedId,
            body.title,
            body.comment
        )
    }

    fun deleteUgcDesign(body: UgcDesignDeleteRequest) = suspendApiNotNull {
        api.deleteUgcDesign(
            body.feedId
        )
    }

    fun getUgcDesignFeed(body: UgcDesignFeedRequest) = suspendApiNotNull {
        api.getUgcDesignFeed(
            body.sortType,
            body.pageSize,
            body.pageNumber
        )
    }

    fun getUgcDesignFeedV2(
        sortType: Int,
        pageNumber: Int,
        pageSize: Int,
        entrance: Int?,
        tagTypes: List<Int>?,
    ) = suspendApiNotNull {
        val tagTypesParam = if (entrance == null) {
            null
        } else if (tagTypes.isNullOrEmpty()) {
            entrance.toString()
        } else {
            tagTypes.joinToString(",")
        }
        api.getUgcDesignFeedV2(
            sortType,
            pageSize,
            pageNumber,
            tagTypesParam
        )
    }

    fun getUgcRookieFeed(
        pageNumber: Int,
        pageSize: Int
    ) = suspendApiNotNull {
        api.getUgcRookieFeed(
            pageNumber,
            pageSize
        )
    }

    fun getUgcDesignDetail(body: UgcDesignDetailRequest) = suspendApiNotNull {
        api.getUgcDesignDetail(
            body.feedId
        )
    }

    fun getProfileCurrentCloths(body: ProfileCurrentClothesRequest) = suspendApiNotNull {
        api.getProfileCurrentClothes(
            body.targetUuid
        )
    }

    fun getProfileCurrentClothesFlow(body: ProfileCurrentClothesRequest) = flow {
        emit(DataSource.getDataResultForApi {
            api.getProfileCurrentClothes(
                body.targetUuid
            )
        })
    }

    fun getUgcDesignProfileTags(body: UgcDesignProfileTagRequest) = suspendApiNotNull {
        api.getUgcDesignProfileTags(
            body.entrance
        )
    }

    fun getUgcDesignProfile(body: UgcDesignProfileRequest) = suspendApiNotNull {
        api.getUgcDesignProfile(
            body.targetUuid,
            body.tag,
            body.pageSize,
            body.pageNumber
        )
    }

    fun getUgcDesignProfileV2(
        targetUuid: String,
        tag: Int,
        pageNumber: Int,
        pageSize: Int
    ) = suspendApiNotNull {
        api.getUgcDesignProfileV2(
            targetUuid,
            tag,
            pageSize,
            pageNumber
        )
    }

    fun lightUp(body: LightUpBody) = flow {
        emit(DataSource.getDataResultForApi { api.lightUp(body) })
    }

    fun getUgcAssetProfile(targetUuid: String, pageSize: Int) = suspendApiNotNull {
        api.getUgcAssetProfile(targetUuid, pageSize)
    }

    fun publicBatch(feedIds: Iterable<String>) = suspendApiNotNull {
        api.publicUgcAssetBatch(feedIds.joinToString(","))
    }

    fun privateBatch(feedIds: Iterable<String>) = suspendApiNotNull {
        api.privateUgcAssetBatch(feedIds.joinToString(","))
    }

    fun pinUgcAsset(feedId: String, pin: Boolean) = suspendApiNotNull {
        if (pin) {
            api.pinUgcAsset(feedId)
        } else {
            api.unpinUgcAsset(feedId)
        }
    }

    fun getUgcAssetTagTree() = suspendApiNotNull {
        api.getUgcAssetTagTree()
    }

    fun deleteUgcAsset(id: String) = suspendApiNotNull {
        api.eraseUgcAsset(id)
    }

    fun getMyModules(
        sortType: Int,
        pageSize: Int,
        pageNumber: Int,
        tagTypes: List<Int>?,
        published: Boolean?
    ) = suspendApiNotNull {
        api.getMyModules(sortType, pageSize, pageNumber, tagTypes?.joinToString(","), published)
    }

    fun publicUgcAsset(id: String) = suspendApiNotNull {
        api.publishUgcAsset(id)
    }

    fun privateUgcAsset(id: String) = suspendApiNotNull {
        api.abolishUgcAsset(id)
    }

    fun getModuleGuideStatus(): Flow<DataResult<Int>> = flow {
        emit(DataSource.getDataResultForApi {
            api.getModuleGuideStatus()
        }.map {
            when (it) {
                0 -> BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO
                else -> BaseAccountInteractor.MODULE_GUIDE_STATUS_API_DONE
            }
        })
    }
}