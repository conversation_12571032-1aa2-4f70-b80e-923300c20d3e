package com.socialplay.gpark.data.mapper

import com.socialplay.gpark.data.model.*
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.GameHomeInfo
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.entity.HomePageEntity
import com.socialplay.gpark.data.model.entity.MyPlayedGameEntity
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.profile.recent.GameEntity
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.UserRelation
import com.socialplay.gpark.util.RatingUtil

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/16
 * desc   :
 * </pre>
 */


class MetaMapper {

    fun map(input: GameDetailInfo, updateTimestamp: Long, downloadPercent: Float, lastPlayedTime: Long, firstDownloadTimestamp: Long): MyPlayedGameEntity {
        return MyPlayedGameEntity(input.id, updateTimestamp, input.name, input.icon, input.type, input.startupExtension, downloadPercent, input.resource?.packageName, firstDownloadTimestamp, lastPlayedTime, input.gameTags)
    }

    fun map(input: GameDetailEntity, updateTimestamp: Long, downloadPercent: Float, lastPlayedTime: Long, firstDownloadTimestamp: Long): MyPlayedGameEntity {
        return MyPlayedGameEntity(input.id, updateTimestamp, input.name, input.gameIcon, input.type, input.startupExtension, downloadPercent, input.resource?.packageName, firstDownloadTimestamp, lastPlayedTime, input.gameTags)
    }

    fun map(input: GameDetailEntity): GameDetailInfo {
        return GameDetailInfo(
            input.id,
            input.name,
            input.gameIcon,
            input.description,
            input.type,
            input.startupExtension,
            input.images,
            input.videos,
            if (input.author == null || input.author.name.isNullOrEmpty() || input.author.avatar.isNullOrEmpty()) null
            else AuthorInfo(
                input.author.id,
                input.author.number,
                input.author.name,
                input.author.avatar,
                input.author.introduction ?: "",
                input.author.tags,
                input.author.follow
            ),
            if (input.sns == null) null else SnsInfo(input.sns.playerCount, input.sns.likeCount, input.sns.rate),
            if (input.resource == null) null else ResourceInfo(input.resource.url, input.resource.resourceType, input.resource.size, input.resource.fingerprint, input.resource.packageName, input.resource.upgradeStrategy, input.resource.upgradeInstallType, input.resource.editorVersion),
            input.gameTags,
            input.canPlay,
            input.mwTip,
            shareCount = input.shareCount,
            hasCommunity = input.hasCommunity,
            createTime = input.createTime,
            releaseTime = input.releaseTime,
            updateDescription = input.updateDescription,
            labelInfo = input.labelInfo,
            extend = input.extend,
            totalTippedCoins = input.totalTippedCoins,
            myTippableCoins = input.myTippableCoins,
            myTippedCoins = input.myTippedCoins,
            myTippedTimes = input.myTippedTimes,
        )
    }

    fun map(input: GameDetailInfo): GameDetailEntity {
        return GameDetailEntity(
            input.id,
            input.name,
            input.icon,
            input.description,
            input.type,
            input.startupExtension,
            input.images,
            input.videos,
            if (input.author == null) null else AuthorInfo(
                input.author.id,
                input.author.number,
                input.author.name,
                input.author.avatar,
                input.author.introduction,
                input.author.tags,
                input.author.follow
            ),
            if (input.sns == null) null else SnsInfo(input.sns.playerCount, input.sns.likeCount, input.sns.rate),
            if (input.resource == null) null else ResourceInfo(input.resource.url, input.resource.resourceType, input.resource.size, input.resource.fingerprint, input.resource.packageName, input.resource.upgradeStrategy, input.resource.upgradeInstallType, input.resource.editorVersion),
            input.gameTags,
            input.shareCount,
            input.hasCommunity,
            input.createTime,
            input.releaseTime,
            input.updateDescription,
            input.labelInfo,
            input.extend,
            totalTippedCoins = input.totalTippedCoins,
            myTippableCoins = input.myTippableCoins,
            myTippedCoins = input.myTippedCoins,
            myTippedTimes = input.myTippedTimes,
        )
    }

    fun map(input: GameDetailEntity, resourceUrl: String): GameDetailInfo {
        return GameDetailInfo(
            input.id,
            input.name,
            input.gameIcon,
            input.description,
            input.type,
            input.startupExtension,
            input.images,
            input.videos,
            if (input.author == null) null else AuthorInfo(
                input.author.id,
                input.author.number,
                input.author.name,
                input.author.avatar,
                input.author.introduction,
                input.author.tags,
                input.author.follow
            ),
            if (input.sns == null) null else SnsInfo(input.sns.playerCount, input.sns.likeCount, input.sns.rate),
            if (input.resource == null) null else ResourceInfo(resourceUrl, input.resource.resourceType, input.resource.size, input.resource.fingerprint, input.resource.packageName, input.resource.upgradeStrategy, input.resource.upgradeInstallType, input.resource.editorVersion),
            input.gameTags,
            input.canPlay,
            input.mwTip,
            shareCount = input.shareCount,
            createTime = input.createTime,
            releaseTime = input.releaseTime,
            updateDescription = input.updateDescription,
            labelInfo = input.labelInfo,
            extend = input.extend,
            totalTippedCoins = input.totalTippedCoins,
            myTippableCoins = input.myTippableCoins,
            myTippedCoins = input.myTippedCoins,
            myTippedTimes = input.myTippedTimes,
        )
    }

    fun map(input: ApkInfo, resourceUrl: String, id: String): GameDetailInfo {
        return GameDetailInfo(
            id,
            input.appName,
            "",
            "",
            1,
            null,
            null,
            null,
            null,
            null,
            ResourceInfo(resourceUrl, 1, 0, "", input.packageName, 0, 0, null),
            listOf()
        )
    }

    fun map(input: MyPlayedGameEntity): MyPlayedGame {
        return MyPlayedGame(input.id, input.updateTimestamp, input.name, input.icon, input.type, input.startupExtension, input.downloadPercent, input.packageName, input.firstDownloadTimestamp, input.lastPlayedTime, input.gameTags, isUgcGame = input.isUgcType())
    }

    fun mapPostCard(input: GameEntity): PostCardInfo {
        return PostCardInfo(
            resourceType = PostCardInfo.TYPE_PGC,
            gameId = input.gameId,
            packageName = input.packageName ?:"",
            likeCount = input.likeCount.toLong(),
            player = 0,
            gameAuthor = input.developerNickname,
            gameIcon = input.iconUrl,
            gameLabelList = null,
            gameName = input.name,
            gameScore = input.gameScore,
            offset = null,
        )
    }

    fun mapPostCard(input: UgcGameInfo.Games, userInfo: MetaUserInfo?): PostCardInfo {
        return PostCardInfo(
            resourceType = PostCardInfo.TYPE_UGC,
            gameId = input.id,
            packageName = input.packageName,
            likeCount = input.loveQuantity,
            player = input.pvCount,
            gameAuthor = userInfo?.nickname,
            gameIcon = input.banner,
            gameLabelList = null,
            gameName = input.ugcGameName,
            gameScore = 0F,
            offset = null,
        )
    }

    fun mapPostCard(input: RecentUgcGameEntity): PostCardInfo {
        return PostCardInfo(
            resourceType = PostCardInfo.TYPE_UGC,
            gameId = input.id,
            packageName = input.packageName,
            likeCount = input.likeCount,
            player = input.popularity,
            gameAuthor = input.username,
            gameIcon = input.gameIcon,
            gameLabelList = null,
            gameName = input.gameName,
            gameScore = 0F,
            offset = null,
        )
    }

    fun map(input: HomePageEntity): GameItem {
        return GameItem(
            input.id,
            input.name,
            input.icon,
            input.packageName ?: "",
            input.sns?.playerCount ?: 0,
            input.sns?.likeCount ?: 0,
            input.sns?.rate,
            RatingUtil.getRating(input.id, input.packageName).toFloat(),
            input.startupExtension,
            input.squareIcon ?: "",
            input.type
        )
    }

    fun map(input: ListItem): SearchGameItem {
        val item = input.pgc?:input.ugc
        val isUgc = input.contentType == 2
        return SearchGameItem(
            item?.ugid?:item?.gameCode?:input.id?:"",
            item?.packageName?:"",
            item?.iconUrl ?: item?.banner,
            item?.gameName?:input.keyword,
            item?.authorName ?: item?.userName,
            item?.likeCount,
            item?.pvCount,
            isUgc,
            itemType = if (input.keyword.isNullOrEmpty()) 0 else 1,
            contentId = input.contentId
        )
    }

    fun map(input: EditorNotice.Notice?, type: Int, hasRead:Boolean = false): EditorNotice.OuterShowNotice {
        return if (input == null) {
            EditorNotice.OuterShowNotice(null, 0, type, hasRead)
        } else {
            EditorNotice.OuterShowNotice(input.content, input.sendTimeLong, type, hasRead)
        }
    }

    fun mapNotice(input: EditorNotice.Notice): NoticeWrapper {
        return NoticeWrapper(NoticeWrapper.TYPE_EDITOR, editorNotice = input)
    }

    fun map(input: Int): UserRelation? {
        return UserRelation.values().firstOrNull {
            it.value == input
        }
    }

    fun map(input: UgcGameInfo.Games?): GameHomeInfo? {
        input?.id ?: return null
        return GameHomeInfo(
            id = input.id,
            packageName = input.packageName,
            ugcGameName = input.ugcGameName,
            banner = input.banner,
            loveQuantity = input.loveQuantity,
            likeIt = input.likeIt,
            gameCode = input.availableGameCode
        )
    }

    private fun getNeighborPosition(gameId: String, map: Map<String, Int>?): Int {
        return map?.get(gameId) ?: 0
    }

}