package com.socialplay.gpark.data.model


data class HomeTopMessage(
    var fromIcon: String?,
    var fromName: String?,
    var fromUuid: String?,
    var groupClassify: String?,
    var groupId: Long?,
    var id: Long?,
    var msgId: String?,
    var pack: String?,
    var readFlag: String?,
    var sendTime: Long?,
    var simplifyMsg: String?,
    var source: String?,
    var uuid: String?,
    var tempId: String?,
    var sourceTaskId: String?,
    var groupConfig:Config?,
){
    data class Config(
        var contentType: Int?,
        var type: Long?,
        var gameId: String?,
        val tabList: List<SysHeaderInfo.TabItemEntity>?
    )
}