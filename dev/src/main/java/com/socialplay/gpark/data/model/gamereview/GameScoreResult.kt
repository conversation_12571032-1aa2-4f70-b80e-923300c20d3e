package com.socialplay.gpark.data.model.gamereview

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import timber.log.Timber

@Parcelize
data class GameScoreResult(
    @Transient
    var gameId: Long = 0,
    val avg: Float,
    val commentCount: Long,
    val ratingData: Map<Int, Float>
) : Parcelable {

    /**
     * @throws NumberFormatException 不要将结果转换为float!不同国家小数点可能是逗号，或者数字不为阿拉伯数字
     */
    fun getFormatAvg(): String {
        return try {
            String.format("%.1f", avg)
        } catch (e: Exception) {
            Timber.e("check_suggest ${e}")
            "0.0"
        }
    }
}
