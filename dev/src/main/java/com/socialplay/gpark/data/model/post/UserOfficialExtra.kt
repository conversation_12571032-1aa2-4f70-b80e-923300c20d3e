package com.socialplay.gpark.data.model.post

import android.os.Parcelable
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.UserTagInfo
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2023/10/10
 * Desc: 官方、马甲号信息
 */
@Parcelize
data class UserOfficialExtra(
    val isOfficial: Boolean,
    var tags: List<UserTagInfo>?,
    val ootdPrivateSwitch: Boolean = false,
    val labelInfo: LabelInfo? = null
) : Parcelable {

    val tagIds get() = tags?.map { it.id }
    val isOfficialV2 get() = isOfficial || MetaUserInfo.isOfficial(tags)
    val isCreator get() = MetaUserInfo.isCreator(tags)
}