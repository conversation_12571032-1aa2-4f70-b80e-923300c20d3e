package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.mgs.MgsGameConfigData
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.MMKVScope
import com.tencent.mmkv.MMKV

class MgsKV(override val mmkv: MMKV) : MMKVScope {
    companion object {
        // MGS游戏的配置信息
        private const val KEY_MGS_GAME_CONFIG = "key_mgs_game_config"

        private const val KEY_SHARE_ID_PROCESS_TIME = "key_share_id_process_time"
    }

    /**
     * 获取所有mgs游戏的配置数据缓存
     * @return 缓存map
     */
    fun getMgsGameConfigMap(): HashMap<String, MgsGameConfigData?>? = GsonUtil.gsonSafeParseCollection(mmkv.getString(KEY_MGS_GAME_CONFIG, "") ?: "")

    /**
     * 保存mgs游戏的配置数据缓存
     * @param bean mgs游戏的包名
     * @param packageName mgs游戏的包名
     * @return 缓存bean的json格式
     */
    fun saveMgsGameConfig(bean: MgsGameConfigData?, packageName: String) {
        if (packageName.isEmpty()) {
            return
        }
        val map = getMgsGameConfigMap() ?: HashMap()
        if (bean == null) {
            map.remove(packageName)
        } else {
            map[packageName] = bean
        }
        mmkv.putString(KEY_MGS_GAME_CONFIG, GsonUtil.gson.toJson(map))
    }

}