package com.socialplay.gpark.data.interactor

import android.content.Context
import android.webkit.WebSettings
import com.meta.pandora.Pandora
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.util.ChannelUtil
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.ProcessUtil
import com.socialplay.gpark.util.StorageUtils
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.StringBuilder
import java.util.*

/**
 * Created by yaqi.liu on 2021/5/13
 */
//TODO 主进程-初始化框架、拿到结果广播到所有其他进程
class DeviceInteractor(private val metaKV: MetaKV, val context: Context) {

    private var _androidId: String? = null
    private var _imei: String? = null
    private var _uniquePsuedoId: String? = null
    private var _channelId: String? = null
    private var _subChannelId: String? = null
    private var _apkChannelId: String? = null
    private var _metaTracking: String? = null
    private var _tDId: String? = null
    private val accountInteractor by GlobalContext.get().inject<AccountInteractor>()
    private var _userAgent: String? = null

    // 设备ID初始化完成后的回调列表
    private val deviceIdInitCallbacks = mutableListOf<() -> Unit>()

    // androidId初始化完成后的回调列表
    private val androidIdInitCallbacks = mutableListOf<() -> Unit>()

    // 标记oaid是否初始化完成
    private var isOaidInitialized = false

    // 标记基本设备ID是否初始化完成
    private val isBasicDeviceIdInitialized: Boolean
        get() = _androidId != null

    // 标记所有设备ID是否初始化完成
    private val isAllDeviceIdInitialized: Boolean
        get() = isBasicDeviceIdInitialized && isOaidInitialized

    // 标记androidId是否初始化完成
    private val isAndroidIdInitialized: Boolean
        get() = _androidId != null

    /**
     * 添加设备ID初始化完成的回调
     * @param callback 设备ID初始化完成后要执行的回调函数
     */
    fun addDeviceIdInitCallback(callback: () -> Unit) {
        if (isAllDeviceIdInitialized) {
            // 如果所有设备ID已经初始化完成，直接执行回调
            callback()
        } else {
            // 否则添加到回调列表中
            deviceIdInitCallbacks.add(callback)
        }
    }

    /**
     * 检查并触发设备ID初始化完成的回调
     */
    private fun checkAndNotifyDeviceIdInitialized() {
        if (isAllDeviceIdInitialized) {
            deviceIdInitCallbacks.forEach { it() }
            deviceIdInitCallbacks.clear()
        }
    }

    /**
     * 添加androidId初始化完成的回调
     * @param callback androidId初始化完成后要执行的回调函数
     */
    fun addAndroidIdInitCallback(callback: () -> Unit) {
        if (isAndroidIdInitialized) {
            // 如果androidId已经初始化完成，直接执行回调
            callback()
        } else {
            // 否则添加到回调列表中
            androidIdInitCallbacks.add(callback)
        }
    }

    /**
     * 检查并触发androidId初始化完成的回调(成功与否，都回调)
     */
    private fun checkAndNotifyAndroidIdInitialized() {
        androidIdInitCallbacks.forEach { it() }
        androidIdInitCallbacks.clear()
    }

    companion object {
        /**
         * imei缺省值
         */
        private const val DEFAULT_IMEI = "0"
        private const val DEFAULT_ = "0"

        //小米渠道
        const val CHANNEL_MILY = "mily"

        //豌豆荚
        const val CHANNEL_WDJLY = "wdjly"

        //百度
        const val CHANNEL_BAIDULY = "baiduly"

        //应用宝
        const val CHANNEL_YYBLY = "yybly"
    }


    fun init() {
        generateImei()
        generateAndroidId()
        generateOaId()
        generateUniquePsuedoID()
        generateChannelId()
        generateRAM()
        generateCpuModel()
        generateDiskSize()
        generateUserAgent()

        // 如果oaid从缓存中加载，则设置标记并检查是否触发回调
        if (_oaid != null) {
            isOaidInitialized = true
            checkAndNotifyDeviceIdInitialized()
        }
    }

    val tDId: String = ""

    private fun generateChannelId() {
        _apkChannelId = ChannelUtil.getApkChannelId(context)

        _subChannelId = metaKV.device.subChannelId
        if (_subChannelId.isNullOrBlank()) {
            _subChannelId = ChannelUtil.getSubChannelId(context, _apkChannelId)
            metaKV.device.subChannelId = _subChannelId
        }

        _channelId = metaKV.device.channelId
        if (_channelId.isNullOrBlank()) {
            _channelId = _apkChannelId + if (!_subChannelId.isNullOrBlank()) "-$_subChannelId" else ""
            metaKV.device.channelId = _channelId
        }
    }

    val channelId: String
        get() = _channelId ?: ChannelUtil.DEFAULT_CHANNEL

    val apkChannelId: String
        get() = _apkChannelId ?: ChannelUtil.DEFAULT_CHANNEL

    val subChannelId: String
        get() = _subChannelId.orEmpty()

    /***
     * imei
     */
    val imei: String
        get() = _imei ?: DEFAULT_IMEI

    private fun generateImei() {
        _imei = metaKV.device.imei
        if (_imei.isNullOrEmpty()) {
            _imei = DeviceUtil.generateImei(context)
            if (!_imei.isNullOrEmpty() && DEFAULT_IMEI != _imei) {
                metaKV.device.imei = _imei
            }
        }
    }

    /***
     * oaid
     */
    @Volatile
    var _oaid: String? = null
    val oaid: String
        get() = _oaid ?: ""

    private fun generateOaId() {
        val oaidCache = metaKV.device.oaid
        // OAID 官方库内部的 libmsaoaidauth.so 有偶先闪退的问题, 而且在 Java 层无法 catch 住
        // 为降低 OAID 库导致的崩溃率, 这里尽量用之前获取到的 oaid 缓存
        // 弊端: 如果用户重置了oaid, 乐园和派对在同一台设备上, 可能会出现关联不上的问题
        if (oaidCache.isNullOrEmpty()) {
            if (ProcessUtil.isMainProcess(context)) {
                DeviceInteractorWrapper.generateOaId(context, metaKV) { result ->
                    if (result.isNotEmpty()) {
                        _oaid = result
                        metaKV.device.oaid = result
                    }
                    // oaid初始化完成，无论成功与否
                    isOaidInitialized = true
                    // 检查是否触发回调
                    checkAndNotifyDeviceIdInitialized()
                }
            } else {
                // 非主进程不会初始化oaid，直接标记为已初始化
                isOaidInitialized = true
                checkAndNotifyDeviceIdInitialized()
            }
        } else {
            _oaid = oaidCache
            // 从缓存加载的oaid，直接标记为已初始化
            isOaidInitialized = true
            // 不需要在这里检查回调，因为init方法中会检查
        }
    }

    /***
     * smid
     */
    @Volatile
    var _smis: String? = null
    val smid: String
        get() = _smis ?: ""

    /***
     * androidId
     */
    val androidId: String
        get() = _androidId ?: ""

    private fun generateAndroidId() {
        _androidId = metaKV.device.android
        if (_androidId.isNullOrEmpty()) {
            _androidId = DeviceUtil.getAndroidId(context)
            if ("9774d56d682e549c" == _androidId) {
                _androidId = ""
            }
            if (!_androidId.isNullOrEmpty()) {
                metaKV.device.android = _androidId
            }
        }

        // androidId初始化完成，触发回调
        checkAndNotifyAndroidIdInitialized()
    }

    /**
     * installationId
     */
    val installationId: String
        get() = Pandora.getInstallationId()


    /**
     * uniquePsuedoId
     */
    val uniquePsuedoId: String
        get() = _uniquePsuedoId ?: ""

    /**
     * 根据设备信息计算唯一标识
     *
     * @return
     */
    private fun generateUniquePsuedoID() {
        _uniquePsuedoId = metaKV.device.unipsuedoid
        if (_uniquePsuedoId.isNullOrEmpty()) {
            val serial = runCatching { DeviceUtil.getSerial() }.getOrDefault(installationId)
            _uniquePsuedoId = if (serial.isNullOrEmpty()) {
                installationId
            } else {
                runCatching {
                    UUID(
                        DeviceUtil.getMszDevIdShort().hashCode().toLong(),
                        serial.hashCode().toLong()
                    ).toString()
                }.getOrDefault(installationId)
            }
            if (!_uniquePsuedoId.isNullOrEmpty()) {
                metaKV.device.unipsuedoid = _uniquePsuedoId
            }
        }
    }

    /**
     * deviceId(原：onlyId)
     */
    val deviceId: String
        get() {
            val arrayOf = mutableListOf(androidId)
            arrayOf.addAll(DeviceInteractorWrapper.ids())
            return arrayOf.firstOrNull { it.isNotBlank() } ?: ""
        }

    /**
     * 是否是平板设备
     */
    val isTablet by lazy { DeviceUtil.isTablet(context) }

    /**
     * Meta Tracking
     * 233乐园投放处理激活相关逻辑标志位
     */
    val metaTracking: String
        get() = _metaTracking ?: ChannelUtil.DEFAULT_META_TRACKING

    private var _ramSize: Long? = null
    val ramSize: Long
        get() = _ramSize ?: DeviceUtil.getTotalMemory(context).also {
            if (it > 0) {
                _ramSize = it
            }
        }

    private fun generateRAM() {
        Timber.d("ramSize:$ramSize")
    }

    private var _cpuModel: String? = null
    val cpuModel: String
        get() = _cpuModel ?: DeviceUtil.getCpuInfo().also {
            if (it.isNotEmpty() && it != "unknown") {
                _cpuModel = it
            }
        }

    private fun generateCpuModel() {
        Timber.d("cpu: $cpuModel")
    }

    private var _diskSize: Long? = null
    val diskSize: Long
        get() = _diskSize ?: StorageUtils.getInternalMemoryTotalSize().also {
            if (it > 0) {
                _diskSize = it
            }
        }

    private fun generateDiskSize() {
        Timber.d("cpu: $diskSize")
    }

    /**
     * 用户获取权限后刷新
     */
    fun refresh() {
        generateImei()
    }

    /**
     * 获取 WebView UA
     */
    val userAgent: String
        get() = _userAgent ?: generateUserAgent()


    private fun generateUserAgent(context: Context = GlobalContext.get().get()): String {
        val userAgent = try {
            WebSettings.getDefaultUserAgent(context)
        } catch (e: Exception) {
            System.getProperty("http.agent")
        }
        val sb = StringBuilder()
        if (!userAgent.isNullOrEmpty()) {
            var i = 0
            val length: Int = userAgent.length
            while (i < length) {
                val c: Char = userAgent[i]
                if (c <= '\u001f' || c >= '\u007f') {
                    sb.append(String.format("\\u%04x", c.code))
                } else {
                    sb.append(c)
                }
                i++
            }
        }
        _userAgent = sb.toString()
        return _userAgent ?: ""
    }
}