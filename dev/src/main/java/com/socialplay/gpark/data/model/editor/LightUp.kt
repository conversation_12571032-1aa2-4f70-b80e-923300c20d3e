package com.socialplay.gpark.data.model.editor

import android.os.Parcelable
import com.bin.cpbus.CpEventBus
import kotlinx.parcelize.Parcelize

data class LightUpBody(val gameId: String, val lightUpCnt: Long, val lightUpTimes: Long)

data class SparkLightUpData(val balanceList: List<Balance>?)

data class SparkEvent(
    val gameId: String,
    val lightUpMyCount: Long,
    val lightUpTotalCount: Long,
    val lightUpTimes: Long,
) {

    companion object {
        fun gameSpark(
            gameId: String,
            myCount: Long,
            totalCount: Long,
            times: Long
        ) {
            CpEventBus.post(
                SparkEvent(
                    gameId = gameId,
                    lightUpMyCount = myCount,
                    lightUpTotalCount = totalCount,
                    lightUpTimes = times
                )
            )
        }
    }
}

@Parcelize
data class Balance(val accountType: String, val balance: Long) : Parcelable {

    companion object {
        const val TYPE_USER_SPARK = "YHHH"
        const val TYPE_USER_TINDER = "YHHZ"
        const val TYPE_MAP_SPARK = "ZPHH"

        fun getSparkBalance(balanceList: List<Balance>?): SparkBalance {
            var consumableSpark = 0L
            var unusableSpark = 0L
            balanceList?.forEach {
                if (it.isUserSpark) {
                    consumableSpark = it.balance
                } else if (it.isUserTinder) {
                    unusableSpark = it.balance
                }
            }
            return SparkBalance(consumableSpark, unusableSpark)
        }
    }

    val isUserSpark get() = accountType == TYPE_USER_SPARK
    val isUserTinder get() = accountType == TYPE_USER_TINDER
}

data class UserBalanceRequestBody(val queryType: String, val gameId: String?) {

    companion object {
        const val TYPE_USER = "user"
        const val TYPE_GAME = "game"

        fun user() = UserBalanceRequestBody(TYPE_USER, null)
        fun game(gameId: String) = UserBalanceRequestBody(TYPE_GAME, gameId)
    }
}

data class SparkBalance(val consumable: Long, val unusable: Long)