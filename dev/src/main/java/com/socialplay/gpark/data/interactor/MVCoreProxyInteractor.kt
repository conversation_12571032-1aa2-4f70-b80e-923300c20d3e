package com.socialplay.gpark.data.interactor

import android.content.Context
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.lib.mwbiz.ProxyStartupOptions
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.SingleReplyMutableSharedFlow
import com.socialplay.gpark.util.SingleReplySharedFlow
import com.socialplay.gpark.util.extension.collectIn
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/09/29
 *     desc   :
 */
class MVCoreProxyInteractor {

    //标识是否连接到游戏引擎所在进程，连接到游戏引擎进程后才能进行启动游戏更新View之类的操作
    private val _connectionStatusFlow: SingleReplyMutableSharedFlow<Boolean> = SingleReplyMutableSharedFlow(false)
    val connectionStatusFlow: SingleReplySharedFlow<Boolean> = _connectionStatusFlow

    //标识引擎是否完成初始化，完成初始化后，就可以修改渲染的View了
    private val _engineReadyFlow: SingleReplyMutableSharedFlow<Boolean> = SingleReplyMutableSharedFlow(false)
    val engineReadyFlow: SingleReplySharedFlow<Boolean> = _engineReadyFlow

    //标识引擎是否可用
    private val _engineAvailableFlow: SingleReplyMutableSharedFlow<Boolean> = SingleReplyMutableSharedFlow(false)
    val engineAvailableFlow: SingleReplySharedFlow<Boolean> = _engineAvailableFlow

    private val scope: CoroutineScope = MainScope()

    init {
        connectionStatusFlow.collectIn(scope) {
            if (it) {
                while (MWBizProxy.isConnected() && !MWBizProxy.isAvailable()) {
                    delay(100)
                }

                if (MWBizProxy.isAvailable()) {
                    _engineAvailableFlow.emit(true)
                }
            } else {
                _engineAvailableFlow.emit(false)
            }
        }
    }

    private val isStartupCalled = AtomicBoolean(false)
    fun startup(context: Context) {
        if (!isStartupCalled.compareAndSet(false, true)) {
            return
        }
        if (MWBiz.isAvailable()) {
            startupInternal(context)
        } else {
            MWBiz.addHostProcessEngineAvailable { available, err ->
                startupInternal(context)
            }
        }
    }

    private fun startupInternal(context: Context){

        //调用启动五秒内没有收到Connect事件，则可能启动失败了
        val proxyStartupTimeoutJob = scope.launch {
            delay(5 * 1000)
            Analytics.track(EventConstants.EVENT_MV_PROXY_STARTUP_TIMEOUT)
        }


        MWBizProxy.addEventCallback { key, params ->
            Timber.d("Received proxy event: key=$key params=$params")
            scope.launch {
                when (key) {
                    MWBizProxy.EVENT_CONNECTION_CONNECTED -> {
                        if (proxyStartupTimeoutJob.isActive && !proxyStartupTimeoutJob.isCancelled) {
                            proxyStartupTimeoutJob.cancel("Received proxy service connect event")
                        }
                        Analytics.track(EventConstants.EVENT_MV_PROXY_CONNECTED)
                        _connectionStatusFlow.emit(true)
                    }
                    MWBizProxy.EVENT_CONNECTION_DISCONNECTED -> {
                        val editorGameLoadInteractor = GlobalContext.get().get<EditorGameLoadInteractor>()
                        Analytics.track(
                            EventConstants.EVENT_MV_PROXY_DISCONNECTED,
                            mapOf(
                                "message" to (params?.getOrNull(0) ?: ""),
                                "errorcode" to (params?.getOrNull(1) ?: ""),
                                "is_foreground" to (if(editorGameLoadInteractor.isAvatarViewVisible()) 1 else 0),
                            )
                        )

                        //监听断开连接事件，恢复这些状态值为默认状态
                        EditorGameInteractHelper.resetLoadStatus()

                        _engineReadyFlow.emit(false)
                        _connectionStatusFlow.emit(false)
                    }
                    MWBizProxy.EVENT_ENGINE_READY                             -> {
                        Analytics.track(EventConstants.EVENT_MV_PROXY_READY)
                        _engineReadyFlow.emit(true)
                    }
                    MWBizProxy.EVENT_CONNECT_SERVICE_RETRY_MAX_COUNT_EXCEEDED -> {
                        Analytics.track(EventConstants.EVENT_MV_PROXY_RETRY_MAX_COUNT_EXCEEDED)
                    }
                    MWBizProxy.EVENT_METHOD_EXEC_TIMEOUT -> {
                        Analytics.track(EventConstants.EVENT_METHOD_EXEC_TIMEOUT){
                            put("monitorId", (params?.getOrNull(0) ?: ""))
                            put("actionName", (params?.getOrNull(1) ?: ""))
                            put("args", (params?.getOrNull(2) ?: ""))
                            put("elapsed_time", (params?.getOrNull(3) ?: ""))
                        }
                    }

                    MWBizProxy.EVENT_METHOD_EXEC_START -> {
                        Analytics.track(EventConstants.EVENT_METHOD_EXEC_START){
                            put("actionName", (params?.getOrNull(0) ?: ""))
                            put("args", (params?.getOrNull(1) ?: ""))
                        }
                    }

                    MWBizProxy.EVENT_DETECTED_HOST_PROCESS_RESTARTED -> {
                        Analytics.track(EventConstants.EVENT_MV_PROXY_HOST_PROCESS_RESTART_DETECTED)
                    }
                    else                                                        -> {}
                }
            }
        }

        Analytics.track(EventConstants.EVENT_MV_PROXY_START)

        MWBizProxy.startup(
            context,
            ProxyStartupOptions(
                autoReconnect = true,
                enabledMethodExecTimeoutTracing = true,
                enabledQueuedEvent = true,
                processRestartMaxCount = 5,
                processRestartTimeLimit = TimeUnit.SECONDS.toMillis(5).toInt(),
                methodExecTimeOutInMs = TimeUnit.SECONDS.toMillis(10),
                startServiceMode = PandoraToggle.isOpenAvatarForegroundNotification,
            )
        )
    }
}