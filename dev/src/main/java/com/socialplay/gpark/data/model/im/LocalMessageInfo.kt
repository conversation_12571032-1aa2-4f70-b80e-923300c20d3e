package com.socialplay.gpark.data.model.im

import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.ui.im.conversation.LoadMessageDirection


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/25
 *     desc   :
 *
 */
data class LocalMessageInfo(
    var eldestMessage: Message?,
    var newestMessage: Message?,

    var reqCount: Int,
    var direction: LoadMessageDirection,

    var adapterCount: Int,
    var isClickUnread: Boolean = false,
    var mHasMoreLocalMessagesDown: Boolean,
    var finalCount: Int,
    var scrollMode :Int,
    var getHistoryCount:Int=0,
    var isSuccess :Boolean = false,
    var isClean :Boolean = false
)
