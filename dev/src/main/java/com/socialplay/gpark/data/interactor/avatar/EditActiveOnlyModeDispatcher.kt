package com.socialplay.gpark.data.interactor.avatar

import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner

class EditActiveOnlyModeDispatcher(
    owner: IAvatarLifecycleDispatcherOwner
) : AbstractAvatarLifecycleDispatcher(owner) {

    override fun dispatchEnterEditMode(lifecycleOwner: LifecycleOwner, activity: FragmentActivity) {
        if (isLifecycleOwnerChanged(lifecycleOwner)) {
            resetLifecycleOwner(lifecycleOwner, activity)
            if (owner.isAvailable && owner.isGameLoaded) {
                bindLifecycle()
            }
        }
    }

    override fun dispatchEnterViewMode(lifecycleOwner: LifecycleOwner, activity: FragmentActivity) {
        unbindLifecycle()
        this.lifecycleOwner = null
    }

    override fun dispatchAvailableChanged(app: Application, isAvailable: Boolean) {
        if (!isAvailable) {
            unbindLifecycle()
            dispatchApplicationEvent(app, ApplicationEvent.DESTROY)
        } else {
            dispatchApplicationEvent(app, ApplicationEvent.CREATED)
            if (owner.viewMode == ViewMode.Edit && !isBoundLifecycle() && owner.isGameLoaded) {
                bindLifecycle()
            }
        }
    }

    override fun dispatchAvatarGameLoadFinish(isLoaded: Boolean) {
        if (isLoaded && owner.isAvailable) {
            bindLifecycle()
        }
    }
}