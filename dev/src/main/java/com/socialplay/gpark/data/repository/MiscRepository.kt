package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.successOr
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.MetaSimpleUserDao
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.data.model.feedback.FeedbackOptionData
import com.socialplay.gpark.data.model.feedback.FeedbackRequest
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.im.SystemNotification
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.toLongOrZero
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.Locale

class MiscRepository(
    val metaApi: MetaApi,
    private val db: AppDatabase,
    private val dao: MetaSimpleUserDao,
    private val metaKV: MetaKV
) {

    suspend fun getAvailableHomeNotice() = flow {
        val result = DataSource.getDataResultForApi { metaApi.fetchHomeNotice() }
        if (result.data?.id.isNullOrEmpty()) {
            emit(result)
        } else {
            val closeTime = metaKV.userStatusKV.getHomeNoticeCloseTime(result.data?.id?:"")
            if (DateUtil.isTimeStampToday(SimpleDateFormat("MM-dd-yyyy", Locale.ROOT), closeTime)) {
                emit(DataResult.Success(data = null))
            } else {
                emit(result)
            }
        }
    }

    fun setHomeNoticeClose(noticeId: String, time: Long) {
        metaKV.userStatusKV.setHomeNoticeCloseTime(noticeId, time)
    }

    suspend fun getOperationNoticeList(pageNum: Int, pageSize: Int): Flow<DataResult<List<SystemNotification>>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.getOperationNoticeList(pageNum, pageSize)
        }

        val dataList = result.data?.dataList
        if (result.succeeded) {
            emit(DataResult.Success(data = dataList ?: emptyList()))
        } else {
            emit(DataResult.Error(result.code ?: -1, result.message ?: ""))
        }
    }

    fun markOperationNoticeRead(systemNotification: UniJumpConfig): Flow<DataResult<Any>> = flow {
        val latestReadSystemNotice = metaKV.userStatusKV.getLatestReadSystemNotice()

        if (latestReadSystemNotice == null || latestReadSystemNotice.second < systemNotification.effectiveTimeBegin.toLongOrZero) {
            metaKV.userStatusKV.setLatestReadSystemNotice(systemNotification.id.toString(), systemNotification.effectiveTimeBegin.toLongOrZero)
        }

        emit(DataResult.Success(data = true))
    }

    @Deprecated("replace with getLatestOperationNoticeV2 ")
    suspend fun getLatestOperationNotice(): Flow<DataResult<SystemNotification?>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.getOperationNoticeList(1, 1)
        }

        val dataList = result.data?.dataList
        if (result.succeeded && !dataList.isNullOrEmpty()) {
            val notification = dataList[0]

            //根据本地记录的已读最新消息，调整获取到的消息是否已读标识
            val latestReadSystemNotice = metaKV.userStatusKV.getLatestReadSystemNotice()
            notification.isRead = latestReadSystemNotice != null && latestReadSystemNotice.second >= notification.timeStart

            emit(DataResult.Success(data = notification))
        } else {
            emit(DataResult.Error(result.code ?: -1, result.message ?: ""))
        }
    }

    fun getLatestOperationNoticeV2(): Flow<DataResult<UniJumpConfig>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.getNoticeList(
                mapOf(
                    "scopeCode" to "all",
                    "operatingPosition" to UniJumpConfig.POSITION_IM, /*运营位：1详情页、4飞轮运营位*/
                )
            )
        }

        val dataList = result.data
        if (result.succeeded && !dataList.isNullOrEmpty()) {
            val notification = dataList[0]

            //根据本地记录的已读最新消息，调整获取到的消息是否已读标识
            val latestReadSystemNotice = metaKV.userStatusKV.getLatestReadSystemNotice()
            notification.isRead =
                latestReadSystemNotice != null && latestReadSystemNotice.second >= notification.effectiveTimeBegin.toLongOrZero

            emit(DataResult.Success(data = notification))
        } else {
            emit(DataResult.Error(result.code ?: -1, result.message ?: ""))
        }
    }

    fun getNoticeList(gameId: String?, operatingPosition: String): Flow<DataResult<List<UniJumpConfig>>> = flow {
        if (operatingPosition == UniJumpConfig.POSITION_ROLE_FLY_WHEEL) {
            val cache = metaKV.miscKV.getRoleFlyWheelList()
            if (!cache.isNullOrEmpty()) {
                emit(DataResult.Success(cache, true))
            }
        }
        val result = DataSource.getDataResultForApi {metaApi.getNoticeList(mapOf(
            "scopeCode" to (gameId ?: "all"),
            "operatingPosition" to operatingPosition, /*运营位：1详情页、4飞轮运营位*/
        ))}
        if (operatingPosition == UniJumpConfig.POSITION_ROLE_FLY_WHEEL) {
            metaKV.miscKV.replaceRoleFlyWheelList(result.data)
        }
        emit(result)
    }

    fun getFeedbackOptionList(): Flow<List<FeedbackConfigItem>?> = flow {
        val data = runCatching {
            val netValue = suspendApiNotNull { metaApi.fetchFeedbackTypeList(FeedbackRequest()) }.invoke()
            if (!netValue.isNullOrEmpty()) {
                metaKV.miscKV.replaceFeedbackTypeList(netValue)
            }
            netValue
        }.getOrElse {
            val cache = metaKV.miscKV.getFeedbackTypeList()
            if (cache.isNullOrEmpty()) {
                throw it
            } else {
                cache
            }
        }
        emit(data)
    }

    fun getFeedbackDiscordLink(): suspend () -> String? {
        val cache = metaKV.tTaiKV.feedbackDiscord
        return if (cache.isEmpty()) {
            suspendApi {
                metaApi.getTTaiConfig(TTaiKV.ID_FEEDBACK_DISCORD)
            }.map {
                it?.value
            }
        } else {
            suspend {
                cache
            }
        }
    }

    suspend fun submitNewFeedback(feedbackInfo: SubmitNewFeedbackRequest): Boolean {
        return suspendApiNotNull { metaApi.submitNewFeedback(feedbackInfo) }.invoke()
    }

    fun getTTaiConfigById(resourceId: Int): Flow<DataResult<TTaiConfig>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getTTaiConfig(resourceId) }
        result.successOr(null)?.let {
            metaKV.tTaiKV.saveConfig(it)
        }
        emit(result)
    }

    /**
     * 用gameId获取对应游戏详情的运营位
     */
    fun getGameDetailOperationInfo(
        biz: String,
        bizId: String,
        pageNum: Int,
        pageSize: Int
    ) = suspendApiNotNull {
        metaApi.getGameDetailOperationInfo(
            biz,
            bizId,
            pageNum,
            pageSize
        )
    }
}