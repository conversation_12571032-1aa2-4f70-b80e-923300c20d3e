package com.socialplay.gpark.data.model.videofeed

import android.os.Parcelable
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import kotlinx.parcelize.Parcelize

@Parcelize
data class VideoFeedArgs(
    val resId: ResIdBean,
    val videoId: String? = null,
    val style: Int = STYLE_DEFAULT,
    val dataSource: Int = DATASOURCE_TYPE_RECOMMEND
) : Parcelable {

    companion object {
        const val STYLE_DEFAULT = 0
        const val STYLE_RECOMMEND = 1

        const val DATASOURCE_TYPE_RECOMMEND = 1
    }
}
