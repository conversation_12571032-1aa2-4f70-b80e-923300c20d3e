package com.socialplay.gpark.data.model.post

/**
 * Created by bo.li
 * Date: 2024/3/20
 * Desc: feed底栏中转跳转数据
 */
data class FeedJumpTabRelayData(
    val jumpReason: String,
    val videoPinPostId: String?
) {
    companion object {
        // 跳转视频流
        const val BUNDLE_KEY = "key_jump_feed_extra"
        const val TYPE_JUMP_VIDEO_FEED = "jumpVideoFeed"
        const val TYPE_JUMP_POST_FEED = "jumpPostRecommend"
        const val TYPE_JUMP_POST_FOLLOWING = "jumpPostFollowing"
    }
}

/**
 * feed底栏跳转数据
 * @param [tab] 跳转的tab
 * @see [com.socialplay.gpark.data.model.post.FeedJumpTabRelayData] type 种类
 */
data class FeedJumpTabInfo(
    val tab: CommunityBlockType
)