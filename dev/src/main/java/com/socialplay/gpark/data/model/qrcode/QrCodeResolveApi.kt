package com.socialplay.gpark.data.model.qrcode

import android.os.Parcelable
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.util.GsonUtil
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Type

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/03
 *     desc   :
 * </pre>
 */
data class QrCodeResolveApiRequest(
    val qrStr: String?
)

data class QrCodeResolveApiResponse(
    val type: String?,
    val data: QrCodeResolveApiData?
) {

    companion object {
        const val TYPE_CREATOR_QR_LOGIN = "CREATOR_QR_LOGIN"
    }
}

sealed class QrCodeResolveApiData : Parcelable

@Parcelize
data class CreatorQrLoginData(
    val firstBinding: Boolean = false, // 是否为第一次绑定
    val customToken: String? = null, // firebase token，firebase业务用
    val refreshToken: String?, // refresh token，业务用，双token
    val accessToken: String? = null, // accessToken，业务用，双token
    val accessTokenExpire: Long, // accessToken剩余过期时间
    val userInfo: MetaUserInfo? = null
) : QrCodeResolveApiData() {

    fun toAuthInfoApiResult() = AuthInfoApiResult(
        firstBinding = firstBinding,
        customToken = customToken,
        refreshToken = refreshToken,
        accessToken = accessToken,
        accessTokenExpire = accessTokenExpire,
        userInfo = userInfo
    )
}

class QrCodeResolveApiDeserializer : JsonDeserializer<QrCodeResolveApiResponse?> {

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): QrCodeResolveApiResponse? {
        try {
            val jsonObject = json?.asJsonObject ?: return null
            val type = jsonObject.get("type").asString
            val data = jsonObject.get("data").asJsonObject
            val params = when (type) {
                QrCodeResolveApiResponse.TYPE_CREATOR_QR_LOGIN -> {
                    GsonUtil.gson.fromJson(data, CreatorQrLoginData::class.java)
                }

                else -> {
                    null
                }
            }
            return QrCodeResolveApiResponse(type, params)
        } catch (e: Exception) {
            return null
        }
    }
}

