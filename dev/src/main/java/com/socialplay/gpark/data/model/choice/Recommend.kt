package com.socialplay.gpark.data.model.choice

import com.google.gson.annotations.SerializedName

/**
 * 精选-游戏列表
 * <AUTHOR>
 * @date 2021/07/05
 */
class Recommend {
    var dataList: List<ChoiceGameInfo>? = null
    var total: Int = 0
    var end: Boolean = false
    var group: Int = 0
    var offset: Int? = null
    @SerializedName("request_id")
    var requestId:String? = null
}

//  group:
//                    0-对照组（原来的精选card）
//                    1-样式1（房间双排）
//                    2-样式2（房间单排）