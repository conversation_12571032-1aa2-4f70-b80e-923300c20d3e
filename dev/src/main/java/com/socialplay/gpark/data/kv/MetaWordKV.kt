package com.socialplay.gpark.data.kv

import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.mw.MWConst
import com.socialplay.gpark.util.extension.getEnvValue
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV


class MetaWordKV(override val mmkv: MMKV) : MMKVScope {
    companion object {
        const val KEY_CORE_HOT_FIX_URL = "key_core_hot_fix_url"
        const val KEY_VERSE_ROOM_URL = "key_verse_room_url"
        const val KEY_MW_ENGINE_ENV = "key_mw_engine_env"
        private const val KEY_SELECT_VERSION = "key_select_version"
        const val KEY_MW_DS_VERSION_ENV = "key_mw_ds_version_env"
        const val KEY_MW_CUSTOM_ENV = "key_key_mw_custom_env"
    }

    var coreHotfixUrl by kvProperty(BuildConfig.MW_CORE_URL.getEnvValue(), KEY_CORE_HOT_FIX_URL)

    var verseRoomUrl by kvProperty(BuildConfig.MW_ROOM_URL.getEnvValue(), KEY_VERSE_ROOM_URL)

    var selectVersion by kvProperty("", KEY_SELECT_VERSION)

    var mwCustomDevelopEnvParams by kvProperty("",KEY_MW_CUSTOM_ENV) // 自定义参数

    val isAutoUpdate: Boolean
        get() {
            //指定版本之后，就不适用自动更新逻辑
            return selectVersion == MWConst.USE_ONLINE
        }


    fun getDevDsVersion(): String {
        return mmkv.getString(KEY_MW_DS_VERSION_ENV, "") ?: ""
    }

    fun setDevDsVersion(version: String) {
        mmkv.putString(KEY_MW_DS_VERSION_ENV, version)
    }


}