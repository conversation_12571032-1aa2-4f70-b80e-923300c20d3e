package com.socialplay.gpark.data.model.post

import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.model.post.PostPublish.Companion.filterEmptyUrl
import com.socialplay.gpark.util.extension.ifNullOrBlank

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc: 社区feed流数据结构
 */

data class CommunityFeedWrapper(
    val dataList: List<CommunityFeedInfo>,
    val total: Long,
    val end: Boolean
) {

    val validate: CommunityFeedWrapper
        get() {
            val ts = System.currentTimeMillis()
            return copy(dataList = dataList.map {
                it.copy(
                    mediaList = PostPublish.validateMedias(it.mediaList, ts).filterEmptyUrl(),
                    gameCardList = PostPublish.validateGames(it.gameCardList),
                    tagList = PostPublish.validateTags(it.tagList),
                    styleCardList = PostPublish.validateOutfits(it.styleCardList),
                    clothesCardList = PostPublish.validateUgcDesigns(it.clothesCardList)
                )
            })
        }
}

data class CommunityFeedInfo(
    // 帖子ID
    val postId: String,
    // 点赞数
    val likeCount: Long,
    // 用户点赞状态：0无感、1点赞
    val opinion: Int,
    // 评论数
    val commentCount: Long,
    // 顶部评论列表
    val topCommentList: List<PostCommentInfo>?,
    val uid: String,
    val nickname: String?,
    val avatar: String?,
    val createTime: Long,
    val lastDiscussionTime: Long,
    // 内容
    val content: String?,
    // 点击数
    val clickCount: Long,
    // 所属tag
    val tagList: List<PostTag>?,
    // 资源列表：视频、图片
    val mediaList: List<PostMediaResource>?,
    // 卡片列表
    val gameCardList: List<PostCardInfo>?,
    // 用户状态
    val userStatus: FriendStatus?,
    // 马甲号信息
    val user: UserOfficialExtra?,
    // 是否置顶
    val top: Boolean,
    // 分享数
    val shareCount: Long,
    // 话题列表
    val communityTagList: List<TopicBean>?,
    // 产品线，用不到
    val pack: String? = null,
    // 无游戏圈概念
    val circleId: String? = null,
    // 无游戏圈概念
    val circleName: String? = null,
    // 无title概念
    val title: String? = null,
    // 装扮卡片
    val styleCardList: List<PostStyleCard>? = null,

    // 拍剧拍同款
    val plotCardList: List<PostMomentCard>? = null,

    val clothesCardList: List<PostUgcDesignCard>? = null,

    // 本地用：发布中的帖子的本地id
    val localId: Long? = null,
    // 本地用：是否是发布中的帖子
    val localPublishing: Boolean = false,
    val labelInfo: LabelInfo? = null,
    val status: Int = PostDetail.STATUS_OK
) {

    val tagIds get() = user?.tagIds

    // 本地用：ui排序id，兼容发布中的帖子
    val localUniqueId: String
        get() = if (localId != null) "$localId" else postId

    val imageList: List<PostMediaResource>?
        get() = mediaList?.filter { it.resourceType == PostMediaResource.TYPE_IMG }

    val firstVideo: PostMediaResource?
        get() = videoList?.firstOrNull()

    val videoList: List<PostMediaResource>?
        get() = mediaList?.filter { it.resourceType == PostMediaResource.TYPE_VIDEO }

    val opinionLike: Boolean
        get() = opinion == OPTION_LIKE

    val canGoRoom
        get() = userStatus != null && userStatus.toLocalStatus() == FriendStatus.PLAYING_GAME && userStatus.gameStatus?.room?.roomIdFromCp != null

    val outfit: PostStyleCard?
        get() = if (canTryOn) styleCardList?.firstOrNull { it.isOutfit } else null

    val ugcDesign: PostUgcDesignCard?
        get() = clothesCardList?.firstOrNull { it.isUgcDesign }

    val canTryOn: Boolean
        get() = user?.ootdPrivateSwitch ?: false

    val reviewOk get() = PostDetail.isOk(status)
    val reviewInProgress get() = PostDetail.isInProgress(status)
    val reviewFail get() = PostDetail.isFail(status)
    val reviewStatus2TrackParam get() = PostDetail.convertReviewStatus2TrackParam(status)

    val netThumbNail
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.cover
            ?: mediaList?.firstOrNull { it.isNetResource && it.isImage }?.resourceValue
    val netVideo
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.resourceValue

    companion object {
        const val OPTION_LIKE = 1
        const val OPTION_NO_STATE = 0

        fun getOpinion(like: Boolean): Int {
            return if (like) OPTION_LIKE else OPTION_NO_STATE
        }
    }
}