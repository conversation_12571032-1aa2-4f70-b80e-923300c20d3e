package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendRequestInfo
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.succeeded
import com.socialplay.gpark.data.model.friend.FriendRequestInfoWrapper
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */
class FriendRequestsPagingSource(val size: Int) : PagingSource<Int, FriendRequestInfoWrapper>() {

    override fun getRefreshKey(state: PagingState<Int, FriendRequestInfoWrapper>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FriendRequestInfoWrapper> {

        val cur = params.key ?: 1
        val prevKey = null

        return try {
            val data = FriendBiz.getFriendRequestList(cur, size)
            val items = data.data?.dataList?.map { FriendRequestInfoWrapper(it) } ?: emptyList()
            val isEnd = data.data?.end == true || items.isNullOrEmpty()
            val nextKey = if (isEnd) null else cur + 1
            Timber.d("FriendRequestsPagingSource：cur:$cur, prevKey:$prevKey, nextKey:$nextKey, params:$params")
            if (data.succeeded) {
                LoadResult.Page(items, prevKey, nextKey)
            } else {
                LoadResult.Error(NullPointerException())
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}