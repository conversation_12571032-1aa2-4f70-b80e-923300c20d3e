package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/20
 *     desc   :
 * </pre>
 */
internal class MetaAppDatabase_Migration_6_7 : Migration(6, 7) {
    override fun migrate(database: SupportSQLiteDatabase) {
        try {
            //language=RoomSql
            database.execSQL(
                "ALTER TABLE `im_user` ADD COLUMN `isOfficial` INTEGER NOT NULL DEFAULT 0"
            )
        } catch (ignore: Throwable) {
        }
    }
}