package com.socialplay.gpark.data.model.user

/**
 * Created by bo.li
 * Date: 2022/9/28
 * Desc:
 * https://mock.metaapp.cn/project/189/interface/api/cat_7938
 */
// 0 无关系 1 我对目标有关系 2 目标对我有关系 3 互有关系
enum class UserRelation(val value: Int) {
    None(value = 0),
    Me2Other(value = 1),
    Other2Me(value = 2),
    Both(value = 3)
}

// 屏蔽
enum class RelationType(val value: String) {
    Block(value = "block"),
    Follow(value = "follow"),
    AiBot("AiBot")
}

// 关系方向
enum class RelationDirection(val value: String) {
    Forward(value = "forward"),
    Reverse(value = "reverse")
}