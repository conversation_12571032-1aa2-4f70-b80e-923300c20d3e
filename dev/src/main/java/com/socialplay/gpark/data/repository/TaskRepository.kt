package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.task.DailyTaskInfo
import com.socialplay.gpark.data.model.task.FinishDailyTaskBody
import kotlinx.coroutines.CoroutineScope

class TaskRepository(
    private val api: MetaApi,
    private val coroutineScope: CoroutineScope,
    private val metaKV: MetaKV,
) {
    suspend fun hasDailyTaskReward(): suspend () -> Boolean = suspendApiNotNull {
        api.queryDailyTaskRewardStatus()
    }.map { it == 1 }


    fun finishDailyTask(activityId: String, logicId: String): suspend () -> Boolean =
        suspendApiNotNull {
            api.finishDailyTask(FinishDailyTaskBody(activityId, logicId))
        }

}