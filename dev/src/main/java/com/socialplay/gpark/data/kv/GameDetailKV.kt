package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.util.GsonUtil
import com.tencent.mmkv.MMKV

/**
 * Created by bo.li
 * Date: 2021/5/25
 * Desc:
 */
class GameDetailKV(private val mmkv: MMKV, private val accountKV: AccountKV) {
    companion object {
        private const val KEY_ENTERED_GAME_DETAIL_TIMES = "key_entered_game_detail_times_"
        private const val KEY_TRENDING_INSERT_GAME_INFO = "key_trending_insert_game_info_"
        private const val KEY_TRENDING_INSERT_GAME_TIME = "key_trending_insert_game_time_"
    }

    /**
     * 获取进详情页的次数
     */
    fun getEnteredGames(gameId: String): Long {
        val times = mmkv.getLong("$KEY_ENTERED_GAME_DETAIL_TIMES$gameId", 0) + 1
        mmkv.putLong("$KEY_ENTERED_GAME_DETAIL_TIMES$gameId", times)
        return times
    }

    fun generateLocalLoadGameID(): String {
        val key = "generateLocalLoadGameID"
        val id = mmkv.getLong(key, Long.MIN_VALUE)
        mmkv.putLong(key, id + 1)
        return id.toString()
    }

    /**
     * 保存首页trending强插游戏信息与时间
     */
    fun saveTrendingInsertGame(gameInfo: ChoiceGameInfo) {
        if (mmkv.getString(KEY_TRENDING_INSERT_GAME_INFO, null).isNullOrEmpty()) {
            mmkv.putString(KEY_TRENDING_INSERT_GAME_INFO, GsonUtil.gson.toJson(gameInfo))
            mmkv.putLong(KEY_TRENDING_INSERT_GAME_TIME, System.currentTimeMillis())
        }
    }

    /**
     * 清除首页trending强插游戏信息与时间
     */
    fun clearTrendingInsertGame() {
        mmkv.remove(KEY_TRENDING_INSERT_GAME_INFO)
        mmkv.remove(KEY_TRENDING_INSERT_GAME_TIME)
    }

    /**
     * 获取首页trending强插游戏信息
     */
    fun getTrendingInsertGame(): ChoiceGameInfo? {
        return GsonUtil.gsonSafeParse(mmkv.getString(KEY_TRENDING_INSERT_GAME_INFO, null))
    }

    /**
     * 获取首页trending强插游戏时间
     */
    fun getTrendingInsertTime(): Long {
        return mmkv.getLong(KEY_TRENDING_INSERT_GAME_TIME, 0)
    }
}