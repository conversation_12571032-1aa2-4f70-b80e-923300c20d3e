package com.socialplay.gpark.data.model.room

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.IChoiceItem
import org.koin.core.context.GlobalContext

/**
 * @des:
 * @author: lijunjia
 * @date: 2023/7/11 17:14
 */
data class ChatRoomInfo(
    val roomId: String = "",
    val roomName: String = "",
    val limitNumber: Int = 0,
    val number: Int = 0,
    val tag: String? = null,
    val description: String = "",
    var gameId: String = "",
    var platformGameId:String = "",
    val version: String = "",
    val style: String = "",
    val creatorUuid: String = "",
    val creatorNickname: String = "",
    val creatorAvatar: String = "",
    val aliveTime: Long = 0,
    val members: List<RoomMemberInfo>? = null,
    val image: String = ""

):IChoiceItem
{
    var pkg: String? = null // ugc语音房模板创建房间时传入

    fun getRoomStyle(): String {
        return if (style.isNullOrEmpty()) GlobalContext.get().get<Context>().getString(R.string.club_cap) else style
    }
}

data class RoomMemberInfo(
    val roomId: String = "",
    val nickname: String = "",
    val avatar: String = "",
    val gender: String = "",
)


class HomeRoomGameConfig {
    val gameId: String? = null
}