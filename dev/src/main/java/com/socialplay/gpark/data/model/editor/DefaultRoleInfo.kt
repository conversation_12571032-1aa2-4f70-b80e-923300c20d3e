package com.socialplay.gpark.data.model.editor

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2022/10/9
 * Desc: 角色默认形象
 */
@Parcelize
data class DefaultRoleInfo(
    val id: String,
    // 全身像
    val wholeBodyImage: String?,
    // 全身像2
    val wholeBodyImage2: String?,
    // 头像
    val portrait: String?,
    // 本地用：是否选择
    var isSelected: Boolean = false
): Parcelable