package com.socialplay.gpark.data.model.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/20
 *     desc   :
 * </pre>
 */
@Entity(tableName = "share_record")
data class ShareRecordEntity(
    @PrimaryKey
    val id: String,
    val platform: String,
    val shareTime: Long = System.currentTimeMillis()
)