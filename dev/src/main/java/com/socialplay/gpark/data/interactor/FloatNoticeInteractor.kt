package com.socialplay.gpark.data.interactor

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.asFlow
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.mgs.data.model.*
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendShareResult
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.succeeded
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.errMsg
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.FloatNoticeShowData
import com.socialplay.gpark.data.model.friend.CommonShareResult
import com.socialplay.gpark.data.model.friend.SendFriendAskData
import com.socialplay.gpark.data.model.groupchat.JoinGroupChatAskData
import com.socialplay.gpark.data.model.mgs.MgsInviteData
import com.socialplay.gpark.data.model.mgs.MgsShareContent
import com.socialplay.gpark.data.model.notification.ImGroupMessage
import com.socialplay.gpark.data.model.notification.ImPrivateMessage
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.friend.FriendConstants
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.notice.message.IMNotificationManager
import com.socialplay.gpark.ui.view.floatnotice.FloatNoticeView
import com.socialplay.gpark.ui.view.floatnotice.FloatNoticeView.Companion.TYPE_V1
import com.socialplay.gpark.ui.view.floatnotice.FloatNoticeView.Companion.TYPE_V2
import com.socialplay.gpark.util.ProcessUtil
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.cancellable
import okhttp3.internal.toLongOrDefault
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * @author: ning.wang
 * @date: 2021-11-10 11:40 上午
 * @desc:
 */
class FloatNoticeInteractor(private val accountInteractor: AccountInteractor, private val friendInteractor: FriendInteractor, private val metaRepository: com.socialplay.gpark.data.IMetaRepository) {

    companion object {
        private const val TAG = "WnFloatNoticeInteractor"

        private const val SOURCE = "source_game_invite_dialog"

        // 命令类型：MGS游戏内邀请
        const val TYPE_UNIT_INVITE = "unit_invite"

        // 类型：web页邀请的游戏房间邀请（无命令）
        const val TYPE_MGS_GAME_SHARE = "mgs_game_share"

        // 命令类型：好友申请
        const val TYPE_SEND_FRIEND_ASK = "send_friend_ask"

        /**
         * 命令类型：加群申请
         */
        const val TYPE_CHAT_GROUP_ASK = "chat_group_ask"

        // 类型：邀请加好友弹窗（无命令）
        const val TYPE_INVITE_ADD_FRIEND = "avatar_add_friend"

        // 类型：小屋邀请加好友弹窗（无命令）
        const val TYPE_INVITE_ADD_FRIEND_HUT = "hut_add_friend"
        //Im私聊消息
        const val TYPE_IM_PRIVATE_MESSAGE= "im_private_message"

        /**
         * 群聊消息
         */
        const val TYPE_IM_GROUP_MESSAGE= "im_group_message"

        // 命令类型：封禁
        const val TYPE_USER_LOGIN_LIMIT = "USER_LOGIN_LIMIT"

        // 命令类型：警告
        const val TYPE_USER_REPORT_WARN = "USER_REPORT_WARN"

        // 加入方式：不加入房间
        const val JOIN_MODE_OTHER = -1

        // 加入方式：加入房间
        const val JOIN_MODE_JOIN = 0
    }

    private var floatNoticeView: FloatNoticeView? = null

    private var resourceContext: Context = GlobalContext.get().get()
    private val imNotificationManager = IMNotificationManager(friendInteractor)


    fun showFloatNotice(
        resourceContext: Context,
        activity: Activity,
        fragment: Fragment?,
        type: String,
        any: Any?,
        curGamePackageName: String?,
        curGameId: String?,
        isInGame: Boolean
    ) {
        this.resourceContext = resourceContext
        setShowData(activity, fragment, type, any, curGamePackageName, curGameId, isInGame)
    }

    fun showFloatNotice(resourceContext: Context, activity: Activity, fragment: Fragment?, shareId: String, type: String) {
        this.resourceContext = resourceContext
        MainScope().launch {
            accountInteractor.accessTokenLiveData.asFlow().cancellable().collect {
                it ?: return@collect
                when(type) {
                    MgsShareContent.TYPE_JOIN_ROOM -> {
                        fetchAndShowJoinRoom(this, activity, fragment, shareId)
                    }
                    MgsShareContent.TYPE_FRIEND_SHARE,  -> {
                        fetchAndShowAvatarAddFriend(this, activity, fragment, shareId, TYPE_INVITE_ADD_FRIEND)
                    }
                }
            }
        }
    }

    private suspend fun fetchAndShowJoinRoom(scope: CoroutineScope, activity: Activity, fragment: Fragment?, shareId: String) {
        MgsBiz.getMgsRoomByShareId(shareId).collect { result ->
            val data = result.data
            if (result.succeeded && data != null) {
                withContext(Dispatchers.Main) {
                    setShowData(activity, fragment, TYPE_MGS_GAME_SHARE, result.data, null, null, false)
                }
            }
            scope.cancel()
        }
    }

    private suspend fun fetchAndShowAvatarAddFriend(scope: CoroutineScope, activity: Activity, fragment: Fragment?, shareId: String, shareType: String) {
        FriendBiz.getInviteInfoByShareId(shareId).collect { result ->
            if (result.succeeded && result.data != null) {
                withContext(Dispatchers.Main) {
                    setShowData(activity, fragment, shareType, result.data, null, null, false)
                }
            }
            scope.cancel()
        }
    }

    /**
     * 处理弹窗要展示的数据
     *
     * @param type 类型
     * @param json 数据json
     */
    private fun setShowData(
        activity: Activity,
        fragment: Fragment?,
        type: String,
        bean: Any?,
        curGamePackageName: String?,
        curGameId: String?,
        isInGame: Boolean
    ) {
        Timber.tag(TAG).d("type: $type, bean: $bean")
        kotlin.runCatching {
            when (type) {
                // MGS游戏邀请
                TYPE_UNIT_INVITE -> {
                    val data = (bean as MgsInviteData?) ?: return
                    val location = if (!isInGame) "user_in_app" else "user_in_games"
                    getFloatNoticeView(
                        activity,
                        type = if (PandoraToggle.isIMEntrance) TYPE_V2 else TYPE_V1
                    )?.setData(data.toShowData(), isInGame, onShowFloat = {
                        joinRoomAnalytics(EventConstants.GAME_INVITE_TOAST_SHOW, data, location)
                    }, action = {
                        if (isCancel(it)) return@setData
                        handleMgsInvite(
                            activity,
                            fragment,
                            it,
                            data,
                            curGamePackageName,
                            curGameId,
                            location
                        )

                    })
                }
                //MGS游戏分享
                TYPE_MGS_GAME_SHARE -> {
                    val data = bean as MgsGameShareResult? ?: return
                    getFloatNoticeView(activity)?.setData(data.toShowData(), isInGame) {
                        if (isCancel(it)) return@setData
                        handleMgsShare(activity, fragment, it, data, curGamePackageName, curGameId)
                    }
                }
                //好友邀请
                TYPE_SEND_FRIEND_ASK -> {
                    if (!PandoraToggle.enableFriendRequestPopUp) return
                    if (!accountInteractor.enableFriendRequestsNotice) return
                    val data = bean as? SendFriendAskData ?: return
                    Analytics.track(
                        EventConstants.FRIENDS_REQUEST_UPS_DISPLAY,
                        "source" to (if (isInGame) 1 else 2)
                    )
                    imNotificationManager.playMedia(GlobalContext.get().get())
                    getFloatNoticeView(
                        activity,
                        type = TYPE_V2,
                        imType = TYPE_SEND_FRIEND_ASK
                    )?.setData(data.toShowData(curGameId, curGamePackageName), isInGame) {
                        handleAddFriend(it, isInGame, data, activity, fragment)
                    }
                }

                // 邀请加好友
                TYPE_INVITE_ADD_FRIEND -> {
                    (bean as? FriendShareResult)?.let {
                        handleInviteAvatarAddFriend(it, activity, isInGame, type)
                    }
                }
                // 小屋邀请加好友
                TYPE_INVITE_ADD_FRIEND_HUT -> {
                    (bean as? CommonShareResult)?.let {
                        handleInviteHutAddFriend(it, activity, isInGame, type)
                    }
                }
                //im私聊
                TYPE_IM_PRIVATE_MESSAGE -> {
                    (bean as? ImPrivateMessage)?.let {
                        handleIMMessage(
                            bean,
                            fragment,
                            activity,
                            isInGame,
                            curGameId,
                            TYPE_IM_PRIVATE_MESSAGE,
                            it.toShowData(activity)
                        )
                    }
                }
                // IM 群聊
                TYPE_IM_GROUP_MESSAGE -> {
                    (bean as? ImGroupMessage)?.let {
                        handleIMMessage(
                            bean,
                            fragment,
                            activity,
                            isInGame,
                            curGameId,
                            TYPE_IM_GROUP_MESSAGE,
                            it.toShowData(activity)
                        )
                    }
                }
                // 加群申请
                TYPE_CHAT_GROUP_ASK -> {
                    val data = bean as? JoinGroupChatAskData ?: return
                    imNotificationManager.playMedia(GlobalContext.get().get())
                    getFloatNoticeView(
                        activity,
                        type = TYPE_V2,
                        imType = TYPE_CHAT_GROUP_ASK
                    )?.setData(data.toShowData(curGameId, curGamePackageName), isInGame) {
                        handleJoinGroupAsk(it, isInGame, data, activity, fragment)
                    }
                }
                else -> {
                }
            }
        }
    }

    private var imMessageNoticeViewRef: WeakReference<FloatNoticeView?>? = null

    private fun tryHideIMMessageNoticeView() {
        val imMessageNoticeView = imMessageNoticeViewRef?.get() ?: return
        if (!imMessageNoticeView.isEnd() && imMessageNoticeView.isAutoDisMiss) {
            imMessageNoticeView.hideView()
        }
        imMessageNoticeViewRef = null
    }

    private fun handleIMMessage(
        bean:Any?,
        fragment: Fragment?,
        activity: Activity,
        isInGame: Boolean,
        curGameId: String?,
        imType: String,
        showData: FloatNoticeShowData
    ){
        val data = bean ?: return
        if (!imNotificationManager.showMessageView()) {
            //免打扰，或者已经关闭通知
            return
        }
        tryHideIMMessageNoticeView()
        val context: Application = GlobalContext.get().get()
        Analytics.track(
            EventConstants.EVENT_IM_UPS_DISPLAY,
            mapOf("source" to if (fragment == null) "0" else "1")
        )
        imNotificationManager.playMedia(context)
        val floatIMMessageNoticeView = updateFloatNoticeView(activity, true, FloatNoticeView.TYPE_IM_RECEIVE_MESSAGE, imType)
        if (floatIMMessageNoticeView != null) {
            imMessageNoticeViewRef = WeakReference(floatIMMessageNoticeView)
        }
        floatIMMessageNoticeView?.setData(
            showData,
            isInGame
        ) { action ->
            if (action == FloatNoticeView.ACTION_CANCEL || action == FloatNoticeView.ACTION_SLIDE_CANCEL) {
                floatIMMessageNoticeView.hideView()
                imNotificationManager.release()
                return@setData
            }
            if (isCancelPrivate(action, activity.application, isInGame)) return@setData
            imNotificationManager.dialogDismiss()
            imNotificationManager.release()
            val height =
                floatIMMessageNoticeView.height.toFloat() + floatIMMessageNoticeView.viewTop
            if (data is ImPrivateMessage) {
                data.receiveTime = System.currentTimeMillis()
                imNotificationManager.handlerPrivateMessage(
                    activity,
                    context,
                    curGameId,
                    data,
                    height,
                    (floatIMMessageNoticeView.right - floatIMMessageNoticeView.getViewWidth()).toFloat(),
                    (floatIMMessageNoticeView.right).toFloat()
                ) {
                    floatIMMessageNoticeView.hideView()
                }
            } else if (data is ImGroupMessage) {
                data.receiveTime = System.currentTimeMillis()
                imNotificationManager.handlerGroupMessage(
                    activity,
                    context,
                    curGameId,
                    data,
                    height,
                    (floatIMMessageNoticeView.right - floatIMMessageNoticeView.getViewWidth()).toFloat(),
                    (floatIMMessageNoticeView.right).toFloat()
                ) {
                    floatIMMessageNoticeView.hideView()
                }
            }
        }
    }

    /**
     * 处理 申请加好友
     *
     * @param actionResult -1 上滑取消或自动取消 0 同意 1 拒绝
     */
    private fun handleAddFriend(
        actionResult: Int,
        isInGame: Boolean,
        data: SendFriendAskData,
        activity: Activity,
        fragment: Fragment?
    ) {
        when (actionResult) {
            FloatNoticeView.ACTION_AGREE -> {
                Analytics.track(EventConstants.FRIENDS_REQUEST_UPS_ACCEPT)
                acceptAddFriend(data.uid, data.nickname, isInGame, fragment)
            }

            FloatNoticeView.ACTION_REFUSE,
            FloatNoticeView.ACTION_CANCEL,
            FloatNoticeView.ACTION_SLIDE_CANCEL -> {
                Analytics.track(EventConstants.FRIENDS_REQUEST_UPS_CLOSE)
                hideFloatNotice()
            }
        }
    }

    private fun handleJoinGroupAsk(
        actionResult: Int,
        isInGame: Boolean,
        data: JoinGroupChatAskData,
        activity: Activity,
        fragment: Fragment?
    ) {
        when (actionResult) {
            FloatNoticeView.ACTION_AGREE -> {
                acceptGroupJoin(data.askId)
            }

            FloatNoticeView.ACTION_REFUSE,
            FloatNoticeView.ACTION_CANCEL,
            FloatNoticeView.ACTION_SLIDE_CANCEL -> {
                hideFloatNotice()
            }
        }
    }

    /**
     * 展示小屋邀请添加好友弹窗
     */
    private fun handleInviteHutAddFriend(data: CommonShareResult, activity: Activity, isInGame: Boolean, type: String) {
        getFloatNoticeView(activity)?.setData(data.toShowData(), isInGame, onShowFloat = {
            inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_HOME_TOAST_SHOW, type, data.shareUser?.uid ?: "")
        }, action = {
            if (it == FloatNoticeView.ACTION_REFUSE || it == FloatNoticeView.ACTION_SLIDE_CANCEL) {
                inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_HOME_TOAST_CLOSE, type, data.shareUser?.uid ?: "")
            } else if (it == FloatNoticeView.ACTION_AGREE) {
                inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_HOME_TOAST_ADD, type, data.shareUser?.uid ?: "")
                friendInteractor.applyAddFriend(data.shareUser?.uid ?: "", "", "", type)
            }
        })

    }

    /**
     * 展示邀请添加好友弹窗
     */
    private fun handleInviteAvatarAddFriend(data: FriendShareResult, activity: Activity, isInGame: Boolean, type: String) {
        getFloatNoticeView(activity, true)?.setData(data.toShowData(), isInGame, onShowFloat = {
            inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_AVATAR_TOAST_SHOW, type, data.content?.uid ?: "")
        }, action = {
            if (it == FloatNoticeView.ACTION_REFUSE || it == FloatNoticeView.ACTION_SLIDE_CANCEL) {
                inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_AVATAR_TOAST_CLOSE, type, data.content?.uid ?: "")
            } else if (it == FloatNoticeView.ACTION_AGREE) {
                inviteFriendAnalytics(EventConstants.EVENT_FRIENDS_AVATAR_TOAST_ADD, type, data.content?.uid ?: "")
                friendInteractor.applyAddFriend(data.content?.uid ?: "", "", "", if (type == TYPE_INVITE_ADD_FRIEND) "social_friend_share" else type)
            }
        })
    }

    private fun inviteFriendAnalytics(event: Event, type: String, otherUid: String) {
        Analytics.track(event) {
            put("other_uuid", otherUid)
            put("type", type)
        }
    }

    private fun joinRoomAnalytics(event: Event, data: MgsInviteData, location: String) {
        Analytics.track(event) {
            putAll(
                mapOf(
                    "accept_location" to location,
                    "gameid" to data.gameId,
                    "gamepkg" to data.packageName,
                )
            )
        }
    }

    private fun acceptAddFriend(
        uuid: String,
        nickcname: String,
        isInGame: Boolean,
        fragment: Fragment?
    ) = GlobalScope.launch {
        val result = FriendBiz.agreeFriendRequest(uuid, FriendConstants.SOURCE_FRIEND_APPLY_DIALOG)
        withContext(Dispatchers.Main) {
            hideFloatNotice()
            if ((result.succeeded && result.data == true) || FriendBiz.hasFriendByUuid(uuid)) {
                ToastUtil.showShort(R.string.mgs_request_friend_add_success)
                friendInteractor.refreshFriendsUnreadRequestsAsync()
                if (!isInGame && fragment != null && fragment.isAdded) {
                    Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 0) }
                    kotlin.runCatching {
                        MetaRouter.IM.gotoConversation(
                            fragment,
                            uuid,
                            nickcname,
                            source = "friend_requests_dialog"
                        )
                    }
                }
            } else {
                ToastUtil.showShort(R.string.failed_to_process_request)
            }
        }
    }

    private fun acceptGroupJoin(
        askId:String?
    ) = GlobalScope.launch {
        askId ?: return@launch
        val askIdLong = askId.toLongOrDefault(-1)
        if (askIdLong == -1L) {
            return@launch
        }
        val result = metaRepository.processApplyJoinGroupChat(
            askIdLong,
            true
        )
        withContext(Dispatchers.Main) {
            hideFloatNotice()
            if (result.succeeded && result.data != null) {
                ToastUtil.showShort(R.string.toast_notification_group_chat_apply_agree_success)
            } else {
                val context: Application = GlobalContext.get().get()
                ToastUtil.showShort(
                    result.message
                        ?: context.getString(R.string.toast_notification_group_chat_apply_agree_failed)
                )
            }
        }
    }

    private fun handleMgsInvite(
        activity: Activity,
        fragment: Fragment?,
        actionResult: Int,
        data: MgsInviteData,
        curGamePackageName: String?,
        curGameId: String?,
        location: String
    ) {
        hideFloatNotice()
        if (actionResult == FloatNoticeView.ACTION_REFUSE) {
            joinRoomAnalytics(EventConstants.GAME_INVITE_TOAST_REFUSE_CLICK, data, location)
            return
        }
        joinRoomAnalytics(EventConstants.GAME_INVITE_TOAST_ACCEPT_CLICK, data, location)
        launchGame(
            activity,
            fragment,
            data.roomIdFromCp,
            data.packageName,
            data.gameId,
            data.toGameRoom(),
            data.fromUuid,
            curGamePackageName,
            curGameId
        )
    }


    private fun getFloatNoticeView(activity: Activity, isAutoDisMiss: Boolean = true,  type: Int = TYPE_V1,imType: String? = null): FloatNoticeView? {
        if (imType == TYPE_IM_PRIVATE_MESSAGE || imType == TYPE_IM_GROUP_MESSAGE) {
            // IM 私聊/群聊 消息弹窗
            val floatIMMessageNoticeView = FloatNoticeView.showView(
                resourceContext,
                activity,
                isAutoDisMiss,
                type
            )
            return floatIMMessageNoticeView
        }
        if (floatNoticeView != null) {
            floatNoticeView?.hideView()
            floatNoticeView = null
        }
        floatNoticeView = FloatNoticeView.showView(resourceContext, activity, isAutoDisMiss, type)
        return floatNoticeView
    }

    private fun updateFloatNoticeView(
        activity: Activity,
        isAutoDisMiss: Boolean = true,
        type: Int = TYPE_V1,
        imType: String = "imType"
    ): FloatNoticeView? {
       return getFloatNoticeView(activity, isAutoDisMiss, type, imType)
    }

    private fun handleMgsShare(activity: Activity, fragment: Fragment?, actionResult: Int, shareResult: MgsGameShareResult, curGamePackageName: String?, curGameId: String?) {
        floatNoticeView?.hideView()

        if (actionResult == FloatNoticeView.ACTION_REFUSE) {
            return
        }
        val gameInfo = shareResult.content
        gameInfo?.apply {
            //启动游戏
            launchGame(
                activity,
                fragment,
                roomIdFromCp,
                packageName ?: "",
                gameId ?: "",
                toGameRoom(),
                fromUuid,
                curGamePackageName,
                curGameId
            )
        }
    }

    fun launchGame(
        activity: Activity,
        fragment: Fragment?,
        roomIdFromCp: String?,
        packageName: String,
        gameId: String,
        gameRoom: MgsBriefRoomInfo?,
        fromUuid: String?,
        curGamePackageName: String?,
        curGameId: String?
    ) {
        GlobalScope.launch {
            // 处理TS -> TS的邀请
            val isTsProcess = ProcessUtil.checkTsProcessAlive(resourceContext) && !ProcessUtil.isMainProcess(resourceContext)
            if (isTsProcess) {
                //游戏进程
                if (isTsProcess && !roomIdFromCp.isNullOrEmpty() && !curGamePackageName.isNullOrEmpty() && gameId.isNotEmpty()) {
                    val tempResult = MgsBiz.canJoinMgsGameRoomV2(gameId, roomIdFromCp)
                    var canJoinRoomResult = if (!tempResult.succeeded) tempResult.errMsg() else null
                    val nowRoomId = MgsBiz.getMgsRoomInfo(gameId)?.roomIdFromCp
                    if (nowRoomId == roomIdFromCp) {
                        //已经在当前房间里
                        canJoinRoomResult = resourceContext.getString(R.string.join_room_error)
                    }
                    if (canJoinRoomResult.isNullOrEmpty()) {
                        MgsBiz.joinRoomEvent(gameId, curGamePackageName, roomIdFromCp)
                        joinRoomResultAnalytics(gameId, packageName, "success")
                        Timber.tag(TAG)
                            .d("processName: ${ProcessUtil.getCurrentProcessName()}, $packageName -- $gameId running.... send joinRoomEvent to game $roomIdFromCp")
                    } else {
                        withContext(Dispatchers.Main) {
                            ToastUtil.gameShowShort(canJoinRoomResult)
                        }
                        joinRoomResultAnalytics(gameId, packageName, canJoinRoomResult)
                    }
                    return@launch
                }
            }

            val joinMode = if (roomIdFromCp.isNullOrEmpty()) JOIN_MODE_OTHER else JOIN_MODE_JOIN
            val roomInfo = if (roomIdFromCp.isNullOrEmpty()) null else gameRoom
            fragment?.let {
                MgsGameRoomLauncher.enterMgsGame(fragment, packageName, gameId, roomInfo, SOURCE, joinMode, fromUuid)
            }
            // 按理说不会走到这，TS -> TS前面处理，233 -> TS不用这个方法
//            ?: MgsGameRoomLauncher.enterMgsGameInGame(GlobalContext.get().get(), packageName, gameId, roomInfo, SOURCE, joinMode)
        }
    }

    /**
     * 判断是否是自动取消或上滑关闭
     *
     * @param action 操作类型
     * @param type 消息类型
     * @param packageName 游戏包名
     * @return
     */
    private fun isCancel(action: Int): Boolean {
        if (action == FloatNoticeView.ACTION_CANCEL || action == FloatNoticeView.ACTION_SLIDE_CANCEL) {
//            slideCloseAnalytics(type, packageName)
            hideFloatNotice()
            return true
        }
        return false
    }
    /**
     * 判断是否是自动取消或上滑关闭
     *
     * @param action 操作类型
     * @param type 消息类型
     * @param packageName 游戏包名
     * @return
     */
    private fun isCancelPrivate(action: Int, context: Context, isInGame:Boolean?): Boolean {
        if (action == FloatNoticeView.ACTION_REFUSE) {
            //免打扰
            imNotificationManager.saveIMTipLastTime(context, isInGame)
            imNotificationManager.release()
            return true
        } else if (action == FloatNoticeView.ACTION_CANCEL || action == FloatNoticeView.ACTION_SLIDE_CANCEL) {
            imNotificationManager.release()
            return true
        }
        return false
    }

    /**
     * 隐藏通知弹窗
     */
    private fun hideFloatNotice() {
        floatNoticeView?.hideView()
        floatNoticeView = null
    }

    /**
     * 上滑关闭埋点
     *
     * @param type 类型
     * @param packageName 游戏包名
     */
    private fun slideCloseAnalytics(type: String, packageName: String?) {
//        val event = when (type) {
//            TYPE_SEND_FRIEND_ASK                  -> {
//                FloatNoticeEvent.EVENT_FLOAT_NOTICE_FRIEND_APPLY_SLIDE_CLOSE
//            }
//            TYPE_MGS_GAME_SHARE, TYPE_UNIT_INVITE -> {
//                FloatNoticeEvent.EVENT_FLOAT_NOTICE_INVITE_GAME_SLIDE_CLOSE
//            }
//            else                                  -> {
//                null
//            }
//        }
//        event?.let {
//            val builder = Analytics.kind(it)
//            packageName?.let { gamePackageName ->
//                builder.put("gamepkg", gamePackageName)
//            }
//            builder.send()
//        }
    }

    private fun MgsGameShareResult.toShowData(): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        return FloatNoticeShowData(
            content?.fromAvatar ?: "",
            content?.fromNickName ?: "",
            context.getString(R.string.invited_you_to_play, content?.gameName),
            null,
            context.getString(R.string.floatnotice_mgs_invite_agree_txt),
            content?.gameId ?: "",
            content?.gameName ?: "",
            content?.packageName ?: ""
        )
    }

    private fun FriendShareResult.toShowData(): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        val titleBackup = context.getString(R.string.share_invite_friend_title, context.getString(R.string.app_name))
        return FloatNoticeShowData(
            content?.portrait ?: "",
            content?.nickname ?: "",
            titleBackup,
            null,
            context.getString(R.string.meta_mgs_add_friend),
            "",
            "",
            ""
        )
    }

    private fun CommonShareResult.toShowData(): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        val titleBackup = context.getString(R.string.share_invite_friend_title, context.getString(R.string.app_name))
        return FloatNoticeShowData(
            shareUser?.portrait ?: "",
            shareUser?.nickname ?: "",
            titleBackup,
            null,
            context.getString(R.string.meta_mgs_add_friend),
            "",
            "",
            ""
        )
    }

    private fun MgsGameShareInfo.toGameRoom(): MgsBriefRoomInfo {
        return MgsBriefRoomInfo(roomIdFromCp = this.roomIdFromCp, roomName = null, roomShowNum = this.roomShowNum, roomTags = null)
    }

    fun MgsInviteData.toGameRoom(): MgsBriefRoomInfo {
        return MgsBriefRoomInfo(roomIdFromCp = this.roomIdFromCp, roomName = null, roomShowNum = null, roomTags = null)
    }

    fun MgsInviteData.toShowData(): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        return FloatNoticeShowData(
            this.avatar,
            this.nickname,
            this.inviteText,
            null,
            context.getString(R.string.floatnotice_mgs_invite_agree_txt),
            this.gameId,
            this.gameName,
            this.packageName,
            refuseText =  context.getString(R.string.invite_refuse)
        )
    }

    fun SendFriendAskData.toShowData(gameId: String?, gamePkg: String?): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        return FloatNoticeShowData(
            this.portrait,
            context.getString(R.string.friend_request),
            context.getString(
                R.string.mgs_request_friend_content,
                this.nickname,
                this.reason ?: context.getString(R.string.friend_requests)
            ),
            null,
            context.getString(R.string.friend_agree),
            gameId.orEmpty(),
            "",
            gamePkg.orEmpty(),
            imType = TYPE_SEND_FRIEND_ASK
        )
    }

    private fun JoinGroupChatAskData.toShowData(gameId: String?, gamePkg: String?): FloatNoticeShowData {
        val context: Application = GlobalContext.get().get()
        return FloatNoticeShowData(
            this.askAvatar ?: "",
            context.getString(R.string.notification_title_group_chat_apply),
            context.getString(
                R.string.notification_content_group_chat_apply,
                this.askNickname,
                this.groupName,
            ),
            null,
            context.getString(R.string.notification_group_chat_apply_agree),
            gameId.orEmpty(),
            "",
            gamePkg.orEmpty(),
            imType = TYPE_CHAT_GROUP_ASK
        )
    }

    private fun joinRoomResultAnalytics(gameId: String, packageName: String, canJoinRoomTip: String) {
        Analytics.track(EventConstants.JOIN_ROOM_RESULT) {
            putAll(
                mapOf(
                    "gameid" to gameId,
                    "gamepkg" to packageName,
                    "result" to canJoinRoomTip
                )
            )
        }
    }
}