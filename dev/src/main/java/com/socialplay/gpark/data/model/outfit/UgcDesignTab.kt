package com.socialplay.gpark.data.model.outfit

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
@Parcelize
data class ProfileCurrentCloth(
    val itemId: String,
    val iconUrl: String?,
    val tagType: Int,
    val ugc: Boolean,
    val published: Boolean,
    val guid: String?,
    val tags: List<String>?,
    val acquiredType: Int,
    val price: Long?,
) : Parcelable {

    companion object {
        const val TYPE_FREE = 1
        const val TYPE_STORE = 3
        const val TYPE_CREDIT = 8
        const val TYPE_VIP = 2
        const val TYPE_DRAW = 5
        const val TYPE_EVENT = 4
        const val TYPE_GIFT_PACK = 7
    }

    val viewable: Boolean get() = ugc && published
    val trackId get() = guid ?: itemId
}

data class ProfileCurrentClothesRequest(
    val targetUuid: String
)

data class UgcDesignProfileTag(
    val code: Int,
    val msg: String?
)

data class UgcAssetEntrance(
    val code: Int,
    val msg: String?,
    val children: List<UgcAssetEntrance>?
) {
    companion object {
        const val ENTRANCE_VISIBILITY = -3
        const val ENTRANCE_SORT = -2
        const val ENTRANCE_CATEGORY = -1

        const val ENTRANCE_UGC_DESIGN = 0
        const val ENTRANCE_UGC_MOD = 1
    }

    fun toUgcDesignProfileTag() = UgcDesignProfileTag(code, msg)
}

data class UgcDesignProfileTagRequest(
    val entrance: Int
) {

    companion object {
        fun design() = UgcDesignProfileTagRequest(UgcAssetEntrance.ENTRANCE_UGC_DESIGN)
        fun module() = UgcDesignProfileTagRequest(UgcAssetEntrance.ENTRANCE_UGC_MOD)
    }
}

data class UgcAssetProfile(
    val itemId: String,
    val iconUrl: String?,
    val pinned: Boolean,
    val published: Boolean,
    val guid: String?,
) {

    val trackId get() = guid ?: itemId
}

data class UgcDesignProfileRequest(
    val targetUuid: String,
    val tag: Int,
    val pageNumber: Int,
    val pageSize: Int
)

data class UgcDesignProfileResponse(
    val isLastPage: Boolean,
    val data: List<UgcAssetProfile>?,
)

data class UgcAssetProfileEntrance(
    val entrance: Int,
    val msg: String?,
    val items: List<UgcAssetProfile>?
) {
    val isSupported get() = isDesign || isModule
    val isDesign get() = entrance == 0
    val isModule get() = entrance == 1
}