package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.room.CanJoinRoom
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.data.model.room.CottageVisitor
import com.socialplay.gpark.data.model.room.CreateRoomBody
import com.socialplay.gpark.data.model.room.GetRoomListBody
import com.socialplay.gpark.data.model.room.GetRoomListResult
import com.socialplay.gpark.data.model.room.RoomStyle
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * @des:
 * @author: lijunjia
 * @date: 2023/7/11 17:28
 */
class HomeRoomRepository(val metaApi: MetaA<PERSON>, val tTaiKV: TTaiKV) {

    suspend fun getHomeRoomList(
        gameIds: List<String>?,
        pageNum: Int,
        pageSize: Int
    ): DataResult<GetRoomListResult> {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getHomeRoomList(
                GetRoomListBody(
                    current = pageNum,
                    size = pageSize,
                    gameIds = gameIds
                )
            )
        }
        return dataResult
    }

    fun getHomeRoomListV2(
        gameIds: List<String>?,
        pageNum: Int,
        pageSize: Int
    ): suspend () -> GetRoomListResult = suspendApiNotNull {
        metaApi.getHomeRoomList(
            GetRoomListBody(
                current = pageNum,
                size = pageSize,
                gameIds = gameIds
            )
        )
    }

    suspend fun createHomeRoom(
        roomStyle: RoomStyle,
        roomName: String,
        roomTag: String
    ): Flow<DataResult<ChatRoomInfo>> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.createHomeRoom(
                CreateRoomBody(
                    roomStyle.gameId,
                    roomName,
                    roomTag,
                    roomStyle.style,
                    roomStyle.image
                )
            )
        }
        emit(dataResult)
    }

    suspend fun getHomeRoomDetail(roomId: String): Flow<DataResult<ChatRoomInfo>> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getHomeRoomDetail(roomId)
        }
        emit(dataResult)
    }

    fun canJoinRoom(roomId: String, version: String = ""): Flow<DataResult<CanJoinRoom>> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.canJoinRoom(hashMapOf("roomId" to roomId).apply {
                if (version.isNotEmpty()) put("version", version)
            })
        }
        emit(dataResult)
    }

    fun getCottageRoomList(currentPage: Int) = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getCottageRoomList(currentPage)
        }
        if (tTaiKV.dsItem.isNullOrEmpty()) {
            DataSource.getDataResultForApi {
                metaApi.getTTaiConfig(TTaiKV.ID_DS_ROOM_ITEM)
            }.let {
                it.data?.let { it1 -> tTaiKV.saveConfig(it1) }
            }
        }
        val dsItemImage =  tTaiKV.dsItem
        dataResult.data?.houses?.map { it.image= if(dsItemImage.isNullOrEmpty()) it.image else dsItemImage }
        emit(dataResult.data)
    }

    fun getCottageRoomInfo(roomId: String): Flow<CottageRoomInfo?> =flow{
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getCottageRoomInfo(roomId)
        }
        if (tTaiKV.dsDetail.isNullOrEmpty()) {
            DataSource.getDataResultForApi {
                metaApi.getTTaiConfig(TTaiKV.ID_DS_ROOM_DETAIL)
            }.let {
                it.data?.let { it1 -> tTaiKV.saveConfig(it1) }
            }
        }
        val dsDetailImage = tTaiKV.dsDetail
        dataResult.data?.image =
            if (dsDetailImage.isNullOrEmpty()) dataResult.data?.image else dsDetailImage
        emit(dataResult.data)
    }

    fun getHomeVisitorCount(roomId: String): Flow<List<CottageVisitor>?> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getHomeVisitorCount(roomId)
        }
        emit(dataResult.data)
    }

    suspend fun getUserHouse(uuid: String)= flow {
        emit(DataSource.getDataResultForApi {
            metaApi.queryUserHouseInfo(uuid)
        })

    }

    fun fetchMyChatRoomPublishedList(lastId: String?) = suspendApiNotNull {
        metaApi.fetchMyChatRoomPublishedList(mapOf("lastId" to lastId))
    }

    fun fetchChatRoomTemplateList() = suspendApiNotNull {
        metaApi.fetchChatRoomTemplateList()
    }

}