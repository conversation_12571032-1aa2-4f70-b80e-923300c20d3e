package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

class GroupChatKV(private val mmkv: MMKV) {
    companion object {
        // 是否有创群权限
        private const val KEY_CREATE_GROUP_POWER = "key_create_group_power_"
        private const val KEY_GROUP_MEMBER_LIST_LATEST_VERSION =
            "key_group_member_list_latest_version_"
        private const val KEY_GROUP_MEMBER_LIST_CACHE_VERSION =
            "key_group_member_list_cache_version_"
    }

    fun haveCreateGroupPower(uuid: String): Boolean {
        return mmkv.getBoolean("$KEY_CREATE_GROUP_POWER${uuid}", false)
    }

    fun saveCreateGroupPower(uuid: String, value: Boolean) {
        mmkv.putBoolean("$KEY_CREATE_GROUP_POWER${uuid}", value)
    }

    fun updateGroupMemberLatestVersion(
        groupId: Long,
        versionCode: Long,
    ) {
        mmkv.putLong("$KEY_GROUP_MEMBER_LIST_LATEST_VERSION${groupId}", versionCode)
    }

    fun updateGroupMemberCacheVersion(
        groupId: Long,
        versionCode: Long,
    ) {
        mmkv.putLong("$KEY_GROUP_MEMBER_LIST_CACHE_VERSION${groupId}", versionCode)
    }

    fun getGroupMemberLatestVersion(
        groupId: Long
    ): Long {
        return mmkv.getLong("$KEY_GROUP_MEMBER_LIST_LATEST_VERSION${groupId}", -1)
    }

    fun getGroupMemberCacheVersion(
        groupId: Long
    ): Long {
        return mmkv.getLong("$KEY_GROUP_MEMBER_LIST_CACHE_VERSION${groupId}", -1)
    }
}