package com.socialplay.gpark.data.api

/**
 * Created by bo.li
 * Date: 2023/10/31
 * Desc: 双token
 * https://meta.feishu.cn/wiki/TcH4wEqWyiaAK2kXdXmcj3w3nFe
 * https://meta.feishu.cn/wiki/PgZiwpkMJiiYZVk9ny3cwd6Mn5C
 */

const val NAMED_DTOKEN_RETROFIT = "dtoken-retrofit"
const val NAMED_DTOKEN_OKHTTP_CLIENT = "dtoken-okhttp-client"

// 刷新accessToken接口 https://mock.metaapp.cn/project/811/interface/api/45045
const val API_URL_REFRESH_ACCESS_TOKEN = "authorize/v2/dtoken/token/renew"