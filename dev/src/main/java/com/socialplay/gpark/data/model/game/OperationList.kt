package com.socialplay.gpark.data.model.game

/**
 * Created by bo.li
 * Date: 2022/6/24
 * Desc: 运营位列表
 */
data class OperationList(
    val dataList: MutableList<OperationInfo>,
    val end: Boolean,
    val total: Int
)

data class OperationInfo(
    // 业务名称
    val biz: String?,
    // 业务ID
    val bizId: String?,
    // 内容
    val content: String?,
    // 内容类型: post/url
    val contentType: String?,
    val createTime: Long,
    val endTime: Long,
    val opTag: OperationTag?,
    val positionId: String?,
    val startTime: Long,
    // 状态:1未开始;2已开始;3已结束
    val status: Int,
    // 标题
    val title: String?,
    val updateTime: Long
) {

    companion object {
        private const val TYPE_URL = "url"
        private const val TYPE_ARTICLE = "post"
    }

    fun isArticleType(): Boolean {
        return contentType == TYPE_ARTICLE
    }

    fun isWebType(): Boolean {
        return contentType == TYPE_URL
    }
}

data class OperationTag(
    // 业务名称
    val biz: String?,
    // 标签
    val tag: String?,
    // 标签底色
    val tagBgColor: String?,
    // 运营标签ID
    val tagId: Int
)