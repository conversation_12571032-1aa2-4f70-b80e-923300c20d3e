package com.socialplay.gpark.data.model.videofeed

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.socialplay.gpark.data.model.post.PostDetail
import kotlinx.parcelize.Parcelize


/**
 * 视频流ApiResult
 */
@Parcelize
data class VideoFeedApiResult(
    @SerializedName("postDetails", alternate = ["items"])
    val items: List<PostDetail>?,

    @SerializedName("version", alternate = ["reqId"])
    val reqId: String?,

    @SerializedName("offset")
    val offset: Int,

    val isEnd: Boolean
) : Parcelable