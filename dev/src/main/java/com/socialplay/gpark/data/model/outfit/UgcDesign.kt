package com.socialplay.gpark.data.model.outfit

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcDesignLikeRequest(
    val feedId: String,
    val positive: Boolean
)

class UgcDesignGetRequest private constructor(
    val feedIds: String
) {
    companion object {
        fun single(feedId: String) = UgcDesignGetRequest(feedId)
        fun multiple(feedIds: List<String>) = UgcDesignGetRequest(feedIds.joinToString(","))
    }
}

data class UgcDesignEditRequest(
    val feedId: String,
    val title: String,
    val comment: String
)

data class UgcDesignDeleteRequest(
    val feedId: String
)