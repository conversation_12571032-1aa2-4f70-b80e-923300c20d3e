package com.socialplay.gpark.data.model

import android.os.Parcelable
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.socialplay.gpark.util.GsonUtil
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Type

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2022/05/05
 * desc   :
 * </pre>
 */


data class QrResult(
    val action: String?,
    val data: QrParams?
) {
    companion object {
        const val ACTION_PREVIEW_GAME = "previewerGame"
        const val ACTION_SSO_LOGIN = "qrLogin"
        const val ACTION_SCHEME = "scheme"
    }
}

sealed class QrParams : Parcelable

@Parcelize
data class PreviewGameToken(
    val token: String,
    val runLevel: String?
) : QrParams()

@Parcelize
data class SsoLoginRequest(
    val tips: String?,
    val icon: String?,
    val url: String
) : QrParams()

@Parcelize
data class SchemeJump(
    val schemeUrl: String
) : QrParams()

class QrResultDeserializer : JsonDeserializer<QrResult?> {

    override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): QrResult {
        try {
            val jsonObject = json.asJsonObject
            val action = jsonObject.get("action").asString
            val data = jsonObject.get("data").asJsonObject
            val params = when (action) {
                QrResult.ACTION_PREVIEW_GAME -> {
                    GsonUtil.gson.fromJson(data, PreviewGameToken::class.java)
                }
                QrResult.ACTION_SSO_LOGIN -> {
                    GsonUtil.gson.fromJson(data, SsoLoginRequest::class.java)
                }
                QrResult.ACTION_SCHEME -> {
                    GsonUtil.gson.fromJson(data, SchemeJump::class.java)
                }
                else -> {
                    null
                }
            }
            return QrResult(action, params)
        } catch (e: Exception) {
            error("Failed to parse QR code")
        }
    }
}