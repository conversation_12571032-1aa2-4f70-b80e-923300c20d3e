package com.socialplay.gpark.data.model.choice

import com.socialplay.gpark.data.model.LoadStatus

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/16 1:51 下午
 * @describe:
 * @param isUpdateAdapter 是否更新列表样式 page==1
 * @param originalDataList 接口请求到的原始数据
 * @param group 0-对照组卡片样式，1-一行一个，2-一行2个
 */
class ChoiceResultEvent(var loadStatus: LoadStatus, var originalDataList: MutableList<ChoiceCardInfo>? = null, var isUpdateAdapter: Boolean = false, var group: Int = 0, var message: String? = null) {
    override fun toString(): String {
        return "ChoiceResultEvent[ loadType=$loadStatus, originalDataList=${originalDataList?.size}, isUpdateAdapter=$isUpdateAdapter, message=$message ]"
    }

    fun toDetailedString(): String {
        var cards: String = ""
        var total = 0
        originalDataList?.forEach { card ->
            cards = "$cards\n   cardId:${card.cardId}, cardType:${card.cardType}, contentType:${card.contentType}, gameSize:${card.gameList?.size}, cardName:${card.cardName}"
            total += card.gameList?.size ?: 0
        }
        return "ChoiceResultEvent[ loadType=$loadStatus, isUpdateAdapter=$isUpdateAdapter, originalDataList=${originalDataList?.size}, message=$message, dataListSize=$total, $cards \n]"
    }
}

