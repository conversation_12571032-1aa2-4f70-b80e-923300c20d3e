package com.socialplay.gpark.data.model

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/16
 * desc   :
 * </pre>
 */


data class GameItem(
    val id: String,
    val name: String,
    val icon: String,
    val packageName: String,
    val playerCount: Long, // 玩家数量
    val likeCount: Long, // 点赞数量
    val rate: Double?, // 点赞百分比
    val fakeRating: Float, // rating 3-5之间
    val startupExtension: String?,
    val squareIcon: String,
    val type: Int //游戏类型 按二进制位区分 0b1：普通安装包游戏； 0b10：普通存档类游戏； 0b100：MetaVerse游戏home_page
){

    fun isTsGame(): Boolean {
        //直接使用 GameDetailInfo 类中的类型,避免后面直接修改一份就可以全部生效
        return type and GameDetailInfo.GAME_TYPE_TS == GameDetailInfo.GAME_TYPE_TS
    }

    /**
     * 游戏类型转换成String类型（用于发送埋点）
     */
    fun typeToString(): String {
        return when {
            isTsGame() -> "ts"
            else -> "apk"
        }
    }

}