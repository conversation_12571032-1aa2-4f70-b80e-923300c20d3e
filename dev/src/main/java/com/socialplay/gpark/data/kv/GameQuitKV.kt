package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.game.DelayedExitCheckRecord
import com.socialplay.gpark.data.model.game.GameQuitInfo
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.MMKVScope
import com.tencent.mmkv.MMKV

class GameQuitKV(override val mmkv: MMKV) : MMKVScope {


    companion object {
        const val GAME_QUITE_NPS_STARTED_GAMES = "game_quit_started_games"
        const val DELAYED_EXIT_CHECK_RECORD_PREFIX = "delayed_exit_check_record_"
    }


    fun addStartedGame(gameInfo: GameQuitInfo) {
        val startedGames = ArrayList(getStartedGames()).apply {
            removeAll { it.pkg == gameInfo.pkg }
        }

        startedGames.add(gameInfo)

        mmkv.putString(GAME_QUITE_NPS_STARTED_GAMES, GsonUtil.safeToJson(startedGames, "[]"))
    }

    fun removeStartedGame(pkg: String) {
        val startedGames = ArrayList(getStartedGames())
        startedGames.removeAll { it.pkg ==pkg }
        mmkv.putString(GAME_QUITE_NPS_STARTED_GAMES, GsonUtil.safeToJson(startedGames, "[]"))
    }

    fun getStartedGames(): List<GameQuitInfo> {
        return GsonUtil.gsonSafeParseCollection(mmkv.getString(GAME_QUITE_NPS_STARTED_GAMES, "[]")) ?: emptyList()
    }

    fun getStartedGame(pkg: String): GameQuitInfo? {
        return getStartedGames().find { it.pkg == pkg }
    }

    fun addDelayExitCheckRecord(record: DelayedExitCheckRecord) {
        mmkv.putString(DELAYED_EXIT_CHECK_RECORD_PREFIX + record.pkg, GsonUtil.safeToJson(record))
    }

    fun removeDelayExitCheckRecord(packageName: String) {
        mmkv.remove(DELAYED_EXIT_CHECK_RECORD_PREFIX + packageName)
    }

    fun getDelayExitCheckRecord(packageName: String): DelayedExitCheckRecord? {
        val recordStr = mmkv.getString(DELAYED_EXIT_CHECK_RECORD_PREFIX + packageName, null) ?: return null
        return GsonUtil.gsonSafeParse(recordStr)
    }

}