package com.socialplay.gpark.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(
    tableName = "game_play_time"
//    ,
//    indices = [
//        Index(value = ["game_id"], name = "index_game_play_time_game_id"),
//        Index(value = ["package_name"], name = "index_game_play_time_package_name"),
//        Index(value = ["record_time"], name = "index_game_play_time_record_time")
//    ]
)
data class PlayGameTimeEntity(
    @ColumnInfo(name = "game_id") val gameId: String?,
    @ColumnInfo(name = "package_name") val packageName: String?,
    @ColumnInfo(name = "duration") val duration: Long,
    @ColumnInfo(name = "session") val session: String,
    @ColumnInfo(name = "record_time") val recordTime: Long = System.currentTimeMillis(),
    @ColumnInfo(name = "file_id") val fileId: String? = null,
    @PrimaryKey(autoGenerate = true) val id: Long = 0
) {
}
