package com.socialplay.gpark.data.model

import com.socialplay.gpark.BuildConfig

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/07/09
 * desc   :
 * </pre>
 */


sealed class ApiErrorInfo(val url: String)

class ApiErrorTimeout(url: String) : ApiErrorInfo(url) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (other == null || javaClass != other.javaClass) return false
        val o = other as ApiErrorInfo
        return url == o.url
    }

    override fun hashCode(): Int {
        return url.hashCode()
    }

    override fun toString(): String {
        if (BuildConfig.DEBUG) {
            return "${javaClass.simpleName}  $url"
        }
        return super.toString()
    }


}

class ApiErrorCode(url: String, val code: Int) : ApiErrorInfo(url) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (other == null || javaClass != other.javaClass) return false
        val o = other as ApiErrorCode
        return code == o.code && url == o.url
    }

    override fun hashCode(): Int {
        return arrayOf(url, code).contentHashCode()
    }

    override fun toString(): String {
        if (BuildConfig.DEBUG) {
            return "${javaClass.simpleName} $url $code"
        }
        return super.toString()
    }
}