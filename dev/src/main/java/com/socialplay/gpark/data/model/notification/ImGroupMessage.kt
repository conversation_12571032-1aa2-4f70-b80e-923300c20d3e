package com.socialplay.gpark.data.model.notification

import android.content.Context
import com.ly123.tes.mgs.im.ImMessageHelper
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.MessageContent
import com.ly123.tes.mgs.metacloud.origin.MessageCustomData
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.model.FloatNoticeShowData

class ImGroupMessage(
    val customData: MessageCustomData?,
    val messageId: String,
    val content: String,
    var receiveTime: Long,
    val atType: Message.ATType,
) {
    fun toShowData(context: Context): FloatNoticeShowData {
        return FloatNoticeShowData(
            icon = this.customData?.groupInfo?.icon ?: "",
            title = this.customData?.groupInfo?.name ?: "",
            inviteText = content,
            contentText = content,
            agreeText = context.getString(R.string.im_tip_reply),
            gameId = "",
            gameName = "",
            packageName = "",
            isClickDismiss = false,
            imType = FloatNoticeInteractor.TYPE_IM_GROUP_MESSAGE,
            showTime = System.currentTimeMillis(),
            atType = atType,
        )
    }
    companion object{
        fun getSummerContent(messageContent: MessageContent?, context: Context): CharSequence {
            if (messageContent == null) {
                return ""
            }
            val messageProvider =
                ImMessageHelper.getInstance().getMessageTemplate(messageContent.javaClass)
            if (messageProvider != null) {
                val content = messageProvider.getContentSummary(context, messageContent)
                content?.let {
                    return content.toString().replace("\n", " ")
                }
            }
            return context.getString(R.string.im_unknown_content)
        }
        fun isSupportMessage(message: Message): Boolean {
            if (message.messageType == Message.MessageType.UNKNOWN) {
                //自定义消息
                return false
            }
            val messageProvider = ImMessageHelper.getInstance().getMessageTemplate(message.content.javaClass)
            return messageProvider != null
        }
    }
}