package com.socialplay.gpark.data.kv

import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/17
 * desc   :
 * </pre>
 */


class BuildConfigKV(override val mmkv: MMKV) : MMKVScope {

    companion object {
        const val KEY_BASE_URL = "BASE_URL"
        private const val TEMPORARY_HOUR = 30 * 60 * 1000L
        const val META_APP_DEVELOPER_TOGGLE = "meta_app_developer_toggle"
        private const val DEV_OPEN_STATUS = "DEV_OPEN_STATUS"
        private const val DEV_OPEN_STATUS_TIME = "DEV_OPEN_STATUS_TIME"
    }

    fun putString(key: String, value: String) {
        mmkv.putString(key, value)
    }

    fun putInt(key: String, value: Int) {
        mmkv.putInt(key, value)
    }

    fun putBoolean(key: String, value: Boolean) {
        mmkv.putBoolean(key, value)
    }

    fun putFloat(key: String, value: Float) {
        mmkv.putFloat(key, value)
    }

    fun putLong(key: String, value: Long) {
        mmkv.putLong(key, value)
    }


    fun hasKey(key: String): Boolean {
        return mmkv.containsKey(key)
    }

    fun removeKey(key: String) {
        mmkv.remove(key)
    }

    fun getInt(key: String): Int {
        return mmkv.getInt(key, -1)
    }

    fun getLong(key: String): Long {
        return mmkv.getLong(key, -1L)
    }

    fun getString(key: String): String? {
        return mmkv.getString(key, "")
    }

    fun getFloat(key: String): Float {

        return mmkv.getFloat(key, -1f)
    }

    fun getBoolean(key: String): Boolean {
        return mmkv.getBoolean(key, false)
    }


    private var isDevOpenStatus: Boolean by kvProperty(false, DEV_OPEN_STATUS)
    private var devOpenStatusDeadLine: Long by kvProperty(0L, DEV_OPEN_STATUS_TIME)

    fun isAlreadyOpenDev(): Boolean {
        return isDevOpenStatus && devOpenStatusDeadLine >= System.currentTimeMillis()
    }

    fun setDeveloperOpen(open: Boolean) {
        isDevOpenStatus = open
        if (open) {
            devOpenStatusDeadLine =
                System.currentTimeMillis() + (if (BuildConfig.DEBUG) TEMPORARY_HOUR * 2 * 24 * 30L else TEMPORARY_HOUR)
        }
    }
}