package com.socialplay.gpark.data.model.pay

object IAPConstants {
    //内购支付场景 - PG币
    const val IAP_SCENE_PG_COIN = "PG_COIN"
    //内购支付场景 - 会员
    const val IAP_SCENE_VIP_PLUS = "VIP_PLUS"
    const val IAP_SCENE_VIP_PLUS_RENEW = "VIP_PLUS_RENEW"
    //重新订阅默认值
    const val IAP_SCENE_VIP_PLUS_RENEW_ORDER = ""

    /**
     * 业务员编码
     */
    const val IAP_SCENE_CODE_PG_COIN: Int = 190
    /**
     * 业务员编码
     */
    const val IAP_SCENE_CODE_SUBS: Int = 150

    /**
     * 商品类型: 代币
     */
    const val IAP_PRODUCT_TYPE_PG_COIN = "64"
    /**
     * 商品类型: 会员赠送
     */
    const val IAP_PRODUCT_TYPE_MEMBER_AWARD = "2048"

    /**
     * 是否支持该场景
     */
    fun isSceneSupport(scene: String?) = listOf(IAP_SCENE_PG_COIN, IAP_SCENE_VIP_PLUS, IAP_SCENE_VIP_PLUS_RENEW).contains(scene)

    /**
     * 支付渠道
     */
    const val PAY_CHANNEL_GOOGLE = 65
    const val PAY_CHANNEL_SIMULATE= 16
    fun getPayChannel(): Int {
        return if (com.socialplay.gpark.BuildConfig.ENV_TYPE.lowercase() == "test") PAY_CHANNEL_SIMULATE else PAY_CHANNEL_GOOGLE
    }
    fun getSceneCodeOrDefault(scene: String): Int = if(scene == IAP_SCENE_VIP_PLUS_RENEW || scene == IAP_SCENE_VIP_PLUS) IAP_SCENE_CODE_SUBS else IAP_SCENE_CODE_PG_COIN
}