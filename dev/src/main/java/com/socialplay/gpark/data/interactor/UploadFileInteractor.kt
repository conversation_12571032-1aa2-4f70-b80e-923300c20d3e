package com.socialplay.gpark.data.interactor

import android.app.Application
import com.luck.picture.lib.utils.MediaUtils
import com.meta.lib.api.resolve.data.model.code
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.exception
import com.meta.lib.api.resolve.data.model.message
import com.meta.lib.api.resolve.data.model.succeeded
import com.meta.upload.core.IsUploadCancelled
import com.meta.upload.core.MetaUpload
import com.meta.upload.core.UploadProgressHandler
import com.meta.upload.core.data.model.UploadResult
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.oss.OssHelper
import com.socialplay.gpark.function.videocompression.ISiliCompressor
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.MediaFile
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import top.zibin.luban.Luban
import java.io.File

/**
 * 腾讯云上传文件文档
 * https://cloud.tencent.com/document/product/436/12159
 *
 * 七牛云上传文件文档
 * https://developer.qiniu.com/kodo/1236/android#3
 */
class UploadFileInteractor(val context: Application) {

    companion object {
        const val UPLOAD_FAILED_CODE_ILLEGAL = 451
        const val BITRATE = 900000
        const val BIZ_CODE_FEEDBACK = "user_feedback_resource"
        const val BIZ_CODE_COMMUNITY = "community-temp"
        const val BIZ_CODE_OC_SHORTS_BACKGROUND = "pj_custom_bgimg"
        // 举报复用反馈
        const val BIZ_CODE_REPORT = BIZ_CODE_FEEDBACK
        const val BIZ_CODE_AI_BOT ="ai-bot-ugc-image"
    }

    fun init() {
        OssHelper.init()
    }

    /**
     * 多个文件上传
     */
    fun uploadList(
        bizCode: String,
        files: List<File>,
        randomDir: Boolean = true,
        taskId: String = "",
        progressHandler: UploadProgressHandler = { _, _, _, _, _ -> },
        compressHandler: (File) -> File = { it },
        isCancelled: IsUploadCancelled = { _, _ -> false }
    ): Flow<List<DataResult<UploadResult?>>> =
        MetaUpload.uploadList(
            context,
            bizCode,
            files,
            randomDir,
            taskId,
            progressHandler,
            compressHandler,
            isCancelled
        ).map { array ->
            buildList(array.size) {
                for (result in array) {
                    add(convert(result))
                }
            }
        }

    /**
     * 上传单个文件
     * @return 文件url, 返回null 代表上传失败
     */
    fun uploadSingle(
        bizCode: String,
        file: File,
        randomDir: Boolean = true,
        taskId: String = "",
        progressHandler: UploadProgressHandler = { _, _, _, _, _ -> },
        compressHandler: (File) -> File = { it },
        isCancelled: IsUploadCancelled = { _, _ -> false }
    ): Flow<DataResult<UploadResult?>> = MetaUpload.uploadSingle(
        context,
        bizCode,
        file,
        randomDir,
        taskId,
        1,
        1,
        progressHandler,
        compressHandler,
        isCancelled
    ).map {
        convert(it)
    }

    /**
     * 依次上传文件
     */
    fun uploadList1By1(
        bizCode: String,
        files: List<File>,
        randomDir: Boolean = true,
        taskId: String = "",
        progressHandler: UploadProgressHandler = { _, _, _, _, _ -> },
        compressHandler: (File) -> File = { it },
        isCancelled: IsUploadCancelled = { _, _ -> false }
    ): Flow<DataResult<UploadResult?>> = MetaUpload.uploadList1By1(
        context,
        bizCode,
        files,
        randomDir,
        taskId,
        progressHandler,
        compressHandler,
        isCancelled
    ).map {
        convert(it)
    }

    fun <T> convert(src: com.meta.lib.api.resolve.data.model.DataResult<T>): DataResult<T?> =
        if (src.succeeded) {
            DataResult.Success(src.data)
        } else {
            DataResult.Error(src.code ?: 0, src.message.orEmpty(), src.exception as? Exception)
        }

    fun compress(src: File): File {
        val path = src.absolutePath
        return kotlin.runCatching {
            when {
                MediaFile.isVideoFileType(path) -> {
                    val size = MediaUtils.getVideoSize(context, path)
                    if (size.width > 0 && size.height > 0) {
                        val compressPath = FileUtil.createFileWithSuffix(
                            DownloadFileProvider.uploadCacheFolder.absolutePath,
                            ".mp4"
                        )
                        ISiliCompressor.compressVideo(
                            context,
                            src.absolutePath,
                            compressPath,
                            size.width,
                            size.height,
                            BITRATE
                        )
                        val compressedFile = File(compressPath)
                        if (compressedFile.length() <= 1024 || compressedFile.length() > src.length()) {
                            src
                        } else {
                            compressedFile
                        }
                    } else {
                        src
                    }
                }

                MediaFile.isGifFileType(path) -> {
                    src
                }

                else -> {
                    Luban.with(context).load(path).get()[0]
                }
            }
        }.getOrElse { src }
    }
}