package com.socialplay.gpark.data.model.editor

import java.io.Serializable

/**
 * Created by bo.li
 * Date: 2022/3/7
 * Desc:
 */
data class TSTypeInfo(
    // ugc/pgc的游戏id
    val code: String,
    // 游戏包名
    val packageName: String?,
    // 游戏标签
    val tags: ArrayList<Int>?,
    // 游戏类型
    val type: Int,
    // 游戏名称
    val displayName: String?
):Serializable {
    companion object {
        // type: PGC游戏
        private const val PGC_TYPE = 1
        // type: UGC游戏
        private const val UGC_TYPE = 2

        // type: UGC游戏
        private const val MGS_TAG = 1
    }

    fun isUgcGame(): Boolean {
        return type == UGC_TYPE
    }

    fun isMgsGame(): Boolean {
        return tags?.contains(MGS_TAG) == true
    }
}