package com.socialplay.gpark.contract.web.legacy

import android.webkit.WebView
import com.meta.lib.web.core.WebCore
import com.meta.web.model.BasicWebArgs
import com.meta.web.ui.EmbeddedWebCoreFragment


@Deprecated("Not in use anymore, contact web dept.")
class EmbeddedLegacyJsApiContract(
    private val fragment: EmbeddedWebCoreFragment,
    private val webCore: WebCore,
    private val webView: WebView,
    private val webArgs: BasicWebArgs,
) : AbsLegacyJsApiContract(fragment, webCore, webView, webArgs)