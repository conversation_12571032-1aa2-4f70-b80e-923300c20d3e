package com.socialplay.gpark.contract.web.legacy

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.webkit.WebView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.model.RechargeResultMgs
import com.socialplay.gpark.contract.web.fromTab
import com.socialplay.gpark.contract.web.gameId
import com.socialplay.gpark.contract.web.gamePackageName
import com.meta.lib.web.core.WebCore
import com.meta.web.model.BasicWebArgs
import com.meta.web.ui.FullScreenWebCoreDialog
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.dialog.WebViewDialog
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.web.GameWebDialog
import com.socialplay.gpark.ui.web.WebFragment
import com.socialplay.gpark.ui.web.jsinterfaces.IJsBridgeContract
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import com.socialplay.gpark.ui.web.jsinterfaces.LoginSource
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.isAtLeastStarted
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber


@Deprecated("Not in use anymore, contact web dept.")
open class AbsLegacyJsApiContract(
    private val fragment: Fragment,
    private val webCore: WebCore,
    private val webView: WebView,
    private val webArgs: BasicWebArgs
) : IJsBridgeContract {

    companion object {

        const val QUERY_KEY_SOURCE = "source"
        const val QUERY_KEY_GAME_ID = "gameid"
        const val QUERY_KEY_TYPE = "type"
        const val QUERY_KEY_STYLE = "style"
    }

    val webTs: Long = System.currentTimeMillis() //可用户多个WebFragment实例区分
    var backTime: Long = 0
    val requestId = "web_${SystemClock.elapsedRealtime()}"

    private val refreshReasonSet = hashSetOf<String>()

    private var _source: String? = null
    private var _gameId: String? = null
    private var _type: String? = null
    private var _style: String? = null

    override val context: Context?
        get() = fragment.context
    override val viewLifecycleOwner: LifecycleOwner
        get() = fragment.viewLifecycleOwner
    override val lifecycle: Lifecycle
        get() = fragment.lifecycle
    override val lifecycleScope: LifecycleCoroutineScope
        get() = fragment.lifecycleScope
    override val lifecycleOwner: LifecycleOwner
        get() = fragment
    override val activity: Activity?
        get() = fragment.activity

    init {
        fragment.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == Lifecycle.Event.ON_CREATE) {
                    CpEventBus.register(this@AbsLegacyJsApiContract)
                } else if (event == Lifecycle.Event.ON_DESTROY) {
                    CpEventBus.unregister(this@AbsLegacyJsApiContract)
                }
            }
        })

        fragment.viewLifecycleOwnerLiveData.observe(fragment) {
            it.lifecycle.addObserver(object : LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (event == Lifecycle.Event.ON_CREATE) {
                        registerListeners()
                    }
                }
            })
        }

        runCatching { parseQueryParameters(webArgs.url) }
    }

    private fun parseQueryParameters(url: String) {
        if (WebUtil.isHttpOrHttpsScheme(url)) {
            val httpUrl = url.toHttpUrl()
            _source = httpUrl.queryParameter(QUERY_KEY_SOURCE)
            _gameId = httpUrl.queryParameter(QUERY_KEY_GAME_ID)
            _type = httpUrl.queryParameter(QUERY_KEY_TYPE)
            _style = httpUrl.queryParameter(QUERY_KEY_STYLE)
            Timber.i("web source=$_source, gameid=${_gameId} , type=${_type}")
        }
        if (_gameId.isNullOrBlank()) {
            _gameId = webArgs.gameId
        }
    }

    /**
     * 注册监听
     */
    private fun registerListeners() {
        fragment.setFragmentResultListenerByActivity(
            BaseProfilePage.RESULT_WEB_REFRESH,
            viewLifecycleOwner
        ) { _, bundle: Bundle ->
            if (refreshReasonSet.contains(bundle.getString("reason"))) {
                WebUtil.loadJs(
                    fragment,
                    webView = webView,
                    JsBridgeHelper.JS_METHOD_REFRESH_UI,
                    null
                )
            }
        }
        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == Lifecycle.Event.ON_RESUME) {
                    val dailyTaskKV = GlobalContext.get().get<MetaKV>().dailyTaskKV
                    if (dailyTaskKV.getFinishShareOcVideoSuccess()) {
                        Timber.d("dailyTaskKV.getFinishShareOcVideoSuccess = true")
                        WebUtil.loadJs(
                            fragment,
                            webView = webView,
                            JsBridgeHelper.JS_METHOD_REFRESH_UI,
                            null
                        )
                        dailyTaskKV.resetFinishShareOcVideoSuccess()
                    }
                }
            }
        })
    }

    override fun requireContext(): Context {
        return fragment.requireContext()
    }

    override fun requireActivity(): FragmentActivity {
        return fragment.requireActivity()
    }

    override fun hasFragment(): Boolean {
        return true
    }

    override fun isWebFragment(): Boolean {
        return true
    }


    override fun requireFragment(): Fragment {
        return fragment
    }

    override fun startActivity(createChooser: Intent) {
        fragment.startActivity(createChooser)
    }

    override fun isWebViewDialog(): Boolean {
        return false
    }

    override fun toast(res: Int) {
        fragment.toast(res)
    }

    override fun fragmentManager(): FragmentManager {
        return fragment.childFragmentManager
    }


    @Subscribe
    fun onEvent(event: ShareResult) {
        if (event.isSuccess && event.match(requestId,SharePlatform.PLATFORM_FRIEND)) {
            val othersUuid = event.extraData?.get("uuid") ?: return
            val othersName = event.extraData?.get("username") ?: return
            MetaRouter.IM.gotoConversation(
                fragment,
                othersUuid,
                title = othersName
            )
        }
    }

    override fun isFromGame(): Boolean {
        return false
    }

    override fun fromTab(): Boolean {
        return webArgs.fromTab
    }

    override fun webTs(): Long? {
        return webTs
    }

    override fun gamePackageName(): String? {
        return webArgs.gamePackageName
    }

    override fun gameId(): String? = _gameId

    override fun source(): String? = _source

    override fun style(): String? = _style

    override fun getResId(): ResIdBean? {
        return webArgs.resIdBean
    }

    override fun setPageSource(source: String) {
        _source = source
    }

    override fun addRefreshFragmentListenerByReason(reason: String) {
        refreshReasonSet.add(reason)
    }

    override fun preOpenNativePayPage(data: String?) {
    }

    override fun onResumeGame() {

    }

    override fun routerToAccountSetting(source: LoginSource) {

    }

    override fun send2Ue(amount: Int, code: Int, errorMessage: String?) {

    }


    override fun onPaySuccess() {
        /*游戏内的WebViewDialog才有实现*/
    }

    override fun onPayFailed() {
        /*游戏内的WebViewDialog才有实现*/
    }


    override fun notifyBackToWebFromWeb() {
        val time = System.currentTimeMillis()
        backTime = time
        viewLifecycleOwner.lifecycleScope.launch {
            delay(300L)
            if (fragment.isAdded && fragment.isResumed && backTime == time && webCore.lifecycleOwner.isAtLeastStarted) {
                WebUtil.loadJs(webView, JsBridgeHelper.JS_METHOD_BACK_TO_WEB, 1)
            }
        }
    }

    override fun routerToLogin(popUpId: Int) {

    }

    override fun routerToAuthList(hasPasswordItem: Boolean) {

    }

    override fun routerToLoginBind(hasPasswordItem: Boolean) {

    }

    override fun isToolbarVisible(): Boolean? {
        return webCore.internalApi.isNativeTitleShow()
    }

    override fun setStatusBarVisible(visible: Boolean) {
        webCore.internalApi.setStatusShow(visible)
    }

    override fun setToolbarVisible(visible: Boolean) {
        webCore.internalApi.setNativeTitleShow(visible)
    }

    override fun setStatusBarColor(colorStr: String): Boolean? {
        return if (colorStr.startsWith("#")) {
            webCore.internalApi.setStatusColor(colorStr.substring(1))
        } else {
            webCore.internalApi.setStatusColor(colorStr)
        }
    }

    override fun setStatusBarTextColor(dark: Boolean) {
        webCore.internalApi.setStatusMode(dark)
    }

    override fun goBack(): Boolean {
        return webCore.internalApi.goBack()
    }

    override fun closeAll(removeWebView: Boolean) {
        webCore.internalApi.closeAll(removeWebView)
    }

    override fun closeLoading() {
        webCore.webContainer.closeLoading(webCore)
    }

    override fun routerToLogin(source: String) {
        MetaRouter.Login.login(fragment, source)
    }

    override fun gameToLogin(source: String) {
        MetaRouter.Main.gameToLogin(fragment.requireContext(), com.socialplay.gpark.data.model.LoginSource.Web(source))
    }

    override fun closeWebView(removeWebView: Boolean) {
        if (PandoraToggle.isOpenBabelWeb) {
            webCore.internalApi.closeAll(removeWebView)
        } else {
            when (fragment) {
                is GameWebDialog -> {
                    fragment.navigateToPreviousPage(removeWebView)
                }

                else -> {}
            }
        }
    }

    private fun onResultToGame(payResult: PayResult, extra: String) {
        when (payResult.iapScene) {
            IAPConstants.IAP_SCENE_VIP_PLUS, IAPConstants.IAP_SCENE_VIP_PLUS_RENEW, IAPConstants.IAP_SCENE_PG_COIN -> {
                val rechargeResultMgs = GsonUtil.gsonSafeParseCollection<RechargeResultMgs>(extra)
                rechargeResultMgs?.let {
                    it.code = payResult.code ?: 0
                    it.message = payResult.reason
                    it.amount = payResult.amount
                    GamePayLifecycle.sendRechargeResult2UE(it)
                }
            }
        }
    }

    override fun onPayResultToGame(payResult: PayResult) {
        when (fragment) {
            is WebFragment -> {
                fragment.getExtra()?.let { onResultToGame(payResult, it) }
            }

            is WebViewDialog -> {
                fragment.getExtra()?.let { onResultToGame(payResult, it) }
            }
        }
    }
}