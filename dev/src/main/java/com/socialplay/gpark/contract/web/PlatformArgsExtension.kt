package com.socialplay.gpark.contract.web

import com.meta.web.model.BasicWebArgs
import com.meta.web.model.BasicWebArgsBuilder
import com.meta.web.model.WebDialogArgsBuilder

private const val EXTRA_KEY_IsShowMetaAppShare = "platform.isShowMetaAppShare"
private const val EXTRA_KEY_json = "platform.json"
private const val EXTRA_KEY_extra = "platform.extra"
private const val EXTRA_KEY_gameId = "platform.gameId"
private const val EXTRA_KEY_gamePackageName = "platform.gamePackageName"
private const val EXTRA_KEY_fromTab = "platform.fromTab"
private const val EXTRA_KEY_isCommunity = "platform.isCommunity"

private const val EXTRA_KEY_messageId = "platform.messageId"
private const val EXTRA_KEY_rechargeStatus = "platform.rechargeStatus"


// region isShowMetaAppShare
var BasicWebArgsBuilder.isShowMetaAppShare: Boolean
    get() = extras.getBoolean(EXTRA_KEY_IsShowMetaAppShare, false)
    set(value) = extras.putBoolean(EXTRA_KEY_IsShowMetaAppShare, value)

val BasicWebArgs.isShowMetaAppShare: Boolean
    get() = extras.getBoolean(EXTRA_KEY_IsShowMetaAppShare, false)

// endregion

// region json
var BasicWebArgsBuilder.json: String?
    get() = extras.getString(EXTRA_KEY_json, null)
    set(value) = extras.putString(EXTRA_KEY_json, value)

val BasicWebArgs.json: String?
    get() = extras.getString(EXTRA_KEY_json, null)

// endregion

// region extra

var BasicWebArgsBuilder.extra: String?
    get() = extras.getString(EXTRA_KEY_extra, null)
    set(value) = extras.putString(EXTRA_KEY_extra, value)

val BasicWebArgs.extra: String?
    get() = extras.getString(EXTRA_KEY_extra, null)

//endregion

// region gameId
var BasicWebArgsBuilder.gameId: String?
    get() = extras.getString(EXTRA_KEY_gameId, null)
    set(value) = extras.putString(EXTRA_KEY_gameId, value)

val BasicWebArgs.gameId: String?
    get() = extras.getString(EXTRA_KEY_gameId, null)
// endregion

// region gamePackageName
var BasicWebArgsBuilder.gamePackageName: String?
    get() = extras.getString(EXTRA_KEY_gamePackageName, null)
    set(value) = extras.putString(EXTRA_KEY_gamePackageName, value)

val BasicWebArgs.gamePackageName: String?
    get() = extras.getString(EXTRA_KEY_gamePackageName, null)
// endregion

// region fromTab

var BasicWebArgsBuilder.fromTab: Boolean
    get() = extras.getBoolean(EXTRA_KEY_fromTab, false)
    set(value) = extras.putBoolean(EXTRA_KEY_fromTab, value)

val BasicWebArgs.fromTab: Boolean
    get() = extras.getBoolean(EXTRA_KEY_fromTab, false)

// endregion

// region isCommunity
var BasicWebArgsBuilder.isCommunity: Boolean
    get() = extras.getBoolean(EXTRA_KEY_isCommunity, false)
    set(value) = extras.putBoolean(EXTRA_KEY_isCommunity, value)

val BasicWebArgs.isCommunity: Boolean
    get() = extras.getBoolean(EXTRA_KEY_isCommunity, false)
// endregion

var WebDialogArgsBuilder.messageId: Int
    get() = extras.getInt(EXTRA_KEY_messageId, 0)
    set(value) = extras.putInt(EXTRA_KEY_messageId, value)

val BasicWebArgs.messageId: Int
    get() = extras.getInt(EXTRA_KEY_messageId, 0)


var WebDialogArgsBuilder.rechargeStatus: Int
    get() = extras.getInt(EXTRA_KEY_rechargeStatus, 0)
    set(value) = extras.putInt(EXTRA_KEY_rechargeStatus, value)

val BasicWebArgs.rechargeStatus: Int
    get() = extras.getInt(EXTRA_KEY_rechargeStatus, 0)
