<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#000">

    <LinearLayout
        android:layout_width="327dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/bg_common_simple_dialog"
        android:paddingBottom="20dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/icon_delete_neighbor"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:textColor="@color/color_1A1A1A"
            android:visibility="gone"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv"
            tools:text="last version iuashgduigsauifghuiashfiosahopfhsaiohf ioshafiohas oifhsioahf sioahf ioshf oi "
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/content"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/dp_2"
            android:visibility="gone"
            android:textColor="@color/color_333333"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_goneMarginTop="0dp"
            tools:text="content content content content content content content content content content content content content content "
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp_16"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/content"
            tools:listitem="@layout/adapter_simple_button" />

    </LinearLayout>

</FrameLayout>