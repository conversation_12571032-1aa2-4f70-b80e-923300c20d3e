<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_pin"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/pin_cap"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/v_pin_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_pin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_pin" />

            <View
                android:id="@+id/v_divider_1"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_pin" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_hide"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/ugc_asset_hide"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_1" />

            <View
                android:id="@+id/v_hide_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_hide"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_hide" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>