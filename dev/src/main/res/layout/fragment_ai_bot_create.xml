<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/bg_line_ai_bot_ugc_back"
        android:layout_width="match_parent"
        android:layout_height="354dp"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/img_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_16"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBar" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/welcome_to_app"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/img_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/img_back" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tl"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/bg_white_5_s_360"
        android:minWidth="283dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/img_back"
        app:tabIndicator="@drawable/bg_white_16_s_360"
        app:tabIndicatorColor="@color/white"
        app:tabIndicatorFullWidth="true"
        app:tabIndicatorHeight="32dp"
        app:tabMode="fixed"
        app:tabRippleColor="@color/transparent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vpChoose"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_28"
        app:layout_constraintBottom_toTopOf="@+id/tv_style"
        app:layout_constraintTop_toBottomOf="@+id/tl"
        />

    <TextView
        android:id="@+id/tv_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_8"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/ai_bot_generate_style"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toTopOf="@+id/ry_style"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ry_style"
        android:layout_width="match_parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:orientation="horizontal"
        android:layout_marginHorizontal="12dp"
        android:layout_height="@dimen/dp_104"
        android:layout_marginBottom="@dimen/dp_12"
        app:layout_constraintBottom_toTopOf="@+id/tv_generate" />

    <TextView
        android:id="@+id/tv_generate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/shape_white_corner"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:gravity="center"
        android:paddingVertical="13dp"
        android:text="@string/ai_bot_generate"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>