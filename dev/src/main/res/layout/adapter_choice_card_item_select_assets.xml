<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/selectAssetsRoot"
    android:layout_width="@dimen/dp_120"
    android:layout_height="wrap_content">

    <!-- Asset image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_asset_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:shapeAppearance="@style/round_corner_12dp"
        tools:src="@mipmap/ic_launcher" />

    <!-- Asset title -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_asset_title"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="start|top"
        android:lines="1"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_2"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@id/iv_asset_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Asset Name" />

    <!-- Asset price -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_asset_price"
        style="@style/MetaTextView.S11.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:paddingHorizontal="@dimen/dp_2"
        android:textColor="@color/color_FF5F42"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_asset_title"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="999,999"
        tools:visibility="visible" />

    <!-- Asset tag -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_asset_tag"
        style="@style/MetaTextView.S9.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:background="@drawable/shape_gray_corner_4"
        android:paddingHorizontal="@dimen/dp_4"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/color_666666"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_asset_price"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="New"
        tools:visibility="visible" />

    <!-- Creator name -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_creator_name"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="start|top"
        android:lines="1"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_2"
        android:textColor="@color/color_999999"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_asset_tag"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Creator Name"
        tools:visibility="visible" />

    <!-- Stats (likes, downloads, etc.) -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_stats"
        style="@style/MetaTextView.S9.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="start|top"
        android:lines="1"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_2"
        android:textColor="@color/color_CCCCCC"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_creator_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="123 likes"
        tools:visibility="visible" />

    <!-- Buy Now Button -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/btn_buy_now"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_ffef30_round_10"
        android:gravity="center"
        android:text="Buy Now"
        android:textColor="@color/color_1A1A1A"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_stats"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
