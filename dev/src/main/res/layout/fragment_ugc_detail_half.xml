<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.lihang.ShadowLayout
        android:id="@+id/sl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:clickable="false"
        app:hl_layoutBackground="@color/white"
        app:hl_shadowColor="@color/black_15"
        app:hl_shadowHiddenBottom="true"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowHiddenRight="true"
        app:hl_shadowLimit="@dimen/dp_18"
        app:hl_shadowOffsetY="-10dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/v_shadow_mask"
            android:layout_width="match_parent"
            android:layout_height="472dp" />

    </com.lihang.ShadowLayout>

    <View
        android:id="@+id/v_bg_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:alpha="0"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
        android:id="@+id/cdl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="-28dp">

        <com.socialplay.gpark.ui.view.coordinatorlayout.BottomSheetCoordinatorLayout
            android:id="@+id/bscdl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.socialplay.gpark.ui.view.coordinatorlayout.BottomSheetCoordinatorBehavior">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="@dimen/dp_28"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <com.socialplay.gpark.ui.view.InterceptParentView
                        android:id="@+id/icl_offset"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_48"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:background="@color/white"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/icl_offset" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guide_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_begin="@dimen/dp_16" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guide_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_end="@dimen/dp_16" />

                    <ImageView
                        android:id="@+id/iv_author_avatar"
                        android:layout_width="@dimen/dp_48"
                        android:layout_height="@dimen/dp_48"
                        android:layout_marginTop="@dimen/dp_16"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/tv_mw_not_compatible"
                        app:layout_goneMarginTop="@dimen/dp_18"
                        tools:src="@drawable/icon_default_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_author_name"
                        style="@style/MetaTextView.S18.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toTopOf="@id/tv_author_craft_count"
                        app:layout_constraintEnd_toEndOf="@id/tv_follow_btn"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="Michael James" />

                    <include
                        android:id="@+id/label_group"
                        layout="@layout/layout_label_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@id/tv_author_craft_count"
                        app:layout_constraintEnd_toStartOf="@id/tv_creator_tag"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="@id/tv_author_name"
                        app:layout_constraintTop_toTopOf="@id/tv_author_craft_count"/>


                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_creator_tag"
                        style="@style/MetaTextView.S10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:background="@drawable/bg_e6e6e6_24"
                        android:paddingHorizontal="@dimen/dp_6"
                        android:paddingVertical="@dimen/dp_1"
                        android:text="@string/creator_cap"
                        android:textColor="@color/color_666666"
                        app:layout_constraintBottom_toBottomOf="@id/tv_author_craft_count"
                        app:layout_constraintEnd_toStartOf="@id/tv_author_craft_count"
                        app:layout_constraintStart_toEndOf="@id/label_group"
                        app:layout_constraintTop_toTopOf="@id/tv_author_craft_count"
                        app:layout_goneMarginStart="0dp" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_author_craft_count"
                        style="@style/MetaTextView.S12.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintStart_toEndOf="@id/tv_creator_tag"
                        app:layout_goneMarginEnd="0dp"
                        tools:text="@string/ugc_detail_portfolio" />

                    <View
                        android:id="@+id/v_author_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/label_group"
                        app:layout_constraintStart_toStartOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        tools:visibility="gone" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_follow_btn"
                        style="@style/MetaTextView.S12.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_corner_s100_stroke_c1a1a1a_s05"
                        android:drawableStart="@drawable/ic_add_1a1a1a_s12"
                        android:drawablePadding="@dimen/dp_4"
                        android:gravity="center"
                        android:minHeight="@dimen/sp_22"
                        android:paddingHorizontal="@dimen/dp_12"
                        android:paddingVertical="@dimen/dp_4"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        app:uiLineHeight="@dimen/sp_22"
                        tools:text="Follow"
                        tools:visibility="visible" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/siv_desc"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/dp_12"
                        android:scaleType="centerCrop"
                        app:layout_constraintDimensionRatio="343:195"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
                        app:shapeAppearance="@style/round_corner_16dp"
                        tools:src="@drawable/placeholder" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_played"
                        style="@style/MetaTextView.S14.PoppinsRegular400"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_gradient_black_70_bottom_round_16"
                        android:paddingStart="@dimen/dp_84"
                        android:paddingTop="@dimen/dp_24"
                        android:paddingBottom="@dimen/dp_16"
                        android:textColor="@color/white"
                        app:layout_constraintBottom_toBottomOf="@id/siv_desc"
                        app:layout_constraintEnd_toEndOf="@id/siv_desc"
                        app:layout_constraintStart_toStartOf="@id/siv_desc"
                        tools:text="1560.8K player" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/siv_player_1"
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginTop="@dimen/dp_8"
                        android:padding="@dimen/dp_1"
                        app:layout_constraintBottom_toBottomOf="@id/tv_played"
                        app:layout_constraintStart_toStartOf="@id/tv_played"
                        app:layout_constraintTop_toTopOf="@id/tv_played"
                        app:shapeAppearance="@style/circleStyle"
                        app:strokeColor="@color/white"
                        app:strokeWidth="@dimen/dp_1"
                        tools:src="@drawable/icon_default_avatar" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/siv_player_2"
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:layout_marginStart="@dimen/dp_18"
                        android:padding="@dimen/dp_1"
                        app:layout_constraintBottom_toBottomOf="@id/siv_player_1"
                        app:layout_constraintStart_toStartOf="@id/siv_player_1"
                        app:layout_constraintTop_toTopOf="@id/siv_player_1"
                        app:shapeAppearance="@style/circleStyle"
                        app:strokeColor="@color/white"
                        app:strokeWidth="@dimen/dp_1"
                        tools:src="@drawable/icon_default_avatar" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/siv_player_3"
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:layout_marginStart="@dimen/dp_18"
                        android:padding="@dimen/dp_1"
                        app:layout_constraintBottom_toBottomOf="@id/siv_player_1"
                        app:layout_constraintStart_toStartOf="@id/siv_player_2"
                        app:layout_constraintTop_toTopOf="@id/siv_player_1"
                        app:shapeAppearance="@style/circleStyle"
                        app:strokeColor="@color/white"
                        app:strokeWidth="@dimen/dp_1"
                        tools:src="@drawable/icon_default_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_craft_same"
                        style="@style/MetaTextView.S12.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_33"
                        android:background="@drawable/bg_f8f3ff_left_30"
                        android:drawableTop="@drawable/ic_hammer"
                        android:gravity="bottom|end"
                        android:includeFontPadding="false"
                        android:paddingVertical="@dimen/dp_3"
                        android:paddingStart="@dimen/dp_22"
                        android:paddingEnd="@dimen/dp_15"
                        android:text="@string/create_v2_build"
                        android:textColor="@color/color_B884FF"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/siv_desc" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_mw_not_compatible"
                        style="@style/MetaTextView.S12.PoppinsRegular400"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_14"
                        android:background="@drawable/shape_mw_not_compatible"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_13"
                        android:textColor="@color/color_FF5F42"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/siv_desc"
                        tools:text="      The experience version is outdated. Please enter again after the creator updates the experience."
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_mw_not_compatible_icon"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:src="@drawable/icon_im_conversation_error"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="@id/tv_mw_not_compatible"
                        app:layout_constraintTop_toTopOf="@id/tv_mw_not_compatible"
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_game_title"
                        style="@style/MetaTextView.S25.PoppinsBold700"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_20"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:lineSpacingMultiplier="1.2"
                        android:maxLines="2"
                        android:textColor="@color/color_333333"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/v_drag_bar"
                        app:layout_goneMarginTop="@dimen/dp_16"
                        tools:text="Open Square" />

                    <com.socialplay.gpark.ui.view.ExpandableTextView
                        android:id="@+id/ftv_desc"
                        style="@style/MetaTextView.S13"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_4"
                        android:layout_marginTop="@dimen/dp_18"
                        android:lineSpacingMultiplier="1.2"
                        android:textColor="@color/color_333333"
                        android:textColorHighlight="@color/transparent"
                        app:etv_EnableToggleClick="true"
                        app:etv_MaxLinesOnShrink="6"
                        app:etv_ToExpandHintColor="@color/color_B3B3B3"
                        app:etv_ToShrinkHintColor="@color/color_B3B3B3"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/iv_author_avatar"
                        tools:text="lf you think your life is a failure, what would you do? The restarts of your new online life provides a variety of options. In the cyber world of the galf you think your life is a failure, what would you do? The restarts of your new online life provides a variety of options. In the cyber world of the gacyber world." />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_update_time"
                        style="@style/MetaTextView.S11"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_4"
                        android:layout_marginTop="@dimen/dp_8"
                        android:lineSpacingMultiplier="1.2"
                        android:textColor="@color/neutral_color_4"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toBottomOf="@id/ftv_desc"
                        tools:text="Nov 12, 2022  14:12 AM" />

                    <View
                        android:id="@+id/v_split_top"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_8"
                        android:layout_marginTop="@dimen/dp_8"
                        android:background="@color/color_F6F6F6"
                        app:layout_constraintTop_toBottomOf="@id/tv_update_time" />

                    <com.socialplay.gpark.ui.view.InterceptParentView
                        android:id="@+id/ipcl_comment_count_hang_bg"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_56"
                        android:layout_marginTop="@dimen/dp_20"
                        android:background="@color/white"
                        android:visibility="invisible"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/v_drag_bar"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_4"
                        android:layout_marginTop="@dimen/dp_12"
                        android:background="@drawable/sp_b3b3b3_corner_18"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/icl_offset" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.socialplay.gpark.ui.view.InterceptParentConstraintLayout
                    android:id="@+id/ipcl_comment_count"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_48"
                    android:background="@color/white"
                    android:paddingHorizontal="@dimen/dp_16">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_label"
                        style="@style/MetaTextView.S18.PoppinsBold700"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/comment_cap"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="@id/guide_left"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count"
                        style="@style/MetaTextView.S14"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_marginEnd="@dimen/dp_44"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tv_post_btn"
                        app:layout_constraintStart_toEndOf="@id/tv_comment_label"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_post_btn"
                        style="@style/MetaTextView.S14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_pen_post"
                        android:gravity="center_vertical"
                        android:text="@string/post_cap"
                        android:textColor="@color/color_4AB4FF"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </com.socialplay.gpark.ui.view.InterceptParentConstraintLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:overScrollMode="never"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        </com.socialplay.gpark.ui.view.coordinatorlayout.BottomSheetCoordinatorLayout>

    </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ib_close"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:alpha="0"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/ic_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/fl_title_bar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/v_bg_title_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ib_close"
        app:layout_constraintTop_toTopOf="@id/v_bg_title_bar">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title_bar_game_name"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:maxLines="1"
            android:translationY="@dimen/dp_48"
            tools:text="Extreme Long Title" />

    </FrameLayout>

    <!--region 底栏-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:background="@color/white"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_like"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_27"
            android:layout_marginTop="@dimen/dp_18"
            android:src="@drawable/selector_like"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_like"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:hapticFeedbackEnabled="true"
            android:maxLines="1"
            android:textColor="@color/color_ugc_like"
            app:layout_constraintEnd_toEndOf="@id/iv_like"
            app:layout_constraintStart_toStartOf="@id/iv_like"
            app:layout_constraintTop_toBottomOf="@id/iv_like"
            app:layout_constraintWidth_max="@dimen/dp_50" />

        <View
            android:id="@+id/v_like_click"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="-13dp"
            android:layout_marginTop="-13dp"
            android:layout_marginEnd="-13dp"
            android:layout_marginBottom="-13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_like"
            app:layout_constraintEnd_toEndOf="@id/iv_like"
            app:layout_constraintStart_toStartOf="@id/iv_like"
            app:layout_constraintTop_toTopOf="@id/iv_like" />

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_26"
            android:layout_marginTop="@dimen/dp_18"
            android:src="@drawable/ic_share_s24_1a1a1a"
            app:layout_constraintStart_toEndOf="@id/iv_like"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_share"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:hapticFeedbackEnabled="true"
            android:maxLines="1"
            android:text="@string/share"
            app:layout_constraintEnd_toEndOf="@id/iv_share"
            app:layout_constraintStart_toStartOf="@id/iv_share"
            app:layout_constraintTop_toBottomOf="@id/iv_share"
            app:layout_constraintWidth_max="@dimen/dp_50" />

        <View
            android:id="@+id/v_share_click"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="-13dp"
            android:layout_marginTop="-13dp"
            android:layout_marginEnd="-13dp"
            android:layout_marginBottom="-13dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_share"
            app:layout_constraintEnd_toEndOf="@id/iv_share"
            app:layout_constraintStart_toStartOf="@id/iv_share"
            app:layout_constraintTop_toTopOf="@id/iv_share" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_enter"
            style="@style/Button.S18.PoppinsBlack900.Height48"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_16"
            android:text="@string/enter_suggested_game"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_share"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_E6E6E6"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <!--endregion-->

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_420"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>