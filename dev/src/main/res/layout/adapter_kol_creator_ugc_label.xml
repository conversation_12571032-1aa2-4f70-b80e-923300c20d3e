<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/llLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_f0f0f0_corner_360"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_14"
        android:paddingVertical="@dimen/dp_6"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="@font/poppins_regular_400"
            android:gravity="center"
            android:textSize="@dimen/dp_12"
            tools:text="PlayfulPlayful" />
    </LinearLayout>

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="@dimen/dp_8"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>