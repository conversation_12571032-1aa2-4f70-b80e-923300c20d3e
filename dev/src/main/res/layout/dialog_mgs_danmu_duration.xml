<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_252"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_1a1a1a_round_24"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="@dimen/dp_12">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/bullet_chat_desc"
        android:textColor="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_8"

        android:background="@drawable/bg_white_s_1"

        app:layout_constraintLeft_toLeftOf="parent"

        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_marginLeft="@dimen/dp_1"
            android:layout_marginRight="@dimen/dp_1"
            android:background="@drawable/bg_14ffff_c_top_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.MetaTextView
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_11"
                android:layout_marginRight="@dimen/dp_11"
                android:text="@string/duration_rare"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.MetaTextView
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_11"
                android:layout_marginRight="@dimen/dp_11"
                android:text="@string/total_play_time"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_one"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_input_one"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_12"
                android:layout_marginRight="@dimen/dp_12"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.socialplay.gpark.ui.view.StrokeTextView
                    android:id="@+id/etInputOne"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_17"
                    android:layout_marginLeft="10dp"
                    android:layout_toEndOf="@+id/cl_user_icon_one"
                    android:background="@drawable/icon_danmu_one"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_12"
                    android:shadowColor="@color/black"
                    android:shadowDx="0.8"
                    android:shadowDy="0.8"
                    android:text="@string/bullet_chat_example"
                    app:textStrokeWidth="0.4dp"
                    app:textStrokeColor="@color/black"

                    android:textColor="@color/color_9FDDFF"
                    android:textColorHint="@color/white"
                    android:textSize="@dimen/sp_8"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_icon_one"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:id="@+id/rl_user_icon_one"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:visibility="visible"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="parent"

                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/img_user_icon_one"
                            android:layout_width="@dimen/dp_23"
                            android:layout_height="@dimen/dp_23"
                            android:layout_centerInParent="true"
                            app:strokeColor="@color/transparent"
                            android:src="@drawable/icon_danmu_user_default"
                            app:layout_constraintBottom_toBottomOf="@+id/user_icon_one"
                            app:layout_constraintLeft_toLeftOf="@+id/user_icon_one"
                            app:layout_constraintRight_toRightOf="@+id/user_icon_one"
                            app:layout_constraintTop_toTopOf="@+id/user_icon_one"
                            app:shapeAppearance="@style/circleStyle" />

                        <ImageView
                            android:id="@+id/user_icon_one"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_28"
                            android:layout_centerInParent="true"
                            android:src="@drawable/icon_user_s_one"
                            android:visibility="visible" />

                    </RelativeLayout>

                    <com.socialplay.gpark.ui.view.StrokeTextView
                        android:id="@+id/text_count_one"
                        style="@style/MetaTextView.S10.PoppinsExtraBold700"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:gravity="bottom"
                        android:includeFontPadding="false"
                        android:text="@string/bullet_chat_rank1"
                        android:textColor="@color/color_BCFFF8"
                        android:textSize="@dimen/dp_8"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@+id/rl_user_icon_one"
                        app:layout_constraintRight_toRightOf="@+id/rl_user_icon_one"
                        app:textStrokeColor="@color/color_24A6FF"
                        app:textStrokeWidth="0.5dp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bullet_chat_rank1_time"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_two"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            android:background="@color/white_8"
            android:layout_marginLeft="@dimen/dp_1"
            android:layout_marginRight="@dimen/dp_1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_one">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_input_two"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_11"
                android:layout_marginRight="@dimen/dp_11"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.socialplay.gpark.ui.view.StrokeTextView
                    android:id="@+id/etInputTwo"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_17"
                    android:layout_marginLeft="10dp"
                    android:layout_toEndOf="@+id/cl_user_icon_two"
                    android:background="@drawable/icon_danmu_two"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_12"
                    android:shadowColor="@color/black"
                    android:shadowDx="0.8"
                    android:shadowDy="0.8"
                    app:textStrokeWidth="0.4dp"
                    app:textStrokeColor="@color/black"
                    android:text="@string/bullet_chat_example"
                    android:textColor="@color/color_F990FB"
                    android:textColorHint="@color/white"
                    android:textSize="@dimen/sp_8"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_icon_two"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:id="@+id/rl_user_icon_two"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:visibility="visible"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="parent"

                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/img_user_icon_two"
                            android:layout_width="@dimen/dp_23"
                            android:layout_height="@dimen/dp_23"
                            android:layout_centerInParent="true"
                            app:strokeColor="@color/transparent"
                            android:src="@drawable/icon_danmu_user_default"
                            app:layout_constraintBottom_toBottomOf="@+id/user_icon_two"
                            app:layout_constraintLeft_toLeftOf="@+id/user_icon_two"
                            app:layout_constraintRight_toRightOf="@+id/user_icon_two"
                            app:layout_constraintTop_toTopOf="@+id/user_icon_two"
                            app:shapeAppearance="@style/circleStyle" />

                        <ImageView
                            android:id="@+id/user_icon_two"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_28"
                            android:layout_centerInParent="true"
                            android:src="@drawable/icon_user_s_two"
                            android:visibility="visible" />

                    </RelativeLayout>

                    <com.socialplay.gpark.ui.view.StrokeTextView
                        android:id="@+id/text_count_two"
                        style="@style/MetaTextView.S10.PoppinsExtraBold700"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:gravity="bottom"
                        android:includeFontPadding="false"
                        android:text="@string/bullet_chat_rank5"
                        android:textColor="@color/color_E7A2FF"
                        android:textSize="@dimen/dp_8"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@+id/rl_user_icon_two"
                        app:layout_constraintRight_toRightOf="@+id/rl_user_icon_two"
                        app:textStrokeColor="@color/color_9932FF"
                        app:textStrokeWidth="0.5dp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bullet_chat_rank5_time"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_three"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_two">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_input_three"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_12"
                android:layout_marginRight="@dimen/dp_12"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.socialplay.gpark.ui.view.StrokeTextView
                    android:id="@+id/etInput_three"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_17"
                    android:layout_marginLeft="10dp"
                    android:layout_toEndOf="@+id/cl_user_icon_three"
                    android:background="@drawable/icon_danmu_three"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_12"
                    android:shadowColor="@color/black"
                    android:shadowDx="0.8"
                    android:shadowDy="0.8"
                    app:textStrokeWidth="0.4dp"
                    app:textStrokeColor="@color/black"
                    android:text="@string/bullet_chat_example"
                    android:textColor="@color/color_FFCE50"
                    android:textColorHint="@color/white"
                    android:textSize="@dimen/sp_8"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_icon_three"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:id="@+id/rl_user_icon_three"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:visibility="visible"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="parent"

                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/img_user_icon_three"
                            android:layout_width="@dimen/dp_23"
                            android:layout_height="@dimen/dp_23"
                            android:layout_centerInParent="true"
                            app:strokeColor="@color/transparent"
                            android:src="@drawable/icon_danmu_user_default"
                            app:layout_constraintBottom_toBottomOf="@+id/user_icon_three"
                            app:layout_constraintLeft_toLeftOf="@+id/user_icon_three"
                            app:layout_constraintRight_toRightOf="@+id/user_icon_three"
                            app:layout_constraintTop_toTopOf="@+id/user_icon_three"
                            app:shapeAppearance="@style/circleStyle" />

                        <ImageView
                            android:id="@+id/user_icon_three"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_28"
                            android:layout_centerInParent="true"
                            android:scaleType="centerCrop"
                            android:src="@drawable/icon_user_s_three"
                            android:visibility="visible" />

                    </RelativeLayout>

                    <com.socialplay.gpark.ui.view.StrokeTextView
                        android:id="@+id/text_count_three"
                        style="@style/MetaTextView.S10.PoppinsExtraBold700"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp_2"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:gravity="bottom"
                        android:includeFontPadding="false"
                        android:text="@string/bullet_chat_rank10"
                        android:textColor="@color/color_FFE350"
                        android:textSize="@dimen/dp_8"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@+id/rl_user_icon_three"
                        app:layout_constraintRight_toRightOf="@+id/rl_user_icon_three"
                        app:textStrokeColor="@color/color_E67D17"
                        app:textStrokeWidth="0.5dp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bullet_chat_rank10_time"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            android:layout_marginLeft="@dimen/dp_1"
            android:layout_marginRight="@dimen/dp_1"
            android:background="@drawable/bg_14ffff_c_buttom_12"
            android:paddingBottom="@dimen/dp_1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_three">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_11"
                android:layout_marginRight="@dimen/dp_11"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.socialplay.gpark.ui.view.StrokeTextView
                    android:id="@+id/etInput_four"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_21"
                    android:layout_marginLeft="10dp"
                    android:layout_toEndOf="@+id/cl_user_icon"
                    android:background="@drawable/icon_danmu_four"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_12"
                    android:shadowColor="@color/black"
                    android:shadowDx="0.8"
                    android:shadowDy="0.8"
                    app:textStrokeWidth="0.4dp"
                    app:textStrokeColor="@color/black"
                    android:text="@string/bullet_chat_example"
                    android:textColor="@color/color_FFF54D"
                    android:textColorHint="@color/white"
                    android:textSize="@dimen/sp_8"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:visibility="visible"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="parent"

                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/img_user_icon"
                            android:layout_width="@dimen/dp_23"
                            android:layout_height="@dimen/dp_23"
                            android:layout_centerInParent="true"
                            app:strokeColor="@color/transparent"
                            android:src="@drawable/icon_danmu_user_default"
                            app:layout_constraintBottom_toBottomOf="@+id/rl_user_icon"
                            app:layout_constraintLeft_toLeftOf="@+id/rl_user_icon"
                            app:layout_constraintRight_toRightOf="@+id/rl_user_icon"
                            app:layout_constraintTop_toTopOf="@+id/rl_user_icon"
                            app:shapeAppearance="@style/circleStyle" />

                        <ImageView
                            android:id="@+id/rl_user_icon"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_28"
                            android:layout_centerInParent="true"
                            android:scaleType="centerCrop"
                            android:src="@drawable/icon_user_s_four"
                            android:visibility="visible" />

                    </RelativeLayout>

                    <com.socialplay.gpark.ui.view.StrokeTextView
                        android:id="@+id/text_count"
                        style="@style/MetaTextView.S10.PoppinsExtraBold700"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp_2"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:gravity="bottom"
                        android:includeFontPadding="false"
                        android:text="@string/bullet_chat_rank15"
                        android:textColor="@color/color_FFF772"
                        android:textSize="@dimen/dp_8"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@+id/cl_user_icon"
                        app:layout_constraintRight_toRightOf="@+id/cl_user_icon"
                        app:textStrokeColor="@color/color_7D27F9"
                        app:textStrokeWidth="0.5dp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bullet_chat_rank15_time"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>