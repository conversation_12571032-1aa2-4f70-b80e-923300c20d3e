<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleEditReview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title_text="@string/write_review"
        app:title_text_color="@color/colorAccent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_dec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_36"
        android:text="@string/tap_a_star_to_rate"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleEditReview" />

    <com.socialplay.gpark.ui.view.RatingView
        android:id="@+id/ratingbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_17"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@+id/tv_dec"
        app:layout_constraintLeft_toRightOf="@+id/tv_dec"
        app:rating="0"
        app:ratingCount="5"
        app:ratingEmpty="@drawable/ic_empty_start_4ab4ff"
        app:ratingFilled="@drawable/ic_fill_start_4ab4ff"
        app:ratingMargin="@dimen/dp_8"
        app:ratingSize="@dimen/dp_24" />

    <View
        android:id="@+id/vGameReview"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_200"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_27"
        android:background="@drawable/bg_game_review_edit"
        android:paddingVertical="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_40"
        app:layout_constraintTop_toBottomOf="@id/tv_dec" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etGameReview"
        style="@style/EditText"
        android:maxLength="-1"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_31"
        android:background="@drawable/bg_game_review_edit"
        android:hint="@string/edit_game_review_hint"
        android:paddingVertical="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/vGameReview"
        app:layout_constraintTop_toBottomOf="@id/tv_dec" />

    <Button
        android:id="@+id/tvPost"
        style="@style/Button.S18.PoppinsBlack900"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_26"
        android:background="@drawable/bg_button_normal_38"
        android:text="@string/text_confirm"
        app:layout_constraintTop_toBottomOf="@+id/vGameReview" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>