<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.WrapNestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 用于位移loadingView -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvUgc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dp_8"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loadingUgc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

</com.socialplay.gpark.ui.view.WrapNestedScrollableHost>