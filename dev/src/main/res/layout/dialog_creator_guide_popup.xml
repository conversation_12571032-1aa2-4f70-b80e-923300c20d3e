<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#5000">

    <ImageView
        android:id="@+id/ivImg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_10"
        android:layout_marginVertical="@dimen/dp_48"
        android:src="@color/color_999999"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.7:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvClose"
        style="@style/Button.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:enabled="false"
        android:src="@drawable/ic_avatar_popup_close"
        app:layout_constraintBottom_toBottomOf="@id/ivImg"
        app:layout_constraintEnd_toEndOf="@id/ivImg"
        app:layout_constraintStart_toStartOf="@id/ivImg" />

</androidx.constraintlayout.widget.ConstraintLayout>