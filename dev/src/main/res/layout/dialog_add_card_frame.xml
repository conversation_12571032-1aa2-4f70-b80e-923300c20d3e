<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/includeHandle"
        layout="@layout/include_common_bottom_sheet_handle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_28"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.DispatchDisallowConstrainLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/includeHandle">

        <com.socialplay.gpark.ui.view.TitleBarLayout
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:isDividerVisible="false"
            app:layout_constraintTop_toBottomOf="@id/includeHandle"
            app:showBackIcon="false"
            app:showRightText="false"
            app:title_text="@string/add" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvConfirm"
            style="@style/Button.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            android:minWidth="@dimen/dp_90"
            android:minHeight="@dimen/dp_31"
            android:paddingVertical="@dimen/dp_5"
            android:text="@string/dialog_confirm"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="@id/title" />

        <!--不要删除CoordinatorLayout的id，会导致fragment重建时折叠布局展开-->
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/cl"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_scrollFlags="scroll">

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rvAdded"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <View
                        android:id="@+id/vAddedLine"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_8"
                        android:layout_marginTop="@dimen/dp_8"
                        android:background="@color/neutral_color_10"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fcv_search_game"
                android:name="com.socialplay.gpark.ui.post.tab.AddCardItemFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.socialplay.gpark.ui.view.DispatchDisallowConstrainLayout>

</androidx.constraintlayout.widget.ConstraintLayout>