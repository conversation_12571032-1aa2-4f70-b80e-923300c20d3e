<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="5.5dp"
    android:layout_marginBottom="@dimen/dp_16">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="166:117"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/shapeRound10Style" />

    <View
        android:id="@+id/v_mask_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_26"
        android:background="@drawable/bg_black_50_bottom_s12"
        app:layout_constraintBottom_toBottomOf="@id/iv_cover" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_easy"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginVertical="@dimen/dp_6"
        android:gravity="center_vertical|start"
        android:singleLine="true"
        android:text="@string/easy"
        android:textColor="@color/color_FFEF30"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/v_mask_bottom"
        app:layout_constraintStart_toStartOf="@id/v_mask_bottom"
        app:layout_constraintTop_toTopOf="@id/v_mask_bottom" />

    <com.socialplay.gpark.ui.view.RatingView
        android:id="@+id/ratingbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@id/tv_easy"
        app:layout_constraintStart_toEndOf="@id/tv_easy"
        app:layout_constraintTop_toTopOf="@id/tv_easy"
        app:rating="3"
        app:ratingCount="3"
        app:ratingEmpty="@drawable/ic_fill_start_ffef30"
        app:ratingFilled="@drawable/ic_fill_start_ffef30"
        app:ratingMargin="@dimen/dp_2"
        app:ratingSize="@dimen/dp_12" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_tag"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:background="@drawable/bg_907cff_round_16"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_8"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/tv_easy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_easy"
        tools:text="tag" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_2"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:lines="1"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@id/iv_cover"
        tools:text="Work nameWork nameWork nameWork nameWork nameWork nameWork nameWork name" />

</androidx.constraintlayout.widget.ConstraintLayout>
