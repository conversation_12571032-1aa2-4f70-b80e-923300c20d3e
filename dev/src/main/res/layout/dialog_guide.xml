<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_60">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="@dimen/dp_343"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:translationY="-30dp"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="@dimen/dp_38"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <View
                android:id="@+id/v_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/bg_white_corner_38"
                app:layout_constraintBottom_toBottomOf="@id/rv"
                app:layout_constraintTop_toTopOf="@id/tv_title" />

            <ImageView
                android:id="@+id/iv_figure"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_guide_figure"
                android:translationY="-60dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/v_bg" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_title"
                style="@style/MetaTextView.S20.PoppinsBold700"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_60"
                android:gravity="center"
                android:minHeight="@dimen/dp_30"
                android:paddingTop="@dimen/dp_68"
                android:text="@string/guide_interest_welcome"
                android:textSize="@dimen/dp_20"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tv_sub_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                app:uiLineHeight="@dimen/dp_30" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_sub_title"
                style="@style/MetaTextView.S16.PoppinsSemiBold600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:gravity="center"
                android:minHeight="@dimen/dp_24"
                android:text="@string/guide_interest_title"
                android:textSize="@dimen/dp_16"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/rv"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:uiLineHeight="@dimen/dp_24" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:overScrollMode="ifContentScrolls"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingTop="@dimen/dp_31"
                android:paddingBottom="@dimen/dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_max="464dp"
                app:layout_constraintTop_toBottomOf="@id/tv_sub_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>