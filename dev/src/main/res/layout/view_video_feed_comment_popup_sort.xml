<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_16">

        <LinearLayout
            android:id="@+id/ll_sort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_recent_reply"
                style="@style/MetaTextView.S14.PoppinsRegular400.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/recent_reply" />

            <View
                android:id="@+id/v_divider"
                android:layout_width="match_parent"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_like_sort"
                style="@style/MetaTextView.S14.PoppinsRegular400.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="@string/recent_reply"
                android:padding="@dimen/dp_16"
                android:text="@string/likes_sort" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>