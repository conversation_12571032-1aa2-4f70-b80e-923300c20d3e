<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="2dp">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/dp_120"
        android:maxWidth="@dimen/dp_120"
        android:textColor="@color/colorAccentPrimary"
        android:textSize="@dimen/sp_14"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="META_BASE_URL" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:background="@color/color_EFEFEF"
        android:minHeight="@dimen/dp_45"
        android:maxLines="1"
        android:singleLine="true"
        android:lines="1"
        android:textColor="@color/color_1C1C1C"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintLeft_toRightOf="@id/tvName"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvName" />

</androidx.constraintlayout.widget.ConstraintLayout>