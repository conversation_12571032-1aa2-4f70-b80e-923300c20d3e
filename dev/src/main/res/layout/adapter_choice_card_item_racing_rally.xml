<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/racingRallyCreationRoot"
    android:layout_width="@dimen/dp_140"
    android:layout_height="wrap_content">

    <!-- Main image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintDimensionRatio="140:140"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:shapeAppearance="@style/round_corner_12dp"
        tools:src="@mipmap/ic_launcher" />

    <!-- Bottom gradient overlay -->
    <View
        android:id="@+id/iv_bottom_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_34"
        android:background="@drawable/shape_home_item_gradient_16"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintStart_toStartOf="@id/iv_game_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_game_icon" />

    <!-- Creator avatar -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv_creator_avatar"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_6"
        android:padding="@dimen/dp_1"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/iv_bottom_bg"
        app:layout_constraintBottom_toBottomOf="@id/iv_bottom_bg"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1"
        tools:visibility="visible"
        tools:src="@drawable/icon_default_avatar" />

    <!-- Creator nickname -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_creator_nickname"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:singleLine="true"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/siv_creator_avatar"
        app:layout_constraintStart_toEndOf="@id/siv_creator_avatar"
        app:layout_constraintEnd_toStartOf="@id/tv_zan"
        app:layout_constraintTop_toTopOf="@id/siv_creator_avatar"
        tools:text="Creator Name"
        tools:visibility="visible" />

    <!-- Like count -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_zan"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_6"
        android:drawableStart="@drawable/icon_zan"
        android:drawablePadding="@dimen/dp_4"
        android:drawableTint="@color/white"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_bottom_bg"
        app:layout_constraintEnd_toEndOf="@id/iv_bottom_bg"
        tools:text="123"
        tools:visibility="visible" />

    <!-- Game title -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="start|top"
        android:lines="2"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_3"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@id/iv_game_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Racing Rally Creation" />

</androidx.constraintlayout.widget.ConstraintLayout>
