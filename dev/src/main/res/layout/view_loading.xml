<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:layout_height="match_parent"
    tools:layout_width="match_parent"
    android:clickable="true"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <LinearLayout
        android:id="@+id/ll_net_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/img_net_error"
            android:layout_width="@dimen/dp_150"
            android:layout_height="@dimen/dp_150"
            android:layout_gravity="center"
            android:src="@drawable/icon_no_network_connection" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_net_error"
            style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/loading_net_error" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_net_error_retry"
            style="@style/MetaTextView.S15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_1a1a1a_round_24"
            android:textColor="@color/white"
            android:paddingHorizontal="@dimen/dp_28"
            android:paddingVertical="@dimen/dp_8"
            android:text="@string/retry_cap"
            tools:visibility="visible" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_other_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/img_other_error"
            android:layout_width="@dimen/dp_150"
            android:layout_height="@dimen/dp_150"
            android:layout_gravity="center"
            android:src="@drawable/icon_no_network_connection" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_other_error"
            style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/brvah_load_failed" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_other_error_retry"
            style="@style/MetaTextView.S15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_ffef30_round_24"
            android:textColor="@color/color_212121"
            android:paddingHorizontal="@dimen/dp_28"
            android:paddingVertical="@dimen/dp_8"
            android:text="@string/retry_cap"
            tools:visibility="gone" />

    </LinearLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/pb_loading"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/circle_loading"
        android:layout_marginRight="11dp"
        app:layout_constraintRight_toLeftOf="@id/tv_loading"
        app:layout_constraintBottom_toBottomOf="@id/tv_loading"
        app:layout_constraintTop_toTopOf="@id/tv_loading"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_loading"
        style="@style/MetaTextView.S15.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/loading"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_empty"
        style="@style/MetaTextView.S15.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/dp_12"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginHorizontal="78dp"
        android:textColor="@color/color_9B9FA6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:drawableTop="@drawable/icon_no_recent_activity"
        tools:visibility="visible"
        tools:text="no data"/>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/btnEmpty"
        style="@style/MetaTextView.S15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/bg_1a1a1a_round_24"
        app:layout_constraintTop_toBottomOf="@id/tv_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        android:textColor="@color/white"
        android:paddingHorizontal="@dimen/dp_28"
        android:paddingVertical="@dimen/dp_8"
        android:text="@string/retry_cap" />

</merge>