<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/layoutTitleBar">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivGroupAvatar"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_24"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/shapeRound30Style"
            tools:src="@drawable/icon_item_group_chat_avatar" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGroupName"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivGroupAvatar"
            tools:text="Biubiubiu" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGroupId"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_B3B3B3"
            android:textSize="@dimen/sp_12"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGroupName"
            tools:text="@string/request_join_group_id" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGroupDescTitle"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/request_join_group_title_group_desc"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGroupId" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/tvGroupDescContentScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="@dimen/dp_300"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGroupDescTitle">

            <com.socialplay.gpark.ui.view.ExpandableTextView
                android:id="@+id/tvGroupDescContent"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:textColor="@color/color_666666"
                app:etv_EnableToggleClick="false"
                app:etv_ExtendClickScope="true"
                app:etv_MaxLinesOnShrink="2"
                app:etv_ToExpandHint="@string/more_cap"
                app:etv_ToExpandHintBold="false"
                app:etv_ToExpandHintColor="@color/secondary_color_1"
                app:etv_ToShrinkHintShow="false"
                tools:text="This is a group announcement. This is a group announcement.This is a group announcement.This is a group announcement." />

        </androidx.core.widget.NestedScrollView>

        <View
            android:id="@+id/vInputBg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_144"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/shape_f0f0f0_corner_16"
            app:layout_constraintTop_toBottomOf="@id/tvGroupDescContentScrollView" />

        <com.ly123.tes.mgs.im.view.IMEditText
            android:id="@+id/etInput"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/transparent"
            android:gravity="top"
            android:hint="@string/request_join_group_hint"
            android:inputType="textMultiLine"
            android:padding="@dimen/dp_16"
            android:textColor="@color/color_1A1A1A"
            android:textColorHint="@color/color_B3B3B3"
            android:textCursorDrawable="@drawable/bg_cursor"
            android:textSize="@dimen/sp_14"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toBottomOf="@id/vInputBg"
            app:layout_constraintEnd_toEndOf="@id/vInputBg"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@id/vInputBg"
            app:layout_constraintTop_toTopOf="@id/vInputBg"
            app:layout_constraintVertical_bias="0" />

        <com.socialplay.gpark.ui.view.LoadingButton
            android:id="@+id/loadingBtn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_ffef30_round_40"
            app:buttonText="@string/apply_join_group_button_text"
            app:buttonTextStyle="@style/MetaTextView.S16.PoppinsMedium500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vInputBg"
            app:loadingText="@string/loading"
            app:loadingTextStyle="@style/MetaTextView.S15.PoppinsRegular400" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--  当键盘弹出后, 下面的内容会向上移, 为了防止上移的内容遮挡顶部导航栏, 所以将状态栏写到布局后面()  -->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/layoutTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/white"
        app:isDividerVisible="true"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/join_group_page_title"
        app:title_text_color="@color/color_1A1A1A" />

    <Space
        android:id="@+id/keyboardLine"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_16"
        app:layout_constraintTop_toBottomOf="@id/layoutContent" />

    <Space
        android:id="@+id/keyboardLine2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
