<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:id="@+id/float_notice_root_layout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/dp_20"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/dp_20">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_15">

        <ImageView
            android:id="@+id/iv_user_head"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp_7"
            android:src="@drawable/placeholder_corner_360" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_invite_user_name"
            style="@style/MetaTextView.S15.PoppinsBold700.LeftTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/iv_user_head"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_toEndOf="@id/iv_user_head"
            android:layout_toStartOf="@id/btn_invite_agree"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="im the king of the world" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_invite_title"
            style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_invite_user_name"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_toEndOf="@id/iv_user_head"
            android:layout_toStartOf="@id/btn_invite_agree"
            android:textColor="@color/main_black_80"
            android:textSize="@dimen/sp_12"
            tools:text="SAKURA School Simulator" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_invite_content"
            style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_invite_title"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_toStartOf="@id/btn_invite_agree"
            android:layout_toEndOf="@id/iv_user_head"
            android:textColor="@color/main_black_80"
            android:textSize="@dimen/sp_12"
            tools:text="SAKURA School Simulator" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btn_invite_agree"
            style="@style/Button.S16.PoppinsBlack900.Height28Width74"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/btn_invite_refuse"
            android:paddingHorizontal="@dimen/dp_12"
            android:paddingVertical="@dimen/dp_7"
            android:text="@string/mgs_invite_btn_join" />

        <ImageView
            android:id="@+id/btn_invite_refuse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="@dimen/dp_6"
            android:paddingRight="@dimen/dp_15"
            android:paddingVertical="@dimen/dp_6"
            android:src="@drawable/icon_mgs_dialog_close" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>