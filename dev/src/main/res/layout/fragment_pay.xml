<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">
    <TextView
        android:background="#ff0000"
        android:layout_width="@dimen/dp_200"
        android:textColor="#000"
        android:textSize="@dimen/sp_25"
        android:gravity="center"
        android:layout_height="@dimen/dp_100"
        android:id="@+id/tv_get_product"
        android:text="@string/debug_product"
       />
    <TextView
        android:background="#ff0000"
        android:layout_width="@dimen/dp_200"
        android:layout_marginTop="@dimen/dp_10"
        android:textColor="#000"
        android:textSize="@dimen/sp_25"
        android:gravity="center"
        android:layout_height="@dimen/dp_100"
        android:id="@+id/tv_get_sub_product"
        android:text="subs_product"
        />


    <TextView
        android:id="@+id/tv_pay"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_width="@dimen/dp_200"
        android:layout_height="@dimen/dp_100"
        android:layout_below="@+id/et_product"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/debug_pay"
        android:background="#ff0000"
        android:textColor="#000"
        android:textSize="@dimen/sp_25"
        tools:ignore="MissingConstraints">

    </TextView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="productId:" />
    <EditText
        android:id="@+id/et_product"
        android:layout_width="match_parent"
        android:text="@string/debug_gold_20220620"
        android:layout_height="@dimen/dp_50"
        android:layout_below="@+id/et_type"
        android:layout_centerInParent="true"
        android:layout_centerHorizontal="true"
        android:hint="@string/debug_input_product_id" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="type:" />

    <EditText
        android:id="@+id/et_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:text="@string/debug_in_app"
        android:layout_centerInParent="true"
        android:layout_centerHorizontal="true"
        android:hint="@string/debug_type" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/sence"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="sence：" />

        <EditText
            android:id="@+id/sence_text"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_centerInParent="true"
            android:layout_centerHorizontal="true"
            android:hint="oderId"
            android:text="VIP_PLUS" />

    </LinearLayout>

 <androidx.appcompat.widget.LinearLayoutCompat
     android:layout_width="wrap_content"
     android:orientation="horizontal"
     android:layout_height="wrap_content">
     <TextView
         android:layout_width="wrap_content"
         android:text="planId: "
         android:layout_height="wrap_content"/>
     <EditText
         android:id="@+id/et_plan"
         android:layout_width="wrap_content"
         android:text="pre001-bp01"
         android:layout_height="@dimen/dp_50"
         android:layout_centerInParent="true"
         android:layout_centerHorizontal="true"/>
 </androidx.appcompat.widget.LinearLayoutCompat>



</LinearLayout>