<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/v_icon"
        android:layout_width="@dimen/dp_82"
        android:layout_height="@dimen/dp_82"
        android:background="@drawable/bg_neutral_color_8_round_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_name"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_8"

        android:background="@drawable/bg_neutral_color_8_round_4"
        app:layout_constraintEnd_toEndOf="@id/v_icon"
        app:layout_constraintStart_toStartOf="@id/v_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>
