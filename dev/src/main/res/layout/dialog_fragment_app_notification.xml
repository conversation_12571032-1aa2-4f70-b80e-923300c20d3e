<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="#80000000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_42"
        android:background="@drawable/bg_common_simple_dialog"
        android:clickable="true"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_app_notification"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Space
            android:id="@+id/spaceIvTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_8"
            app:layout_constraintTop_toBottomOf="@id/iv" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/notification_app_title"
            android:textColor="#1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/spaceIvTitle" />

        <Space
            android:id="@+id/spaceTitleContent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_2"
            app:layout_constraintBottom_toTopOf="@id/content"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/content"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableLeft="@drawable/icon_app_notification_three"
            android:drawablePadding="@dimen/dp_9"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dp_24"
            android:text="@string/notification_app_content_one"
            android:textColor="#1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/spaceTitleContent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/content_two"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableLeft="@drawable/icon_app_notification_two"
            android:drawablePadding="@dimen/dp_9"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dp_24"
            android:text="@string/notification_app_content_two"
            android:textColor="#1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/content" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/content_three"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableLeft="@drawable/icon_app_notification_one"
            android:drawablePadding="@dimen/dp_9"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dp_24"
            android:text="@string/notification_app_content_three"
            android:textColor="#1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/content_two" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btnConfirm"
            style="@style/Button.S16.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/notification_app_sure"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/content_three" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btnCancel"
            style="@style/Button.S16.PoppinsMedium500.CancelPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/notification_app_cancel"
            android:textColor="#1A1A1A"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnConfirm" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>