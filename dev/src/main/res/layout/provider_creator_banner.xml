<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_32">

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:banner_auto_loop="true"
        app:banner_infinite_loop="true"
        app:banner_orientation="horizontal" />

    <com.zhpan.indicator.IndicatorView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_8"
        app:vpi_orientation="horizontal"
        app:vpi_slide_mode="smooth"
        app:vpi_slider_checked_color="@color/white"
        app:vpi_slider_normal_color="@color/white_50"
        app:vpi_style="round_rect" />

</FrameLayout>
