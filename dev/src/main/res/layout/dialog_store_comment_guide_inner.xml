<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    tools:background="#80000000">

    <ImageView
        android:layout_width="@dimen/dp_217"
        android:layout_height="@dimen/dp_94"
        android:background="@drawable/store_comment_dialog_img"
        app:layout_constraintBottom_toTopOf="@id/dialog_layout"
        app:layout_constraintLeft_toLeftOf="@id/dialog_layout"
        app:layout_constraintRight_toRightOf="@id/dialog_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_layout"
        android:layout_width="@dimen/dp_343"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_common_simple_dialog"
        android:padding="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/guide_store_evaluate_like_us"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintTop_toTopOf="parent" />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/dislike"
            style="@style/Button.S16.PoppinsMedium500.CancelPrimary"
            android:layout_width="@dimen/dp_135"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center"
            android:minHeight="@dimen/dp_44"
            android:paddingStart="@dimen/dp_25"
            android:paddingEnd="@dimen/dp_25"
            android:text="@string/guide_store_evaluate_dont_like"
            android:textColor="@color/color_1A1A1A"
            app:drawableLeftCompat="@drawable/icon_dislike_emoji"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/like"
            style="@style/Button.S16.PoppinsMedium500.Warn"
            android:layout_width="@dimen/dp_135"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center"
            android:minHeight="@dimen/dp_44"
            android:paddingStart="@dimen/dp_30"
            android:paddingEnd="@dimen/dp_30"
            android:text="@string/guide_store_evaluate_like"
            android:textColor="@color/color_1A1A1A"
            app:drawableLeftCompat="@drawable/icon_like_emoji"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageButton
        android:id="@+id/ib_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginRight="@dimen/dp_24"
        android:background="@null"
        android:src="@drawable/ic_close_16_757575"
        app:layout_constraintRight_toRightOf="@id/dialog_layout"
        app:layout_constraintTop_toTopOf="@id/dialog_layout" />


</androidx.constraintlayout.widget.ConstraintLayout>