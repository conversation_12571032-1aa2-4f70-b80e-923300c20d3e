<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_5"
    android:paddingBottom="@dimen/dp_20">

    <!-- Main game image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <!-- New game indicator -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_new_indicator"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_ffef30_round_4"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:text="NEW"
        android:textColor="@color/color_1A1A1A"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg"
        tools:visibility="visible" />

    <!-- Tag -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_tag"
        style="@style/MetaTextView.S9.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_black_50_corner_4"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg"
        tools:text="Action"
        tools:visibility="visible" />

    <!-- Bottom gradient overlay -->
    <View
        android:id="@+id/vBgBottom"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_32"
        android:background="@drawable/shape_gradient_trans_black_12"
        android:clickable="false"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg" />

    <!-- Creator avatar -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_margin="@dimen/dp_6"
        android:background="@drawable/placeholder_circle"
        android:padding="@dimen/dp_1"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:shapeAppearance="@style/circleStyle" />

    <!-- Creator name -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        tools:text="Creator Name" />

    <!-- Game title -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvDisplayName"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:lines="2"
        android:maxLines="2"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/ivBg"
        tools:text="Amazing Game Title That Might Be Long" />

    <!-- Like count -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLike"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:drawableStart="@drawable/icon_zan"
        android:drawablePadding="@dimen/dp_4"
        android:drawableTint="@color/color_999999"
        android:gravity="center_vertical"
        android:textColor="@color/color_999999"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/tvDisplayName"
        tools:text="573"
        tools:visibility="visible" />

    <!-- Playing count -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_playing_count"
        style="@style/MetaTextView.S11.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:textColor="@color/color_CCCCCC"
        android:visibility="gone"
        app:layout_constraintBaseline_toBaselineOf="@id/tvLike"
        app:layout_constraintStart_toEndOf="@id/tvLike"
        tools:text="12 playing"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
