<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_74"
    android:layout_height="wrap_content">


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        style="@style/Avatar.Round"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_launcher_background" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S14.PoppinsBlack900"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingHorizontal="@dimen/dp_10"
        android:singleLine="true"
        android:maxLines="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        tools:text="12345sssddd6789" />

    <View
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_2"
        android:background="@drawable/bg_online_friend_hint"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar" />

</androidx.constraintlayout.widget.ConstraintLayout>