<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_120"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dp_16"
    android:layout_marginEnd="@dimen/dp_10"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_18">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_build_btn"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_18"
        android:background="@drawable/bg_corner_30_black"
        android:paddingHorizontal="@dimen/dp_30"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/create_v2_build"
        android:textColor="@color/white" />

</LinearLayout>
