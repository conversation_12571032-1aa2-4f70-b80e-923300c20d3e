<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon_tint="@color/white"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_74"
        android:layout_height="@dimen/dp_74"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:gravity="center"
        android:text="@string/intl_you_nickname_is"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        app:layout_goneMarginTop="@dimen/dp_39" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        android:text="@string/intl_you_nickname_is_tips"
        android:textColor="@color/white_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintWidth_max="@dimen/dp_294" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/input_name"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_60"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_24"
        android:hint="@string/intl_name"
        android:textColorHint="@color/white_40"
        app:boxCollapsedPaddingTop="@dimen/dp_12"
        app:boxStrokeWidth="0dp"
        app:boxStrokeWidthFocused="0dp"
        app:hintAnimationEnabled="true"
        app:hintTextAppearance="@style/hintAppearence"
        app:hintTextColor="@color/white_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintWidth_max="@dimen/dp_360">

        <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_white_input_v2"
            android:gravity="center_vertical"
            android:inputType="textNoSuggestions"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textColorHint="@color/white_40"
            android:textCursorDrawable="@drawable/bg_cursor_white"
            android:textSize="@dimen/sp_14"
            tools:text="ああああああああああああああああああああ" />

    </com.google.android.material.textfield.TextInputLayout>

    <ImageView
        android:id="@+id/iv_clear_name"
        android:layout_width="@dimen/dp_48"
        android:layout_height="0dp"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_16"
        android:src="@drawable/ic_startup_clear"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/input_name"
        app:layout_constraintEnd_toEndOf="@id/input_name"
        app:layout_constraintTop_toTopOf="@id/input_name"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name_limit_tips"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="start|center_vertical"
        android:text="@string/nickname_length_tips"
        android:textColor="@color/white_30"
        app:layout_constraintEnd_toEndOf="@id/input_name"
        app:layout_constraintStart_toStartOf="@id/input_name"
        app:layout_constraintTop_toBottomOf="@id/input_name" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_next_btn"
        style="@style/Button.S18.PoppinsBlack900.Height48"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_14"
        android:text="@string/intl_go_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name_limit_tips"
        app:layout_constraintWidth_max="@dimen/dp_285" />

</com.socialplay.gpark.ui.view.InterceptConstraintLayout>