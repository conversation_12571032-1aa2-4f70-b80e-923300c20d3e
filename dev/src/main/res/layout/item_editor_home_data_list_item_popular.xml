<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_gravity="center">


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:background="@drawable/bg_popular_gradient_mask"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/white"
        android:includeFontPadding="false"
        android:shadowColor="#BF000000"
        android:shadowDx="-1"
        android:shadowDy="-1"
        app:uiLineHeight="@dimen/sp_14"
        app:lineHeight="@dimen/sp_14"
        android:lineHeight="@dimen/sp_14"
        android:lineSpacingExtra="0dp"
        android:shadowRadius="1"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Leslie Alexander" />


</androidx.constraintlayout.widget.ConstraintLayout>
