<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/motion_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/motion_publish_scene">

    <View
        android:id="@+id/vBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_choose_publish_scene_gradient" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplate"
        style="@style/MetaTextView.S15.PoppinsBold700.LeftTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_12"
        android:background="@drawable/bg_white_corner_20"
        android:drawableStart="@drawable/icon_template_publish"
        android:drawablePadding="@dimen/dp_14"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/template_all_cap"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvPost" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvPost"
        style="@style/MetaTextView.S15.PoppinsBold700.LeftTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_13"
        android:background="@drawable/bg_white_corner_20"
        android:drawableStart="@drawable/icon_post_publish"
        android:drawablePadding="@dimen/dp_14"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/post_all_cap"
        app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene" />

    <com.lihang.ShadowLayout
        android:id="@+id/slChoosePublishScene"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/tab_layout_height"
        android:background="@color/white"
        app:hl_cornerRadius="@dimen/dp_100"
        app:hl_shadowColor="@color/black"
        app:hl_shadowLimit="@dimen/dp_16"
        app:hl_shadowOffsetY="@dimen/dp_6"
        app:layout_constraintBottom_toTopOf="@id/space"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:layout_gravity="center"
            android:background="@color/white" />

    </com.lihang.ShadowLayout>

    <ImageView
        android:id="@+id/ivPublish"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_post_add"
        app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
        app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
        app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
        app:layout_constraintTop_toTopOf="@id/slChoosePublishScene"
        app:tint="@color/textColorPrimary" />

    <Space
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.motion.widget.MotionLayout>