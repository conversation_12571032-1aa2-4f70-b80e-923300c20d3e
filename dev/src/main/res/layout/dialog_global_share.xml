<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:clickable="true"
    tools:background="@color/black_50">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_blur"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:alpha="0"
        android:foreground="@color/black_60"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_37"
        android:src="@drawable/ic_detail_share_image_close"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_preview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:layout_marginVertical="@dimen/dp_32"
        android:clickable="true"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/cl_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbphv"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintWidth_max="@dimen/dp_300" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_loading"
        style="@style/MetaTextView.S15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_black_40_corner_100"
        android:clickable="true"
        android:paddingStart="@dimen/dp_40"
        android:paddingTop="@dimen/dp_7"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_5"
        android:text="@string/loading"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/cl_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_12"
        android:indeterminateDrawable="@drawable/animated_white_loading_progress"
        app:layout_constraintBottom_toBottomOf="@id/tv_loading"
        app:layout_constraintStart_toStartOf="@id/tv_loading"
        app:layout_constraintTop_toTopOf="@id/tv_loading" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_loading, pb_loading" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.RoundView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:topLeftRadius="@dimen/dp_16"
            app:topRightRadius="@dimen/dp_16" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/share"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/rv_platform"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_min="@dimen/dp_44"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:id="@+id/v_divider_top"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_F0F0F0"
            app:layout_constraintBottom_toTopOf="@id/rv_platform" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv_platform"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingHorizontal="@dimen/dp_6"
            app:layout_constraintBottom_toTopOf="@id/v_divider_mid"
            tools:itemCount="5"
            tools:layoutManager="LinearLayoutManager"
            tools:listitem="@layout/item_share_platform"
            tools:orientation="horizontal" />

        <View
            android:id="@+id/v_divider_mid"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_F0F0F0"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/rv_feature"
            tools:visibility="visible" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv_feature"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingHorizontal="@dimen/dp_6"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/rv_long_image_platform"
            tools:itemCount="5"
            tools:layoutManager="LinearLayoutManager"
            tools:listitem="@layout/item_share_platform"
            tools:orientation="horizontal"
            tools:visibility="visible" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv_long_image_platform"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingHorizontal="@dimen/dp_6"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/space_bottom"
            tools:itemCount="5"
            tools:layoutManager="LinearLayoutManager"
            tools:listitem="@layout/item_share_platform"
            tools:orientation="horizontal"
            tools:visibility="visible" />

        <Space
            android:id="@+id/space_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_20"
            app:layout_constraintBottom_toBottomOf="parent" />

        <View
            android:id="@+id/v_cover"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/rv_long_image_platform"
            app:layout_constraintTop_toTopOf="@id/rv_platform" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>