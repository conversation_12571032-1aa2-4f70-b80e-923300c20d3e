<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/MainBackground"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/fl_avatar_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black" />


    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fcv_loading_layout"
        android:name="com.socialplay.gpark.ui.editor.tab.loadingscreen.v2.AvatarLoadingScreenV2Fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/ll_title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:layout_width="match_parent"
            android:layout_height="0dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingRight="4dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="@dimen/dp_5">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide_entrance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_begin="@dimen/dp_18" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rl_recharge_entrance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                android:background="@drawable/bg_white_corner_38"
                android:paddingHorizontal="@dimen/dp_12"
                android:paddingVertical="@dimen/dp_4"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/guide_entrance"
                app:layout_constraintEnd_toStartOf="@id/ivFeedBack"
                app:layout_constraintTop_toTopOf="@id/guide_entrance"
                app:layout_constraintWidth_min="@dimen/dp_55"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/img_recharge"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/icon_recharge"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_balance"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_6"
                    android:text="@string/zero_int"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/img_recharge"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/ivFeedBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                android:src="@drawable/icon_feedback"
                app:layout_constraintBottom_toBottomOf="@id/guide_entrance"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/guide_entrance" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_recharge_first_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:visibility="visible"
                android:visibility="gone"
                android:layout_marginRight="-10dp"
                android:layout_marginTop="10dp"
                app:layout_constraintRight_toRightOf="@id/rl_recharge_entrance"
                app:layout_constraintTop_toBottomOf="@+id/rl_recharge_entrance">

                <com.lihang.ShadowLayout
                    android:id="@+id/shadowDress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-17dp"
                    app:hl_cornerRadius="@dimen/dp_16"
                    app:hl_shadowColor="@color/black"
                    app:hl_shadowLimit="@dimen/dp_16"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/img_recharge_up">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/icon_home_vistor"
                        android:paddingHorizontal="@dimen/dp_10"
                        android:paddingVertical="@dimen/dp_9"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/img_recharge_up">

                        <ImageView
                            android:id="@+id/img_recharge_big"
                            android:layout_width="@dimen/dp_50"
                            android:layout_height="@dimen/dp_50"
                            android:src="@drawable/icon_recharge_big"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            style="@style/MetaTextView.S13.PoppinsMedium500"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="@dimen/dp_8"
                            android:layout_height="wrap_content"
                            android:text="@string/recharge_tips"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/img_recharge_big"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.lihang.ShadowLayout>

                <ImageView
                    android:id="@+id/img_recharge_up"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/dp_27"
                    android:src="@drawable/icon_rechange_up"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_view_mode_viewport"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintHeight_percent="0.48"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/includeBtns"
            layout="@layout/include_role_btns"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp_4" />

    </FrameLayout>

    <!--不要删除CoordinatorLayout的id，会导致fragment重建时折叠布局展开-->
    <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
        android:id="@+id/cl_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/mAppBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            app:layout_behavior="com.google.android.material.appbar.NoDraggableAppBarBehavior">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/mCtl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:collapsedTitleGravity="center_horizontal"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed">

                <View
                    android:id="@+id/v_role_view_widgets_placeholder"
                    android:layout_width="match_parent"
                    android:layout_height="368dp"/>

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/tbRole"
                    android:layout_width="1dp"
                    android:layout_height="@dimen/dp_173"
                    app:layout_collapseMode="pin" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_white_corner_20_top">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal">

                    <View
                        android:layout_width="@dimen/dp_33"
                        android:layout_height="@dimen/dp_4"
                        android:layout_marginTop="@dimen/dp_8"
                        android:layout_marginBottom="@dimen/dp_12"
                        android:background="@drawable/bg_black_15_s100" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragment_data_list"
            android:name="com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataListFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/clEditorDressUp"
            android:background="@color/white"
            android:layout_marginBottom="@dimen/tab_layout_height"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

    </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>


</androidx.constraintlayout.widget.ConstraintLayout>