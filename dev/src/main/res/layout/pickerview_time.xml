<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <include
        layout="@layout/include_pickerview_topbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pickerview_topbar_height" />

    <LinearLayout
        android:id="@+id/timepicker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:gravity="center"
        android:minHeight="150dp"
        android:orientation="horizontal">

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/year"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.96" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/month"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/day"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/hour"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/min"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/second"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />
    </LinearLayout>


</LinearLayout>