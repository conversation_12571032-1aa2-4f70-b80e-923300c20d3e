<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_150"
        android:layout_height="@dimen/dp_150"
        android:src="@drawable/icon_no_recent_activity" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S15"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_25"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/footer_load_end"
        android:textColor="@color/color_9B9FA6" />

</LinearLayout>