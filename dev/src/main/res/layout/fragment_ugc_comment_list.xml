<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/ic_close"
        app:layout_constraintTop_toTopOf="parent"
        app:showRightText="false" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_comment_label"
        style="@style/MetaTextView.S18.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_56"
        android:gravity="center_vertical"
        android:text="@string/comment_cap"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintStart_toStartOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_comment_count"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_44"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/color_999999"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toStartOf="@id/tv_post_btn"
        app:layout_constraintStart_toEndOf="@id/tv_comment_label"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_post_btn"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawableStart="@drawable/ic_pen_post"
        android:gravity="center_vertical"
        android:text="@string/post_cap"
        android:textColor="@color/color_4AB4FF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_sort_btn"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_11"
        android:layout_marginEnd="@dimen/dp_4"
        android:gravity="center_vertical"
        android:text="@string/most_recent"
        android:textColor="@color/color_4AB4FF"
        app:layout_constraintEnd_toStartOf="@id/iv_sort_btn_icon"
        app:layout_constraintTop_toBottomOf="@id/tbl" />

    <ImageView
        android:id="@+id/iv_sort_btn_icon"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_2"
        android:src="@drawable/ic_ugc_comment_sort_arrow"
        app:layout_constraintBottom_toBottomOf="@id/tv_sort_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_sort_btn" />

    <View
        android:id="@+id/v_sort_btn_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_sort_btn"
        app:layout_constraintEnd_toEndOf="@id/iv_sort_btn_icon"
        app:layout_constraintHeight_min="@dimen/dp_24"
        app:layout_constraintStart_toStartOf="@id/tv_sort_btn"
        app:layout_constraintTop_toTopOf="@id/tv_sort_btn" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_sort_btn" />

    <View
        android:id="@+id/v_sort_option_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_white_14"
        android:clickable="true"
        android:elevation="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/tv_sort_most_recent"
        app:layout_constraintEnd_toEndOf="@id/tv_sort_most_favorable"
        app:layout_constraintStart_toStartOf="@id/tv_sort_most_favorable"
        app:layout_constraintTop_toTopOf="@id/tv_sort_most_favorable" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_sort_most_favorable"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_16"
        android:elevation="@dimen/dp_20"
        android:hint="@string/most_recent"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_16"
        android:text="@string/most_favorable"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_sort_btn" />

    <View
        android:id="@+id/v_split_sort"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@color/color_E6E6E6"
        android:elevation="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tv_sort_most_favorable"
        app:layout_constraintStart_toStartOf="@id/tv_sort_most_favorable"
        app:layout_constraintTop_toBottomOf="@id/tv_sort_most_favorable" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_sort_most_recent"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="@dimen/dp_20"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_16"
        android:text="@string/most_recent"
        android:hint="@string/most_favorable"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tv_sort_most_favorable"
        app:layout_constraintStart_toStartOf="@id/tv_sort_most_favorable"
        app:layout_constraintTop_toBottomOf="@id/v_split_sort" />

    <View
        android:id="@+id/v_cover_click"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>