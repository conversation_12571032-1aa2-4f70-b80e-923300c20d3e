<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clSidebar"
    android:layout_width="@dimen/dp_304"
    android:layout_height="match_parent"
    android:layout_gravity="start"
    android:background="@color/color_F0F0F0"
    android:clickable="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphvSidebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivSidebarAvatar"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_24"
        android:padding="@dimen/dp_1"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbphvSidebar"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSidebarUsername"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/ivSidebarAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivSidebarAvatar"
        app:layout_constraintTop_toTopOf="@id/ivSidebarAvatar"
        tools:text="Yang Tuoer03an Yang dad 123g biubibuiubibu" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rvSidebarEntrance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_16"
        android:clipToPadding="false"
        android:overScrollMode="ifContentScrolls"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/tvSidebarScan"
        app:layout_constraintTop_toBottomOf="@id/ivSidebarAvatar"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        tools:itemCount="1"
        tools:layout_editor_absoluteX="64dp"
        tools:listitem="@layout/item_home_drawer" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSidebarScan"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@drawable/bg_white_round_8_ripple_black_8"
        android:drawableTop="@drawable/ic_feat_24_1a1a1a_scan_code"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_8"
        android:text="@string/profile_side_bar_scan"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvSidebarSetting"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSidebarSetting"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_9"
        android:background="@drawable/bg_white_round_8_ripple_black_8"
        android:drawableTop="@drawable/ic_feat_24_1a1a1a_settings"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_8"
        android:text="@string/profile_side_bar_setting"
        app:layout_constraintBottom_toBottomOf="@id/tvSidebarScan"
        app:layout_constraintEnd_toStartOf="@id/tvSidebarFeedback"
        app:layout_constraintStart_toEndOf="@id/tvSidebarScan"
        app:layout_constraintTop_toTopOf="@id/tvSidebarScan" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSidebarFeedback"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/sp_16"
        android:background="@drawable/bg_white_round_8_ripple_black_8"
        android:drawableTop="@drawable/ic_feat_24_1a1a1a_feedback"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_8"
        android:text="@string/profile_side_bar_feedback"
        app:layout_constraintBottom_toBottomOf="@id/tvSidebarScan"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvSidebarSetting"
        app:layout_constraintTop_toTopOf="@id/tvSidebarScan" />

</androidx.constraintlayout.widget.ConstraintLayout>