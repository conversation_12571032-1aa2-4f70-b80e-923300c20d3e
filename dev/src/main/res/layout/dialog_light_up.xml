<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center">

        <View
            android:id="@+id/v_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@drawable/bg_white_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="@dimen/dp_343" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title_1"
            style="@style/MetaTextView.S16.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_25"
            android:gravity="center_vertical"
            android:text="@string/balance_with_semi"
            app:layout_constraintEnd_toStartOf="@id/tv_title_2"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@id/v_bg"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title_2"
            style="@style/MetaTextView.S14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_25"
            android:drawableEnd="@drawable/ic_light_up_1"
            android:drawablePadding="@dimen/dp_2"
            android:gravity="center_vertical"
            android:text="988"
            android:textColor="@color/color_757575"
            app:layout_constraintEnd_toEndOf="@id/v_bg"
            app:layout_constraintStart_toEndOf="@id/tv_title_1"
            app:layout_constraintTop_toTopOf="parent" />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_content"
            style="@style/MetaTextView.S14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_6"
            android:gravity="center"
            android:text="@string/balance_desc"
            android:textColor="@color/color_757575"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@id/v_bg"
            app:layout_constraintStart_toStartOf="@id/v_bg"
            app:layout_constraintTop_toBottomOf="@id/tv_title_1" />

        <ImageView
            android:id="@+id/iv_close_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_12"
            android:src="@drawable/ic_spark_dialog_close"
            app:layout_constraintEnd_toEndOf="@id/v_bg"
            app:layout_constraintTop_toTopOf="@id/v_bg" />

        <View
            android:id="@+id/v_bg_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_24"
            android:background="@drawable/bg_fff7f3_round_8_ffeadc_stroke_1"
            app:layout_constraintBottom_toBottomOf="@id/tv_x1"
            app:layout_constraintEnd_toStartOf="@id/v_bg_2"
            app:layout_constraintStart_toStartOf="@id/v_bg"
            app:layout_constraintTop_toTopOf="@id/tv_x1" />

        <View
            android:id="@+id/v_bg_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_24"
            android:background="@drawable/bg_fff7f3_round_8_ffeadc_stroke_1"
            app:layout_constraintBottom_toBottomOf="@id/tv_x1"
            app:layout_constraintEnd_toEndOf="@id/v_bg"
            app:layout_constraintStart_toEndOf="@id/v_bg_1"
            app:layout_constraintTop_toTopOf="@id/tv_x1" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_x1"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_48"
            android:drawableStart="@drawable/ic_light_up_gray"
            android:drawablePadding="@dimen/dp_3"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp_12"
            android:text="x1"
            app:layout_constraintEnd_toEndOf="@id/v_bg_1"
            app:layout_constraintStart_toStartOf="@id/v_bg_1"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_x2"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_light_up_gray"
            android:drawablePadding="@dimen/dp_3"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp_12"
            android:text="x2"
            app:layout_constraintBottom_toBottomOf="@id/tv_x1"
            app:layout_constraintEnd_toEndOf="@id/v_bg_2"
            app:layout_constraintStart_toStartOf="@id/v_bg_2"
            app:layout_constraintTop_toTopOf="@id/tv_x1" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_tips"
            style="@style/MetaTextView.S14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_2"
            android:layout_marginTop="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_24"
            android:drawableStart="@drawable/ic_light_up_question_mark"
            android:drawablePadding="@dimen/dp_2"
            android:gravity="center_vertical"
            android:text="@string/what_is_spark"
            android:textColor="@color/color_757575"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@id/v_bg"
            app:layout_constraintStart_toStartOf="@id/v_bg"
            app:layout_constraintTop_toBottomOf="@id/tv_x1" />

        <View
            android:id="@+id/v_tips_click"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="-16dp"
            android:layout_marginTop="-16dp"
            android:layout_marginEnd="-16dp"
            android:layout_marginBottom="-16dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_tips"
            app:layout_constraintEnd_toEndOf="@id/tv_tips"
            app:layout_constraintStart_toStartOf="@id/tv_tips"
            app:layout_constraintTop_toTopOf="@id/tv_tips"
            tools:visibility="gone" />

        <Space
            android:id="@+id/space_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            app:layout_constraintTop_toBottomOf="@id/tv_tips" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>