<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_author_avatar"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/guide_user_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/placeholder" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_author_name"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tv_time"
        app:layout_constraintEnd_toStartOf="@id/iv_official"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="What's iyoskdlafj" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/iv_official"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_author_name"
        app:layout_constraintEnd_toStartOf="@id/space_more"
        app:layout_constraintStart_toEndOf="@id/tv_author_name"
        app:layout_constraintTop_toTopOf="@id/tv_author_name"
        tools:visibility="visible" />

    <View
        android:id="@+id/v_author_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/guide_user_info"
        app:layout_constraintEnd_toEndOf="@id/tv_author_name"
        app:layout_constraintStart_toStartOf="@id/iv_author_avatar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_time"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="@id/guide_user_info"
        app:layout_constraintStart_toStartOf="@id/tv_author_name"
        app:layout_constraintTop_toBottomOf="@id/tv_author_name"
        tools:text="Nov 12, 2022 14:12 AM" />

    <Space
        android:id="@+id/space_more"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/iv_more_btn"
        app:layout_constraintEnd_toStartOf="@id/iv_more_btn"
        app:layout_constraintTop_toTopOf="@id/iv_more_btn" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="@dimen/dp_23"
        android:layout_height="@dimen/dp_23"
        android:layout_marginEnd="@dimen/dp_6"
        android:src="@drawable/ic_post_detail_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_user_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="@dimen/dp_43" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_content"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_16"
        android:lineSpacingMultiplier="1.2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_author_name"
        app:layout_constraintTop_toBottomOf="@id/tv_time"
        tools:text="Infinite Borders puts players in the ever captivating Three Kingdoms period of Ancient China, and offers a blend of strategy, RPG city-building, and gacha mechanics. Having spent considerable time in both its native East Asian markets —previously known as Reign of Warlords or ROW: Tam Quốc. — It is now entering a global release during its Closed Beta Test phase. However, the English localization is still a bit spotty." />

    <ImageView
        android:id="@+id/iv_like_count"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_12"
        android:src="@drawable/icon_post_like_unselected"
        app:layout_constraintStart_toStartOf="@id/tv_author_name"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like_count"
        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
        app:layout_constraintStart_toEndOf="@id/iv_like_count"
        app:layout_constraintTop_toTopOf="@id/iv_like_count"
        tools:text="26" />

    <View
        android:id="@+id/v_like_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
        app:layout_constraintEnd_toEndOf="@id/tv_like_count"
        app:layout_constraintStart_toStartOf="@id/iv_like_count"
        app:layout_constraintTop_toTopOf="@id/iv_like_count" />

    <ImageView
        android:id="@+id/iv_comment_count"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_26"
        android:src="@drawable/ic_post_comment"
        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
        app:layout_constraintStart_toEndOf="@id/tv_like_count"
        app:layout_constraintTop_toTopOf="@id/iv_like_count" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_comment_count"
        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/iv_comment_count"
        app:layout_constraintStart_toEndOf="@id/iv_comment_count"
        app:layout_constraintTop_toTopOf="@id/iv_comment_count"
        tools:text="26" />

    <View
        android:id="@+id/v_comment_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_comment_count"
        app:layout_constraintEnd_toEndOf="@id/tv_comment_count"
        app:layout_constraintStart_toStartOf="@id/iv_comment_count"
        app:layout_constraintTop_toTopOf="@id/iv_comment_count" />

    <View
        android:id="@+id/v_timeline"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_34"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/color_CCCCCC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide_user_info" />

</androidx.constraintlayout.widget.ConstraintLayout>