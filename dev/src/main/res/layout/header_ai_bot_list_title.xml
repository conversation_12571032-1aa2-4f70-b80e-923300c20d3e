<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_6"
    >

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="@string/ai_bot_conversation_list_tile"
        android:fontFamily="@font/poppins_bold_700"
        android:textColor="@color/black_90"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_gender"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/img_gender"
            android:layout_width="@dimen/dp_18"
            app:layout_constraintRight_toLeftOf="@+id/tv_gender"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/icon_gender_female"
            android:layout_height="@dimen/dp_18"/>
        <TextView
            android:id="@+id/tv_gender"
            android:layout_width="wrap_content"
            android:text="woman"
            app:layout_constraintRight_toLeftOf="@+id/img_ai_arrow"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:fontFamily="@font/poppins_regular_400"
            android:layout_marginRight="@dimen/dp_8"
            android:layout_height="wrap_content"/>
        <ImageView
            android:id="@+id/img_ai_arrow"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/icon_aibot_down_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MaxHeightRecyclerView
        android:id="@+id/rvUgcLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_6"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:itemCount="14"
        tools:listitem="@layout/adapter_aibot_label_item" />




    <RelativeLayout
        android:id="@+id/ivMore"
        android:layout_width="wrap_content"
        android:paddingLeft="@dimen/dp_50"
        android:paddingRight="@dimen/dp_8"
        android:background="@drawable/bg_ai_bot_line_bg"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/dp_16"
        app:layout_constraintTop_toTopOf="@id/rvUgcLabel"
        android:layout_height="@dimen/dp_30">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_centerVertical="true"
            android:layout_height="wrap_content"

            android:src="@drawable/icon_ai_bot_tag_more"
            />
    </RelativeLayout>





</androidx.constraintlayout.widget.ConstraintLayout>