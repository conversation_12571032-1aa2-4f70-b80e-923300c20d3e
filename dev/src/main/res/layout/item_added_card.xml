<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_60"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingVertical="@dimen/dp_10">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_10dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintEnd_toStartOf="@id/tvDelete"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Night FuseNight FuseNight FuseNight FuseNight Fust FuseNight FuseNight FuseNight FuseNight Fuse" />

    <TextView
        android:id="@+id/tvDelete"
        style="@style/Button.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_stroke_neutral_7_s32"
        android:minWidth="@dimen/dp_76"
        android:minHeight="@dimen/dp_32"
        android:text="@string/delete_cap"
        android:textColor="@color/neutral_color_3"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivIcon" />

</androidx.constraintlayout.widget.ConstraintLayout>