<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_gravity="center">


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="H,164:118"
        android:src="@drawable/placeholder"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <View
        android:id="@+id/v_shadow_mask"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        android:background="@drawable/bg_bottom_icon_shadow_mask_s12"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_34"/>

    <FrameLayout
        android:id="@+id/fl_author_avatar"
        app:layout_constraintBottom_toBottomOf="@id/v_shadow_mask"
        app:layout_constraintStart_toStartOf="@id/v_shadow_mask"
        app:layout_constraintTop_toTopOf="@id/v_shadow_mask"
        android:background="@drawable/bg_white_circle"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:padding="@dimen/dp_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.google.android.material.imageview.ShapeableImageView
            android:layout_width="@dimen/dp_20"
            android:id="@+id/iv_author_avatar"
            android:layout_height="@dimen/dp_20"
            app:shapeAppearance="@style/circleStyle"
            tools:src="@drawable/placeholder" />
    </FrameLayout>


    <TextView
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:id="@+id/tv_author_name"
        android:layout_marginHorizontal="@dimen/dp_6"
        app:layout_constraintEnd_toEndOf="@id/v_shadow_mask"
        android:maxLines="1"
        android:textColor="@color/white_80"
        app:layout_constraintStart_toEndOf="@+id/fl_author_avatar"
        app:layout_constraintTop_toTopOf="@id/v_shadow_mask"
        app:layout_constraintBottom_toBottomOf="@id/v_shadow_mask"
        tools:text="Leslie Alexander" />


    <TextView
        style="@style/MetaTextView.S15.PoppinsMedium600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:id="@+id/tv_name"
        android:layout_marginHorizontal="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_8"
        android:maxLines="1"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        tools:text="Leslie Alexander" />


    <ImageView
        android:id="@+id/iv_heat_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_8"
        android:src="@drawable/ic_moment_heat"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name"
        app:layout_constraintStart_toStartOf="@id/v_shadow_mask"
        app:tint="@color/neutral_color_4" />


    <TextView
        android:id="@+id/tv_heat"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_heat_icon"
        app:layout_constraintStart_toEndOf="@+id/iv_heat_icon"
        app:layout_constraintTop_toTopOf="@id/iv_heat_icon"
        tools:text="9.9k players" />

</androidx.constraintlayout.widget.ConstraintLayout>