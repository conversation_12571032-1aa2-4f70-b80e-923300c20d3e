<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="@dimen/dp_78"
        android:layout_height="@dimen/dp_78"
        android:background="@drawable/bg_black_80_s14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.ProgressLoadingView
        android:id="@+id/pb"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        app:layout_constraintBottom_toBottomOf="@id/iv_bg"
        app:layout_constraintEnd_toEndOf="@id/iv_bg"
        app:layout_constraintStart_toStartOf="@id/iv_bg"
        app:layout_constraintTop_toTopOf="@id/iv_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>