<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <com.lihang.ShadowLayout
        android:id="@+id/fl_item_box_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_shadowColor="#00000000"
        app:hl_shadowLimit="8dp"
        app:hl_shadowSymmetry="false"
        app:hl_cornerRadius="@dimen/dp_16"
        app:hl_shadowOffsetY="1dp"
        android:focusable="false"
        app:clickable="false"
        android:clickable="false"
        app:hl_shadowHiddenTop="true"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowHiddenRight="true"
        app:layout_constraintBottom_toTopOf="@+id/tv_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_icon"
            style="@style/SuggestedGameIcon"
            android:scaleType="centerCrop"
            android:src="#EEE" />
    </com.lihang.ShadowLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        app:uiLineHeight="@dimen/sp_22"
        android:textColor="@color/neutral_color_2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_horizontal|center_vertical"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/fl_item_box_shadow"
        app:layout_constraintStart_toStartOf="@id/fl_item_box_shadow"
        app:layout_constraintTop_toBottomOf="@id/fl_item_box_shadow"
        tools:text="Change beding for all cows" />


</androidx.constraintlayout.widget.ConstraintLayout>