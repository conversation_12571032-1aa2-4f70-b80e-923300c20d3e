<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/bg_black_80_s8"
        android:gravity="center"
        android:minHeight="@dimen/dp_44"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_12"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/pb"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:indeterminateTint="@color/white" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_msg"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:gravity="center"
            android:lineSpacingMultiplier="1.2"
            android:text="@string/loading"
            android:textColor="@color/white" />

    </LinearLayout>

</FrameLayout>