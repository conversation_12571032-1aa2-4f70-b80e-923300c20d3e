<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_conversation_item"
    android:clickable="true"
    android:focusable="true">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        style="@style/Avatar.Round.Medium"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_12"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_friend_name"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tv_friend_status"
        app:layout_constraintEnd_toStartOf="@id/label_group"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginEnd="@dimen/dp_12"
        tools:text="amazing wtg wtf amazing wtg wtf amazing wtg wtf " />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_friend_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivCheckBox"
        app:layout_constraintStart_toEndOf="@id/tv_friend_name"
        app:layout_constraintTop_toTopOf="@id/tv_friend_name"
        tools:visibility="visible" />

    <View
        android:id="@+id/iv_online"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_6"
        android:background="@drawable/sp_online_dot"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_friend_status"
        app:layout_constraintEnd_toStartOf="@id/tv_friend_status"
        app:layout_constraintStart_toStartOf="@id/tv_friend_name"
        app:layout_constraintTop_toTopOf="@id/tv_friend_status"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_friend_status"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_online"
        app:layout_constraintTop_toBottomOf="@id/tv_friend_name"
        tools:text="Online" />

    <ImageView
        android:id="@+id/ivCheckBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:src="@drawable/icon_group_chat_friend_checked"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <!--  右侧要显示字母index, 需要留出 36dp  -->
    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="@dimen/dp_36"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>