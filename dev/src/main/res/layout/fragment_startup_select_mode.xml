<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_20"
        android:minHeight="@dimen/dp_24"
        android:paddingBottom="@dimen/dp_40"
        android:text="@string/select_mode_desc"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_16"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_24" />

    <ImageView
        android:id="@+id/iv_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_28"
        android:background="@drawable/bg_select_mode"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_75"
        android:paddingBottom="@dimen/dp_19"
        android:src="@drawable/ic_select_mode_loading"
        app:layout_constraintBottom_toTopOf="@id/iv_bottom"
        app:layout_constraintDimensionRatio="319:256"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintWidth_max="@dimen/dp_319" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_top_title"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_16"
        android:minHeight="@dimen/dp_24"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="@id/iv_top"
        app:layout_constraintTop_toTopOf="@id/iv_top"
        app:uiLineHeight="@dimen/dp_24" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_top_desc"
        style="@style/MetaTextView.S13"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_1"
        android:textColor="@color/white_30"
        android:textSize="@dimen/dp_13"
        app:layout_constraintStart_toStartOf="@id/tv_top_title"
        app:layout_constraintTop_toBottomOf="@id/tv_top_title"
        app:uiLineHeight="@dimen/dp_20" />

    <ImageView
        android:id="@+id/iv_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@drawable/bg_select_mode"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_71"
        android:paddingBottom="@dimen/dp_1"
        android:src="@drawable/ic_select_mode_party"
        app:layout_constraintBottom_toTopOf="@id/tv_next_btn"
        app:layout_constraintDimensionRatio="319:256"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_top"
        app:layout_constraintWidth_max="@dimen/dp_319" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_bottom_title"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_16"
        android:minHeight="@dimen/dp_24"
        android:text="@string/select_party"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="@id/iv_bottom"
        app:layout_constraintTop_toTopOf="@id/iv_bottom"
        app:uiLineHeight="@dimen/dp_24" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_bottom_desc"
        style="@style/MetaTextView.S13"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_1"
        android:text="@string/select_party_desc"
        android:textColor="@color/white_30"
        android:textSize="@dimen/dp_13"
        app:layout_constraintStart_toStartOf="@id/tv_bottom_title"
        app:layout_constraintTop_toBottomOf="@id/tv_bottom_title"
        app:uiLineHeight="@dimen/dp_20" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_next_btn"
        style="@style/Button.S18.PoppinsBlack900.Height48"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_34"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="@string/intl_play"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_bottom"
        app:layout_constraintWidth_max="@dimen/dp_285" />

</androidx.constraintlayout.widget.ConstraintLayout>