<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/shape_bord_1dp_dbdbdb"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:padding="@dimen/dp_1"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/plot_short_video_icon" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S15.PoppinsMedium600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:textColor="@color/color_17191C"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:text="@string/app_name" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_avatar_flag"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:background="@drawable/bg_ffe8e4_corner_19"
        android:paddingHorizontal="@dimen/dp_6"
        android:text="@string/plot_my_avatar_flag"
        android:textColor="@color/color_FF5F42"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="@id/iv_icon" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_select"
        style="@style/MetaTextView.S15.PoppinsMedium500"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_plot_select_def"
        android:gravity="center"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>