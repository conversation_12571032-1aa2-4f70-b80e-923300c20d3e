<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.WrapNestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/v_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <ImageView
            android:id="@+id/iv_empty_tip_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/no_friends"
            app:layout_constraintBottom_toTopOf="@+id/tv_empty_tip_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_empty_tip_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_80"
            android:text="@string/no_friends"
            android:textColor="@color/textColorSecondary"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_empty_tip_img" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/sl_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_friend_list"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
</com.socialplay.gpark.ui.view.WrapNestedScrollableHost>