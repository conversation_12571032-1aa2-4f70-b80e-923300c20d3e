<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_reply"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <View
        android:id="@+id/v_center_line"
        android:layout_width="@dimen/dp_0"
        android:layout_height="0.5dp"
        android:layout_marginLeft="@dimen/dp_92"
        android:background="@color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_expand_reply"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_24"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:text="@string/article_repaly_expand_more"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:textColor="@color/neutral_color_4"
        android:drawableEnd="@drawable/ic_community_comment_reply_expand" />

    <TextView
        android:id="@+id/tv_collapse_reply"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:text="@string/article_reply_collapse"
        android:textColor="@color/neutral_color_4"
        android:drawableEnd="@drawable/ic_community_comment_reply_collapse" />

</LinearLayout>