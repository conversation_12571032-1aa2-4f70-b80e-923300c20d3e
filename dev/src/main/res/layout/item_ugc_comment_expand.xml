<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dp_60"
    android:layout_marginEnd="@dimen/dp_16"
    android:gravity="center_vertical">

    <View
        android:id="@+id/startLine"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_05"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/dp_4"
        android:background="@color/color_4AB4FF" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="@dimen/dp_36"
        android:layout_height="wrap_content"/>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_expand_btn"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/color_4AB4FF"
        tools:text="Expand More" />

    <Space
        android:id="@+id/space_expand_btn"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_1" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_collapse_btn"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="@string/collapse_cap"
        android:textColor="@color/color_4AB4FF" />

</LinearLayout>