<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/black_60">

    <!--  当前布局并不会充满整个dialog, 这里加一个高度比较高的Space, 强行撑开 Dialog  -->
    <!--  当键盘弹起时, dialogLayout 才有 translationY 的空间, 避免dialog高度不足时, 修改 translationY 后, 布局  -->
    <!--  避免 dialog 高度不足时, 修改 dialogLayout 的 translationY 后, 内容被截断  -->
    <Space
        android:layout_width="@dimen/dp_1"
        android:layout_height="1000dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialogLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_32"
        android:background="@drawable/bg_white_round_38"
        android:padding="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dialog_customize_flower_count_title"
            android:textColor="@color/color_1A1A1A"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vInputBg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/shape_f0f0f0_corner_16"
            app:layout_constraintTop_toBottomOf="@id/tvTitle" />

        <!-- 输入的内容为1-1000的数字 -->
        <com.ly123.tes.mgs.im.view.IMEditText
            android:id="@+id/etInput"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/transparent"
            android:gravity="center_vertical"
            android:inputType="number"
            android:maxLength="4"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_16"
            android:textColor="@color/color_1A1A1A"
            android:textColorHint="@color/color_B3B3B3"
            android:textCursorDrawable="@drawable/bg_cursor"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/vInputBg"
            app:layout_constraintEnd_toStartOf="@id/ivClearText"
            app:layout_constraintStart_toStartOf="@id/vInputBg"
            app:layout_constraintTop_toTopOf="@id/vInputBg" />

        <ImageView
            android:id="@+id/ivClearText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:padding="@dimen/dp_8"
            android:src="@drawable/icon_edit_group_name_clear_text"
            app:layout_constraintBottom_toBottomOf="@id/vInputBg"
            app:layout_constraintEnd_toEndOf="@id/vInputBg"
            app:layout_constraintTop_toTopOf="@id/vInputBg"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvInputDesc"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_customize_flower_count_input_desc"
            android:textColor="@color/color_999999"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vInputBg" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvConfirm"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_ffef30_round_40"
            android:gravity="center"
            android:text="@string/dialog_customize_flower_count_confirm_btn"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/tvInputDesc" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvCancel"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f0f0f0_corner_40"
            android:gravity="center"
            android:text="@string/dialog_customize_flower_count_cancel_btn"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/tvConfirm" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>