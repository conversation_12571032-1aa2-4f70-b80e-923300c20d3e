<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_male"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_48"
        app:layout_constraintBottom_toTopOf="@id/tv_next_btn"
        app:layout_constraintDimensionRatio="8:21"
        app:layout_constraintEnd_toStartOf="@id/cl_female"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc">

        <ImageView
            android:id="@+id/iv_holo_male"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0"
            android:src="@drawable/ic_create_avatar_holo_male"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="33:8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.737"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.982"
            app:layout_constraintWidth_percent="0.61875"
            tools:alpha="1" />

        <ImageView
            android:id="@+id/iv_char_male"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:scaleX="0.75"
            android:scaleY="0.75"
            android:src="@drawable/ic_create_avatar_char_male"
            tools:scaleX="1.0"
            tools:scaleY="1.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_gender_male"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_12"
        android:alpha="0.5"
        android:src="@drawable/ic_create_avatar_gender_male"
        app:layout_constraintEnd_toEndOf="@id/cl_male"
        app:layout_constraintStart_toStartOf="@id/cl_male"
        app:layout_constraintTop_toBottomOf="@id/cl_male"
        tools:alpha="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_female"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/cl_male"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cl_male"
        app:layout_constraintTop_toTopOf="@id/cl_male">

        <ImageView
            android:id="@+id/iv_holo_female"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0"
            android:src="@drawable/ic_create_avatar_holo_female"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="33:8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.737"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.982"
            app:layout_constraintWidth_percent="0.61875"
            tools:alpha="1" />

        <ImageView
            android:id="@+id/iv_char_female"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:scaleX="0.75"
            android:scaleY="0.75"
            android:src="@drawable/ic_create_avatar_char_female"
            tools:scaleX="1.0"
            tools:scaleY="1.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_gender_female"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_12"
        android:alpha="0.5"
        android:src="@drawable/ic_create_avatar_gender_female"
        app:layout_constraintEnd_toEndOf="@id/cl_female"
        app:layout_constraintStart_toStartOf="@id/cl_female"
        app:layout_constraintTop_toBottomOf="@id/cl_female" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon_tint="@color/white"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_skip_btn"
        style="@style/MetaTextView.S16.PoppinsExtraBold800"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:text="@string/login_skip"
        android:visibility="gone"
        android:textColor="@color/color_FFEF30"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:gravity="center"
        android:text="@string/intl_create_avatar"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        android:text="@string/intl_create_avatar_tips"
        android:textColor="@color/white_45"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_next_btn"
        style="@style/Button.S18.PoppinsBlack900.Height48"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_45"
        android:layout_marginBottom="@dimen/dp_90"
        android:text="@string/intl_go_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>