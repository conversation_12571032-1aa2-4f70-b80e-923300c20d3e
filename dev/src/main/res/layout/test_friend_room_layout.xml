<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp_16">

    <!-- 空白状态测试 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:text="空白状态："
        android:textSize="@dimen/sp_16"
        android:textStyle="bold" />

    <include layout="@layout/layout_friends_empty_state" />

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="@dimen/dp_24"
        android:background="#E6E6E6" />

    <!-- 有数据状态测试 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:text="有数据状态："
        android:textSize="@dimen/sp_16"
        android:textStyle="bold" />

    <include layout="@layout/layout_friends_with_data" />

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="@dimen/dp_24"
        android:background="#E6E6E6" />

    <!-- 单个好友头像测试 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:text="单个好友头像："
        android:textSize="@dimen/sp_16"
        android:textStyle="bold" />

    <include layout="@layout/item_friend_avatar" />

</LinearLayout>
