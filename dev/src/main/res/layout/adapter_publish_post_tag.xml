<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_30"
    android:background="@drawable/shape_f6f6f6_round"
    android:paddingHorizontal="@dimen/dp_4">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_topic_label"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_22"
        android:layout_height="@dimen/dp_22"
        android:background="@drawable/bg_4ab4ff_round_11"
        android:gravity="center"
        android:text="@string/community_hashtag"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_topic_title"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_22"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_4AB4FF"
        android:textSize="@dimen/dp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_topic_del"
        app:layout_constraintStart_toEndOf="@id/tv_topic_label"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_8"
        tools:text="Communityg" />

    <ImageView
        android:id="@+id/iv_topic_del"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:padding="@dimen/dp_8"
        android:src="@drawable/ic_post_del_topic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_topic_title"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>