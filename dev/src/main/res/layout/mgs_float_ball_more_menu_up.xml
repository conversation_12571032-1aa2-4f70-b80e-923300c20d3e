<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.mgs.view.MgsExpandLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_46"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mgs_more_menu_up"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_3"
    android:paddingBottom="@dimen/dp_6">
    <FrameLayout
        android:id="@+id/flParentSet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_8"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_mgs_setting" />
    </FrameLayout>
    <FrameLayout
        android:id="@+id/flParentExit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_8"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMgsMember"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_mgs_exit_game" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/flParentRecord"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_12"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMgsExit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_mgs_screen_record" />
    </FrameLayout>


</com.socialplay.gpark.ui.mgs.view.MgsExpandLinearLayout>
