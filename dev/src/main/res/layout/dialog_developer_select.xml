<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#88000000"
    android:padding="@dimen/dp_5">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSelectTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="标题" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSelectDesc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FF0000"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSelectTitle"
        tools:text="描述" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSelectDone"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@drawable/selector_dev_action_item"
        android:gravity="center"
        android:text="@string/debug_confirm"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="@id/etSelectValue"
        app:layout_constraintLeft_toRightOf="@id/etSelectValue"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/etSelectValue" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etSelectValue"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@color/neutral_color_8"
        android:padding="@dimen/dp_2"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tvSelectDone"
        app:layout_constraintTop_toBottomOf="@id/tvSelectDesc"
        tools:text="AAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSearchDone"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@drawable/selector_dev_action_item"
        android:gravity="center"
        android:text="@string/debug_search_now"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sp_15"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/etSearchValue"
        app:layout_constraintLeft_toRightOf="@id/etSearchValue"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/etSearchValue" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etSearchValue"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@color/neutral_color_8"
        android:padding="@dimen/dp_2"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tvSearchDone"
        app:layout_constraintTop_toBottomOf="@id/etSelectValue"
        tools:text="AAAAAAAAAAA" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSelectList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etSearchValue"
        tools:listitem="@layout/adapter_developer_dialog_select_item" />

</androidx.constraintlayout.widget.ConstraintLayout>