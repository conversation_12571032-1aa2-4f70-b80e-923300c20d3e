<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_more_reply"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_14"
        android:textColor="@color/color_4AB4FF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/show_replies" />

    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_34"
        android:background="@color/color_CCCCCC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>