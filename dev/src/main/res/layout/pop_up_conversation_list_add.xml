<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvAddFriend"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/message_center_popup_icon_add_friend"
                android:drawablePadding="@dimen/dp_4"
                android:padding="@dimen/dp_16"
                android:text="@string/message_center_popup_item_add_friend"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/tvAddFriendClick"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/tvAddFriend"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvAddFriend" />

            <View
                android:id="@+id/divider1"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAddFriendClick" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCreateGroup"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/message_center_popup_icon_create_group"
                android:drawablePadding="@dimen/dp_4"
                android:padding="@dimen/dp_16"
                android:text="@string/message_center_popup_item_create_group"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <View
                android:id="@+id/viewCreateGroupRedDot"
                android:layout_width="@dimen/dp_8"
                android:layout_height="@dimen/dp_8"
                android:layout_marginStart="1.5dp"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="6.5dp"
                android:background="@drawable/sp_red_dot"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/tvCreateGroup"
                app:layout_constraintTop_toTopOf="@id/tvCreateGroup"
                tools:visibility="visible" />

            <View
                android:id="@+id/tvCreateGroupClick"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/tvCreateGroup"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvCreateGroup" />

            <View
                android:id="@+id/divider2"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvCreateGroupClick" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSearchGroup"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/message_center_popup_icon_search_group"
                android:drawablePadding="@dimen/dp_4"
                android:padding="@dimen/dp_16"
                android:text="@string/message_center_popup_item_search_group"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <View
                android:id="@+id/tvSearchGroupClick"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/tvSearchGroup"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvSearchGroup" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>