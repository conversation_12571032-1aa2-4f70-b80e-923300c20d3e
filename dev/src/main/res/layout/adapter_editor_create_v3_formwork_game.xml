<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp_11"
    android:layout_marginBottom="@dimen/dp_16">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardBackgroundColor="@null"
        app:cardCornerRadius="@dimen/dp_10"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="166:117"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:src="@android:color/holo_green_dark" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_gradient_black_87" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_pv"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_8"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="@color/white"
        android:drawableStart="@drawable/ic_fire_42"
        android:drawableTint="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/cv"
        app:layout_constraintEnd_toEndOf="@id/cv"
        app:layout_constraintStart_toStartOf="@id/cv"
        tools:text="0 players" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_2"
        android:layout_marginTop="@dimen/dp_4"
        android:singleLine="true"
        app:layout_constraintTop_toBottomOf="@id/cv"
        tools:text="Work name" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1"
        tools:src="@drawable/icon_default_avatar" />

    <TextView
        android:id="@+id/tv_author_name"
        style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_4"
        android:singleLine="true"
        android:textColor="@color/color_757575"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="Author name" />


</androidx.constraintlayout.widget.ConstraintLayout>
