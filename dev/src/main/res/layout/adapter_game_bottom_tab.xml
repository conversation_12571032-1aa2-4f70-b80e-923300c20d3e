<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv"
        tools:text="游戏圈"
        android:textSize="14sp"
        android:textColor="#212121"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingVertical="17dp"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/iv"
        app:layout_constraintTop_toTopOf="@id/tv"
        app:layout_constraintBottom_toBottomOf="@id/tv"
        app:layout_constraintRight_toLeftOf="@id/tv"
        android:layout_marginRight="6dp"
        android:layout_width="24dp"
        android:layout_height="24dp"/>

    <View
        android:id="@+id/vLine"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="#EEEEEE"
        android:layout_width="1dp"
        android:layout_height="24dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>