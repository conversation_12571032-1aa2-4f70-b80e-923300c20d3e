<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp_12">

    <!-- 头像 -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/icon_default_avatar" />

    <!-- 在线状态指示器 -->
    <ImageView
        android:id="@+id/iv_online_indicator"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:background="@drawable/bg_online_friend_hint"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        tools:visibility="visible" />

    <!-- 状态标签 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_status"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_friend_status_online"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        tools:text="Online"
        tools:visibility="visible" />

    <!-- 用户名 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_username"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="@dimen/dp_60"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_status"
        app:layout_goneMarginTop="@dimen/dp_4"
        tools:text="Tuski biut" />

    <!-- 添加好友按钮 (仅在第一个位置显示) -->
    <FrameLayout
        android:id="@+id/fl_add_friend"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:background="@drawable/home_friends_add"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_add_friend"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/dp_8"
            android:text="@string/add_friends_cap"
            android:textColor="#1A1A1A" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
