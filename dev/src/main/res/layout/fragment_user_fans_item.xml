<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rv_user_fans"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>