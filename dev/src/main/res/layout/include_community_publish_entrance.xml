<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:hl_cornerRadius="@dimen/dp_100"
    app:hl_shadowColor="@color/black"
    app:hl_shadowLimit="@dimen/dp_16"
    app:hl_shadowOffsetY="@dimen/dp_6"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent">

    <View
        android:id="@+id/vPublishBg"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_gravity="center"
        android:background="@color/white" />

    <ImageView
        android:id="@+id/ivPublish"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/ic_publish_post" />

</com.lihang.ShadowLayout>
