<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/black"
    android:layout_height="match_parent"
    android:orientation="vertical">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_weight="1"
    android:layout_height="0dp"
   >

    <View
        android:layout_width="match_parent"
        android:layout_height="354dp"
        android:background="@drawable/bg_ai_bot_character_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/img_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_8"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBar" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/ai_bot_character"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/img_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/img_back" />

    <ImageView
        android:id="@+id/img_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_8"
        android:src="@drawable/icon_ai_bot_clean"
        app:layout_constraintBottom_toBottomOf="@+id/img_back"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/img_back" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/img_back">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_29">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_user_icon"
                android:layout_width="@dimen/dp_117"
                android:layout_height="@dimen/dp_117"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/imgUser"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_default_avatar"
                    app:shapeAppearance="@style/circleStyle" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_d9d9d9_storke_1" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_38"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cl_user_icon">

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:text="@string/account_name"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_4"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="top"
                    android:text="*"
                    android:textColor="@color/white_40"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_name"
                    app:layout_constraintLeft_toRightOf="@+id/tv_name"
                    app:layout_constraintTop_toTopOf="@+id/tv_name" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_generate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_name"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_name">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_8"
                        android:src="@drawable/icon_ai_bot_generate"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_generate"
                        app:layout_constraintRight_toLeftOf="@+id/tv_generate"
                        app:layout_constraintTop_toTopOf="@+id/tv_generate" />

                    <TextView
                        android:id="@+id/tv_generate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:fontFamily="@font/poppins_regular_400"
                        android:gravity="top"
                        android:text="@string/ai_bot_writer"
                        android:textSize="@dimen/sp_14"
                        android:layerType="software"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:id="@+id/tv_name_error"
                android:layout_width="match_parent"
                android:textSize="@dimen/sp_12"
                android:fontFamily="@font/poppins_regular_400"
                android:textColor="@color/color_FF4C45"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/cl_user"
                android:text="@string/ai_bot_content_error_tip"
                android:layout_height="wrap_content"/>
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center_vertical"
                android:maxEms="20"
                android:paddingHorizontal="@dimen/dp_16"
                android:textColor="@color/white_90"
                android:textColorHint="@color/white_40"
                android:enabled="true"
                android:focusableInTouchMode="true"
                android:focusable="true"
                android:textCursorDrawable="@drawable/bg_cursor_white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintTop_toBottomOf="@+id/tv_name_error" />

            <TextView
                android:id="@+id/tv_set"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:text="@string/ai_bot_set"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_name" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:gravity="top"
                android:text="*"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/tv_set"
                app:layout_constraintLeft_toRightOf="@+id/tv_set"
                app:layout_constraintTop_toTopOf="@+id/tv_set" />
            <TextView
                android:id="@+id/tv_set_error"
                android:layout_width="match_parent"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                android:fontFamily="@font/poppins_regular_400"
                android:textColor="@color/color_FF4C45"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                app:layout_constraintTop_toBottomOf="@+id/tv_set"
                android:text="@string/ai_bot_content_error_tip"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_set"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp_84"
                android:maxHeight="196dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="left|top"
                android:hint="@string/ai_bot_set_hint"
                android:lineHeight="@dimen/dp_20"
                android:maxEms="100"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_12"
                android:textColor="@color/white_90"
                android:textColorHint="@color/white_40"
                android:textCursorDrawable="@drawable/bg_cursor_white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintTop_toBottomOf="@+id/tv_set_error" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_tag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_set">

                <TextView
                    android:id="@+id/tv_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:text="@string/tag_cap"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_4"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:gravity="top"
                    android:text="*"
                    android:textColor="@color/white_40"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_tag"
                    app:layout_constraintLeft_toRightOf="@+id/tv_tag"
                    app:layout_constraintTop_toTopOf="@+id/tv_tag" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_tag_more"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_tag"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_tag">

                    <ImageView
                        android:id="@+id/image_tag_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp_5"
                        android:src="@drawable/icon_arrow_right_999999"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_tag_mre"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:fontFamily="@font/poppins_regular_400"
                        android:gravity="top"
                        android:text="@string/more_cap"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintBottom_toBottomOf="@+id/image_tag_more"
                        app:layout_constraintRight_toLeftOf="@+id/image_tag_more"
                        app:layout_constraintTop_toTopOf="@+id/image_tag_more" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_tag_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:minHeight="@dimen/dp_128"
                android:paddingHorizontal="@dimen/dp_16"
                app:layout_constraintTop_toBottomOf="@+id/cl_tag">

                <com.socialplay.gpark.ui.view.FlowLayout
                    android:id="@+id/flow_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_dialogue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:text="@string/ai_bot_dialogue"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cl_tag_des" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:gravity="top"
                android:text=""
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/tv_dialogue"
                app:layout_constraintLeft_toRightOf="@+id/tv_dialogue"
                app:layout_constraintTop_toTopOf="@+id/tv_dialogue" />
            <TextView
                android:id="@+id/tv_dialogue_error"
                android:layout_width="match_parent"
                android:textSize="@dimen/sp_12"
                android:fontFamily="@font/poppins_regular_400"
                android:textColor="@color/color_FF4C45"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tv_dialogue"
                android:text="@string/ai_bot_content_error_tip"
                android:layout_height="wrap_content"/>
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_dialogue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="left|top"
                android:hint="@string/ai_bot_dialogue_hint"
                android:lineHeight="@dimen/dp_20"
                android:maxEms="1000"
                android:minHeight="@dimen/dp_80"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_12"
                android:textColor="@color/white_90"
                android:textColorHint="@color/white_40"
                android:textCursorDrawable="@drawable/bg_cursor_white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintTop_toBottomOf="@+id/tv_dialogue_error" />

            <TextView
                android:id="@+id/tv_space"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:text="@string/ai_bot_space"
                android:textColor="@color/color_757575"
                android:textSize="@dimen/sp_12"
                app:layout_constraintTop_toBottomOf="@+id/et_dialogue" />

            <TextView
                android:id="@+id/tv_prologue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:text="@string/ai_bot_prologue_tile"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_space" />

            <TextView
                android:id="@+id/tv_prologue_error"
                android:layout_width="match_parent"
                android:textSize="@dimen/sp_12"
                android:fontFamily="@font/poppins_regular_400"
                android:textColor="@color/color_FF4C45"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tv_prologue"
                android:text="@string/ai_bot_content_error_tip"
                android:layout_height="wrap_content"/>
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_prologue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="left|top"
                android:hint="@string/ai_bot_prologue_hint"
                android:lineHeight="@dimen/dp_20"
                android:maxEms="1000"
                android:minHeight="@dimen/dp_80"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_12"
                android:textColor="@color/white_90"
                android:textColorHint="@color/white_40"
                android:textCursorDrawable="@drawable/bg_cursor_white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintTop_toBottomOf="@+id/tv_prologue_error" />

            <TextView
                android:id="@+id/tv_intro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_32"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:text="@string/ai_bot_intro_tile"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_prologue" />

            <TextView
                android:id="@+id/tv_intro_error"
                android:layout_width="match_parent"
                android:textSize="@dimen/sp_12"
                android:fontFamily="@font/poppins_regular_400"
                android:textColor="@color/color_FF4C45"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tv_intro"
                android:text="@string/ai_bot_content_error_tip"
                android:layout_height="wrap_content"/>
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_intro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_white_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="left|top"
                android:hint="@string/ai_bot_intro_hint"
                android:lineHeight="@dimen/dp_20"
                android:maxEms="1000"
                android:minHeight="@dimen/dp_72"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_12"
                android:textColor="@color/white_90"
                android:textColorHint="@color/white_40"
                android:textCursorDrawable="@drawable/bg_cursor_white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintTop_toBottomOf="@+id/tv_intro_error" />

            <Space
                android:id="@+id/space"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_80"
                app:layout_constraintTop_toBottomOf="@+id/et_intro" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/load_more_loading_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_50"
        android:visibility="gone">

        <FrameLayout

            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/pb"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:indeterminateTint="@color/white" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/loading_text"
                    style="@style/MetaTextView.S15.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_4"
                    android:text="@string/loading"
                    android:textColor="@color/white" />
            </LinearLayout>


        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black"
        android:paddingVertical="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_complete"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@drawable/shape_white_corner"
            android:fontFamily="@font/poppins_semi_bold_600"
            android:gravity="center"
            android:paddingVertical="13dp"
            android:text="@string/ai_bot_create_complete"
            android:enabled="false"
            android:alpha="0.5"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>