<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
            android:paddingHorizontal="@dimen/dp_16"
            android:clipToPadding="false"
            android:id="@+id/rv_list"
            android:clipChildren="false"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>


    <com.socialplay.gpark.ui.view.LoadingView
        android:background="@color/white"
        android:id="@+id/lv_loading_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>