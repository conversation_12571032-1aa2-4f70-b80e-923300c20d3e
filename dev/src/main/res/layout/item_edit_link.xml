<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:id="@+id/horScroll"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clContent"
            android:layout_width="383dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@drawable/shape_f5f5f5_corner_10"
            android:paddingVertical="@dimen/dp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/img_logo"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_marginLeft="@dimen/dp_16"
                android:src="@drawable/icon_login_google"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                app:layout_constraintBottom_toBottomOf="@+id/img_logo"
                app:layout_constraintLeft_toRightOf="@+id/img_logo"
                app:layout_constraintTop_toTopOf="@+id/img_logo">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="tik"
                    android:textColor="@color/black"
                    android:textSize="@dimen/dp_14"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="https://v.douyin.com/iraaT4e..."
                    android:textColor="@color/color_757575"
                    android:textSize="@dimen/dp_10"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/img_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@drawable/icon_delete_link" />
    </LinearLayout>


</HorizontalScrollView>