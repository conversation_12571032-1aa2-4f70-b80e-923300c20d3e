<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_96"
    android:layout_height="@dimen/dp_96"
    android:layout_marginStart="@dimen/dp_6">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/round_corner_6dp" />

    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_gravity="center"
        android:src="@drawable/ic_post_play"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_del_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_margin="@dimen/dp_2"
        android:src="@drawable/ic_post_del_media"
        android:visibility="gone" />

</FrameLayout>