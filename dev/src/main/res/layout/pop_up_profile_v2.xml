<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_scan"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_feat_24_1a1a1a_scan_code"
                android:drawablePadding="@dimen/dp_10"
                android:padding="@dimen/dp_16"
                android:text="@string/mgs_game_qr_scan"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/v_scan_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_scan"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_scan" />

            <View
                android:id="@+id/v_divider_1"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginStart="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_scan" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_edit"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_profile_edit_v2"
                android:drawablePadding="@dimen/dp_10"
                android:padding="@dimen/dp_16"
                android:text="@string/edit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_1" />

            <View
                android:id="@+id/v_edit_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_edit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_edit" />

            <View
                android:id="@+id/v_divider_2"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginStart="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_edit" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_settings"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_profile_setting"
                android:drawablePadding="@dimen/dp_10"
                android:padding="@dimen/dp_16"
                android:text="@string/settings"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_2" />

            <View
                android:id="@+id/v_settings_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_settings"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_settings" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>