<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_5">

    <TextView
        android:id="@+id/tvSelectItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#B2DFDB"
        android:gravity="center"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Action Name" />

</androidx.constraintlayout.widget.ConstraintLayout>