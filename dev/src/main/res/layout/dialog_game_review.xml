<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_38">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_common_simple_dialog"
        android:paddingBottom="@dimen/dp_26">

        <ImageView
            android:id="@+id/ivGameReviewIcon"
            android:layout_width="@dimen/dp_67"
            android:layout_height="@dimen/dp_67"
            android:layout_marginTop="@dimen/dp_26"
            android:src="@drawable/placeholder_corner_18"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameReviewDesc"
            style="@style/MetaTextView.S16.PoppinsBlack900.CenterTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:paddingHorizontal="@dimen/dp_24"
            android:text="@string/game_review_dialog_desc"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivGameReviewIcon" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameReviewSkip"
            style="@style/Button.S15.PoppinsBold700.Cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_31"
            android:layout_marginTop="@dimen/dp_39"
            android:paddingVertical="@dimen/dp_9"
            android:text="@string/dialog_skip"
            android:textColor="@color/common_dialog_cancel_text"
            app:layout_constraintEnd_toStartOf="@id/viewLocationSpace"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGameReviewDesc" />

        <View
            android:id="@+id/viewLocationSpace"
            android:layout_width="@dimen/dp_12"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/tvGameReviewSkip"
            app:layout_constraintEnd_toStartOf="@id/tvGameReviewRate"
            app:layout_constraintStart_toEndOf="@id/tvGameReviewSkip"
            app:layout_constraintTop_toBottomOf="@id/tvGameReviewDesc" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameReviewRate"
            style="@style/Button.S15.PoppinsBlack900"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_39"
            android:layout_marginRight="@dimen/dp_31"
            android:background="@drawable/bg_common_dialog_confirm"
            android:text="@string/rate"
            android:textColor="@color/common_dialog_confirm_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/viewLocationSpace"
            app:layout_constraintTop_toBottomOf="@id/tvGameReviewDesc" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
