<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.lihang.ShadowLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:clickable="false"
        android:focusable="false"
        app:clickable="false"
        app:hl_cornerRadius="@dimen/dp_20"
        app:hl_shadowColor="#17000000"
        app:hl_shadowLimit="8dp"
        app:hl_shadowOffsetY="1dp"
        app:hl_shadowSymmetry="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.lihang.ShadowLayout
                android:id="@+id/fl_item_box_shadow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:clickable="false"
                android:focusable="false"
                app:clickable="false"
                app:hl_cornerRadius="@dimen/dp_10"
                app:hl_shadowColor="#21000000"
                app:hl_shadowHiddenTop="true"
                app:hl_shadowLimit="8dp"
                app:hl_shadowOffsetY="4dp"
                app:hl_shadowSymmetry="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/iv_icon"
                    android:layout_width="@dimen/dp_64"
                    android:layout_height="@dimen/dp_64"
                    android:scaleType="centerCrop"
                    app:shapeAppearance="@style/shapeRound10Style"
                    tools:src="#eee" />

            </com.lihang.ShadowLayout>


            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_name"
                style="@style/MetaTextView.S16.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:ellipsize="end"
                android:gravity="center_horizontal|center_vertical"
                android:maxWidth="@dimen/dp_200"
                android:maxLines="1"
                android:textColor="@color/neutral_color_2"
                app:layout_constraintEnd_toEndOf="@id/fl_item_box_shadow"
                app:layout_constraintStart_toStartOf="@id/fl_item_box_shadow"
                app:layout_constraintTop_toBottomOf="@id/fl_item_box_shadow"
                app:uiLineHeight="@dimen/sp_22"
                tools:text="Change beding for all cows" />

            <LinearLayout
                android:id="@+id/llUserInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="@id/tv_name"
                app:layout_constraintStart_toStartOf="@id/tv_name"
                app:layout_constraintTop_toBottomOf="@id/tv_name">

                <TextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:text="@string/editor_by"
                    android:textColor="@color/color_757575"
                    app:layout_constraintStart_toStartOf="@id/clBottomDesc"
                    app:layout_constraintTop_toTopOf="@id/clBottomDesc" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/iv_user_avatar"
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:scaleType="centerCrop"
                    app:shapeAppearance="@style/circleStyle"
                    tools:src="#eee" />

                <TextView
                    android:id="@+id/tv_user_name"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:maxLines="1"
                    android:textColor="@color/color_757575"
                    app:layout_constraintStart_toStartOf="@id/clBottomDesc"
                    app:layout_constraintTop_toTopOf="@id/clBottomDesc" />

            </LinearLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clBottomDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_16"
                android:background="@drawable/bg_suggested_for_you_vp_radius_fff9f2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llUserInfo">

                <TextView
                    android:id="@+id/tv_editor_text"
                    style="@style/MetaTextView.S14.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_12"
                    android:text="@string/editor_choice"
                    android:textColor="@color/color_C2952E"
                    app:layout_constraintStart_toStartOf="@id/clBottomDesc"
                    app:layout_constraintTop_toTopOf="@id/clBottomDesc" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:layout_marginEnd="@dimen/dp_12"
                    android:src="@drawable/bg_suggestion_yinhao"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/viewLine"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:layout_marginHorizontal="@dimen/dp_12"
                    android:layout_marginVertical="@dimen/dp_8"
                    android:background="@color/color_FDEAD4"
                    app:layout_constraintTop_toBottomOf="@id/tv_editor_text" />

                <TextView
                    android:id="@+id/tv_desc"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_8"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:lines="4"
                    android:maxLines="4"
                    android:ellipsize="end"
                    android:textColor="@color/color_C2952E"
                    app:layout_constraintBottom_toBottomOf="@id/clBottomDesc"
                    app:layout_constraintEnd_toEndOf="@id/clBottomDesc"
                    app:layout_constraintStart_toStartOf="@id/clBottomDesc"
                    app:layout_constraintTop_toBottomOf="@id/viewLine" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.lihang.ShadowLayout>

</FrameLayout>