<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_8"
        android:textColor="@color/color_080D2D_50"
        android:textSize="@dimen/sp_14"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="aaaaaa" />

</androidx.constraintlayout.widget.ConstraintLayout>