<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#EEE">

    <FrameLayout
        android:id="@+id/fragment_video_feed_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl_title_bar"
        android:layout_width="match_parent"
        app:isDividerVisible="false"
        android:layout_height="wrap_content"
        app:back_icon_tint="@color/white"
        app:background_color="@color/transparent"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder" />

</androidx.constraintlayout.widget.ConstraintLayout>
