<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:minHeight="@dimen/dp_50">

    <View
        android:id="@+id/vFriendIndicator"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_9"
        android:layout_marginLeft="@dimen/dp_9"
        android:layout_marginRight="@dimen/dp_12"
        android:background="@drawable/indicator_friend_tab"
        android:translationY="@dimen/dp_3"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tabTextView"
        app:layout_constraintLeft_toLeftOf="@+id/tabTextView"
        app:layout_constraintRight_toRightOf="@+id/tabTextView"
        app:layout_constraintTop_toTopOf="@id/tabTextView" />

    <TextView
        android:id="@+id/tabTextView"
        style="@style/MetaTextView.S24.PoppinsBlack900"
        android:textColor="@color/selector_tab_text_color"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Message" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUnReadCount"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_6"
        android:background="@drawable/bg_red_corner_360"
        android:translationY="@dimen/dp_6"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tabTextView" />
</androidx.constraintlayout.widget.ConstraintLayout>
