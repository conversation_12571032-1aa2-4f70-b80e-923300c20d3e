<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_robux"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginRight="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_20"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_robux_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/icon_robux_tips_bg"
            android:fontFamily="@font/poppins_semi_bold_600"
            android:text="@string/robux_tips"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_10"
            android:visibility="gone"
            app:layout_constraintRight_toLeftOf="@+id/cl_robux_tip"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_robux_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/icon_robux_record"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/img_robux_tip"
                android:layout_width="54dp"
                android:layout_height="50dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_robux_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_3"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:gravity="bottom"
                android:includeFontPadding="false"
                android:paddingHorizontal="5dp"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_8"
                app:layout_constraintBottom_toBottomOf="@+id/img_robux_tip"
                app:layout_constraintLeft_toLeftOf="@+id/img_robux_tip"
                app:layout_constraintRight_toRightOf="@+id/img_robux_tip"
                tools:text="00:00:00" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/flAnim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="70dp"
        android:layout_marginTop="20dp">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieAnimationView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_progress="1"
            app:lottie_fileName="main_bottom_demo_icon.zip" />
    </FrameLayout>


</FrameLayout>