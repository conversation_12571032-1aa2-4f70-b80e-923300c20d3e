<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_44"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_normal"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:maxLines="1"
        android:paddingTop="@dimen/dp_15"
        android:singleLine="true"
        android:textColor="@color/textColorPrimaryLight"
        tools:text="Add Friend" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_selected"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:maxLines="1"
        android:paddingTop="@dimen/dp_15"
        android:singleLine="true"
        android:textColor="@color/textColorPrimary"
        android:visibility="invisible"
        tools:text="Add Friend"
        tools:visibility="visible" />

</FrameLayout>




