<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@drawable/window_background"
    android:layout_height="match_parent">


    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/v_status_bar_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        app:back_icon="@drawable/icon_back_left_arrow"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/v_status_bar_placeholder"
        app:title_text="Review Game"
        app:title_text_color="@color/colorAccent" />

    <EditText
        android:id="@+id/et_game_id"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="#004D40"
        android:gravity="center"
        android:hint="@string/debug_input_review_id"
        android:textColor="#CCFFFFFF"
        android:textColorHint="#CCAAAAAA"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/btn_search_game"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />

    <TextView
        android:id="@+id/btn_search_game"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="10dp"
        android:background="#EF6C00"
        android:gravity="center"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        android:text="@string/debug_search_now"
        android:textColor="#CCFFFFFF"
        app:layout_constraintBottom_toBottomOf="@id/et_game_id"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_game_id" />

    <ImageView
        android:id="@+id/ivGameDetailGameIcon"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_game_id" />

    <TextView
        android:id="@+id/tvGameDetailGameName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_11"
        android:layout_marginEnd="@dimen/dp_22"
        android:ellipsize="end"
        android:maxLines="3"
        android:textColor="@color/color_game_detail_text"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/ivGameDetailGameIcon"
        app:layout_constraintLeft_toRightOf="@+id/ivGameDetailGameIcon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivGameDetailGameIcon"
        tools:text="Extreme Car Driving hhhhhhhh hhhh" />

    <TextView
        android:id="@+id/tvPackageName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_12"
        android:textColor="@color/color_game_detail_text"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/ivGameDetailGameIcon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivGameDetailGameIcon"
        tools:text="Extreme Car Driving hhhhhhhh hhhh" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/infoContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvPackageName">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/vesionTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:paddingStart="@dimen/dp_12"
                android:text="@string/debug_version_info"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_23"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp_12"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vesionTitle"
                tools:listitem="@layout/adapter_developer_review_game_version" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>


    <androidx.constraintlayout.widget.Group
        android:id="@+id/infoGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvGameDetailGameName,ivGameDetailGameIcon,infoContainer"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_game_id" />

</androidx.constraintlayout.widget.ConstraintLayout>