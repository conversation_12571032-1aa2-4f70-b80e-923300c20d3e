<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#80000000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_42"
        android:background="@drawable/bg_common_simple_dialog"
        android:clickable="true"
        android:paddingHorizontal="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_marginTop="@dimen/dp_17"
            android:src="@drawable/icon_quit_game"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Space
            android:id="@+id/spaceTitleContent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_15"
            app:layout_constraintBottom_toTopOf="@id/content"
            app:layout_constraintTop_toBottomOf="@id/iv" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/content"
            style="@style/MetaTextView.S16.PoppinsRegular400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/dp_2"
            android:textColor="#1A1A1A"
            app:layout_constraintTop_toBottomOf="@id/spaceTitleContent"
            tools:text="content ent content content content content content content content content content " />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btnConfirm"
            style="@style/Button.S16.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/dialog_confirm"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/content"
            app:layout_goneMarginTop="0dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>