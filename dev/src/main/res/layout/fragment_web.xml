<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusPlacedHolder"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/statusPlacedHolder"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:isDividerVisible="false"
        app:title_text_color="@color/textColorPrimary" />

    <FrameLayout
        android:id="@+id/flWeb"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/v_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar"
        android:visibility="visible" />
</RelativeLayout>