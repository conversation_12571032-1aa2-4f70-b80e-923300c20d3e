<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivImage"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_4"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/shapeRound10Style" />

    <ImageView
        android:id="@+id/ivVideoSign"
        android:layout_width="@dimen/dp_29"
        android:layout_height="@dimen/dp_29"
        android:src="@drawable/icon_fullscreen_videoplayer_big_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivImage"
        app:layout_constraintEnd_toEndOf="@id/ivImage"
        app:layout_constraintStart_toStartOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="@id/ivImage"
        app:tint="@color/neutral_color_1" />

    <ImageView
        android:id="@+id/ivDelete"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="-8dp"
        android:layout_marginEnd="-8dp"
        android:background="@drawable/bg_f0f2f4_100"
        android:padding="@dimen/dp_5"
        android:src="@drawable/icon_report_delete"
        app:layout_constraintEnd_toEndOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="@id/ivImage"
        app:tint="@color/neutral_color_1" />

    <ImageView
        android:id="@+id/ivAdd"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_4"
        android:background="@drawable/bg_profile_information_et"
        android:padding="@dimen/dp_13"
        android:src="@drawable/icon_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>