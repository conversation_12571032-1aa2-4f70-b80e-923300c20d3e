<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    tools:background="@color/black"
    android:layout_height="match_parent"
    android:gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_30"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_camera_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_margin="24dp"
            android:gravity="center"
            android:text="@string/mic_permissions"
            android:textColor="#080D2D"
            android:textSize="16sp"
            android:fontFamily="@font/poppins_bold_700"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tv_mic_confirm_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_mic_confirm_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:background="@drawable/bg_common_dialog_confirm"
            android:gravity="center"
            android:fontFamily="@font/poppins_black_900"
            android:paddingVertical="14dp"
            android:text="@string/mic_permissions_open"
            android:textColor="#000000"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/tv_mic_cancel_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_mic_cancel_btn"
            app:layout_constraintTop_toBottomOf="@+id/tv_camera_title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_mic_cancel_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/mic_cancel"
            android:textSize="16sp"
            android:fontFamily="@font/poppins_regular_400"
            android:padding="12dp"
            android:textColor="@color/textColorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
