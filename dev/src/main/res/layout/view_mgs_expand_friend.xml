<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/color_333333"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMgsFriendTitle"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:paddingStart="@dimen/dp_13"
        android:gravity="center"
        android:textSize="@dimen/sp_12"
        android:text="@string/friend_tab_friend"
        android:textColor="@color/white_60" />

    <View
        android:id="@+id/vMgsFriendLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="@dimen/dp_13"
        android:layout_below="@id/tvMgsFriendTitle"
        android:background="@color/white_10" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMgsFriend"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/includeMgsShare"
        android:layout_below="@id/vMgsFriendLine"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_13"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <LinearLayout
        android:id="@+id/llMgsFriendDefaultPage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivMgsFriendDefaultPage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/mgs_not_login" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMgsFriendDefaultPage"
            style="@style/MetaTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_18"
            android:gravity="center"
            android:text="@string/mgs_login_to_invite"
            android:textColor="@color/white_70" />
    </LinearLayout>

    <include
        android:id="@+id/includeMgsShare"
        layout="@layout/mgs_include_share_way"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:visibility="visible"
        android:layout_alignParentBottom="true" />
</RelativeLayout>