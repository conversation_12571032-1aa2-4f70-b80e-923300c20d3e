<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_16">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S18.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/hot_topics" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMore"
        style="@style/MetaTextView.S15.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:drawableEnd="@drawable/icon_arrow_right"
        android:drawablePadding="@dimen/dp_10"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_8"
        android:text="@string/see_all"
        android:textColor="@color/neutral_color_3"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>