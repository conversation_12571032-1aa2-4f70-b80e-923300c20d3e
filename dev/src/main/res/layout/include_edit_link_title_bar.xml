<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/ib_back"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:src="@drawable/icon_back_array_bold_black"
        android:paddingHorizontal="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S16.PoppinsBold700.TitleBar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="50dp"
        android:text="@string/profile_link_url"
        app:layout_constraintBottom_toBottomOf="@id/ib_back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ib_back"
        app:layout_constraintTop_toTopOf="@id/ib_back" />


    <TextView
        android:id="@+id/tv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/profile_link_edit"
        android:textColor="@color/colorPrimaryDark"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/dp_20" />

</androidx.constraintlayout.widget.ConstraintLayout>