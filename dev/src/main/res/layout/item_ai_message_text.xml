<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:minHeight="@dimen/dp_40"
    android:layout_height="wrap_content"
    android:gravity="center">
    <LinearLayout
        android:layout_width="wrap_content"
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_height="wrap_content">
        <LinearLayout
            android:id="@+id/rl_root"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp_40"
            android:background="@drawable/bg_55779b_corner_12"
            android:paddingHorizontal="@dimen/dp_14"
            android:paddingVertical="10dp">

            <TextView
                android:id="@+id/tv_text"
                style="@style/FolderTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="left"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="*Mama* 22222222"
                tools:visibility="gone" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lav_loading"
                android:layout_width="@dimen/dp_76"
                android:layout_height="@dimen/dp_8"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginBottom="@dimen/dp_6"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="@id/tv_text"
                app:layout_constraintTop_toTopOf="@id/tv_text"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                tools:lottie_fileName="ai_assist_chat_dot_3.zip"
                tools:visibility="visible" />
        </LinearLayout>
        <include
            android:id="@+id/root_rebuild"
            layout="@layout/view_ai_bot_rebuild" />
    </LinearLayout>


</LinearLayout>