<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/color_333333">

    <RelativeLayout
        android:id="@+id/llMgsRoomPlayerNum"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:orientation="vertical">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMgsRoomPlayerNum"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_13"
            android:text="@string/mgs_room_member_num"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_12" />

        <View
            android:id="@+id/vMgsRoomLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/dp_13"
            android:background="@color/white_10" />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMgsRoomUser"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/llMgsRoomPlayerNum"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingTop="@dimen/dp_4"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
</RelativeLayout>