<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDialogRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_suggested_for_you">


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_24"
            android:text="@string/suggested_for_you"
            android:textColor="@color/textColorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/close_icon"
            android:padding="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_40"
            tools:listitem="@layout/adapter_game_suggestion_item"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <View
            android:id="@+id/v_mask"
            android:background="@drawable/bg_suggest_list_mask"
            app:layout_constraintStart_toStartOf="@id/rv_list"
            app:layout_constraintEnd_toEndOf="@id/rv_list"
            app:layout_constraintTop_toTopOf="@id/rv_list"
            app:layout_constraintBottom_toBottomOf="@id/rv_list"
            android:layout_width="0dp"
            android:layout_height="0dp"/>

        <com.zhpan.indicator.IndicatorView
            android:id="@+id/indicator"
            app:layout_constraintTop_toBottomOf="@id/rv_list"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_height="wrap_content"/>

        <View
            app:layout_constraintTop_toTopOf="@id/rv_list"
            app:layout_constraintBottom_toBottomOf="@id/rv_list"
            app:layout_constraintStart_toStartOf="@id/rv_list"
            app:layout_constraintEnd_toEndOf="@id/rv_list"
            android:visibility="gone"
            android:layout_width="@dimen/dp_1"
            android:layout_height="0dp"/>


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_enter"
            style="@style/Button.S16.PoppinsBold600.Height48"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:text="@string/enter_suggested_game"
            android:textAllCaps="false"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dp_30"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/indicator" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/includeAnim"
        android:visibility="gone"
        layout="@layout/include_view_trending_insert" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAnimIcon"
        style="@style/SuggestedGameIcon"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <include
        android:id="@+id/includeGuidance"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        layout="@layout/view_trending_insert_guidance" />
</FrameLayout>