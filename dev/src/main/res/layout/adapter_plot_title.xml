<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_25"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_26"
        android:layout_height="@dimen/dp_26"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        tools:src="@drawable/plot_short_video_icon" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S16.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:gravity="center"
        android:textColor="@color/color_17191C"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:text="@string/app_name" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_more"
        style="@style/MetaTextView.S13"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:drawablePadding="@dimen/dp_6"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_16"
        android:text="@string/global_share_system_share"
        android:textColor="@color/black_60"
        app:drawableEndCompat="@drawable/icon_more_arrow"
        app:drawableTint="@color/black_60"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>