<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/im_conversation_item_list_selector"
    android:paddingTop="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_12">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/img_icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/icon_default_avatar" />

    <ImageView
        android:id="@+id/ivContentImage"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginRight="16dp"
        android:src="@drawable/placeholder_corner_8"
        app:layout_constraintBottom_toBottomOf="@id/img_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_icon" />

    <TextView
        android:id="@+id/tvContentImage"
        android:layout_width="60dp"
        android:layout_height="54dp"
        android:layout_marginRight="16dp"
        android:ellipsize="end"
        android:maxLines="3"
        android:textColor="#999999"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/img_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_icon"
        tools:text="like your ireview is savannsdfihfisduhfihssafasfasf……" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="-2dp"
        android:layout_marginRight="80dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toRightOf="@id/img_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="wrap"
        tools:text="Savannah Nguyen" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginRight="80dp"
        android:ellipsize="end"
        android:gravity="left"
        android:maxLines="1"
        android:textColor="#666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="like your review sdfhasidufhsdhf df sudihfisuadhf uishdf uiadhsf uisdhf uisdhf uihdf ihuafhsdifh " />


    <TextView
        android:id="@+id/tv_time"
        style="@style/MetaTextView.S11.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="80dp"
        android:ellipsize="end"
        android:gravity="left"
        android:maxLines="1"
        android:textColor="#B3B3B3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        tools:text="19mins ago" />
</androidx.constraintlayout.widget.ConstraintLayout>