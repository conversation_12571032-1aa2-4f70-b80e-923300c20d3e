<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_close"
        app:back_icon_tint="@color/black"
        app:isDividerVisible="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/report_user_cap" />

    <com.socialplay.gpark.ui.view.FocusableScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:focusAt="@id/clContent"
        app:layout_constraintBottom_toTopOf="@id/vBottom"
        app:layout_constraintTop_toBottomOf="@id/title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clReportUser"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvTitleReportPlayer"
                style="@style/MetaTextView.S14.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/report_user_name"
                android:textColor="@color/neutral_color_3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvUserName"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:ellipsize="end"
                android:gravity="end|center_vertical"
                android:maxLines="1"
                android:singleLine="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@id/tvTitleReportPlayer"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="xxxxxxxxxxxxx" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOption"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitleReportPlayer"
                app:spanCount="1"
                tools:itemCount="10"
                tools:listitem="@layout/item_report_option" />

            <EditText
                android:id="@+id/etReasonDesc"
                style="@style/EditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@drawable/bg_game_review_edit"
                android:gravity="top|start"
                android:hint="@string/report_count_limit"
                android:maxLength="50"
                android:minHeight="@dimen/dp_80"
                android:paddingHorizontal="@dimen/dp_12"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/rvOption"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                android:overScrollMode="never"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_11"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/etReasonDesc"
                app:spanCount="4" />

            <Space
                android:id="@+id/spaceBottom"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_92"
                app:layout_constraintTop_toBottomOf="@id/rvImage" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.socialplay.gpark.ui.view.FocusableScrollView>

    <View
        android:id="@+id/vBottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-8dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvReport" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvReport"
        style="@style/Button.S16.PoppinsBlack900"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_36"
        android:alpha="0.5"
        android:minHeight="@dimen/dp_48"
        android:text="@string/report"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

</androidx.constraintlayout.widget.ConstraintLayout>