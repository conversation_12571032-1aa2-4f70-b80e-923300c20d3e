<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/home_friends_empty_bg"
    android:padding="@dimen/dp_16">

    <!-- 标题文字 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/friend_room_empty_title"
        android:textColor="#1A1A1A"
        android:gravity="start"
        app:layout_constraintEnd_toStartOf="@+id/ll_avatars"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6" />

    <!-- 头像容器 -->
    <LinearLayout
        android:id="@+id/ll_avatars"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginStart="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 黄色头像 -->
        <FrameLayout
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_8">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_friend_avatar_yellow" />

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="@dimen/dp_2"
                android:layout_marginBottom="@dimen/dp_2"
                android:background="@drawable/bg_friend_avatar_yellow"
                android:src="@drawable/ic_add_friend_plus"
                android:scaleType="center"
                android:padding="@dimen/dp_2" />
        </FrameLayout>

        <!-- 紫色头像 -->
        <FrameLayout
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_8">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_friend_avatar_purple" />

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="@dimen/dp_2"
                android:layout_marginBottom="@dimen/dp_2"
                android:background="@drawable/bg_friend_avatar_purple"
                android:src="@drawable/ic_add_friend_plus"
                android:scaleType="center"
                android:padding="@dimen/dp_2" />
        </FrameLayout>

        <!-- 粉色头像 -->
        <FrameLayout
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_8">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_friend_avatar_pink" />

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="@dimen/dp_2"
                android:layout_marginBottom="@dimen/dp_2"
                android:background="@drawable/bg_friend_avatar_pink"
                android:src="@drawable/ic_add_friend_plus"
                android:scaleType="center"
                android:padding="@dimen/dp_2" />
        </FrameLayout>

        <!-- 红色头像 -->
        <FrameLayout
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_friend_avatar_red" />

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="@dimen/dp_2"
                android:layout_marginBottom="@dimen/dp_2"
                android:background="@drawable/bg_friend_avatar_red"
                android:src="@drawable/ic_add_friend_plus"
                android:scaleType="center"
                android:padding="@dimen/dp_2" />
        </FrameLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
