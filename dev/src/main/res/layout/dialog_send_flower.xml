<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dialogRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/color_999999">
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />
    <Space
        android:id="@+id/spaceTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintTop_toBottomOf="@id/statusBar"/>

    <View
        android:id="@+id/viewTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/dialogLayout"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/dialogLayout">

        <include
            android:id="@+id/flow2"
            layout="@layout/item_flow_send_flowers"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="invisible"
            tools:visibility="visible" />

        <include
            android:id="@+id/flow1"
            layout="@layout/item_flow_send_flowers"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="invisible"
            tools:visibility="visible" />

        <include
            android:id="@+id/flow0"
            layout="@layout/item_flow_send_flowers"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="invisible"
            tools:visibility="visible" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialogLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_top_round_16"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/topLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_29"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/send_flower_dialog_title"
            app:layout_constraintBottom_toBottomOf="@id/topLine"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/topLine" />

        <!-- 由于字体渲染原因 tvTitle 底部的空白区域比顶部的空白区域更大, 导致后面的图标看起来偏下, 所以手动向上偏移2个dp -->
        <ImageView
            android:id="@+id/ivTitleHelpIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:src="@drawable/ic_send_flower_dialog_title_help"
            android:translationY="-2dp"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintStart_toEndOf="@id/tvTitle"
            app:layout_constraintTop_toTopOf="@id/tvTitle" />

        <View
            android:id="@+id/vTitleClick"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintEnd_toEndOf="@id/ivTitleHelpIcon"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toTopOf="@id/tvTitle" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvCoinsBalance"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@drawable/bg_ffffc2_16"
            android:drawableStart="@drawable/icon_g_coin_size_20"
            android:drawableEnd="@drawable/icon_send_flower_dialog_coins_add"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="@id/topLine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/topLine"
            tools:text="999" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvNoFlowersSendDesc"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:text="@string/send_flower_dialog_no_flowers_send_desc"
            android:textColor="@color/color_333333"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topLine"
            tools:visibility="gone" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rvGifts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/tvNoFlowersSendDesc"
            app:layout_constraintHeight_max="@dimen/dp_180"
            tools:layout_height="@dimen/dp_196"
            tools:listitem="@layout/item_send_flower_dialog" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_05"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@color/color_E6E6E6"
            app:layout_constraintTop_toBottomOf="@id/rvGifts" />

        <View
            android:id="@+id/bottomLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_37"
            android:layout_marginTop="@dimen/dp_16"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvRequires"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/icon_g_coin_size_20"
            android:drawablePadding="@dimen/dp_8"
            android:text="@string/send_flower_dialog_requires_coins"
            app:layout_constraintBottom_toBottomOf="@id/bottomLine"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/bottomLine" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvRequiresCoins"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="@id/bottomLine"
            app:layout_constraintStart_toEndOf="@id/tvRequires"
            app:layout_constraintTop_toTopOf="@id/bottomLine"
            tools:text="10" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerBtn"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@drawable/bg_ffef30_round_40"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/send_flower_dialog_btn_send_flower"
            app:layout_constraintBottom_toBottomOf="@id/bottomLine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/bottomLine" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout
        android:id="@+id/rechargeLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_gravity="center"
            android:background="@drawable/bg_80000000_round_19"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:id="@+id/rechargeLoadingIv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/icon_recharge_loading" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:text="@string/asset_product_buy_loading"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>