<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:background="@color/black_70">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:clickable="true"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_10">

        <EditText
            android:id="@+id/et"
            style="@style/EditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_f0f0f0_corner_20"
            android:inputType="textMultiLine"
            android:lineSpacingMultiplier="1.2"
            android:maxHeight="@dimen/dp_212"
            android:maxLength="500"
            android:paddingVertical="@dimen/dp_8"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:textSize="@dimen/sp_14" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_reply_btn"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:paddingHorizontal="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_7"
            android:text="@string/reply_cap"
            android:textColor="@color/color_4AB4FF"
            android:visibility="invisible"
            tools:visibility="visible" />

    </FrameLayout>

</FrameLayout>