<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_78"
    android:background="@drawable/bg_e6e6e6_round_14_stroke_05"
    android:paddingHorizontal="@dimen/dp_16">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemTitle"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_16"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvItemDate"
        app:layout_constraintEnd_toStartOf="@id/tvItemCoinsCount"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Recharge" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemDate"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/tvItemTitle"
        app:layout_constraintTop_toBottomOf="@id/tvItemTitle"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Nov 12, 2022 14:22:00 AM" />

    <ImageView
        android:id="@+id/ivItemCoin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_transaction_details_coin"
        app:layout_constraintBottom_toTopOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsCount"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@id/ivItemCoin"
        app:layout_constraintEnd_toStartOf="@id/ivItemCoin"
        app:layout_constraintTop_toTopOf="@id/ivItemCoin"
        tools:text="+99" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsBaseCount"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_194ab4ff_round_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_4AB4FF"
        android:textSize="@dimen/sp_10"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivItemCoinsAdd"
        app:layout_constraintTop_toBottomOf="@id/ivItemCoin"
        tools:text="9,980"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivItemCoinsAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@drawable/ic_text_add_symbol"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintStart_toEndOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintTop_toTopOf="@id/tvItemCoinsBaseCount"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsAwardCount"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_199242ff_round_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_9242FF"
        android:textSize="@dimen/sp_10"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="@id/ivItemCoin"
        app:layout_constraintStart_toEndOf="@id/ivItemCoinsAdd"
        app:layout_constraintTop_toBottomOf="@id/ivItemCoin"
        tools:text="Bonus 300"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>