<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/smallCardRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@mipmap/ic_launcher" />

    <View
        android:id="@+id/iv_bottom_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_34"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_home_item_gradient_16"
        app:layout_constraintBottom_toBottomOf="@+id/iv_game_icon" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv_creator_avatar"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginLeft="@dimen/dp_6"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="@dimen/dp_6"
        android:padding="@dimen/dp_1"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/iv_bottom_bg"
        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_creator_nickname"
        style="@style/MetaTextView.S12.PoppinsRegular400.White"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_4"
        android:layout_marginRight="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:singleLine="true"
        android:textColor="#666"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/siv_creator_avatar"
        app:layout_constraintLeft_toRightOf="@+id/siv_creator_avatar"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/siv_creator_avatar"
        tools:text="nickname"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="left|top"
        android:lines="1"
        android:maxLines="1"
        android:minLines="1"
        android:paddingHorizontal="@dimen/dp_3"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@+id/iv_game_icon"
        tools:text="斗罗大陆里边有个兔子" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_3"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/textColorSecondary"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tv_game_title"
        tools:text="S10赛季奥特曼大战金刚" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_zan"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginBottom="9.5dp"
        android:drawableStart="@drawable/icon_zan"
        android:drawablePadding="@dimen/dp_6"
        android:drawableTint="@color/white"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="2380" />

</androidx.constraintlayout.widget.ConstraintLayout>