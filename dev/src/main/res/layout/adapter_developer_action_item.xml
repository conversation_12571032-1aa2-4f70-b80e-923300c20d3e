<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_5">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_45"
        android:gravity="center"
        android:textColor="#000"
        android:textSize="@dimen/sp_15"
        android:background="@drawable/placeholder_corner_10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Action Name" />

</androidx.constraintlayout.widget.ConstraintLayout>