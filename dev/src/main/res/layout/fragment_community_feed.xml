<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.WrapNestedScrollableHostLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <View
        android:id="@+id/v_tab_bar_placeholder"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@null"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingHorizontal="@dimen/dp_7" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
                android:id="@+id/rvFeed"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loadingFeed"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

</com.socialplay.gpark.ui.view.WrapNestedScrollableHostLinearLayout>