<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_44"
    android:layout_height="@dimen/dp_34"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center">

    <com.socialplay.gpark.ui.view.voice.WaveView
        android:id="@+id/waveVoice"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_centerInParent="true"
        android:gravity="center" />

    <ImageView
        android:id="@+id/ivVoiceState"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:scaleType="centerCrop"
        android:layout_centerInParent="true"
        android:src="@drawable/icon_voice_close" />

</RelativeLayout>
