<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarPlaceholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/cl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S18.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/kol_bottom_tab"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/ivEntrance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="@dimen/dp_16" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_gravity="bottom"
            android:background="@color/neutral_color_10" />
    </FrameLayout>

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/cl_title_bar">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
                android:id="@+id/rvCreate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:overScrollMode="never"
                android:paddingBottom="80dp" />

            <include
                android:id="@+id/includeUgcLabelTab"
                layout="@layout/provider_creator_ugc_label_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top|center" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
</LinearLayout>