<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_84">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivMemberAvatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginStart="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_item_group_chat_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/shapeRound30Style" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMemberName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_14"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvMemberStatus"
        app:layout_constraintEnd_toStartOf="@id/layoutTag"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivMemberAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginEnd="@dimen/dp_16"
        tools:text="404 Brain Not Found---------------------------------" />

    <View
        android:id="@+id/ivMemberStatus"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_6"
        android:background="@drawable/sp_offline_dot"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvMemberStatus"
        app:layout_constraintStart_toStartOf="@id/tvMemberName"
        app:layout_constraintTop_toTopOf="@id/tvMemberStatus"
        tools:background="@drawable/sp_online_dot" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMemberStatus"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_2"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/offline_cap"
        android:textColor="@color/color_CCCCCC"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivMemberStatus"
        app:layout_constraintTop_toBottomOf="@id/tvMemberName"
        tools:text="@string/online_status"
        tools:textColor="@color/color_2DD128" />

    <FrameLayout
        android:id="@+id/layoutTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvMemberName"
        app:layout_constraintEnd_toStartOf="@id/ivMore"
        app:layout_constraintStart_toEndOf="@id/tvMemberName"
        app:layout_constraintTop_toTopOf="@id/tvMemberName"
        app:layout_goneMarginEnd="@dimen/dp_16"
        tools:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTagOwner"
            style="@style/MetaTextView.S10.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_17"
            android:background="@drawable/bg_ffece9_round_19"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp_6"
            android:text="@string/item_group_member_tag_owner"
            android:textColor="@color/color_FF5F42"
            android:textSize="@dimen/sp_10" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTagAdmin"
            style="@style/MetaTextView.S10.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_17"
            android:background="@drawable/bg_dff2ff_round_19"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp_6"
            android:text="@string/item_group_member_tag_admin"
            android:textColor="@color/color_4AB4FF"
            android:textSize="@dimen/sp_10"
            android:visibility="gone" />
    </FrameLayout>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:padding="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_more_vertical_3_points_1a1a1a"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>