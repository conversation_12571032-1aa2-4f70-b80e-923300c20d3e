<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/ml_content_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutDescription="@xml/fragment_friend_search_scene"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/eTSearch"
            style="@style/EditText.Single.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:background="@drawable/shape_f6f6f6_round"
            android:cursorVisible="true"
            android:drawableStart="@drawable/icon_search_friends"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="@string/post_parties_search_placeholder"
            android:imeOptions="actionSearch"
            android:minHeight="@dimen/dp_44"
            android:paddingVertical="@dimen/dp_8"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_32"
            android:singleLine="true"
            app:layout_constraintEnd_toStartOf="@+id/tvSearch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_16" />

        <ImageView
            android:id="@+id/ivClearSearch"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_10"
            android:src="@drawable/icon_search_clear_input"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/eTSearch"
            app:layout_constraintEnd_toEndOf="@+id/eTSearch"
            app:layout_constraintTop_toTopOf="@+id/eTSearch"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSearch"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/search"
            android:textColor="@color/selector_enable_text"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/eTSearch"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/eTSearch"
            app:layout_constraintTop_toTopOf="@+id/eTSearch"
            tools:visibility="visible" />
    </androidx.constraintlayout.motion.widget.MotionLayout>

    <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ml_content_container" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ml_content_container" />

</androidx.constraintlayout.widget.ConstraintLayout>