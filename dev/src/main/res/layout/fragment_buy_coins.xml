<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintTop_toBottomOf="@id/statusBar">

        <ImageView
            android:id="@+id/ivBackBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:padding="@dimen/dp_8"
            android:src="@drawable/ic_close_page"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S18.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/buy_coins_page_title_bar_title"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivReportBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:padding="@dimen/dp_8"
            android:src="@drawable/icon_buy_coins_report"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/bgBalanceCard"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_178"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                app:layout_constraintTop_toTopOf="parent">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_100"
                    android:layout_marginEnd="@dimen/dp_127"
                    android:background="@color/color_A993FF" />

                <ImageView
                    android:layout_width="@dimen/dp_114"
                    android:layout_height="match_parent"
                    android:layout_gravity="start"
                    android:src="@drawable/bg_wallet_balance_card_1" />

                <ImageView
                    android:layout_width="@dimen/dp_127"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:src="@drawable/bg_wallet_balance_card_2" />
            </FrameLayout>

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvBalanceCardTitle"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_27"
                android:layout_marginEnd="@dimen/dp_8"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/buy_coins_page_card_title"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintStart_toStartOf="@id/bgBalanceCard"
                app:layout_constraintTop_toTopOf="@id/bgBalanceCard" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardBalance"
                style="@style/MetaTextView.S18.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6"
                android:drawablePadding="@dimen/dp_6"
                android:textColor="@color/white"
                app:drawableStartCompat="@drawable/icon_buy_coins_g_white"
                app:layout_constraintStart_toStartOf="@id/tvBalanceCardTitle"
                app:layout_constraintTop_toBottomOf="@id/tvBalanceCardTitle"
                tools:text="198" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardDetails"
                style="@style/MetaTextView.S12.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_24"
                android:background="@drawable/bg_white_24"
                android:elevation="@dimen/dp_8"
                android:paddingHorizontal="@dimen/dp_8"
                android:paddingVertical="@dimen/dp_4"
                android:text="@string/buy_coins_page_card_details"
                android:textColor="@color/color_9242FF"
                app:layout_constraintBottom_toBottomOf="@id/tvBalanceCardTitle"
                app:layout_constraintEnd_toEndOf="@id/bgBalanceCard"
                app:layout_constraintTop_toTopOf="@id/tvBalanceCardTitle" />

            <View
                android:id="@+id/cardDivider"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_05"
                android:layout_marginHorizontal="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_94"
                android:background="@color/white_30"
                app:layout_constraintEnd_toEndOf="@id/bgBalanceCard"
                app:layout_constraintStart_toStartOf="@id/bgBalanceCard"
                app:layout_constraintTop_toTopOf="@id/bgBalanceCard" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardTodayEarningsTitle"
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/buy_coins_page_card_today_earnings_title"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/bgBalanceCard"
                app:layout_constraintTop_toBottomOf="@id/cardDivider" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardTodayEarnings"
                style="@style/MetaTextView.S14.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_4"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/tvCardTodayEarningsTitle"
                app:layout_constraintTop_toBottomOf="@id/tvCardTodayEarningsTitle"
                tools:text="+233" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/cardGuideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="@id/bgBalanceCard"
                app:layout_constraintGuide_percent="0.5"
                app:layout_constraintStart_toStartOf="@id/bgBalanceCard" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardTodayExpensesTitle"
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/buy_coins_page_card_today_expenses_title"
                android:textColor="@color/white"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="@id/cardGuideline"
                app:layout_constraintTop_toBottomOf="@id/cardDivider" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCardTodayExpenses"
                style="@style/MetaTextView.S14.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_4"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/tvCardTodayExpensesTitle"
                app:layout_constraintTop_toBottomOf="@id/tvCardTodayExpensesTitle"
                tools:text="-499" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutBuyCoinsTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_8"
                app:layout_constraintTop_toBottomOf="@id/bgBalanceCard">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBuyCoinsTitle"
                    style="@style/MetaTextView.S10.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/buy_coins_page_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toTopOf="@id/tvBuyCoinsMemberDesc"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="Buy G-Coins" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBuyCoinsMemberDesc"
                    style="@style/MetaTextView.S11.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_12"
                    android:visibility="gone"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@id/tvBuyCoinsTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvBuyCoinsTitle"
                    tools:text="You get 10% more as a Premium subscrilber"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvProducts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
                app:layout_constraintTop_toBottomOf="@id/layoutBuyCoinsTitle"
                tools:layout_height="@dimen/dp_320"
                tools:listitem="@layout/item_buy_coins" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvMoreEconomicalTitle"
                style="@style/MetaTextView.S10.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/buy_coins_page_member_more_economical"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rvProducts"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clSubscribe"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:background="@drawable/bg_buy_coins_subscribe"
                android:padding="@dimen/dp_12"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/tvMoreEconomicalTitle"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/ivSubscribe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_buy_coins_subscribe"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSubscribeTitle"
                    style="@style/MetaTextView.S10.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginEnd="@dimen/dp_12"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/buy_coins_page_subscribe_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toTopOf="@id/tvSubscribeDesc1"
                    app:layout_constraintStart_toEndOf="@id/ivSubscribe"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSubscribeDesc1"
                    style="@style/MetaTextView.S11.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_999999"
                    android:textSize="@dimen/sp_11"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toTopOf="@id/tvSubscribeDesc2"
                    app:layout_constraintEnd_toStartOf="@id/ivSubscribeArrow"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@id/tvSubscribeTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvSubscribeTitle"
                    tools:text="@string/buy_coins_page_subscribe_award_desc"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSubscribeDesc2"
                    style="@style/MetaTextView.S11.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_999999"
                    android:textSize="@dimen/sp_11"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivSubscribeArrow"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@id/tvSubscribeTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvSubscribeDesc1"
                    tools:text="@string/buy_coins_page_subscribe_award_desc2"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivSubscribeArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_buy_coins_subscribe_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvBottomDesc"
                style="@style/MetaTextView.S11.PoppinsRegular400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:gravity="center_horizontal"
                android:paddingHorizontal="@dimen/dp_16"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_11"
                app:layout_constraintBottom_toTopOf="@id/bottomSpace"
                app:layout_constraintTop_toBottomOf="@id/clSubscribe"
                tools:text="By purchasing G-Coins, you agree to our Terms of Services and Privacy Policy. You consent to the immediate performance of the agreement and waive your right of withdrawal." />

            <Space
                android:id="@+id/bottomSpace"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_26"
                app:layout_constraintTop_toBottomOf="@id/tvBottomDesc" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <FrameLayout
        android:id="@+id/rechargeLoading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_gravity="center"
            android:background="@drawable/bg_80000000_round_19"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:id="@+id/rechargeLoadingIv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/icon_recharge_loading" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:text="@string/iap_recharge_loading"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </FrameLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loadingView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clTitle"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>