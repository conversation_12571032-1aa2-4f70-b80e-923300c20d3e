<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView
    android:layout_width="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/dp_12"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:cardElevation="@dimen/dp_10">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white"
        android:paddingVertical="@dimen/dp_2">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_gender_all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6">

            <ImageView
                android:id="@+id/img_all"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_ai_bot_expand"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/ai_bot_gender_all"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/img_all"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_gender_female"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6">

            <ImageView
                android:id="@+id/img_female"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_gender_female"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/ai_bot_gender_female"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/img_female"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_gender_male"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6">

            <ImageView
                android:id="@+id/img_Male"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_gender_male"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/ai_bot_gender_male"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/img_Male"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_gender_non"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6">

            <ImageView
                android:id="@+id/img_Non"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_gender_non"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_4"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/ai_bot_gender_non"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/img_Non"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>
</androidx.cardview.widget.CardView>
