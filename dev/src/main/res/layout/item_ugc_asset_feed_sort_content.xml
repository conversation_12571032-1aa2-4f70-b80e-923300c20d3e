<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_42"
    android:layout_marginHorizontal="@dimen/dp_4"
    android:layout_marginVertical="@dimen/dp_6">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_normal"
        style="@style/MetaTextView.S14"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_f5f5f5_corner_4"
        android:gravity="center"
        android:minHeight="@dimen/dp_21"
        android:textColor="@color/color_757575"
        android:textSize="@dimen/dp_14"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="Module" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_selected"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_ffef30_round_4"
        android:gravity="center"
        android:minHeight="@dimen/dp_21"
        android:textSize="@dimen/dp_14"
        android:textStyle="bold"
        android:visibility="gone"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="Module" />

</FrameLayout>
