<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingEnd="@dimen/dp_6"
    tools:layout_height="@dimen/dp_120"
    tools:layout_width="@dimen/dp_110">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/round_corner_8dp" />

    <View
        android:id="@+id/v_style_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-18dp"
        android:background="@drawable/bg_gradient_ugc_room"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_bg"
        app:layout_constraintTop_toTopOf="@id/tv_style" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_style"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_8"
        android:lineSpacingMultiplier="1.2"
        android:maxLines="5"
        android:textColor="@color/white"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_type"
        app:layout_constraintVertical_bias="1"
        tools:text="What hell is going on out there my ?What hell is going on out there my hommie?" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_type"
        style="@style/MetaTextView.S10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_6"
        android:background="@drawable/bg_black_30_corner_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minHeight="@dimen/dp_15"
        android:paddingHorizontal="@dimen/dp_6"
        android:text="@string/unpublished_cap"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_10"
        android:visibility="invisible"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/iv_check"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_15" />

    <ImageView
        android:id="@+id/iv_check"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        android:padding="@dimen/dp_6"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>