<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f5f5f7"
    android:fitsSystemWindows="true">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_title" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent"
        app:title_text="@string/edit" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_reply_btn"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_ffef30_round_24"
        android:paddingHorizontal="@dimen/dp_18"
        android:paddingVertical="6.5dp"
        android:text="@string/post_cap"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />


    <ImageView
        android:id="@+id/iv_outfit"
        android:layout_width="@dimen/dp_172"
        android:layout_height="@dimen/dp_172"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        tools:src="@drawable/placeholder_corner" />

    <EditText
        android:id="@+id/et_title"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_40"
        android:background="@null"
        android:hint="@string/title_of_design"
        android:inputType="text|textNoSuggestions"
        android:maxLength="50"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_28"
        android:paddingBottom="@dimen/dp_12"
        android:singleLine="true"
        android:textColorHint="@color/neutral_color_4"
        app:layout_constraintTop_toBottomOf="@id/iv_outfit" />

    <EditText
        android:id="@+id/et"
        style="@style/MetaTextView.S14"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@null"
        android:gravity="top|start"
        android:hint="@string/write_design_idea"
        android:inputType="textMultiLine|textNoSuggestions"
        android:lineSpacingMultiplier="1.2"
        android:maxLength="500"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_12"
        android:textColorHint="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_title" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@color/color_E6E6E6"
        app:layout_constraintBottom_toTopOf="@id/et"
        app:layout_constraintTop_toBottomOf="@id/et_title" />

</androidx.constraintlayout.widget.ConstraintLayout>