<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/motionLayout"
        android:layout_width="@dimen/dp_33"
        android:layout_height="40dp"
        android:layout_marginLeft="10dp"
        app:layoutDescription="@xml/floating_ball_layout_scene"
        app:motionDebug="NO_DEBUG">

        <ImageView
            android:id="@+id/icon233"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:alpha="1"
            android:src="@drawable/floating_ball_normal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/quiteGame"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:alpha="0"
            android:background="@drawable/bg_floating_ball_quite_game"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iconQuiteGame"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginLeft="29dp"
                android:layout_marginTop="4dp"
                android:src="@drawable/floating_ball_exit_icon"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvQuite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:text="@string/quit"
                android:textColor="@color/color_333333"
                android:textSize="10sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="@id/iconQuiteGame"
                app:layout_constraintRight_toRightOf="@id/iconQuiteGame"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/setting"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:alpha="0"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_floating_ball_quite_game"
            app:layout_constraintTop_toBottomOf="@id/quiteGame"
            app:layout_constraintLeft_toLeftOf="parent"
            >

            <ImageView
                android:id="@+id/iconSetting"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginLeft="29dp"
                android:layout_marginTop="4dp"
                android:src="@drawable/icon_float_ball_setting"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.MetaTextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:text="@string/quit"
                android:textColor="@color/color_333333"
                android:textSize="10sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="@id/iconSetting"
                app:layout_constraintRight_toRightOf="@id/iconSetting"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.motion.widget.MotionLayout>
</FrameLayout>
