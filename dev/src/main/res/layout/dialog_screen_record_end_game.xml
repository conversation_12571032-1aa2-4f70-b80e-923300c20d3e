<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_parent_content"
        android:layout_width="@dimen/dp_314"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/bg_white_corner_20"
        android:paddingBottom="@dimen/dp_16">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_15"
            android:text="@string/recording"
            android:textColor="@color/color_1C1C1C"
            android:textSize="@dimen/sp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.function.record.SimpleVideoView
            android:id="@+id/simple_video_view"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_130"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginRight="@dimen/dp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />


        <TextView
            android:id="@+id/tv_share_to_video_plat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:gravity="center"
            android:text="@string/share"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_15"
            app:layout_constraintLeft_toLeftOf="@id/simple_video_view"
            app:layout_constraintRight_toRightOf="@id/simple_video_view"
            app:layout_constraintTop_toBottomOf="@id/simple_video_view" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_share"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:paddingLeft="@dimen/dp_14"
            android:paddingRight="@dimen/dp_14"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/tv_share_to_video_plat" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_official_ids"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/shape_f5f5f5_corner_10"
            android:padding="@dimen/dp_12"
            app:layout_constraintTop_toBottomOf="@id/rv_share">

            <TextView
                android:id="@+id/tv_title_discord"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/discord_with_colon"
                android:textColor="@color/color_1C1C1C"
                android:textSize="@dimen/sp_11"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_content_discord"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_2"
                android:text="@string/discord_with_colon"
                android:textColor="@color/color_3EAEFF"
                android:textSize="@dimen/sp_11"
                app:layout_constraintBottom_toBottomOf="@id/tv_title_discord"
                app:layout_constraintLeft_toRightOf="@id/tv_title_discord"
                app:layout_constraintTop_toTopOf="@id/tv_title_discord"
                tools:text="<EMAIL>" />

            <TextView
                android:id="@+id/tv_title_tiktok"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/tiktok_with_colon"
                android:textColor="@color/color_1C1C1C"
                android:textSize="@dimen/sp_11"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title_discord" />

            <TextView
                android:id="@+id/tv_content_tiktok"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_2"
                android:textColor="@color/color_3EAEFF"
                android:textSize="@dimen/sp_11"
                app:layout_constraintBottom_toBottomOf="@id/tv_title_tiktok"
                app:layout_constraintLeft_toRightOf="@id/tv_title_tiktok"
                app:layout_constraintTop_toTopOf="@id/tv_title_tiktok"
                tools:text="123456789" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide_line"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/tv_title_youtube"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/youtube_with_colon"
                android:textColor="@color/color_1C1C1C"
                android:textSize="@dimen/sp_11"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/guide_line"
                app:layout_constraintTop_toTopOf="@id/tv_title_tiktok" />

            <TextView
                android:id="@+id/tv_content_youtube"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_2"
                android:textColor="@color/color_3EAEFF"
                android:textSize="@dimen/sp_11"
                app:layout_constraintBottom_toBottomOf="@id/tv_title_youtube"
                app:layout_constraintLeft_toRightOf="@id/tv_title_youtube"
                app:layout_constraintTop_toTopOf="@id/tv_title_youtube"
                tools:text="123456789" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_12"
            android:src="@drawable/grey_round_close"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>


