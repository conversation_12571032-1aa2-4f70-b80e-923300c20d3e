<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_55"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_18"
        android:paddingVertical="@dimen/dp_5"
        android:text="@string/debug_env"
        android:textColor="@color/black_80"
        android:textSize="@dimen/sp_15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_5"
        android:textColor="@color/investigation_dialog_btn"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/tv_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_name"
        tools:text="已开启" />

</androidx.constraintlayout.widget.ConstraintLayout>