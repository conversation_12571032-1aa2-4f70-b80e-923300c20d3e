<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:clickable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:clickable="true">

        <com.socialplay.gpark.ui.view.RoundView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:topLeftRadius="@dimen/dp_16"
            app:topRightRadius="@dimen/dp_16" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            app:layout_constraintBottom_toTopOf="@id/tv_feedback" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_share"
            style="@style/MetaTextView.S12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_24"
            android:drawableTop="@drawable/ic_share_feat_share"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_horizontal"
            android:text="@string/share"
            app:layout_constraintBottom_toTopOf="@id/v_divider_cancel"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_feedback"
            style="@style/MetaTextView.S12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginBottom="@dimen/dp_24"
            android:drawableTop="@drawable/ic_share_feat_feedback"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_horizontal"
            android:text="@string/feedback"
            app:layout_constraintBottom_toTopOf="@id/v_divider_cancel"
            app:layout_constraintStart_toEndOf="@id/tv_share"
            app:layout_goneMarginStart="@dimen/dp_16" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowers"
            style="@style/MetaTextView.S12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginBottom="@dimen/dp_24"
            android:drawableTop="@drawable/ic_share_feat_send_flowers"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_horizontal"
            android:text="@string/game_detail_page_more_dialog_send_flowers"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/v_divider_cancel"
            app:layout_constraintStart_toEndOf="@id/tv_feedback"
            app:layout_goneMarginStart="@dimen/dp_16"
            tools:visibility="visible" />

        <View
            android:id="@+id/v_divider_cancel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_8"
            android:background="@color/color_F6F6F6"
            app:layout_constraintBottom_toTopOf="@id/tv_cancel" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_cancel"
            style="@style/MetaTextView.S15.PoppinsMedium600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/cancel"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>