<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black">


    <com.socialplay.gpark.ui.view.refresh.VideoFeedSwipeRefreshLayout
        android:id="@+id/sl_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <com.socialplay.gpark.ui.view.refresh.SimpleLoadmoreLayout
                android:id="@+id/vfl_loadmore"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">


                    <com.google.android.exoplayer2.ui.StyledPlayerView
                        android:id="@+id/playerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/black"
                        android:visibility="visible"
                        app:player_layout_id="@layout/view_pure_exo_player_view"
                        app:resize_mode="fixed_width"
                        app:surface_type="texture_view"
                        app:use_controller="false" />

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/vp_video_list"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical" />

                    <View
                        android:id="@+id/v_top_shadow"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_130"
                        android:background="@drawable/bg_video_feed_top_shadow" />

                    <com.socialplay.gpark.ui.view.YALikeAnimationView
                        android:id="@+id/like_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.socialplay.gpark.ui.videofeed.FloatingProgressLayout
                        android:id="@+id/fpl_progress_bar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </FrameLayout>


                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="@dimen/dp_38">

                    <ProgressBar
                        android:id="@+id/pb_progress_bar"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/white"
                        android:indeterminateTintMode="src_atop"/>

                    <TextView
                        android:id="@+id/tv_loading_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="@string/loading"
                        style="@style/MetaTextView.S15.PoppinsRegular400"
                        android:textColor="@color/white"/>

                </LinearLayout>

            </com.socialplay.gpark.ui.view.refresh.SimpleLoadmoreLayout>

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.VideoFeedSwipeRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        app:emptyColor="@color/white_80"
        app:loadingColor="@color/white_80"
        android:layout_width="match_parent"
        android:background="@color/black"
        app:net_error_tip_text_color="@color/white_80"
        tools:visibility="gone"
        app:net_error_retry_button_background="@drawable/bg_white_round_24"
        app:net_error_retry_button_text_color="@color/neutral_color_1"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>