<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvType"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_8"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_f6f6f6_corner_10"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minHeight="@dimen/dp_40"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_10"
        android:singleLine="true"
        android:textColor="@color/neutral_color_3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="feedbacback" />

</androidx.constraintlayout.widget.ConstraintLayout>