<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:id="@+id/includeCard"
        layout="@layout/adapter_publish_post_game"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 暂不使用 includeTag -->
    <include
        android:id="@+id/include_moment_take"
        layout="@layout/item_post_moment_take"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/includeCard"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_outfit"
        layout="@layout/item_post_outfit_card_try_on"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/include_moment_take"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_ugc_Design"
        layout="@layout/item_post_ugc_design_card_edit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/include_outfit"
        tools:visibility="visible" />

    <include
        android:id="@+id/includeTag"
        layout="@layout/adapter_publish_post_tag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_14"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_ugc_Design" />

    <include
        android:id="@+id/includeAttitude"
        layout="@layout/adapter_community_post_attitude"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/includeTag" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dp_14"
        android:background="@color/neutral_color_9"
        app:layout_constraintTop_toBottomOf="@id/includeAttitude" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/vLine"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>