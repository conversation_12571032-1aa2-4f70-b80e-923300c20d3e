<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:gravity="bottom"
    android:orientation="vertical"
    tools:background="@color/black_50">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_top_round_16"
        android:clickable="true">

        <View
            android:id="@+id/v_input_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_12"
            android:background="@drawable/shape_f5f5f5_corner_18"
            app:layout_constraintBottom_toTopOf="@id/ry_view"
            app:layout_constraintTop_toTopOf="@id/et_inputMessage"
            app:layout_goneMarginBottom="0dp" />

        <com.ly123.tes.mgs.im.view.IMEditText
            android:id="@+id/et_inputMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:hint="@string/community_detail_write_comment"
            android:inputType="textMultiLine"
            android:maxHeight="@dimen/dp_82"
            android:minHeight="@dimen/dp_32"
            android:paddingTop="@dimen/dp_10"
            android:textColor="@color/color_212121"
            android:textColorHint="@color/color_757575"
            android:textCursorDrawable="@drawable/bg_cursor"
            android:textSize="@dimen/dp_14"
            app:layout_constraintBottom_toTopOf="@id/rv_emoji_preview"
            app:layout_constraintEnd_toEndOf="@id/v_input_bg"
            app:layout_constraintStart_toStartOf="@id/v_input_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/dp_10" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv_emoji_preview"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_4"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp_11"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/v_input_bg"
            app:layout_constraintEnd_toEndOf="@id/v_input_bg"
            app:layout_constraintStart_toStartOf="@id/v_input_bg"
            tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/community_item_emoji_preview"
            tools:visibility="visible" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/ry_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_4"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/quickInputLayout"
            tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/community_item_post_comment_image_upload"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/quickInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_8"
            android:layout_marginStart="@dimen/dp_16"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/space_bottom"
            tools:visibility="visible">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvQuickInputDesc"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/comment_title_guess_recomment"
                android:textColor="@color/color_B3B3B3"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvQuickInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:clipToPadding="false"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvQuickInputDesc"
                app:layout_constraintTop_toTopOf="parent"
                tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_comment_quick_input" />

            <View
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_26"
                android:background="@drawable/shape_gradient_white_start_to_end"
                app:layout_constraintStart_toStartOf="@id/rvQuickInput"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <Space
            android:id="@+id/space_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_10"
            app:layout_constraintBottom_toTopOf="@id/tv_send" />

        <ImageView
            android:id="@+id/iv_more"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_16"
            android:clickable="true"
            android:src="@drawable/community_comment_icon_emoji"
            app:layout_constraintBottom_toBottomOf="@id/tv_send"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_send" />

        <ImageView
            android:id="@+id/iv_image_btn"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_15"
            android:src="@drawable/community_icon_photo_sel"
            app:layout_constraintBottom_toBottomOf="@id/tv_send"
            app:layout_constraintStart_toEndOf="@id/iv_more"
            app:layout_constraintTop_toTopOf="@id/tv_send" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_send"
            style="@style/MetaTextView.S12.PoppinsSemiBold600"
            android:layout_width="@dimen/dp_62"
            android:layout_height="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@drawable/bg_ffef30_round_24"
            android:gravity="center"
            android:text="@string/video_feed_comment_send"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/bottomContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"/>

</LinearLayout>