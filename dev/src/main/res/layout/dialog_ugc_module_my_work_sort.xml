<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_mask_back"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/title_bar_height"
        android:background="@color/white" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/title_bar_height">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_max_height"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.6" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black_70"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_bg" />

        <FrameLayout
            android:id="@+id/v_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="-86dp"
            android:background="@color/white"
            android:clickable="true"
            app:layout_constraintBottom_toBottomOf="@id/rv"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_86"
            android:clipToPadding="false"
            android:overScrollMode="ifContentScrolls"
            android:paddingHorizontal="@dimen/dp_12"
            android:scrollbarSize="@dimen/dp_4"
            android:scrollbarStyle="outsideInset"
            android:scrollbarThumbVertical="@drawable/sp_b3b3b3_corner_18"
            android:scrollbars="vertical"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_reset_btn"
            style="@style/MetaTextView.S15.PoppinsMedium600"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_24"
            android:background="@drawable/bg_ec_s100"
            android:gravity="center"
            android:text="@string/ugc_asset_button_reset"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/v_bg"
            app:layout_constraintEnd_toStartOf="@id/tv_confirm_btn"
            app:layout_constraintStart_toStartOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_confirm_btn"
            style="@style/MetaTextView.S15.PoppinsMedium600"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_24"
            android:background="@drawable/bg_ffef30_round_100"
            android:gravity="center"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/v_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_reset_btn" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_cancel_btn"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_32"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_f0f0f0_corner_360"
        android:gravity="center"
        android:minHeight="@dimen/dp_32"
        android:paddingHorizontal="@dimen/dp_15"
        android:text="@string/cancel"
        android:textSize="@dimen/dp_12"
        android:textStyle="bold"
        app:uiLineHeight="@dimen/dp_32" />

</FrameLayout>