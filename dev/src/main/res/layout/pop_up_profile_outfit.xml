<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_4"
        android:outlineAmbientShadowColor="@color/black_32"
        android:outlineSpotShadowColor="@color/black_32"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_12"
        app:cardElevation="@dimen/dp_4">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_delete_btn"
            style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_delete"
            android:drawablePadding="@dimen/dp_4"
            android:minHeight="@dimen/dp_46"
            android:paddingHorizontal="@dimen/dp_18"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/delete_cap"
            android:textColor="@color/color_FF5F42"
            android:textSize="@dimen/dp_14"
            app:uiLineHeight="@dimen/dp_20" />

    </androidx.cardview.widget.CardView>

</FrameLayout>