<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="54dp">

    <ImageView
        android:id="@+id/ivSearch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:src="@drawable/search_icon_keyword_left"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvKeyword"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:textColor="#1A1A1A"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/ivSearch"
        app:layout_constraintTop_toTopOf="parent"
        android:singleLine="true"
        android:layout_marginRight="16dp"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="aidhaiouhdoih asiojdfoiasjfioasjfiojasiojajsofijasiofjioasjfioajsfiojasiojfoiasjfiojasiofj" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="#F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tvKeyword"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>