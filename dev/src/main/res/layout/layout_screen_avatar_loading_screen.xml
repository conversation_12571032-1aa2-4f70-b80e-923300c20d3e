<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#FFEEEEEE"
        android:scaleType="centerCrop" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_loading_screen_layout"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_art_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <View
            android:id="@+id/v_loading_bottom_bar_mask"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/bg_editor_role_loading_background_mask"
            app:layout_constraintBottom_toBottomOf="parent" />


        <com.socialplay.gpark.ui.view.MetaSimpleMarqueeView
            android:id="@+id/smv_list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_20"
            android:animateLayoutChanges="true"
            android:background="@drawable/bg_avatar_loading_screen_message"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:flipInterval="3500"
            android:inAnimation="@anim/in_bottom"
            android:lines="2"
            android:maxLines="2"
            android:measureAllChildren="false"
            android:outAnimation="@anim/out_top"
            android:paddingHorizontal="@dimen/dp_14"
            android:paddingVertical="@dimen/dp_10"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@id/pb_loading_progress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="320dp"
            app:marqueeAnimDuration="1000"
            app:smvTextColor="@color/white_70"
            app:smvTextEllipsize="end"
            app:smvTextGravity="center"
            app:smvTextSingleLine="false"
            app:smvTextSize="@dimen/sp_12" />

        <ProgressBar
            android:id="@+id/pb_loading_progress"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal.AvatarLoadingScreen"
            android:layout_width="@dimen/dp_310"
            android:layout_height="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_8"
            app:layout_constraintBottom_toTopOf="@+id/tv_loading_message_and_progress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:max="100"
            tools:progress="50" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_loading_message_and_progress"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_46"
            android:textColor="@color/white_86"
            app:layout_constraintBottom_toBottomOf="@id/v_loading_bottom_bar_mask"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="角色熟睡中...56%" />


        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/sbphv_placeholder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginVertical="@dimen/dp_6"
            android:src="@drawable/ic_full_screen_avatar_loading_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>