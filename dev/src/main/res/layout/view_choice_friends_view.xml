<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_friends_title"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/friends_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_friends_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:orientation="horizontal"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_friends_title"
        tools:itemCount="10"
        tools:listitem="@layout/adapter_item_friends" />

    <!-- 空状态视图 -->
    <include
        android:id="@+id/layout_empty_state"
        layout="@layout/layout_friends_empty_state"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_friends_title" />

</androidx.constraintlayout.widget.ConstraintLayout>