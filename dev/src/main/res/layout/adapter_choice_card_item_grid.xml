<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_6"
    android:paddingBottom="@dimen/dp_15">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_round_16">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivGameCover"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            android:src="@color/color_EFEFEF"
            app:layout_constraintDimensionRatio="166:124"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/shapeTopRound16Style" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_24"
            android:layout_marginLeft="@dimen/dp_6"
            android:layout_marginTop="@dimen/dp_6"
            android:background="@drawable/bg_black35_20"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp_2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lottieGameTag"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                app:lottie_fileName="lottie_item_tag.json" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGameTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_2"
                android:layout_marginTop="1dp"
                android:layout_marginRight="@dimen/dp_6"
                android:fontFamily="@font/poppins_bold_700"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="@dimen/dp_12"
                tools:text="Party" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:background="@drawable/shape_gradient_trans_black"
            android:clickable="false"
            app:layout_constraintBottom_toBottomOf="@id/ivGameCover"
            app:layout_constraintLeft_toLeftOf="@id/ivGameCover"
            app:layout_constraintRight_toRightOf="@id/ivGameCover" />

        <LinearLayout
            android:id="@+id/llPlayerContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:minHeight="@dimen/dp_22"
            android:orientation="horizontal"
            android:paddingRight="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="@+id/ivGameCover"
            app:layout_constraintLeft_toLeftOf="@id/ivGameCover"
            app:layout_constraintRight_toLeftOf="@id/tvPlayerNum">

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/dp_21"
                android:layout_height="@dimen/dp_21"
                android:background="@color/color_FF5F42"
                android:foreground="@drawable/shape_transparent_round"
                android:padding="@dimen/dp_1"
                android:src="@color/color_527AFE"
                app:shapeAppearance="@style/circleStyle" />

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/dp_21"
                android:layout_height="@dimen/dp_21"
                android:layout_marginLeft="-9dp"
                android:background="@color/color_FF5F42"
                android:foreground="@drawable/shape_transparent_round"
                android:padding="@dimen/dp_1"
                android:src="@drawable/placeholder_round"
                app:shapeAppearance="@style/circleStyle" />

        </LinearLayout>

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvPlayerNum"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:textColor="@color/white"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            app:layout_constraintBottom_toBottomOf="@+id/llPlayerContainer"
            app:layout_constraintLeft_toRightOf="@+id/llPlayerContainer"
            app:layout_constraintTop_toTopOf="@+id/llPlayerContainer"
            tools:text="16 Player" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameName"
            style="@style/MetaTextView.S12.PoppinsBold700.EditorCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="2"
            android:minLines="2"
            android:paddingLeft="@dimen/dp_8"
            android:paddingRight="@dimen/dp_5"
            android:paddingBottom="@dimen/dp_8"
            android:textColor="@color/color_333333"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivGameCover"
            tools:text="爆炸的房间可爱大爆炸的房间可爱大爆炸的房间可炸的房间爆炸的房间可爱大爆炸的房间可爱大爆炸的房间可炸的房间爆炸的房间可爱大爆炸的房间可爱大爆炸的房间可炸的房间爆炸的房间可爱大爆炸的房间可爱大爆炸的房间可炸的房间" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>