<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.function.apm.page.view.SpeedConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <!--不要删除CoordinatorLayout的id，会导致fragment重建时折叠布局展开-->
    <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
        android:id="@+id/vcl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/mAppBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clUserInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_top"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideProfileLeft"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="@dimen/dp_16" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideProfileRight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_end="@dimen/dp_16" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="@dimen/dp_72"
                    android:layout_height="@dimen/dp_72"
                    android:layout_marginStart="@dimen/dp_16"
                    android:padding="@dimen/dp_2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:shapeAppearance="@style/circleStyle"
                    app:strokeColor="@color/white"
                    app:strokeWidth="@dimen/dp_2"
                    tools:layout_marginTop="@dimen/dp_80"
                    tools:src="@color/black" />

                <com.socialplay.gpark.ui.view.WrapNestedScrollableHostFrameLayout
                    android:id="@+id/wnshflUsername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toTopOf="@id/tvId"
                    app:layout_constraintEnd_toStartOf="@id/ivLabelOfficial"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@id/ivAvatar"
                    app:layout_constraintTop_toTopOf="@id/ivAvatar"
                    app:layout_constraintVertical_chainStyle="packed">

                    <com.socialplay.gpark.ui.view.CallbackEditText
                        android:id="@+id/etUsername"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:gravity="center_vertical"

                        android:singleLine="true" />

                </com.socialplay.gpark.ui.view.WrapNestedScrollableHostFrameLayout>

                <View
                    android:id="@+id/vUsernameMask"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="0dp"
                    android:background="@drawable/gradient_profile_username"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/wnshflUsername"
                    app:layout_constraintEnd_toEndOf="@id/wnshflUsername"
                    app:layout_constraintTop_toTopOf="@id/wnshflUsername" />

                <ImageView
                    android:id="@+id/ivLabelOfficial"
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginStart="@dimen/dp_4"
                    android:src="@drawable/official_certification"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/wnshflUsername"
                    app:layout_constraintEnd_toStartOf="@id/ivLabelCreator"
                    app:layout_constraintStart_toEndOf="@id/wnshflUsername"
                    app:layout_constraintTop_toTopOf="@id/wnshflUsername"
                    tools:visibility="gone" />

                <ImageView
                    android:id="@+id/ivLabelCreator"
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginStart="@dimen/dp_4"
                    android:src="@drawable/ic_label_creator"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/wnshflUsername"
                    app:layout_constraintEnd_toStartOf="@id/ivFigure"
                    app:layout_constraintStart_toEndOf="@id/ivLabelOfficial"
                    app:layout_constraintTop_toTopOf="@id/wnshflUsername"
                    tools:visibility="gone" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvId"
                    style="@style/MetaTextView.S12"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_4"
                    android:drawableEnd="@drawable/ic_profile_copy"
                    android:drawablePadding="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:textColor="@color/neutral_color_3"
                    app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
                    app:layout_constraintStart_toEndOf="@id/ivAvatar"
                    app:layout_constraintTop_toBottomOf="@id/wnshflUsername"
                    tools:text="12345678127812345678" />

                <ImageView
                    android:id="@+id/ivFigure"
                    android:layout_width="@dimen/dp_103"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_19"
                    app:layout_constraintEnd_toEndOf="@id/guideProfileRight"
                    app:layout_constraintHeight_max="@dimen/dp_217"
                    app:layout_constraintTop_toTopOf="@id/ivAvatar" />

                <LinearLayout
                    android:id="@+id/llFriendNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@id/clFanNum"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintStart_toStartOf="@id/guideProfileLeft"
                    app:layout_constraintTop_toBottomOf="@id/ivAvatar">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFriendNum"
                        style="@style/MetaTextView.S11.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/neutral_color_1"
                        tools:text="1K" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFriendNumLabel"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/friend_tab_social"
                        android:textColor="@color/color_696969" />

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clFanNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@id/llFollowingNum"
                    app:layout_constraintStart_toEndOf="@id/llFriendNum"
                    app:layout_constraintTop_toBottomOf="@id/ivAvatar">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFanNum"
                        style="@style/MetaTextView.S11.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/neutral_color_1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="1K" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFanNumLabel"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center_horizontal"
                        android:maxLines="1"
                        android:text="@string/user_fans_title"
                        android:textColor="@color/color_696969"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvFanNum" />

                    <View
                        android:id="@+id/vFanNumRedDot"
                        android:layout_width="@dimen/dp_6"
                        android:layout_height="@dimen/dp_6"
                        android:layout_marginStart="0.5dp"
                        android:layout_marginTop="@dimen/dp_1"
                        android:background="@drawable/sp_red_dot"
                        android:visibility="gone"
                        app:layout_constraintStart_toEndOf="@id/tvFanNum"
                        app:layout_constraintTop_toTopOf="@id/tvFanNum"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/llFollowingNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@id/llLikesNum"
                    app:layout_constraintStart_toEndOf="@id/clFanNum"
                    app:layout_constraintTop_toBottomOf="@id/ivAvatar">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFollowingNum"
                        style="@style/MetaTextView.S11.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/neutral_color_1"
                        tools:text="1K" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvFollowingNumLabel"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/user_follow_title"
                        android:textColor="@color/color_696969" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llLikesNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@id/ivFigure"
                    app:layout_constraintStart_toEndOf="@id/llFollowingNum"
                    app:layout_constraintTop_toBottomOf="@id/ivAvatar">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvLikesNum"
                        style="@style/MetaTextView.S11.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/neutral_color_1"
                        tools:text="1K" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvLikesNumLabel"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/user_like"
                        android:textColor="@color/color_696969" />

                </LinearLayout>

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvSignature"
                    style="@style/MetaTextView.S13.PoppinsRegular400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:ellipsize="end"
                    android:lines="3"
                    app:layout_constraintEnd_toStartOf="@id/ivFigure"
                    app:layout_constraintStart_toStartOf="@id/guideProfileLeft"
                    app:layout_constraintTop_toBottomOf="@id/llLikesNum"
                    tools:text="i\ni\ni\iiii\nioiii"
                    tools:visibility="visible" />

                <View
                    android:id="@+id/vBadgeBg"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_white_50_round_12"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/tvBadge"
                    app:layout_constraintEnd_toEndOf="@id/tvBadge"
                    app:layout_constraintStart_toStartOf="@id/ivBadge"
                    app:layout_constraintTop_toTopOf="@id/tvBadge"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBadge"
                    style="@style/MetaTextView.S10"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_16"
                    android:drawableEnd="@drawable/ic_profile_link_right_arrow"
                    android:drawablePadding="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/dp_15"
                    android:paddingStart="@dimen/dp_4"
                    android:paddingEnd="@dimen/dp_8"
                    android:textSize="@dimen/dp_10"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/ivBadge"
                    app:layout_constraintTop_toBottomOf="@id/tvSignature"
                    app:uiLineHeight="@dimen/dp_15"
                    tools:text="Map Badge"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivBadge"
                    android:layout_width="@dimen/dp_26"
                    android:layout_height="@dimen/dp_18"
                    android:layout_marginStart="@dimen/dp_16"
                    android:paddingStart="@dimen/dp_8"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/tvBadge"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvBadge"
                    tools:visibility="visible" />

                <View
                    android:id="@+id/vLinkBg"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_24"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="-4dp"
                    android:background="@drawable/bg_white_50_round_12"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/ivLinkArrow"
                    app:layout_constraintStart_toEndOf="@id/tvBadge"
                    app:layout_constraintTop_toBottomOf="@id/tvSignature"
                    app:layout_goneMarginStart="@dimen/dp_16"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivLink1"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_20"
                    android:background="@drawable/bg_white_circle"
                    android:padding="@dimen/dp_1"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/tvBadge"
                    app:layout_constraintTop_toBottomOf="@id/tvSignature"
                    app:layout_goneMarginStart="@dimen/dp_20"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivLink2"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:layout_marginStart="-6dp"
                    android:background="@drawable/bg_white_circle"
                    android:padding="@dimen/dp_1"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/ivLink1"
                    app:layout_constraintTop_toTopOf="@id/ivLink1"
                    app:shapeAppearance="@style/circleStyle"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivLink3"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:layout_marginStart="-6dp"
                    android:background="@drawable/bg_white_circle"
                    android:padding="@dimen/dp_1"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/ivLink2"
                    app:layout_constraintTop_toTopOf="@id/ivLink1"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivLinkArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_4"
                    android:layout_marginTop="@dimen/dp_22"
                    android:src="@drawable/ic_profile_link_right_arrow"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/ivLink3"
                    app:layout_constraintTop_toBottomOf="@id/tvSignature"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/barrierBadge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="tvSignature, vBadgeBg, vLinkBg, ivFigure" />

                <com.socialplay.gpark.ui.view.WrapBanner
                    android:id="@+id/bannerEntrance"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_65"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_profile_entrances"
                    android:orientation="horizontal"
                    android:padding="0.5dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/barrierBadge"
                    tools:visibility="visible" />

                <com.zhpan.indicator.IndicatorView
                    android:id="@+id/indicatorEntrance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginBottom="@dimen/dp_3"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/bannerEntrance"
                    app:layout_constraintEnd_toEndOf="@id/bannerEntrance"
                    app:layout_constraintStart_toStartOf="@id/bannerEntrance"
                    app:vpi_orientation="horizontal"
                    app:vpi_slide_mode="normal"
                    app:vpi_slider_checked_color="@color/color_F49D0C"
                    app:vpi_slider_normal_color="@color/color_FDE58A"
                    app:vpi_style="round_rect"
                    tools:visibility="visible" />

                <View
                    android:id="@+id/vBannerBg"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_54"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_profile_entrances"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/bannerEntrance"
                    tools:visibility="visible" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivBannerIcon"
                    android:layout_width="@dimen/dp_36"
                    android:layout_height="@dimen/dp_36"
                    android:layout_marginStart="@dimen/dp_8"
                    android:scaleType="centerCrop"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/vBannerBg"
                    app:layout_constraintStart_toStartOf="@id/vBannerBg"
                    app:layout_constraintTop_toTopOf="@id/vBannerBg"
                    app:shapeAppearance="@style/round_corner_8dp"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBannerTitle"
                    style="@style/MetaTextView.S12.PoppinsSemiBold600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/tvBannerDesc"
                    app:layout_constraintEnd_toStartOf="@id/tvBannerBtn"
                    app:layout_constraintStart_toEndOf="@id/ivBannerIcon"
                    app:layout_constraintTop_toTopOf="@id/vBannerBg"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="Island Kickoff Starts Now! 🚀"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBannerDesc"
                    style="@style/MetaTextView.S10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_4"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_999999"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/vBannerBg"
                    app:layout_constraintEnd_toStartOf="@id/tvBannerBtn"
                    app:layout_constraintStart_toEndOf="@id/ivBannerIcon"
                    app:layout_constraintTop_toBottomOf="@id/tvBannerTitle"
                    tools:text="2025 Island: Registration is Hot!  🔥"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvBannerBtn"
                    style="@style/MetaTextView.S12.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:background="@drawable/bg_ffda62_round_100"
                    android:paddingHorizontal="@dimen/dp_12"
                    android:paddingVertical="@dimen/dp_6"
                    android:text="@string/profile_banner_join"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/vBannerBg"
                    app:layout_constraintEnd_toEndOf="@id/guideProfileRight"
                    app:layout_constraintTop_toTopOf="@id/vBannerBg"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/icBannerCloseBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_6"
                    android:layout_marginEnd="@dimen/dp_14"
                    android:src="@drawable/ic_profile_banner_close"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bannerEntrance"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ivOtherGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_profile_entrances"
                    android:paddingHorizontal="@dimen/dp_11"
                    android:paddingVertical="@dimen/dp_14"
                    android:src="@drawable/ic_profile_entrance_my_group"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vBannerBg" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvOtherGroupTitle"
                    style="@style/MetaTextView.S12.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_7"
                    android:drawablePadding="@dimen/dp_8"
                    android:gravity="center_vertical|start"
                    android:text="@string/profile_other_group_title"
                    app:layout_constraintBottom_toTopOf="@id/tvOtherGroupDesc"
                    app:layout_constraintStart_toEndOf="@id/ivOtherGroup"
                    app:layout_constraintTop_toTopOf="@id/ivOtherGroup"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvOtherGroupDesc"
                    style="@style/MetaTextView.S10.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_7"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical|start"
                    android:text="@string/profile_other_group_desc"
                    android:textColor="@color/color_666666"
                    app:layout_constraintBottom_toBottomOf="@id/ivOtherGroup"
                    app:layout_constraintStart_toEndOf="@id/ivOtherGroup"
                    app:layout_constraintTop_toBottomOf="@id/tvOtherGroupTitle" />

                <androidx.constraintlayout.helper.widget.Layer
                    android:id="@+id/layerOtherGroup"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:constraint_referenced_ids="ivOtherGroup, tvOtherGroupTitle,
                        tvOtherGroupDesc"
                    app:layout_constraintBottom_toBottomOf="@id/ivOtherGroup"
                    app:layout_constraintEnd_toEndOf="@id/tvOtherGroupTitle"
                    app:layout_constraintStart_toStartOf="@id/ivOtherGroup"
                    app:layout_constraintTop_toTopOf="@id/ivOtherGroup"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvFollowBtn"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_ffda62_round_100"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="7.5dp"
                    android:text="@string/follow"
                    android:textColor="@color/color_451A03"
                    android:visibility="gone"
                    app:layout_constraintEnd_toStartOf="@id/tvAddFriendBtn"
                    app:layout_constraintStart_toStartOf="@id/guideProfileLeft"
                    app:layout_constraintTop_toBottomOf="@id/ivOtherGroup"
                    tools:visibility="visible" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvAddFriendBtn"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_profile_btns"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="7.5dp"
                    android:textColor="@color/color_451A03"
                    android:visibility="gone"
                    app:layout_constraintEnd_toStartOf="@id/tvChatBtn"
                    app:layout_constraintStart_toEndOf="@id/tvFollowBtn"
                    app:layout_constraintTop_toBottomOf="@id/ivOtherGroup"
                    app:layout_constraintWidth_min="@dimen/dp_69"
                    tools:visibility="gone" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvChatBtn"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_16"
                    android:background="@drawable/bg_profile_btns"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="7.5dp"
                    android:textColor="@color/color_451A03"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/guideProfileRight"
                    app:layout_constraintStart_toEndOf="@id/tvAddFriendBtn"
                    app:layout_constraintTop_toBottomOf="@id/ivOtherGroup"
                    app:layout_constraintWidth_min="@dimen/dp_69"
                    tools:visibility="visible" />

                <Space
                    android:id="@+id/vBgBottomRound"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_32"
                    app:layout_constraintTop_toBottomOf="@id/tvFollowBtn" />

                <ImageView
                    android:id="@+id/ivFigureTipsDot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_80"
                    android:layout_marginBottom="@dimen/dp_67"
                    android:src="@drawable/ic_profile_try_on_tips_dot"
                    app:layout_constraintBottom_toBottomOf="@id/ivFigure"
                    app:layout_constraintEnd_toEndOf="parent" />

                <ImageView
                    android:id="@+id/ivFigureTipsArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_95"
                    android:src="@drawable/ic_profile_try_on_tips_arrow"
                    app:layout_constraintBottom_toBottomOf="@id/ivFigureTipsDot"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/ivFigureTipsDot" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvFigureTipsContent"
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_99"
                    android:background="@drawable/bg_gradient_profile_try_on_tips"
                    android:gravity="center"
                    android:padding="@dimen/dp_8"
                    android:text="@string/profile_try_on_tap_tips"
                    android:textColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="@id/ivFigureTipsDot"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/ivFigureTipsDot" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupFigureTips"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:constraint_referenced_ids="ivFigureTipsDot, ivFigureTipsArrow,
                        tvFigureTipsContent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.socialplay.gpark.ui.view.DrawerTabLayoutHost
                android:id="@+id/dtlh"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_56"
                android:layout_marginTop="-16dp"
                android:background="@drawable/bg_white_top_round_16">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tlProfile"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent"
                    app:tabGravity="start"
                    app:tabIndicator="@drawable/indicator_profile"
                    app:tabIndicatorColor="@color/color_FFDE70"
                    app:tabIndicatorFullWidth="false"
                    app:tabIndicatorGravity="bottom"
                    app:tabIndicatorHeight="@dimen/dp_8"
                    app:tabMinWidth="@dimen/dp_10"
                    app:tabMode="scrollable"
                    app:tabPaddingBottom="0dp"
                    app:tabPaddingEnd="@dimen/dp_12"
                    app:tabPaddingStart="@dimen/dp_12"
                    app:tabPaddingTop="0dp"
                    app:tabRippleColor="@null" />

            </com.socialplay.gpark.ui.view.DrawerTabLayoutHost>

        </com.google.android.material.appbar.AppBarLayout>

        <com.socialplay.gpark.ui.view.DrawerViewPager2Host
            android:id="@+id/aslfl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:clipToPadding="false"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nsvEmptyTip"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                tools:visibility="visible">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/ivEmptyTipImg"
                        android:layout_width="@dimen/dp_150"
                        android:layout_height="@dimen/dp_150"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/icon_no_network_connection" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvEmptyTipText"
                        style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginHorizontal="@dimen/dp_46"
                        android:layout_marginTop="@dimen/dp_150"
                        android:gravity="center"
                        android:visibility="gone"
                        tools:text="@string/block_empty_message"
                        tools:visibility="visible" />

                </FrameLayout>

            </androidx.core.widget.NestedScrollView>

        </com.socialplay.gpark.ui.view.DrawerViewPager2Host>

    </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vTitleBarBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="-16dp"
        android:alpha="0"
        android:background="@drawable/bg_profile_title_bar"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintTop_toTopOf="parent"
        tools:alpha="1" />

    <FrameLayout
        android:id="@+id/flTitleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintTop_toBottomOf="@id/sbphv">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivTitleBarAvatar"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center"
            android:padding="0.5dp"
            android:translationY="@dimen/dp_48"
            app:shapeAppearance="@style/circleStyle"
            app:strokeColor="@color/white"
            app:strokeWidth="0.5dp" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitleBarFollowBtn"
            style="@style/MetaTextView.S12.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_84"
            android:background="@drawable/sp_c100_1a1a1a_s1"
            android:clickable="false"
            android:drawableStart="@drawable/ic_profile_follow_add_14"
            android:drawablePadding="@dimen/dp_2"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dp_18"
            android:paddingHorizontal="@dimen/dp_10"
            android:paddingVertical="@dimen/dp_5"
            android:text="@string/follow"
            android:textSize="@dimen/dp_12"
            android:translationY="@dimen/dp_48"
            app:uiLineHeight="@dimen/dp_18" />

    </FrameLayout>

    <ImageView
        android:id="@+id/ivDrawerBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_more_line"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintStart_toStartOf="@id/flTitleBar"
        app:layout_constraintTop_toTopOf="@id/flTitleBar"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ibBack"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_back_array_bold_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintStart_toStartOf="@id/flTitleBar"
        app:layout_constraintTop_toTopOf="@id/flTitleBar" />

    <ImageView
        android:id="@+id/ivScanBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_scan_code"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintEnd_toStartOf="@id/ivShareBtn"
        app:layout_constraintTop_toTopOf="@id/flTitleBar"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivShareBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_share"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintEnd_toStartOf="@id/ivMsgBtn"
        app:layout_constraintTop_toTopOf="@id/flTitleBar"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivMsgBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_message"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintEnd_toStartOf="@id/ivMoreBtn"
        app:layout_constraintTop_toTopOf="@id/flTitleBar"
        tools:visibility="visible" />

    <View
        android:id="@+id/vMsgRedDot"
        android:layout_width="@dimen/dp_7"
        android:layout_height="@dimen/dp_7"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivMsgBtn"
        app:layout_constraintTop_toTopOf="@id/ivMsgBtn"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivMoreBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_more_dot"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/flTitleBar"
        app:layout_constraintEnd_toEndOf="@id/flTitleBar"
        app:layout_constraintTop_toTopOf="@id/flTitleBar"
        tools:visibility="visible" />

</com.socialplay.gpark.function.apm.page.view.SpeedConstraintLayout>