<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/v_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@color/color_D9D9D9"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S18.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:textColor="@color/color_17171C"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_divider"
        tools:text="Obby" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_build_btn"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_corner_30_black"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/dp_18"
        android:paddingVertical="@dimen/dp_5"
        android:text="@string/create_v2_build"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <com.socialplay.gpark.ui.view.WrapRecyclerView
        android:id="@+id/wrv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/adapter_editor_create_v2_formwork_game"
        tools:orientation="horizontal" />

</androidx.constraintlayout.widget.ConstraintLayout>