<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_50"
    android:layout_height="@dimen/dp_40">

    <ImageView
        android:id="@+id/img_emoji"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/img_emoji_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_6"
        android:src="@drawable/community_icon_emoji_gif_delete"
        app:layout_constraintEnd_toEndOf="@+id/img_emoji"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>