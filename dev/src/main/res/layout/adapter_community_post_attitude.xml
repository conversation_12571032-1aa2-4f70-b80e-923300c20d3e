<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivLike"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:src="@drawable/icon_post_like_unselected"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLike"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="@id/ivLike"
        app:layout_constraintStart_toEndOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/ivLike"
        tools:text="1000.0K" />

    <ImageView
        android:id="@+id/ivComment"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_26"
        android:src="@drawable/icon_post_comment"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLike"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvComment"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="@id/ivComment"
        app:layout_constraintStart_toEndOf="@id/ivComment"
        app:layout_constraintTop_toTopOf="@id/ivComment"
        tools:text="1000.0K" />

    <ImageView
        android:id="@+id/ivShare"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_26"
        android:src="@drawable/ic_share_thin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvComment"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/neutral_color_4" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvShare"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="@id/ivShare"
        app:layout_constraintStart_toEndOf="@id/ivShare"
        app:layout_constraintTop_toTopOf="@id/ivShare"
        tools:text="1000.0K" />

    <View
        android:id="@+id/layerLike"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvLike"
        app:layout_constraintEnd_toEndOf="@id/tvLike"
        app:layout_constraintStart_toStartOf="@id/ivLike"
        app:layout_constraintTop_toTopOf="@id/tvLike" />

    <View
        android:id="@+id/layerComment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvComment"
        app:layout_constraintEnd_toEndOf="@id/tvComment"
        app:layout_constraintStart_toStartOf="@id/ivComment"
        app:layout_constraintTop_toTopOf="@id/tvComment" />

    <View
        android:id="@+id/layerShare"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvShare"
        app:layout_constraintEnd_toEndOf="@id/tvShare"
        app:layout_constraintStart_toStartOf="@id/ivShare"
        app:layout_constraintTop_toTopOf="@id/tvShare" />

</androidx.constraintlayout.widget.ConstraintLayout>