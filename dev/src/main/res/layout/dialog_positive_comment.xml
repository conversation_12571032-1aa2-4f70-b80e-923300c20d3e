<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="bottom"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="177dp"-->
<!--        android:background="@drawable/bg_positive_comment" />-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/top"
            android:layout_width="343dp"
            android:layout_height="@dimen/dp_170"
            android:layout_gravity="center"
            android:gravity="end"
            android:background="@drawable/bg_positive_comment">
            <ImageView
                android:id="@+id/img_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_publish_post_close"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/dp_54"
                android:layout_marginEnd="@dimen/dp_18" />
            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_title"
                style="@style/MetaTextView.S16.PoppinsExtraBold800"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:layout_marginBottom="@dimen/dp_8"
                android:ellipsize="end"
                android:singleLine="true"
                android:textSize="19sp"
                android:text="@string/positive_comment_dialog_title"
                android:textColor="#1a1a1a"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="343dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="44dp"
            android:background="@drawable/bg_corner_bottom_40_white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">


            <TextView
                android:id="@+id/tv_des"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="2dp"
                android:gravity="center"
                android:text="@string/positive_comment_contents"
                android:textColor="#333"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_positive_comment"
                style="@style/MetaTextView.S15.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_common_dialog_yellow"
                android:gravity="center"
                android:minHeight="@dimen/dp_40"
                android:text="@string/positive_comments" />

            <TextView
                android:id="@+id/tv_feedback"
                style="@style/MetaTextView.S15.PoppinsMedium500"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_common_dialog_cancel"
                android:gravity="center"
                android:text="@string/feedback_questions"
                android:textColor="#333"/>

        </LinearLayout>

</LinearLayout>
