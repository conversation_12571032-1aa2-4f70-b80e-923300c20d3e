# 好友跟房布局说明

## 概述
这个实现为首页好友跟房功能提供了两种状态的布局：
1. **空白状态**: 显示邀请文字和彩色头像
2. **有数据状态**: 显示好友列表，包含在线状态、游戏状态等

## 文件结构

### 布局文件
- `layout_friends_empty_state.xml` - 空白状态布局
- `layout_friends_with_data.xml` - 有数据状态布局  
- `item_friend_avatar.xml` - 单个好友头像item
- `adapter_item_friends.xml` - 更新后的适配器布局
- `test_friend_room_layout.xml` - 测试布局文件

### Drawable资源
- `home_friends_add.xml` - 添加好友图标
- `home_friends_empty_bg.xml` - 空白状态背景
- `bg_friend_avatar_*.xml` - 彩色头像背景（黄、紫、粉、红）
- `bg_friend_status_*.xml` - 状态标签背景（在线、游戏中、离线）
- `ic_add_friend_plus.xml` - 加号图标

### 代码文件
- `ChoiceHomeHeaderFriendsAdapter.kt` - 更新后的适配器
- `FriendRoomStateManager.kt` - 状态管理器

## 使用方法

### 1. 空白状态
当没有好友或需要显示空白状态时：
```kotlin
binding.layoutEmptyState.isVisible = true
// 隐藏其他视图
```

### 2. 有数据状态
当有好友数据时：
```kotlin
binding.layoutEmptyState.isVisible = false
// 设置RecyclerView显示好友列表
```

### 3. 状态标签
好友状态分为三种：
- **在线**: 绿色背景，显示"Online"/"在线"
- **游戏中**: 蓝色背景，显示"Gaming"/"游戏中"
- **离线**: 灰色背景，显示"Offline"/"离线"

## 设计规范

### 尺寸
- 好友头像: 60dp x 60dp
- 状态指示器: 12dp x 12dp
- 空白状态头像: 40dp x 40dp
- 加号图标: 16dp x 16dp

### 颜色
- 黄色头像: #FFD700
- 紫色头像: #B57EFF  
- 粉色头像: #FFB6C1
- 红色头像: #FF6B6B
- 在线状态: #00E585
- 游戏状态: #4AB4FF
- 离线状态: #B3B3B3

### 间距
- 头像间距: 12dp
- 状态标签间距: 4dp
- 内边距: 16dp

## 字符串资源
已添加的字符串资源：
- `friend_room_empty_title` - 空白状态标题
- `friend_status_online` - 在线状态
- `friend_status_gaming` - 游戏状态  
- `friend_status_offline` - 离线状态

## 注意事项
1. 所有尺寸使用dimens.xml中定义的值
2. 颜色使用项目标准色彩规范
3. 文字使用MetaTextView组件
4. 头像使用ShapeableImageView实现圆形效果
5. 支持中英文双语言
