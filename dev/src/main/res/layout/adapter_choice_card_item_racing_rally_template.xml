<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/racingRallyTemplateRoot"
    android:layout_width="@dimen/dp_200"
    android:layout_height="wrap_content">

    <!-- Theme color background -->
    <View
        android:id="@+id/view_theme_background"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Main image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintDimensionRatio="200:120"
        app:layout_constraintTop_toBottomOf="@id/view_theme_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:shapeAppearance="@style/round_corner_12dp"
        tools:src="@mipmap/ic_launcher" />

    <!-- Template label -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_template_label"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/bg_white_20_corner_4"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_4"
        android:text="Template"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/iv_game_icon"
        app:layout_constraintTop_toTopOf="@id/iv_game_icon" />

    <!-- Game title -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="start|top"
        android:lines="2"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@id/iv_game_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Racing Rally Star Template" />

    <!-- Create button -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/btn_create"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_ffef30_round_10"
        android:gravity="center"
        android:text="Create"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
