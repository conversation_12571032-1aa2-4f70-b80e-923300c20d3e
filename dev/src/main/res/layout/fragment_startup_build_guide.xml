<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon_tint="@color/white"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_guide_title"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_6"
        android:alpha="0"
        android:text="@string/newbie_guide_title"
        android:textColor="@color/white"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_69"
        android:layout_marginTop="@dimen/dp_8"
        android:alpha="0"
        android:text="@string/newbie_guide_content"
        android:textColor="@color/white_30"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_guide_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_play_map_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_100"
        android:layout_marginEnd="@dimen/dp_8"
        app:layout_constraintDimensionRatio="148:180"
        app:layout_constraintEnd_toStartOf="@id/cl_build_map_container"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintWidth_max="@dimen/dp_180">

        <com.socialplay.gpark.ui.view.BuildGuideBtnBg
            android:id="@+id/v_bg_play_map"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0" />

        <ImageView
            android:id="@+id/iv_play_map"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginVertical="@dimen/dp_46"
            android:alpha="0"
            android:src="@drawable/ic_guide_play_map"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_play_map"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/newbie_guide_play_btn"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/v_bg_play_map"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_play_map"
            app:layout_constraintVertical_bias="0.6" />

        <com.socialplay.gpark.ui.view.BuildGuideCheckBtn
            android:id="@+id/cb_play_map"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:alpha="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_build_map_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_100"
        android:layout_marginEnd="@dimen/dp_32"
        app:layout_constraintDimensionRatio="148:180"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cl_play_map_container"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintWidth_max="@dimen/dp_180">


        <com.socialplay.gpark.ui.view.BuildGuideBtnBg
            android:id="@+id/v_bg_build_map"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0" />

        <ImageView
            android:id="@+id/iv_build_map"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_48"
            android:alpha="0"
            android:src="@drawable/ic_guide_build_map"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_build_map"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:text="@string/newbie_guide_make_btn"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/iv_build_map"
            app:layout_constraintStart_toStartOf="@id/iv_build_map"
            app:layout_constraintTop_toBottomOf="@id/iv_build_map"
            app:layout_constraintVertical_bias="0.6" />

        <com.socialplay.gpark.ui.view.BuildGuideCheckBtn
            android:id="@+id/cb_build_map"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:alpha="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_next_btn"
        style="@style/MetaTextView.S18.PoppinsBlack900"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_13"
        android:layout_marginTop="@dimen/dp_40"
        android:alpha="0.5"
        android:background="@drawable/bg_ffef30_round_24"
        android:enabled="false"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_10"
        android:text="@string/intl_go_next"
        app:layout_constraintEnd_toEndOf="@id/cl_build_map_container"
        app:layout_constraintStart_toStartOf="@id/cl_play_map_container"
        app:layout_constraintTop_toBottomOf="@id/cl_play_map_container" />

</androidx.constraintlayout.widget.ConstraintLayout>