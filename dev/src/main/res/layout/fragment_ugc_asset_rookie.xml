<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/sbphv"
        app:title_text="@string/module_rookie_tab" />

    <View
        android:id="@+id/v_bg_top_gradient"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_16"
        android:background="@drawable/bg_gradient_ugc_feed_top"
        app:layout_constraintTop_toBottomOf="@id/tbl" />


    <View
        android:id="@+id/v_bg_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_f5f5f7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_bg_top_gradient" />


    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/mrl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:paddingHorizontal="@dimen/dp_10"
                android:paddingTop="@dimen/dp_16"
                tools:listitem="@layout/item_ugc_design_feed"
                tools:spanCount="2" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/lv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>