<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android"
    android:enterFadeDuration="100"
    android:exitFadeDuration="100">

    <item android:drawable="@drawable/tab_icon_avatar_selected" android:state_checked="true" />
    <item android:drawable="@drawable/tab_icon_avatar_selected" android:state_selected="true" />
    <item android:drawable="@drawable/tab_icon_avatar_selected" android:state_pressed="true"/>
    <item android:drawable="@drawable/tab_icon_avatar_dark_unselected" />
</selector>