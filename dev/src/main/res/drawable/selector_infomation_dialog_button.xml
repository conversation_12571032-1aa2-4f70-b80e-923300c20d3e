<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/colorAccentPrimaryDisabled" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/colorAccentPrimary" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/colorAccentPrimary" />
            <corners android:radius="24dp" />
        </shape>
    </item>
</selector>