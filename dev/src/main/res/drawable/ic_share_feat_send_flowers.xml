<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M24,24m-24,0a24,24 0,1 1,48 0a24,24 0,1 1,-48 0">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="9.714"
          android:startY="4.571"
          android:endX="38.857"
          android:endY="46.286"
          android:type="linear">
        <item android:offset="0" android:color="#FFDDDDDD"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M26.441,24.705C26.441,19.578 22.364,16.542 20.959,16.074C19.611,15.624 17.455,15.619 17.364,15.619C17.164,15.619 16.99,15.757 16.944,15.952C16.898,16.147 16.992,16.349 17.171,16.439C17.883,16.795 18.229,17.517 18.229,18.648C18.229,19.211 18.143,19.682 18.043,20.227C17.927,20.859 17.796,21.575 17.796,22.542C17.796,25.317 20.725,27.496 23.416,27.712C23.416,27.712 23.923,27.733 24.28,27.712C26.971,27.496 29.899,25.316 29.899,22.542C29.899,21.279 29.758,20.59 29.633,19.982C29.544,19.547 29.467,19.171 29.467,18.648C29.467,17.517 29.813,16.795 30.525,16.439C30.704,16.349 30.799,16.148 30.752,15.952C30.706,15.757 30.532,15.619 30.331,15.619C30.242,15.619 28.934,15.63 27.508,16.036C27.174,15.626 26.482,14.556 25.3,14.556C24.864,14.556 23.896,14.599 23.396,15.099"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.212,28.265V33.725"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M18.124,32.988C19.219,33.561 20.435,33.226 20.668,33.153L22.043,32.721C22.057,32.716 22.067,32.707 22.08,32.7C22.1,32.69 22.121,32.683 22.138,32.668C22.167,32.645 22.19,32.616 22.207,32.584C22.207,32.583 22.207,32.583 22.207,32.583C22.212,32.574 22.212,32.565 22.215,32.556C22.225,32.531 22.235,32.506 22.237,32.48C22.241,32.442 22.236,32.404 22.225,32.368L21.812,31.053C21.797,30.996 21.417,29.636 20.206,29.003C19.11,28.43 17.894,28.765 17.661,28.838L16.289,29.269C16.142,29.315 16.06,29.471 16.106,29.618L16.52,30.937C16.536,30.994 16.913,32.355 18.124,32.988L18.124,32.988Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M30.444,32.545C29.321,33.198 28.028,32.904 27.781,32.838L26.316,32.45C26.301,32.445 26.29,32.436 26.276,32.43C26.255,32.421 26.232,32.413 26.214,32.399C26.183,32.375 26.157,32.346 26.137,32.313C26.138,32.313 26.137,32.313 26.137,32.313C26.132,32.304 26.132,32.294 26.127,32.284C26.116,32.259 26.105,32.234 26.101,32.206C26.095,32.166 26.098,32.126 26.109,32.088L26.48,30.688C26.493,30.626 26.828,29.181 28.069,28.459C29.192,27.806 30.485,28.1 30.733,28.166L32.194,28.553C32.35,28.595 32.444,28.755 32.402,28.911L32.03,30.316C32.016,30.376 31.685,31.823 30.443,32.545L30.444,32.545Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:strokeLineCap="round"/>
</vector>
