<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="1dp"
    android:viewportWidth="48"
    android:viewportHeight="1">
  <path
      android:strokeWidth="1"
      android:pathData="M48,0.5L-0,0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="-0.5"
          android:endX="48"
          android:endY="-0.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFB57EFF"/>
        <item android:offset="1" android:color="#00F1E7FF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
