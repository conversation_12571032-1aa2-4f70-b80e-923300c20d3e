<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <ConstraintSet android:id="@+id/cs_view_mode">
        <Constraint
            android:id="@id/v_avatar_guide"
            app:layout_constraintHeight_percent="0.48" />

        <Constraint
            android:id="@id/iv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <Constraint android:id="@+id/tv_loading_progress"
            app:layout_constraintBottom_toBottomOf="@+id/v_avatar_guide"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_4"
            android:visibility="invisible"/>

    </ConstraintSet>

    <ConstraintSet android:id="@+id/cs_edit_mode">
        <Constraint
            android:id="@id/v_avatar_guide"
            app:layout_constraintHeight_percent="0.48" />

        <Constraint
            android:id="@id/iv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <Constraint android:id="@+id/tv_loading_progress"
            app:layout_constraintBottom_toBottomOf="@+id/v_avatar_guide"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_4"
            android:visibility="visible"/>

    </ConstraintSet>

    <Transition
        android:id="@+id/ts_view_to_edit_mode"
        app:constraintSetEnd="@id/cs_edit_mode"
        app:constraintSetStart="@+id/cs_view_mode"
        app:duration="120">

        <KeyFrameSet>

            <KeyAttribute
                app:framePosition="0"
                android:alpha="0"
                android:visibility="visible"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

            <KeyAttribute
                app:framePosition="78"
                android:alpha="0"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

            <KeyAttribute
                app:framePosition="100"
                android:alpha="1"
                android:visibility="visible"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>
        </KeyFrameSet>

        <KeyFrameSet>
            <KeyAttribute app:motionTarget="@id/fl_shimmer" android:alpha="1" app:framePosition="0"/>
            <KeyAttribute app:motionTarget="@id/fl_shimmer"  android:alpha="1" app:framePosition="78"/>
            <KeyAttribute app:motionTarget="@id/fl_shimmer"  android:alpha="1" app:framePosition="100"/>
        </KeyFrameSet>
    </Transition>


    <Transition
        android:id="@+id/ts_edit_to_view_mode"
        app:constraintSetEnd="@id/cs_view_mode"
        app:constraintSetStart="@+id/cs_edit_mode"
        app:duration="120">

        <KeyFrameSet>
            <KeyTrigger app:framePosition="100" app:motionTarget="@id/cl_edit_mode_view_container"/>

            <KeyAttribute
                app:framePosition="0"
                android:alpha="1"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

            <KeyAttribute
                app:framePosition="78"
                android:alpha="1"
                android:visibility="visible"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

            <KeyAttribute
                app:framePosition="99"
                android:alpha="0"
                android:visibility="visible"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

            <KeyAttribute
                app:framePosition="100"
                android:alpha="0"
                android:visibility="gone"
                app:motionTarget="@id/cl_edit_mode_view_container">
            </KeyAttribute>

        </KeyFrameSet>


        <KeyFrameSet>
            <KeyAttribute app:motionTarget="@id/fl_shimmer"  android:alpha="1" app:framePosition="0"/>
            <KeyAttribute app:motionTarget="@id/fl_shimmer"  android:alpha="1" app:framePosition="78"/>
            <KeyAttribute app:motionTarget="@id/fl_shimmer"  android:alpha="0" app:framePosition="100"/>
        </KeyFrameSet>


    </Transition>


</MotionScene>
