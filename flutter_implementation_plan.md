# Flutter实现计划

## 阶段一：基础架构搭建

### 1. 项目结构创建
```
flutter_modules/
├── android/                 # Android集成配置
├── lib/
│   ├── core/               # 核心功能
│   │   ├── bridge/         # 原生桥接
│   │   ├── network/        # 网络层
│   │   ├── storage/        # 存储层
│   │   └── utils/          # 工具类
│   ├── modules/            # 功能模块
│   │   ├── settings/       # 设置模块
│   │   ├── profile/        # 个人资料
│   │   ├── friends/        # 朋友系统
│   │   ├── notifications/  # 通知系统
│   │   └── payment/        # 支付系统
│   ├── shared/             # 共享组件
│   │   ├── widgets/        # 通用组件
│   │   ├── themes/         # 主题样式
│   │   └── constants/      # 常量定义
│   └── main.dart           # 入口文件
├── pubspec.yaml            # 依赖配置
└── analysis_options.yaml   # 代码规范
```

### 2. 依赖配置 (pubspec.yaml)
```yaml
name: gpark_flutter_modules
description: Flutter modules for GPark app

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # 路由管理
  go_router: ^12.1.3
  
  # 网络请求
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # 依赖注入
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # UI组件
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  
  # 工具库
  freezed_annotation: ^2.4.1
  equatable: ^2.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码生成
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  injectable_generator: ^2.4.1
  
  # 代码规范
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
```

## 阶段二：桥接层实现

### 1. Android端桥接管理器
```kotlin
// FlutterBridgeManager.kt
package com.socialplay.gpark.flutter

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import org.koin.core.context.GlobalContext

class FlutterBridgeManager private constructor() {
    
    companion object {
        private const val ENGINE_ID = "gpark_flutter_engine"
        
        // Method Channels
        const val CHANNEL_ACCOUNT = "com.socialplay.gpark/account"
        const val CHANNEL_NAVIGATION = "com.socialplay.gpark/navigation"
        const val CHANNEL_DATA = "com.socialplay.gpark/data"
        const val CHANNEL_ANALYTICS = "com.socialplay.gpark/analytics"
        
        // Event Channels
        const val EVENT_USER_STATUS = "com.socialplay.gpark/user_status"
        const val EVENT_FRIEND_UPDATE = "com.socialplay.gpark/friend_update"
        
        @Volatile
        private var INSTANCE: FlutterBridgeManager? = null
        
        fun getInstance(): FlutterBridgeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FlutterBridgeManager().also { INSTANCE = it }
            }
        }
    }
    
    private var flutterEngine: FlutterEngine? = null
    private var isInitialized = false
    
    // 桥接处理器
    private lateinit var accountBridge: AccountBridge
    private lateinit var navigationBridge: NavigationBridge
    private lateinit var dataBridge: DataBridge
    private lateinit var analyticsBridge: AnalyticsBridge
    
    fun initialize(context: Context) {
        if (isInitialized) return
        
        // 创建Flutter引擎
        flutterEngine = FlutterEngine(context).apply {
            // 执行Dart入口点
            dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
        }
        
        // 缓存引擎
        FlutterEngineCache.getInstance().put(ENGINE_ID, flutterEngine!!)
        
        // 初始化桥接器
        setupBridges()
        
        isInitialized = true
    }
    
    private fun setupBridges() {
        val messenger = flutterEngine!!.dartExecutor.binaryMessenger
        
        // 账户桥接
        accountBridge = AccountBridge(GlobalContext.get().get())
        MethodChannel(messenger, CHANNEL_ACCOUNT).setMethodCallHandler(accountBridge)
        
        // 导航桥接
        navigationBridge = NavigationBridge()
        MethodChannel(messenger, CHANNEL_NAVIGATION).setMethodCallHandler(navigationBridge)
        
        // 数据桥接
        dataBridge = DataBridge(GlobalContext.get().get())
        MethodChannel(messenger, CHANNEL_DATA).setMethodCallHandler(dataBridge)
        
        // 分析桥接
        analyticsBridge = AnalyticsBridge()
        MethodChannel(messenger, CHANNEL_ANALYTICS).setMethodCallHandler(analyticsBridge)
        
        // 事件通道
        setupEventChannels(messenger)
    }
    
    private fun setupEventChannels(messenger: io.flutter.plugin.common.BinaryMessenger) {
        // 用户状态事件
        EventChannel(messenger, EVENT_USER_STATUS).setStreamHandler(
            UserStatusEventHandler(GlobalContext.get().get())
        )
        
        // 好友更新事件
        EventChannel(messenger, EVENT_FRIEND_UPDATE).setStreamHandler(
            FriendUpdateEventHandler(GlobalContext.get().get())
        )
    }
    
    fun getFlutterEngine(): FlutterEngine? = flutterEngine
}
```

### 2. 账户桥接实现
```kotlin
// AccountBridge.kt
package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.data.interactor.AccountInteractor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AccountBridge(
    private val accountInteractor: AccountInteractor
) : MethodChannel.MethodCallHandler {
    
    private val scope = CoroutineScope(Dispatchers.Main)
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getUserProfile" -> getUserProfile(result)
            "updateUserProfile" -> updateUserProfile(call, result)
            "isLoggedIn" -> isLoggedIn(result)
            "logout" -> logout(result)
            else -> result.notImplemented()
        }
    }
    
    private fun getUserProfile(result: MethodChannel.Result) {
        scope.launch {
            try {
                val account = accountInteractor.accountLiveData.value
                if (account != null) {
                    val profileData = mapOf(
                        "uuid" to account.uuid,
                        "nickname" to account.nickname,
                        "portrait" to account.portrait,
                        "signature" to account.signature,
                        "userNumber" to account.userNumber,
                        "email" to account.email
                    )
                    result.success(profileData)
                } else {
                    result.error("NO_USER", "User not logged in", null)
                }
            } catch (e: Exception) {
                result.error("ERROR", e.message, null)
            }
        }
    }
    
    private fun updateUserProfile(call: MethodCall, result: MethodChannel.Result) {
        scope.launch {
            try {
                val nickname = call.argument<String>("nickname")
                val signature = call.argument<String>("signature")
                
                // 调用原生更新逻辑
                // TODO: 实现具体的更新逻辑
                
                result.success(true)
            } catch (e: Exception) {
                result.error("UPDATE_ERROR", e.message, null)
            }
        }
    }
    
    private fun isLoggedIn(result: MethodChannel.Result) {
        val isLoggedIn = accountInteractor.accountLiveData.value != null
        result.success(isLoggedIn)
    }
    
    private fun logout(result: MethodChannel.Result) {
        scope.launch {
            try {
                // 调用原生登出逻辑
                accountInteractor.logout()
                result.success(true)
            } catch (e: Exception) {
                result.error("LOGOUT_ERROR", e.message, null)
            }
        }
    }
}
```

### 3. Flutter端桥接服务
```dart
// lib/core/bridge/bridge_service.dart
import 'package:flutter/services.dart';

class BridgeService {
  // Method Channels
  static const MethodChannel _accountChannel = 
      MethodChannel('com.socialplay.gpark/account');
  static const MethodChannel _navigationChannel = 
      MethodChannel('com.socialplay.gpark/navigation');
  static const MethodChannel _dataChannel = 
      MethodChannel('com.socialplay.gpark/data');
  static const MethodChannel _analyticsChannel = 
      MethodChannel('com.socialplay.gpark/analytics');
  
  // Event Channels
  static const EventChannel _userStatusChannel = 
      EventChannel('com.socialplay.gpark/user_status');
  static const EventChannel _friendUpdateChannel = 
      EventChannel('com.socialplay.gpark/friend_update');
  
  // 账户相关方法
  static Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      final result = await _accountChannel.invokeMethod('getUserProfile');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }
  
  static Future<bool> updateUserProfile({
    String? nickname,
    String? signature,
  }) async {
    try {
      final result = await _accountChannel.invokeMethod('updateUserProfile', {
        if (nickname != null) 'nickname': nickname,
        if (signature != null) 'signature': signature,
      });
      return result == true;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }
  
  static Future<bool> isLoggedIn() async {
    try {
      final result = await _accountChannel.invokeMethod('isLoggedIn');
      return result == true;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }
  
  // 导航相关方法
  static Future<void> navigateToNative(String route, [Map<String, dynamic>? params]) async {
    try {
      await _navigationChannel.invokeMethod('navigateToNative', {
        'route': route,
        'params': params,
      });
    } catch (e) {
      print('Error navigating to native: $e');
    }
  }
  
  // 分析相关方法
  static Future<void> trackEvent(String eventName, Map<String, dynamic> properties) async {
    try {
      await _analyticsChannel.invokeMethod('trackEvent', {
        'eventName': eventName,
        'properties': properties,
      });
    } catch (e) {
      print('Error tracking event: $e');
    }
  }
  
  // 事件流
  static Stream<Map<String, dynamic>> get userStatusStream {
    return _userStatusChannel.receiveBroadcastStream()
        .map((event) => Map<String, dynamic>.from(event));
  }
  
  static Stream<Map<String, dynamic>> get friendUpdateStream {
    return _friendUpdateChannel.receiveBroadcastStream()
        .map((event) => Map<String, dynamic>.from(event));
  }
}
```

## 阶段三：第一个模块实现 - 设置页面

### 1. 设置模块结构
```
lib/modules/settings/
├── data/
│   ├── models/
│   │   └── settings_model.dart
│   ├── repositories/
│   │   └── settings_repository.dart
│   └── datasources/
│       └── settings_datasource.dart
├── presentation/
│   ├── pages/
│   │   └── settings_page.dart
│   ├── widgets/
│   │   ├── setting_item.dart
│   │   └── setting_section.dart
│   └── providers/
│       └── settings_provider.dart
└── domain/
    ├── entities/
    │   └── setting_entity.dart
    └── usecases/
        └── get_settings_usecase.dart
```

### 2. 设置数据模型
```dart
// lib/modules/settings/data/models/settings_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_model.freezed.dart';
part 'settings_model.g.dart';

@freezed
class SettingsModel with _$SettingsModel {
  const factory SettingsModel({
    @Default(true) bool notificationsEnabled,
    @Default(true) bool soundEnabled,
    @Default(true) bool vibrationEnabled,
    @Default('auto') String language,
    @Default('auto') String theme,
    @Default(true) bool autoUpdate,
    @Default(false) bool debugMode,
  }) = _SettingsModel;
  
  factory SettingsModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsModelFromJson(json);
}
```

这个架构方案提供了：

1. **清晰的模块分离** - 核心功能保持原生，非核心功能迁移到Flutter
2. **稳定的桥接机制** - 通过Method Channel和Event Channel实现双向通信
3. **渐进式迁移** - 从简单模块开始，逐步扩展
4. **性能优化** - 预热Flutter引擎，缓存机制
5. **风险控制** - 保持原生备选方案，支持动态开关

您希望我继续详细实现某个特定模块，还是需要调整架构方案的某些部分？
