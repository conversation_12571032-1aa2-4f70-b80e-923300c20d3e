# Flutter设置页面UI实现

## 1. 设置页面主界面
```dart
// lib/modules/settings/presentation/pages/settings_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/settings_provider.dart';
import '../widgets/setting_item.dart';
import '../widgets/setting_section.dart';
import '../../../../core/bridge/bridge_service.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsNotifierProvider);
    final settingItems = ref.watch(settingItemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _handleBack(context),
        ),
      ),
      body: settingsAsync.when(
        data: (settings) => _buildSettingsList(context, ref, settingItems),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorView(context, error),
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context, WidgetRef ref, List<SettingItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        
        if (item.type == SettingType.navigation && item.key.endsWith('_header')) {
          return SettingSection(title: item.title);
        }
        
        return SettingItemWidget(
          item: item,
          onChanged: (value) => _handleSettingChanged(ref, item.key, value),
          onTap: () => _handleSettingTap(context, ref, item),
        );
      },
    );
  }

  Widget _buildErrorView(BuildContext context, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            '加载设置失败',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }

  void _handleSettingChanged(WidgetRef ref, String key, dynamic value) {
    ref.read(settingsNotifierProvider.notifier).updateSetting(key, value);
    
    // 发送分析事件
    BridgeService.trackEvent('setting_changed', {
      'setting_key': key,
      'setting_value': value.toString(),
    });
  }

  void _handleSettingTap(BuildContext context, WidgetRef ref, SettingItem item) {
    switch (item.key) {
      case 'language':
        _showLanguageSelector(context, ref, item);
        break;
      case 'theme':
        _showThemeSelector(context, ref, item);
        break;
      case 'about':
        _navigateToAbout(context);
        break;
      case 'logout':
        _showLogoutDialog(context);
        break;
    }
  }

  void _showLanguageSelector(BuildContext context, WidgetRef ref, SettingItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildOptionSelector(
        context,
        ref,
        '选择语言',
        item.key,
        item.options ?? [],
        item.value,
      ),
    );
  }

  void _showThemeSelector(BuildContext context, WidgetRef ref, SettingItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildOptionSelector(
        context,
        ref,
        '选择主题',
        item.key,
        item.options ?? [],
        item.value,
      ),
    );
  }

  Widget _buildOptionSelector(
    BuildContext context,
    WidgetRef ref,
    String title,
    String key,
    List<SettingOption> options,
    dynamic currentValue,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ...options.map((option) => ListTile(
            title: Text(option.label),
            trailing: currentValue == option.value
                ? const Icon(Icons.check, color: Colors.blue)
                : null,
            onTap: () {
              _handleSettingChanged(ref, key, option.value);
              Navigator.pop(context);
            },
          )),
        ],
      ),
    );
  }

  void _navigateToAbout(BuildContext context) {
    // 导航到关于页面
    BridgeService.navigateToNative('/about');
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleLogout(context);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _handleLogout(BuildContext context) async {
    try {
      await BridgeService.logout();
      BridgeService.navigateToNative('/login');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('退出登录失败: $e')),
      );
    }
  }

  void _handleBack(BuildContext context) {
    // 返回原生页面
    BridgeService.navigateToNative('/main');
  }
}
```

## 2. 设置项组件
```dart
// lib/modules/settings/presentation/widgets/setting_item.dart
import 'package:flutter/material.dart';
import '../../data/models/settings_model.dart';

class SettingItemWidget extends StatelessWidget {
  final SettingItem item;
  final ValueChanged<dynamic>? onChanged;
  final VoidCallback? onTap;

  const SettingItemWidget({
    super.key,
    required this.item,
    this.onChanged,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    switch (item.type) {
      case SettingType.toggle:
        return _buildToggleItem(context);
      case SettingType.selection:
        return _buildSelectionItem(context);
      case SettingType.navigation:
        return _buildNavigationItem(context);
      case SettingType.action:
        return _buildActionItem(context);
    }
  }

  Widget _buildToggleItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: Switch(
        value: item.value as bool? ?? false,
        onChanged: onChanged,
      ),
      onTap: () => onChanged?.call(!(item.value as bool? ?? false)),
    );
  }

  Widget _buildSelectionItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildNavigationItem(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildActionItem(BuildContext context) {
    final isDestructive = item.key == 'logout';
    
    return ListTile(
      title: Text(
        item.title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
      onTap: onTap,
    );
  }
}
```

## 3. 设置分组组件
```dart
// lib/modules/settings/presentation/widgets/setting_section.dart
import 'package:flutter/material.dart';

class SettingSection extends StatelessWidget {
  final String title;
  final EdgeInsetsGeometry? padding;

  const SettingSection({
    super.key,
    required this.title,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
```

## 4. 主应用路由配置
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'modules/settings/presentation/pages/settings_page.dart';
import 'core/bridge/bridge_service.dart';

void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'GPark Flutter Modules',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      routerConfig: _router,
    );
  }
}

final _router = GoRouter(
  initialLocation: '/settings',
  routes: [
    GoRoute(
      path: '/settings',
      builder: (context, state) => const SettingsPage(),
    ),
    // 其他模块路由...
  ],
);
```

## 5. Android端桥接扩展
```kotlin
// SettingsBridge.kt
package com.socialplay.gpark.flutter.bridge

import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.interactor.AccountInteractor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

class SettingsBridge : MethodChannel.MethodCallHandler {
    
    private val scope = CoroutineScope(Dispatchers.Main)
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val accountInteractor: AccountInteractor by lazy { GlobalContext.get().get() }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getSettings" -> getSettings(result)
            "updateSettings" -> updateSettings(call, result)
            "logout" -> logout(result)
            else -> result.notImplemented()
        }
    }
    
    private fun getSettings(result: MethodChannel.Result) {
        scope.launch {
            try {
                val settings = mapOf(
                    "notificationsEnabled" to metaKV.appKV.notificationsEnabled,
                    "soundEnabled" to metaKV.appKV.soundEnabled,
                    "vibrationEnabled" to metaKV.appKV.vibrationEnabled,
                    "language" to metaKV.appKV.language,
                    "theme" to metaKV.appKV.theme,
                    "autoUpdate" to metaKV.appKV.autoUpdate,
                    "debugMode" to metaKV.appKV.debugMode,
                    "friendRequestNotification" to metaKV.appKV.friendRequestNotification,
                    "gameInviteNotification" to metaKV.appKV.gameInviteNotification,
                    "systemNotification" to metaKV.appKV.systemNotification
                )
                result.success(settings)
            } catch (e: Exception) {
                result.error("GET_SETTINGS_ERROR", e.message, null)
            }
        }
    }
    
    private fun updateSettings(call: MethodCall, result: MethodChannel.Result) {
        scope.launch {
            try {
                val settings = call.arguments as Map<String, Any>
                
                settings["notificationsEnabled"]?.let { 
                    metaKV.appKV.notificationsEnabled = it as Boolean 
                }
                settings["soundEnabled"]?.let { 
                    metaKV.appKV.soundEnabled = it as Boolean 
                }
                settings["vibrationEnabled"]?.let { 
                    metaKV.appKV.vibrationEnabled = it as Boolean 
                }
                settings["language"]?.let { 
                    metaKV.appKV.language = it as String 
                }
                settings["theme"]?.let { 
                    metaKV.appKV.theme = it as String 
                }
                settings["autoUpdate"]?.let { 
                    metaKV.appKV.autoUpdate = it as Boolean 
                }
                settings["debugMode"]?.let { 
                    metaKV.appKV.debugMode = it as Boolean 
                }
                settings["friendRequestNotification"]?.let { 
                    metaKV.appKV.friendRequestNotification = it as Boolean 
                }
                settings["gameInviteNotification"]?.let { 
                    metaKV.appKV.gameInviteNotification = it as Boolean 
                }
                settings["systemNotification"]?.let { 
                    metaKV.appKV.systemNotification = it as Boolean 
                }
                
                result.success(true)
            } catch (e: Exception) {
                result.error("UPDATE_SETTINGS_ERROR", e.message, null)
            }
        }
    }
    
    private fun logout(result: MethodChannel.Result) {
        scope.launch {
            try {
                accountInteractor.logout()
                result.success(true)
            } catch (e: Exception) {
                result.error("LOGOUT_ERROR", e.message, null)
            }
        }
    }
}
```

## 6. 集成测试
```dart
// test/modules/settings/settings_integration_test.dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gpark_flutter_modules/main.dart' as app;

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Settings Integration Tests', () {
    testWidgets('should load settings from native', (tester) async {
      // 模拟原生端返回的设置数据
      const mockSettings = {
        'notificationsEnabled': true,
        'soundEnabled': false,
        'vibrationEnabled': true,
        'language': 'zh',
        'theme': 'auto',
      };

      // 设置Method Channel模拟
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.socialplay.gpark/data'),
        (call) async {
          if (call.method == 'getSettings') {
            return mockSettings;
          }
          return null;
        },
      );

      app.main();
      await tester.pumpAndSettle();

      // 验证设置项是否正确显示
      expect(find.text('推送通知'), findsOneWidget);
      expect(find.text('声音'), findsOneWidget);
      expect(find.text('震动'), findsOneWidget);
    });

    testWidgets('should update settings', (tester) async {
      bool settingsUpdated = false;
      
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.socialplay.gpark/data'),
        (call) async {
          if (call.method == 'updateSettings') {
            settingsUpdated = true;
            return true;
          }
          return null;
        },
      );

      app.main();
      await tester.pumpAndSettle();

      // 点击通知开关
      await tester.tap(find.byType(Switch).first);
      await tester.pumpAndSettle();

      expect(settingsUpdated, isTrue);
    });
  });
}
```

这个完整的设置模块实现提供了：

1. **完整的UI界面** - 现代化的Material Design风格
2. **响应式状态管理** - 使用Riverpod进行状态管理
3. **双向数据同步** - 与原生端实时同步设置
4. **用户体验优化** - 乐观更新、错误处理、加载状态
5. **完整的测试覆盖** - 单元测试和集成测试

接下来您希望我实现哪个模块？个人资料模块还是朋友系统？
